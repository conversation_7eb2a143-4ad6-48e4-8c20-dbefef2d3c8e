'use client';

import { useState, useCallback } from 'react';
import axios from 'axios';

export interface GeneratedMedia {
  url: string;
  metadata: {
    prompt: string;
    type: 'image' | 'video';
    timestamp: string;
    provider: string;
    processingTime?: number;
  };
}

export interface MediaGenerationState {
  data: GeneratedMedia | null;
  loading: boolean;
  error: string | null;
  progress: number;
}

export function useMediaGeneration() {
  const [state, setState] = useState<MediaGenerationState>({
    data: null,
    loading: false,
    error: null,
    progress: 0,
  });

  const generateMedia = useCallback(async (
    prompt: string,
    type: 'image' | 'video',
    options?: {
      style?: 'realistic' | 'illustration' | 'scientific' | 'educational';
      duration?: number;
      dimensions?: { width: number; height: number };
    }
  ) => {
    setState(prev => ({ 
      ...prev, 
      loading: true, 
      error: null, 
      progress: 0 
    }));

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setState(prev => ({
        ...prev,
        progress: Math.min(prev.progress + Math.random() * 20, 90)
      }));
    }, 500);

    try {
      const response = await axios.post('/api/generate-media', {
        prompt,
        type,
        style: options?.style || 'educational',
        duration: options?.duration || (type === 'video' ? 5 : undefined),
        dimensions: options?.dimensions || { width: 800, height: 600 },
      });

      clearInterval(progressInterval);

      if (response.data.success) {
        setState({
          data: {
            url: response.data.data.url,
            metadata: response.data.data.metadata,
          },
          loading: false,
          error: null,
          progress: 100,
        });
        
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Media generation failed');
      }
    } catch (error) {
      clearInterval(progressInterval);
      
      const errorMessage = axios.isAxiosError(error)
        ? error.response?.data?.error || error.message
        : 'Failed to generate media';

      setState({
        data: null,
        loading: false,
        error: errorMessage,
        progress: 0,
      });

      throw new Error(errorMessage);
    }
  }, []);

  const clearMedia = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      progress: 0,
    });
  }, []);

  const retry = useCallback(async () => {
    if (state.data?.metadata) {
      const { prompt, type } = state.data.metadata;
      await generateMedia(prompt, type);
    }
  }, [state.data, generateMedia]);

  return {
    ...state,
    generateMedia,
    clearMedia,
    retry,
  };
}
