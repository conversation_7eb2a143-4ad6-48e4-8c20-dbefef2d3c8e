'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  BookOpen, 
  Maximize2, 
  Minimize2, 
  Volume2, 
  VolumeX, 
  Play, 
  Pause,
  SkipBack,
  Ski<PERSON><PERSON>or<PERSON>,
  Settings,
  Eye,
  Moon,
  Sun
} from 'lucide-react';

interface ReadingModeProps {
  content: string;
  visualizations: Array<{
    id: string;
    text: string;
    url: string;
    type: 'image' | 'video';
    timestamp: number;
  }>;
  onClose?: () => void;
}

export default function ReadingMode({ content, visualizations, onClose }: ReadingModeProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [lineHeight, setLineHeight] = useState(1.6);
  const [isAutoScroll, setIsAutoScroll] = useState(false);
  const [scrollSpeed, setScrollSpeed] = useState(50);
  const [currentVisualization, setCurrentVisualization] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  
  const contentRef = useRef<HTMLDivElement>(null);
  const scrollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-scroll functionality
  useEffect(() => {
    if (isAutoScroll && contentRef.current) {
      scrollIntervalRef.current = setInterval(() => {
        if (contentRef.current) {
          const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
          const maxScroll = scrollHeight - clientHeight;
          
          if (scrollTop < maxScroll) {
            contentRef.current.scrollTop += 1;
            setReadingProgress((scrollTop / maxScroll) * 100);
          } else {
            setIsAutoScroll(false);
          }
        }
      }, 100 - scrollSpeed);
    } else {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
        scrollIntervalRef.current = null;
      }
    }

    return () => {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
      }
    };
  }, [isAutoScroll, scrollSpeed]);

  // Handle scroll progress
  const handleScroll = () => {
    if (contentRef.current && !isAutoScroll) {
      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      const maxScroll = scrollHeight - clientHeight;
      setReadingProgress((scrollTop / maxScroll) * 100);
    }
  };

  // Fullscreen toggle
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (isFullscreen) {
          toggleFullscreen();
        } else {
          onClose?.();
        }
      } else if (e.key === 'f' || e.key === 'F') {
        toggleFullscreen();
      } else if (e.key === 'd' || e.key === 'D') {
        setIsDarkMode(!isDarkMode);
      } else if (e.key === ' ') {
        e.preventDefault();
        setIsAutoScroll(!isAutoScroll);
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isFullscreen, isDarkMode, isAutoScroll, onClose]);

  const nextVisualization = () => {
    setCurrentVisualization((prev) => 
      prev < visualizations.length - 1 ? prev + 1 : 0
    );
  };

  const prevVisualization = () => {
    setCurrentVisualization((prev) => 
      prev > 0 ? prev - 1 : visualizations.length - 1
    );
  };

  return (
    <div className={`fixed inset-0 z-50 ${isDarkMode ? 'bg-gray-900' : 'bg-white'} transition-colors`}>
      {/* Header Controls */}
      <div className={`flex items-center justify-between p-4 border-b ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
      }`}>
        <div className="flex items-center space-x-4">
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-gray-300' 
                : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            <BookOpen className="h-5 w-5" />
          </button>
          
          <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Modo Leitura Imersivo
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Reading Progress */}
          <div className="flex items-center space-x-2">
            <div className={`w-32 h-2 rounded-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
              <div 
                className="h-2 bg-indigo-600 rounded-full transition-all"
                style={{ width: `${readingProgress}%` }}
              />
            </div>
            <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {Math.round(readingProgress)}%
            </span>
          </div>

          {/* Auto-scroll Controls */}
          <button
            onClick={() => setIsAutoScroll(!isAutoScroll)}
            className={`p-2 rounded-lg transition-colors ${
              isAutoScroll 
                ? 'bg-indigo-600 text-white' 
                : isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-300' 
                  : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            {isAutoScroll ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </button>

          {/* Dark Mode Toggle */}
          <button
            onClick={() => setIsDarkMode(!isDarkMode)}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-gray-300' 
                : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </button>

          {/* Settings */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className={`p-2 rounded-lg transition-colors ${
              showSettings 
                ? 'bg-indigo-600 text-white' 
                : isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-300' 
                  : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            <Settings className="h-4 w-4" />
          </button>

          {/* Fullscreen Toggle */}
          <button
            onClick={toggleFullscreen}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-gray-300' 
                : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className={`p-4 border-b ${
          isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
        }`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Font Size */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Tamanho da Fonte
              </label>
              <input
                type="range"
                min="12"
                max="24"
                value={fontSize}
                onChange={(e) => setFontSize(Number(e.target.value))}
                className="w-full"
              />
              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {fontSize}px
              </span>
            </div>

            {/* Line Height */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Espaçamento
              </label>
              <input
                type="range"
                min="1.2"
                max="2.0"
                step="0.1"
                value={lineHeight}
                onChange={(e) => setLineHeight(Number(e.target.value))}
                className="w-full"
              />
              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {lineHeight}x
              </span>
            </div>

            {/* Scroll Speed */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Velocidade Auto-scroll
              </label>
              <input
                type="range"
                min="10"
                max="90"
                value={scrollSpeed}
                onChange={(e) => setScrollSpeed(Number(e.target.value))}
                className="w-full"
              />
              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {scrollSpeed}%
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex h-full">
        {/* Text Content */}
        <div className="flex-1 flex flex-col">
          <div 
            ref={contentRef}
            onScroll={handleScroll}
            className={`flex-1 overflow-y-auto p-8 ${
              isDarkMode ? 'text-gray-100' : 'text-gray-900'
            }`}
            style={{ 
              fontSize: `${fontSize}px`, 
              lineHeight: lineHeight,
              maxWidth: '800px',
              margin: '0 auto'
            }}
          >
            <div 
              dangerouslySetInnerHTML={{ __html: content }}
              className="prose prose-lg max-w-none"
            />
          </div>
        </div>

        {/* Visualization Sidebar */}
        {visualizations.length > 0 && (
          <div className={`w-96 border-l ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className={`font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                  Visualizações
                </h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={prevVisualization}
                    className={`p-1 rounded transition-colors ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-gray-400' 
                        : 'hover:bg-gray-200 text-gray-600'
                    }`}
                  >
                    <SkipBack className="h-4 w-4" />
                  </button>
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {currentVisualization + 1}/{visualizations.length}
                  </span>
                  <button
                    onClick={nextVisualization}
                    className={`p-1 rounded transition-colors ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-gray-400' 
                        : 'hover:bg-gray-200 text-gray-600'
                    }`}
                  >
                    <SkipForward className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {visualizations[currentVisualization] && (
                <div className="space-y-4">
                  <div className="aspect-video rounded-lg overflow-hidden bg-gray-200">
                    {visualizations[currentVisualization].type === 'video' ? (
                      <video 
                        controls 
                        className="w-full h-full object-cover"
                        src={visualizations[currentVisualization].url}
                      />
                    ) : (
                      <img 
                        src={visualizations[currentVisualization].url}
                        alt={visualizations[currentVisualization].text}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  
                  <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    <strong>Conceito:</strong> {visualizations[currentVisualization].text}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Keyboard Shortcuts Help */}
      <div className={`absolute bottom-4 left-4 text-xs ${
        isDarkMode ? 'text-gray-500' : 'text-gray-400'
      }`}>
        <div>ESC: Sair | F: Tela cheia | D: Modo escuro | ESPAÇO: Auto-scroll</div>
      </div>
    </div>
  );
}
