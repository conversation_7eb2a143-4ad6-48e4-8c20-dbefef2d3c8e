{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/hooks/useAnalysis.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport axios from 'axios';\nimport { useWebSocket } from './useWebSocket';\n\nexport interface AnalysisData {\n  concept: string;\n  definition: string;\n  visualizationPrompt: {\n    originalText: string;\n    enhancedPrompt: string;\n    visualizationType: 'image' | 'video';\n    scientificContext: string;\n    educationalLevel: 'basic' | 'intermediate' | 'advanced';\n  };\n  relatedConcepts: string[];\n  difficulty: 'basic' | 'intermediate' | 'advanced';\n  educationalSuggestions: string[];\n  timestamp: string;\n}\n\nexport interface AnalysisState {\n  data: AnalysisData | null;\n  loading: boolean;\n  error: string | null;\n}\n\nexport function useAnalysis() {\n  const [state, setState] = useState<AnalysisState>({\n    data: null,\n    loading: false,\n    error: null,\n  });\n\n  const analyzeText = useCallback(async (\n    selectedText: string,\n    context?: string,\n    visualizationType: 'image' | 'video' = 'image'\n  ) => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const response = await axios.post('/api/analyze', {\n        selectedText,\n        context,\n        visualizationType,\n      });\n\n      if (response.data.success) {\n        setState({\n          data: response.data.data,\n          loading: false,\n          error: null,\n        });\n        return response.data.data;\n      } else {\n        throw new Error(response.data.error || 'Analysis failed');\n      }\n    } catch (error) {\n      const errorMessage = axios.isAxiosError(error)\n        ? error.response?.data?.error || error.message\n        : 'Failed to analyze text';\n\n      setState({\n        data: null,\n        loading: false,\n        error: errorMessage,\n      });\n\n      throw new Error(errorMessage);\n    }\n  }, []);\n\n  const clearAnalysis = useCallback(() => {\n    setState({\n      data: null,\n      loading: false,\n      error: null,\n    });\n  }, []);\n\n  const retry = useCallback(async () => {\n    if (state.data?.visualizationPrompt.originalText) {\n      await analyzeText(\n        state.data.visualizationPrompt.originalText,\n        state.data.visualizationPrompt.scientificContext,\n        state.data.visualizationPrompt.visualizationType\n      );\n    }\n  }, [state.data, analyzeText]);\n\n  return {\n    ...state,\n    analyzeText,\n    clearAnalysis,\n    retry,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AA4BO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAC9B,cACA,SACA,oBAAuC,OAAO;YAE9C;wDAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,gBAAgB;oBAChD;oBACA;oBACA;gBACF;gBAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;oBACzB,SAAS;wBACP,MAAM,SAAS,IAAI,CAAC,IAAI;wBACxB,SAAS;wBACT,OAAO;oBACT;oBACA,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,KAAK,IAAI;gBACzC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,wIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SACpC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,GAC5C;gBAEJ,SAAS;oBACP,MAAM;oBACN,SAAS;oBACT,OAAO;gBACT;gBAEA,MAAM,IAAI,MAAM;YAClB;QACF;+CAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAChC,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;QACF;iDAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YACxB,IAAI,MAAM,IAAI,EAAE,oBAAoB,cAAc;gBAChD,MAAM,YACJ,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAC3C,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAChD,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;YAEpD;QACF;yCAAG;QAAC,MAAM,IAAI;QAAE;KAAY;IAE5B,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;IACF;AACF;GAtEgB", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/components/editor/text-editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport Highlight from '@tiptap/extension-highlight';\nimport TextStyle from '@tiptap/extension-text-style';\nimport { Color } from '@tiptap/extension-color';\nimport { useState, useCallback } from 'react';\nimport { Sparkles, Type, Palette, Eye, Brain, Loader2 } from 'lucide-react';\nimport { useAnalysis } from '@/hooks/useAnalysis';\n\ninterface TextEditorProps {\n  onTextSelect?: (text: string) => void;\n  onContentChange?: (content: string) => void;\n  onAnalysisComplete?: (analysis: any) => void;\n}\n\nexport default function TextEditor({ onTextSelect, onContentChange, onAnalysisComplete }: TextEditorProps) {\n  const [selectedText, setSelectedText] = useState('');\n  const [showToolbar, setShowToolbar] = useState(false);\n  const { analyzeText, loading: analyzing, error: analysisError } = useAnalysis();\n\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      Highlight.configure({\n        multicolor: true,\n      }),\n      TextStyle,\n      Color,\n    ],\n    immediatelyRender: false,\n    content: `\n      <p>A <mark data-color=\"#fbbf24\">mitose</mark> é um processo fundamental da divisão celular onde uma célula se divide para formar duas células filhas geneticamente idênticas.</p>\n      \n      <p>Durante a <mark data-color=\"#fbbf24\">prófase</mark>, os cromossomos se condensam e tornam-se visíveis ao microscópio. O envelope nuclear começa a se desintegrar.</p>\n      \n      <p>Na <mark data-color=\"#fbbf24\">metáfase</mark>, os cromossomos se alinham no centro da célula, formando a placa metafásica. Este é um momento crucial para garantir que cada célula filha receba o número correto de cromossomos.</p>\n      \n      <p>Durante a <mark data-color=\"#fbbf24\">anáfase</mark>, as cromátides irmãs se separam e migram para polos opostos da célula.</p>\n      \n      <p>Finalmente, na <mark data-color=\"#fbbf24\">telófase</mark>, novos envelopes nucleares se formam ao redor de cada conjunto de cromossomos, completando o processo de divisão.</p>\n    `,\n    onUpdate: ({ editor }) => {\n      const content = editor.getHTML();\n      onContentChange?.(content);\n    },\n    onSelectionUpdate: ({ editor }) => {\n      const { from, to } = editor.state.selection;\n      const text = editor.state.doc.textBetween(from, to, '');\n      \n      if (text.trim().length > 0) {\n        setSelectedText(text.trim());\n        setShowToolbar(true);\n        onTextSelect?.(text.trim());\n      } else {\n        setSelectedText('');\n        setShowToolbar(false);\n      }\n    },\n  });\n\n  const highlightSelection = useCallback((color: string) => {\n    if (editor && selectedText) {\n      editor.chain().focus().toggleHighlight({ color }).run();\n      onTextSelect?.(selectedText);\n    }\n  }, [editor, selectedText, onTextSelect]);\n\n  const generateVisualization = useCallback(async (visualizationType: 'image' | 'video' = 'image') => {\n    if (selectedText && !analyzing) {\n      try {\n        const analysis = await analyzeText(selectedText, '', visualizationType);\n        onAnalysisComplete?.(analysis);\n        onTextSelect?.(selectedText);\n      } catch (error) {\n        console.error('Analysis failed:', error);\n      }\n    }\n  }, [selectedText, analyzing, analyzeText, onAnalysisComplete, onTextSelect]);\n\n  if (!editor) {\n    return (\n      <div className=\"border rounded-lg p-4 min-h-[400px] bg-gray-50 animate-pulse\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Floating Toolbar */}\n      {showToolbar && selectedText && (\n        <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mb-2 z-10\">\n          <div className=\"bg-white border rounded-lg shadow-lg p-2 flex items-center space-x-2\">\n            <span className=\"text-xs text-gray-600 px-2\">\"{selectedText.slice(0, 20)}...\"</span>\n            <div className=\"w-px h-6 bg-gray-200\"></div>\n            \n            {/* Highlight Colors */}\n            <button\n              onClick={() => highlightSelection('#fbbf24')}\n              className=\"w-6 h-6 bg-yellow-300 rounded hover:scale-110 transition-transform\"\n              title=\"Destacar em amarelo\"\n            />\n            <button\n              onClick={() => highlightSelection('#60a5fa')}\n              className=\"w-6 h-6 bg-blue-400 rounded hover:scale-110 transition-transform\"\n              title=\"Destacar em azul\"\n            />\n            <button\n              onClick={() => highlightSelection('#34d399')}\n              className=\"w-6 h-6 bg-green-400 rounded hover:scale-110 transition-transform\"\n              title=\"Destacar em verde\"\n            />\n            \n            <div className=\"w-px h-6 bg-gray-200\"></div>\n            \n            {/* Generate Visualization Buttons */}\n            <button\n              onClick={() => generateVisualization('image')}\n              disabled={analyzing}\n              className=\"flex items-center space-x-1 px-3 py-1 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {analyzing ? <Loader2 className=\"h-3 w-3 animate-spin\" /> : <Sparkles className=\"h-3 w-3\" />}\n              <span>Imagem</span>\n            </button>\n\n            <button\n              onClick={() => generateVisualization('video')}\n              disabled={analyzing}\n              className=\"flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {analyzing ? <Loader2 className=\"h-3 w-3 animate-spin\" /> : <Brain className=\"h-3 w-3\" />}\n              <span>Vídeo</span>\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Editor Toolbar */}\n      <div className=\"border-b p-2 bg-gray-50 rounded-t-lg\">\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => editor.chain().focus().toggleBold().run()}\n            className={`p-2 rounded hover:bg-gray-200 transition-colors ${\n              editor.isActive('bold') ? 'bg-gray-200' : ''\n            }`}\n          >\n            <Type className=\"h-4 w-4\" />\n          </button>\n          \n          <button\n            onClick={() => editor.chain().focus().toggleItalic().run()}\n            className={`p-2 rounded hover:bg-gray-200 transition-colors ${\n              editor.isActive('italic') ? 'bg-gray-200' : ''\n            }`}\n          >\n            <span className=\"text-sm font-serif italic\">I</span>\n          </button>\n\n          <div className=\"w-px h-6 bg-gray-300\"></div>\n\n          <button\n            onClick={() => editor.chain().focus().toggleHighlight({ color: '#fbbf24' }).run()}\n            className={`p-2 rounded hover:bg-gray-200 transition-colors ${\n              editor.isActive('highlight') ? 'bg-gray-200' : ''\n            }`}\n          >\n            <Palette className=\"h-4 w-4\" />\n          </button>\n\n          <div className=\"flex-1\"></div>\n\n          <div className=\"flex items-center space-x-2 text-xs text-gray-600\">\n            <Eye className=\"h-4 w-4\" />\n            <span>Destaque termos para gerar visualizações</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Editor Content */}\n      <div className=\"border border-t-0 rounded-b-lg p-4 min-h-[400px] bg-white prose prose-sm max-w-none\">\n        <EditorContent editor={editor} />\n      </div>\n\n      {/* Selected Text Info */}\n      {selectedText && (\n        <div className=\"mt-2 p-2 bg-indigo-50 border border-indigo-200 rounded text-sm\">\n          <span className=\"text-indigo-700 font-medium\">Texto selecionado:</span>\n          <span className=\"text-indigo-600 ml-2\">\"{selectedText}\"</span>\n          {analyzing && (\n            <div className=\"flex items-center mt-2 text-indigo-600\">\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n              <span>Analisando com IA...</span>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Analysis Error */}\n      {analysisError && (\n        <div className=\"mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm\">\n          <span className=\"text-red-700 font-medium\">Erro na análise:</span>\n          <span className=\"text-red-600 ml-2\">{analysisError}</span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AAiBe,SAAS,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,kBAAkB,EAAmB;;IACvG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,WAAW,EAAE,SAAS,SAAS,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE5E,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,8JAAA,CAAA,UAAU;YACV,sKAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAClB,YAAY;YACd;YACA,0KAAA,CAAA,UAAS;YACT,kKAAA,CAAA,QAAK;SACN;QACD,mBAAmB;QACnB,SAAS,CAAC;;;;;;;;;;IAUV,CAAC;QACD,QAAQ;4CAAE,CAAC,EAAE,MAAM,EAAE;gBACnB,MAAM,UAAU,OAAO,OAAO;gBAC9B,kBAAkB;YACpB;;QACA,iBAAiB;4CAAE,CAAC,EAAE,MAAM,EAAE;gBAC5B,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,KAAK,CAAC,SAAS;gBAC3C,MAAM,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,IAAI;gBAEpD,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG;oBAC1B,gBAAgB,KAAK,IAAI;oBACzB,eAAe;oBACf,eAAe,KAAK,IAAI;gBAC1B,OAAO;oBACL,gBAAgB;oBAChB,eAAe;gBACjB;YACF;;IACF;IAEA,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACtC,IAAI,UAAU,cAAc;gBAC1B,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC;oBAAE;gBAAM,GAAG,GAAG;gBACrD,eAAe;YACjB;QACF;qDAAG;QAAC;QAAQ;QAAc;KAAa;IAEvC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO,oBAAuC,OAAO;YAC7F,IAAI,gBAAgB,CAAC,WAAW;gBAC9B,IAAI;oBACF,MAAM,WAAW,MAAM,YAAY,cAAc,IAAI;oBACrD,qBAAqB;oBACrB,eAAe;gBACjB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oBAAoB;gBACpC;YACF;QACF;wDAAG;QAAC;QAAc;QAAW;QAAa;QAAoB;KAAa;IAE3E,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,eAAe,8BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;gCAA6B;gCAAE,aAAa,KAAK,CAAC,GAAG;gCAAI;;;;;;;sCACzE,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;;;;;;sCAER,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;;;;;;sCAER,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;;;;;;sCAGR,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BACC,SAAS,IAAM,sBAAsB;4BACrC,UAAU;4BACV,WAAU;;gCAET,0BAAY,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAA4B,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CAChF,6LAAC;8CAAK;;;;;;;;;;;;sCAGR,6LAAC;4BACC,SAAS,IAAM,sBAAsB;4BACrC,UAAU;4BACV,WAAU;;gCAET,0BAAY,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAA4B,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CAC7E,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;4BACtD,WAAW,CAAC,gDAAgD,EAC1D,OAAO,QAAQ,CAAC,UAAU,gBAAgB,IAC1C;sCAEF,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,6LAAC;4BACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;4BACxD,WAAW,CAAC,gDAAgD,EAC1D,OAAO,QAAQ,CAAC,YAAY,gBAAgB,IAC5C;sCAEF,cAAA,6LAAC;gCAAK,WAAU;0CAA4B;;;;;;;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC;oCAAE,OAAO;gCAAU,GAAG,GAAG;4BAC/E,WAAW,CAAC,gDAAgD,EAC1D,OAAO,QAAQ,CAAC,eAAe,gBAAgB,IAC/C;sCAEF,cAAA,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGrB,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qKAAA,CAAA,gBAAa;oBAAC,QAAQ;;;;;;;;;;;YAIxB,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAA8B;;;;;;kCAC9C,6LAAC;wBAAK,WAAU;;4BAAuB;4BAAE;4BAAa;;;;;;;oBACrD,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;YAOb,+BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAA2B;;;;;;kCAC3C,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;;;;;;;AAK/C;GAjMwB;;QAG4C,8HAAA,CAAA,cAAW;QAE9D,qKAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/hooks/useMediaGeneration.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport axios from 'axios';\n\nexport interface GeneratedMedia {\n  url: string;\n  metadata: {\n    prompt: string;\n    type: 'image' | 'video';\n    timestamp: string;\n    provider: string;\n    processingTime?: number;\n  };\n}\n\nexport interface MediaGenerationState {\n  data: GeneratedMedia | null;\n  loading: boolean;\n  error: string | null;\n  progress: number;\n}\n\nexport function useMediaGeneration() {\n  const [state, setState] = useState<MediaGenerationState>({\n    data: null,\n    loading: false,\n    error: null,\n    progress: 0,\n  });\n\n  const generateMedia = useCallback(async (\n    prompt: string,\n    type: 'image' | 'video',\n    options?: {\n      style?: 'realistic' | 'illustration' | 'scientific' | 'educational';\n      duration?: number;\n      dimensions?: { width: number; height: number };\n    }\n  ) => {\n    setState(prev => ({ \n      ...prev, \n      loading: true, \n      error: null, \n      progress: 0 \n    }));\n\n    // Simulate progress updates\n    const progressInterval = setInterval(() => {\n      setState(prev => ({\n        ...prev,\n        progress: Math.min(prev.progress + Math.random() * 20, 90)\n      }));\n    }, 500);\n\n    try {\n      const response = await axios.post('/api/generate-media', {\n        prompt,\n        type,\n        style: options?.style || 'educational',\n        duration: options?.duration || (type === 'video' ? 5 : undefined),\n        dimensions: options?.dimensions || { width: 800, height: 600 },\n      });\n\n      clearInterval(progressInterval);\n\n      if (response.data.success) {\n        setState({\n          data: {\n            url: response.data.data.url,\n            metadata: response.data.data.metadata,\n          },\n          loading: false,\n          error: null,\n          progress: 100,\n        });\n        \n        return response.data.data;\n      } else {\n        throw new Error(response.data.error || 'Media generation failed');\n      }\n    } catch (error) {\n      clearInterval(progressInterval);\n      \n      const errorMessage = axios.isAxiosError(error)\n        ? error.response?.data?.error || error.message\n        : 'Failed to generate media';\n\n      setState({\n        data: null,\n        loading: false,\n        error: errorMessage,\n        progress: 0,\n      });\n\n      throw new Error(errorMessage);\n    }\n  }, []);\n\n  const clearMedia = useCallback(() => {\n    setState({\n      data: null,\n      loading: false,\n      error: null,\n      progress: 0,\n    });\n  }, []);\n\n  const retry = useCallback(async () => {\n    if (state.data?.metadata) {\n      const { prompt, type } = state.data.metadata;\n      await generateMedia(prompt, type);\n    }\n  }, [state.data, generateMedia]);\n\n  return {\n    ...state,\n    generateMedia,\n    clearMedia,\n    retry,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAuBO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QACvD,MAAM;QACN,SAAS;QACT,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAChC,QACA,MACA;YAMA;iEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,SAAS;wBACT,OAAO;wBACP,UAAU;oBACZ,CAAC;;YAED,4BAA4B;YAC5B,MAAM,mBAAmB;kFAAY;oBACnC;0FAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,UAAU,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI;4BACzD,CAAC;;gBACH;iFAAG;YAEH,IAAI;gBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,uBAAuB;oBACvD;oBACA;oBACA,OAAO,SAAS,SAAS;oBACzB,UAAU,SAAS,YAAY,CAAC,SAAS,UAAU,IAAI,SAAS;oBAChE,YAAY,SAAS,cAAc;wBAAE,OAAO;wBAAK,QAAQ;oBAAI;gBAC/D;gBAEA,cAAc;gBAEd,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;oBACzB,SAAS;wBACP,MAAM;4BACJ,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG;4BAC3B,UAAU,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;wBACvC;wBACA,SAAS;wBACT,OAAO;wBACP,UAAU;oBACZ;oBAEA,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,KAAK,IAAI;gBACzC;YACF,EAAE,OAAO,OAAO;gBACd,cAAc;gBAEd,MAAM,eAAe,wIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SACpC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,GAC5C;gBAEJ,SAAS;oBACP,MAAM;oBACN,SAAS;oBACT,OAAO;oBACP,UAAU;gBACZ;gBAEA,MAAM,IAAI,MAAM;YAClB;QACF;wDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC7B,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,UAAU;YACZ;QACF;qDAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACxB,IAAI,MAAM,IAAI,EAAE,UAAU;gBACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;gBAC5C,MAAM,cAAc,QAAQ;YAC9B;QACF;gDAAG;QAAC,MAAM,IAAI;QAAE;KAAc;IAE9B,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;IACF;AACF;GAlGgB", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/components/visualization/visualization-panel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Play, Download, <PERSON>rkles, Loader2, Image as ImageIcon, Video, AlertCircle } from 'lucide-react';\nimport { useMediaGeneration } from '@/hooks/useMediaGeneration';\n\ninterface VisualizationPanelProps {\n  selectedText: string;\n  analysisData?: any;\n  onGenerate?: (text: string, type: 'image' | 'video') => void;\n  onVisualizationGenerated?: (text: string, url: string, type: 'image' | 'video') => void;\n}\n\ninterface GeneratedContent {\n  type: 'image' | 'video';\n  url: string;\n  prompt: string;\n  timestamp: number;\n}\n\nexport default function VisualizationPanel({ selectedText, analysisData, onGenerate, onVisualizationGenerated }: VisualizationPanelProps) {\n  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);\n  const [generationType, setGenerationType] = useState<'image' | 'video'>('image');\n  const [history, setHistory] = useState<GeneratedContent[]>([]);\n\n  const {\n    generateMedia,\n    loading: isGenerating,\n    error: generationError,\n    progress\n  } = useMediaGeneration();\n\n  // Generate content using real API\n  const generateContent = async (text: string, type: 'image' | 'video') => {\n    try {\n      // Use enhanced prompt from analysis if available\n      const prompt = analysisData?.visualizationPrompt?.enhancedPrompt ||\n                    `Visualização científica educativa de: ${text}`;\n\n      const result = await generateMedia(prompt, type, {\n        style: 'educational',\n        duration: type === 'video' ? 5 : undefined,\n        dimensions: { width: 800, height: 600 }\n      });\n\n      const newContent: GeneratedContent = {\n        type,\n        url: result.url,\n        prompt: result.metadata.prompt,\n        timestamp: result.metadata.timestamp ? new Date(result.metadata.timestamp).getTime() : Date.now()\n      };\n\n      setGeneratedContent(newContent);\n      setHistory(prev => [newContent, ...prev.slice(0, 4)]); // Keep last 5\n\n      // Notify parent component about new visualization\n      onVisualizationGenerated?.(text, result.url, type);\n      onGenerate?.(text, type);\n    } catch (error) {\n      console.error('Content generation failed:', error);\n    }\n  };\n\n  const handleGenerate = () => {\n    if (selectedText.trim()) {\n      generateContent(selectedText, generationType);\n    }\n  };\n\n  const downloadContent = () => {\n    if (generatedContent) {\n      const link = document.createElement('a');\n      link.href = generatedContent.url;\n      link.download = `${selectedText.replace(/\\s+/g, '_')}_${generatedContent.type}.${generatedContent.type === 'video' ? 'mp4' : 'jpg'}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Visualização</h3>\n        <div className=\"flex items-center space-x-2\">\n          {generatedContent && (\n            <>\n              <button \n                onClick={() => {/* Play functionality */}}\n                className=\"p-2 text-gray-600 hover:text-indigo-600 transition-colors\"\n                title=\"Reproduzir\"\n              >\n                <Play className=\"h-5 w-5\" />\n              </button>\n              <button \n                onClick={downloadContent}\n                className=\"p-2 text-gray-600 hover:text-indigo-600 transition-colors\"\n                title=\"Baixar\"\n              >\n                <Download className=\"h-5 w-5\" />\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Content Type Selector */}\n      <div className=\"flex items-center space-x-2 mb-4\">\n        <span className=\"text-sm text-gray-600\">Tipo:</span>\n        <div className=\"flex bg-gray-100 rounded-lg p-1\">\n          <button\n            onClick={() => setGenerationType('image')}\n            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${\n              generationType === 'image' \n                ? 'bg-white text-indigo-600 shadow-sm' \n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <ImageIcon className=\"h-4 w-4\" />\n            <span>Imagem</span>\n          </button>\n          <button\n            onClick={() => setGenerationType('video')}\n            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${\n              generationType === 'video' \n                ? 'bg-white text-indigo-600 shadow-sm' \n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <Video className=\"h-4 w-4\" />\n            <span>Vídeo</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      <div className=\"border rounded-lg min-h-[350px] bg-gray-50 relative overflow-hidden\">\n        {isGenerating ? (\n          // Loading State with Progress\n          <div className=\"absolute inset-0 flex items-center justify-center bg-white/90\">\n            <div className=\"text-center max-w-xs\">\n              <Loader2 className=\"h-8 w-8 text-indigo-600 animate-spin mx-auto mb-4\" />\n              <p className=\"text-gray-600 mb-2\">Gerando {generationType === 'video' ? 'vídeo' : 'imagem'}...</p>\n              <p className=\"text-sm text-gray-500 mb-4\">\"{selectedText.slice(0, 50)}...\"</p>\n\n              {/* Progress Bar */}\n              <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\n                <div\n                  className=\"bg-indigo-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${progress}%` }}\n                ></div>\n              </div>\n              <p className=\"text-xs text-gray-500\">{Math.round(progress)}% completo</p>\n            </div>\n          </div>\n        ) : generationError ? (\n          // Error State\n          <div className=\"absolute inset-0 flex items-center justify-center bg-red-50\">\n            <div className=\"text-center max-w-xs\">\n              <AlertCircle className=\"h-8 w-8 text-red-600 mx-auto mb-4\" />\n              <p className=\"text-red-600 mb-2\">Erro na geração</p>\n              <p className=\"text-sm text-red-500 mb-4\">{generationError}</p>\n              <button\n                onClick={() => generateContent(selectedText, generationType)}\n                className=\"px-4 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors\"\n              >\n                Tentar Novamente\n              </button>\n            </div>\n          </div>\n        ) : generatedContent ? (\n          // Generated Content\n          <div className=\"p-4\">\n            {generatedContent.type === 'video' ? (\n              <video \n                controls \n                className=\"w-full h-64 rounded-lg bg-black\"\n                poster=\"https://via.placeholder.com/600x400/4f46e5/ffffff?text=Video+Preview\"\n              >\n                <source src={generatedContent.url} type=\"video/mp4\" />\n                Seu navegador não suporta vídeo HTML5.\n              </video>\n            ) : (\n              <img \n                src={generatedContent.url} \n                alt={generatedContent.prompt}\n                className=\"w-full h-64 object-cover rounded-lg\"\n              />\n            )}\n            \n            <div className=\"mt-4 p-3 bg-white rounded-lg border\">\n              <p className=\"text-sm text-gray-600 mb-1\">Prompt gerado:</p>\n              <p className=\"text-sm text-gray-900\">{generatedContent.prompt}</p>\n            </div>\n          </div>\n        ) : (\n          // Empty State\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Sparkles className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                {selectedText \n                  ? `Pronto para visualizar: \"${selectedText.slice(0, 30)}${selectedText.length > 30 ? '...' : ''}\"`\n                  : 'Destaque um termo no texto para gerar visualização'\n                }\n              </p>\n              {selectedText && (\n                <button\n                  onClick={handleGenerate}\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto\"\n                >\n                  <Sparkles className=\"h-4 w-4\" />\n                  <span>Gerar {generationType === 'video' ? 'Vídeo' : 'Imagem'}</span>\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Analysis Results */}\n      {analysisData && (\n        <div className=\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-blue-900 mb-2\">Análise IA</h4>\n          <div className=\"space-y-2 text-sm\">\n            <div>\n              <span className=\"font-medium text-blue-800\">Conceito:</span>\n              <span className=\"text-blue-700 ml-2\">{analysisData.concept}</span>\n            </div>\n            <div>\n              <span className=\"font-medium text-blue-800\">Definição:</span>\n              <p className=\"text-blue-700 mt-1\">{analysisData.definition}</p>\n            </div>\n            <div>\n              <span className=\"font-medium text-blue-800\">Nível:</span>\n              <span className=\"text-blue-700 ml-2 capitalize\">{analysisData.difficulty}</span>\n            </div>\n            {analysisData.relatedConcepts?.length > 0 && (\n              <div>\n                <span className=\"font-medium text-blue-800\">Conceitos relacionados:</span>\n                <div className=\"flex flex-wrap gap-1 mt-1\">\n                  {analysisData.relatedConcepts.map((concept: string, index: number) => (\n                    <span key={index} className=\"px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded\">\n                      {concept}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* History */}\n      {history.length > 0 && (\n        <div className=\"mt-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Histórico</h4>\n          <div className=\"flex space-x-2 overflow-x-auto pb-2\">\n            {history.map((item, index) => (\n              <button\n                key={item.timestamp}\n                onClick={() => setGeneratedContent(item)}\n                className=\"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-transparent hover:border-indigo-300 transition-colors\"\n              >\n                {item.type === 'video' ? (\n                  <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n                    <Video className=\"h-6 w-6 text-gray-500\" />\n                  </div>\n                ) : (\n                  <img\n                    src={item.url}\n                    alt={`Histórico ${index + 1}`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                )}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAoBe,SAAS,mBAAmB,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,wBAAwB,EAA2B;;IACtI,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAE7D,MAAM,EACJ,aAAa,EACb,SAAS,YAAY,EACrB,OAAO,eAAe,EACtB,QAAQ,EACT,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IAErB,kCAAkC;IAClC,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,iDAAiD;YACjD,MAAM,SAAS,cAAc,qBAAqB,kBACpC,CAAC,sCAAsC,EAAE,MAAM;YAE7D,MAAM,SAAS,MAAM,cAAc,QAAQ,MAAM;gBAC/C,OAAO;gBACP,UAAU,SAAS,UAAU,IAAI;gBACjC,YAAY;oBAAE,OAAO;oBAAK,QAAQ;gBAAI;YACxC;YAEA,MAAM,aAA+B;gBACnC;gBACA,KAAK,OAAO,GAAG;gBACf,QAAQ,OAAO,QAAQ,CAAC,MAAM;gBAC9B,WAAW,OAAO,QAAQ,CAAC,SAAS,GAAG,IAAI,KAAK,OAAO,QAAQ,CAAC,SAAS,EAAE,OAAO,KAAK,KAAK,GAAG;YACjG;YAEA,oBAAoB;YACpB,WAAW,CAAA,OAAQ;oBAAC;uBAAe,KAAK,KAAK,CAAC,GAAG;iBAAG,GAAG,cAAc;YAErE,kDAAkD;YAClD,2BAA2B,MAAM,OAAO,GAAG,EAAE;YAC7C,aAAa,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,aAAa,IAAI,IAAI;YACvB,gBAAgB,cAAc;QAChC;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,kBAAkB;YACpB,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG,iBAAiB,GAAG;YAChC,KAAK,QAAQ,GAAG,GAAG,aAAa,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,iBAAiB,IAAI,CAAC,CAAC,EAAE,iBAAiB,IAAI,KAAK,UAAU,QAAQ,OAAO;YACpI,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;kCACZ,kCACC;;8CACE,6LAAC;oCACC,SAAS,KAA+B;oCACxC,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,wEAAwE,EAClF,mBAAmB,UACf,uCACA,qCACJ;;kDAEF,6LAAC,uMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,wEAAwE,EAClF,mBAAmB,UACf,uCACA,qCACJ;;kDAEF,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACZ,eACC,8BAA8B;8BAC9B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAE,WAAU;;oCAAqB;oCAAS,mBAAmB,UAAU,UAAU;oCAAS;;;;;;;0CAC3F,6LAAC;gCAAE,WAAU;;oCAA6B;oCAAE,aAAa,KAAK,CAAC,GAAG;oCAAI;;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGnC,6LAAC;gCAAE,WAAU;;oCAAyB,KAAK,KAAK,CAAC;oCAAU;;;;;;;;;;;;;;;;;2BAG7D,kBACF,cAAc;8BACd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAoB;;;;;;0CACjC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAC1C,6LAAC;gCACC,SAAS,IAAM,gBAAgB,cAAc;gCAC7C,WAAU;0CACX;;;;;;;;;;;;;;;;2BAKH,mBACF,oBAAoB;8BACpB,6LAAC;oBAAI,WAAU;;wBACZ,iBAAiB,IAAI,KAAK,wBACzB,6LAAC;4BACC,QAAQ;4BACR,WAAU;4BACV,QAAO;;8CAEP,6LAAC;oCAAO,KAAK,iBAAiB,GAAG;oCAAE,MAAK;;;;;;gCAAc;;;;;;iDAIxD,6LAAC;4BACC,KAAK,iBAAiB,GAAG;4BACzB,KAAK,iBAAiB,MAAM;4BAC5B,WAAU;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAE,WAAU;8CAAyB,iBAAiB,MAAM;;;;;;;;;;;;;;;;;2BAIjE,cAAc;8BACd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAE,WAAU;0CACV,eACG,CAAC,yBAAyB,EAAE,aAAa,KAAK,CAAC,GAAG,MAAM,aAAa,MAAM,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC,GAChG;;;;;;4BAGL,8BACC,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;;4CAAK;4CAAO,mBAAmB,UAAU,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS/D,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,6LAAC;wCAAK,WAAU;kDAAsB,aAAa,OAAO;;;;;;;;;;;;0CAE5D,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,6LAAC;wCAAE,WAAU;kDAAsB,aAAa,UAAU;;;;;;;;;;;;0CAE5D,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,6LAAC;wCAAK,WAAU;kDAAiC,aAAa,UAAU;;;;;;;;;;;;4BAEzE,aAAa,eAAe,EAAE,SAAS,mBACtC,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDACZ,aAAa,eAAe,CAAC,GAAG,CAAC,CAAC,SAAiB,sBAClD,6LAAC;gDAAiB,WAAU;0DACzB;+CADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYxB,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;gCAEC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET,KAAK,IAAI,KAAK,wBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;yDAGnB,6LAAC;oCACC,KAAK,KAAK,GAAG;oCACb,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG;oCAC7B,WAAU;;;;;;+BAZT,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;AAsBnC;GAzQwB;;QAUlB,qIAAA,CAAA,qBAAkB;;;KAVA", "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/hooks/useWebSocket.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState, useCallback } from 'react';\nimport { io, Socket } from 'socket.io-client';\n\nexport interface WebSocketState {\n  connected: boolean;\n  sessionId: string | null;\n  users: string[];\n  error: string | null;\n}\n\nexport interface WebSocketEvents {\n  onTextSelected?: (data: { userId: string; selectedText: string; timestamp: number }) => void;\n  onUserJoined?: (data: { userId: string; sessionId: string }) => void;\n  onUserLeft?: (data: { userId: string; sessionId: string }) => void;\n  onAnalysisStarted?: (data: { userId: string; text: string; type: 'image' | 'video' }) => void;\n  onAnalysisProgress?: (data: { userId: string; progress: number; stage: string }) => void;\n  onAnalysisCompleted?: (data: { userId: string; result: any }) => void;\n  onAnalysisError?: (data: { userId: string; error: string }) => void;\n  onGenerationStarted?: (data: { userId: string; prompt: string; type: 'image' | 'video' }) => void;\n  onGenerationProgress?: (data: { userId: string; progress: number; stage: string }) => void;\n  onGenerationCompleted?: (data: { userId: string; result: any }) => void;\n  onGenerationError?: (data: { userId: string; error: string }) => void;\n}\n\nexport function useWebSocket(events?: WebSocketEvents) {\n  const socketRef = useRef<Socket | null>(null);\n  const [state, setState] = useState<WebSocketState>({\n    connected: false,\n    sessionId: null,\n    users: [],\n    error: null,\n  });\n\n  // Initialize socket connection\n  useEffect(() => {\n    // For development, we'll simulate WebSocket functionality\n    // In production with custom server, this would connect to actual WebSocket\n    console.log('WebSocket simulation mode - custom server not running');\n\n    // Simulate connection\n    setState(prev => ({ ...prev, connected: true, error: null }));\n\n    return () => {\n      console.log('WebSocket simulation disconnected');\n    };\n\n    /*\n    // Real WebSocket implementation (uncomment when using custom server)\n    const socket = io(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', {\n      transports: ['websocket', 'polling'],\n      autoConnect: true,\n    });\n\n    socketRef.current = socket;\n\n    // Connection events\n    socket.on('connect', () => {\n      setState(prev => ({ ...prev, connected: true, error: null }));\n      console.log('WebSocket connected');\n    });\n\n    socket.on('disconnect', () => {\n      setState(prev => ({ ...prev, connected: false }));\n      console.log('WebSocket disconnected');\n    });\n\n    socket.on('connect_error', (error) => {\n      setState(prev => ({ ...prev, error: error.message }));\n      console.error('WebSocket connection error:', error);\n    });\n\n    // Session events\n    socket.on('session-joined', (data) => {\n      setState(prev => ({ ...prev, sessionId: data.sessionId }));\n      console.log('Joined session:', data.sessionId);\n    });\n\n    socket.on('user-joined', (data) => {\n      setState(prev => ({ \n        ...prev, \n        users: [...prev.users.filter(id => id !== data.userId), data.userId]\n      }));\n      events?.onUserJoined?.(data);\n    });\n\n    socket.on('user-left', (data) => {\n      setState(prev => ({ \n        ...prev, \n        users: prev.users.filter(id => id !== data.userId)\n      }));\n      events?.onUserLeft?.(data);\n    });\n\n    // Text selection events\n    socket.on('text-selected', (data) => {\n      events?.onTextSelected?.(data);\n    });\n\n    // Analysis events\n    socket.on('analysis-started', (data) => {\n      events?.onAnalysisStarted?.(data);\n    });\n\n    socket.on('analysis-progress', (data) => {\n      events?.onAnalysisProgress?.(data);\n    });\n\n    socket.on('analysis-completed', (data) => {\n      events?.onAnalysisCompleted?.(data);\n    });\n\n    socket.on('analysis-error', (data) => {\n      events?.onAnalysisError?.(data);\n    });\n\n    // Generation events\n    socket.on('generation-started', (data) => {\n      events?.onGenerationStarted?.(data);\n    });\n\n    socket.on('generation-progress', (data) => {\n      events?.onGenerationProgress?.(data);\n    });\n\n    socket.on('generation-completed', (data) => {\n      events?.onGenerationCompleted?.(data);\n    });\n\n    socket.on('generation-error', (data) => {\n      events?.onGenerationError?.(data);\n    });\n\n    return () => {\n      socket.disconnect();\n    };\n    */\n  }, [events]);\n\n  // Join a session\n  const joinSession = useCallback((sessionId: string) => {\n    if (socketRef.current) {\n      socketRef.current.emit('join-session', sessionId);\n    }\n  }, []);\n\n  // Leave current session\n  const leaveSession = useCallback(() => {\n    if (socketRef.current && state.sessionId) {\n      socketRef.current.emit('leave-session', state.sessionId);\n      setState(prev => ({ ...prev, sessionId: null, users: [] }));\n    }\n  }, [state.sessionId]);\n\n  // Emit text selection\n  const emitTextSelection = useCallback((selectedText: string) => {\n    if (socketRef.current && state.sessionId) {\n      socketRef.current.emit('text-selection', {\n        sessionId: state.sessionId,\n        selectedText,\n        timestamp: Date.now()\n      });\n    }\n  }, [state.sessionId]);\n\n  // Emit analysis request\n  const emitAnalysisRequest = useCallback((text: string, type: 'image' | 'video') => {\n    if (socketRef.current && state.sessionId) {\n      socketRef.current.emit('analysis-request', {\n        sessionId: state.sessionId,\n        text,\n        type\n      });\n    }\n  }, [state.sessionId]);\n\n  // Emit generation request\n  const emitGenerationRequest = useCallback((prompt: string, type: 'image' | 'video') => {\n    if (socketRef.current && state.sessionId) {\n      socketRef.current.emit('generation-request', {\n        sessionId: state.sessionId,\n        prompt,\n        type\n      });\n    }\n  }, [state.sessionId]);\n\n  return {\n    ...state,\n    joinSession,\n    leaveSession,\n    emitTextSelection,\n    emitAnalysisRequest,\n    emitGenerationRequest,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AA0BO,SAAS,aAAa,MAAwB;;IACnD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,WAAW;QACX,WAAW;QACX,OAAO,EAAE;QACT,OAAO;IACT;IAEA,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,0DAA0D;YAC1D,2EAA2E;YAC3E,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB;0CAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW;wBAAM,OAAO;oBAAK,CAAC;;YAE3D;0CAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;;QAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyFA,GACF;iCAAG;QAAC;KAAO;IAEX,iBAAiB;IACjB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC/B,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,IAAI,CAAC,gBAAgB;YACzC;QACF;gDAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC/B,IAAI,UAAU,OAAO,IAAI,MAAM,SAAS,EAAE;gBACxC,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB,MAAM,SAAS;gBACvD;8DAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;4BAAM,OAAO,EAAE;wBAAC,CAAC;;YAC3D;QACF;iDAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACrC,IAAI,UAAU,OAAO,IAAI,MAAM,SAAS,EAAE;gBACxC,UAAU,OAAO,CAAC,IAAI,CAAC,kBAAkB;oBACvC,WAAW,MAAM,SAAS;oBAC1B;oBACA,WAAW,KAAK,GAAG;gBACrB;YACF;QACF;sDAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,MAAc;YACrD,IAAI,UAAU,OAAO,IAAI,MAAM,SAAS,EAAE;gBACxC,UAAU,OAAO,CAAC,IAAI,CAAC,oBAAoB;oBACzC,WAAW,MAAM,SAAS;oBAC1B;oBACA;gBACF;YACF;QACF;wDAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,0BAA0B;IAC1B,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC,QAAgB;YACzD,IAAI,UAAU,OAAO,IAAI,MAAM,SAAS,EAAE;gBACxC,UAAU,OAAO,CAAC,IAAI,CAAC,sBAAsB;oBAC3C,WAAW,MAAM,SAAS;oBAC1B;oBACA;gBACF;YACF;QACF;0DAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;IACF;AACF;GA1KgB", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/components/realtime/realtime-status.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Users, Wifi, WifiOff, Eye, Activity } from 'lucide-react';\nimport { useWebSocket } from '@/hooks/useWebSocket';\n\ninterface RealtimeStatusProps {\n  onTextSelected?: (data: { userId: string; selectedText: string; timestamp: number }) => void;\n  onAnalysisUpdate?: (data: any) => void;\n  onGenerationUpdate?: (data: any) => void;\n}\n\nexport default function RealtimeStatus({\n  onTextSelected,\n  onAnalysisUpdate,\n  onGenerationUpdate\n}: RealtimeStatusProps) {\n  const [sessionId, setSessionId] = useState<string>('');\n  const [recentActivity, setRecentActivity] = useState<string[]>([]);\n\n  // Generate session ID only on client side to avoid hydration mismatch\n  useEffect(() => {\n    setSessionId(`session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);\n  }, []);\n\n  const addActivity = (activity: string) => {\n    setRecentActivity(prev => [activity, ...prev.slice(0, 4)]);\n  };\n\n  const {\n    connected,\n    users,\n    error,\n    joinSession,\n    leaveSession,\n  } = useWebSocket({\n    onTextSelected: (data) => {\n      addActivity(`Usuário selecionou: \"${data.selectedText.slice(0, 30)}...\"`);\n      onTextSelected?.(data);\n    },\n    onUserJoined: (data) => {\n      addActivity(`Usuário ${data.userId.slice(-6)} entrou na sessão`);\n    },\n    onUserLeft: (data) => {\n      addActivity(`Usuário ${data.userId.slice(-6)} saiu da sessão`);\n    },\n    onAnalysisStarted: (data) => {\n      addActivity(`Análise iniciada: ${data.text.slice(0, 30)}...`);\n      onAnalysisUpdate?.(data);\n    },\n    onAnalysisCompleted: (data) => {\n      addActivity(`Análise concluída`);\n      onAnalysisUpdate?.(data);\n    },\n    onGenerationStarted: (data) => {\n      addActivity(`Geração ${data.type} iniciada`);\n      onGenerationUpdate?.(data);\n    },\n    onGenerationCompleted: (data) => {\n      addActivity(`Geração ${data.type} concluída`);\n      onGenerationUpdate?.(data);\n    },\n  });\n\n  // Auto-join session on mount\n  useEffect(() => {\n    if (connected && sessionId) {\n      addActivity('Conectado ao sistema em tempo real (modo simulação)');\n      // joinSession(sessionId); // Uncomment when using real WebSocket server\n    }\n\n    return () => {\n      // leaveSession(); // Uncomment when using real WebSocket server\n    };\n  }, [connected, sessionId]);\n\n  return (\n    <div className=\"bg-white border rounded-lg p-4 shadow-sm\">\n      {/* Connection Status */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-2\">\n          {connected ? (\n            <Wifi className=\"h-4 w-4 text-green-600\" />\n          ) : (\n            <WifiOff className=\"h-4 w-4 text-red-600\" />\n          )}\n          <span className={`text-sm font-medium ${connected ? 'text-green-600' : 'text-red-600'}`}>\n            {connected ? 'Conectado' : 'Desconectado'}\n          </span>\n        </div>\n        \n        {/* Users Count */}\n        <div className=\"flex items-center space-x-1 text-sm text-gray-600\">\n          <Users className=\"h-4 w-4\" />\n          <span>{users.length + 1} usuário{users.length !== 0 ? 's' : ''}</span>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"mb-4 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600\">\n          Erro de conexão: {error}\n        </div>\n      )}\n\n      {/* Session Info */}\n      {sessionId && (\n        <div className=\"mb-4 p-2 bg-gray-50 rounded text-xs\">\n          <div className=\"flex items-center space-x-1 text-gray-600\">\n            <Eye className=\"h-3 w-3\" />\n            <span>Sessão: {sessionId.slice(-8)}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Active Users */}\n      {users.length > 0 && (\n        <div className=\"mb-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Usuários Ativos</h4>\n          <div className=\"space-y-1\">\n            {users.map((userId) => (\n              <div key={userId} className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                <span>Usuário {userId.slice(-6)}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Recent Activity */}\n      {recentActivity.length > 0 && (\n        <div>\n          <div className=\"flex items-center space-x-1 mb-2\">\n            <Activity className=\"h-4 w-4 text-gray-600\" />\n            <h4 className=\"text-sm font-medium text-gray-900\">Atividade Recente</h4>\n          </div>\n          <div className=\"space-y-1 max-h-32 overflow-y-auto\">\n            {recentActivity.map((activity, index) => (\n              <div \n                key={index} \n                className=\"text-xs text-gray-600 p-2 bg-gray-50 rounded\"\n                style={{ opacity: 1 - (index * 0.2) }}\n              >\n                {activity}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Connection Instructions */}\n      {!connected && (\n        <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm\">\n          <p className=\"text-blue-800 font-medium mb-1\">Conectando...</p>\n          <p className=\"text-blue-600\">\n            O sistema de tempo real permite colaboração e atualizações instantâneas.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAYe,SAAS,eAAe,EACrC,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EACE;;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjE,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QACjF;mCAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,kBAAkB,CAAA,OAAQ;gBAAC;mBAAa,KAAK,KAAK,CAAC,GAAG;aAAG;IAC3D;IAEA,MAAM,EACJ,SAAS,EACT,KAAK,EACL,KAAK,EACL,WAAW,EACX,YAAY,EACb,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;QACf,cAAc;2CAAE,CAAC;gBACf,YAAY,CAAC,qBAAqB,EAAE,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC;gBACxE,iBAAiB;YACnB;;QACA,YAAY;2CAAE,CAAC;gBACb,YAAY,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,iBAAiB,CAAC;YACjE;;QACA,UAAU;2CAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,eAAe,CAAC;YAC/D;;QACA,iBAAiB;2CAAE,CAAC;gBAClB,YAAY,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;gBAC5D,mBAAmB;YACrB;;QACA,mBAAmB;2CAAE,CAAC;gBACpB,YAAY,CAAC,iBAAiB,CAAC;gBAC/B,mBAAmB;YACrB;;QACA,mBAAmB;2CAAE,CAAC;gBACpB,YAAY,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC;gBAC3C,qBAAqB;YACvB;;QACA,qBAAqB;2CAAE,CAAC;gBACtB,YAAY,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC;gBAC5C,qBAAqB;YACvB;;IACF;IAEA,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,aAAa,WAAW;gBAC1B,YAAY;YACZ,wEAAwE;YAC1E;YAEA;4CAAO;gBACL,gEAAgE;gBAClE;;QACF;mCAAG;QAAC;QAAW;KAAU;IAEzB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,0BACC,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;qDAEhB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CAErB,6LAAC;gCAAK,WAAW,CAAC,oBAAoB,EAAE,YAAY,mBAAmB,gBAAgB;0CACpF,YAAY,cAAc;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;;oCAAM,MAAM,MAAM,GAAG;oCAAE;oCAAS,MAAM,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;YAK/D,uBACC,6LAAC;gBAAI,WAAU;;oBAAwE;oBACnE;;;;;;;YAKrB,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC;;gCAAK;gCAAS,UAAU,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;YAMrC,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,uBACV,6LAAC;gCAAiB,WAAU;;kDAC1B,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;;4CAAK;4CAAS,OAAO,KAAK,CAAC,CAAC;;;;;;;;+BAFrB;;;;;;;;;;;;;;;;YAUjB,eAAe,MAAM,GAAG,mBACvB,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;kCAEpD,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,SAAS,IAAK,QAAQ;gCAAK;0CAEnC;+BAJI;;;;;;;;;;;;;;;;YAYd,CAAC,2BACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAiC;;;;;;kCAC9C,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAOvC;GAtJwB;;QAuBlB,+HAAA,CAAA,eAAY;;;KAvBM", "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/components/reading/reading-mode.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { \n  BookOpen, \n  Maximize2, \n  Minimize2, \n  Volume2, \n  VolumeX, \n  Play, \n  Pause,\n  SkipBack,\n  Ski<PERSON><PERSON>or<PERSON>,\n  Settings,\n  Eye,\n  Moon,\n  Sun\n} from 'lucide-react';\n\ninterface ReadingModeProps {\n  content: string;\n  visualizations: Array<{\n    id: string;\n    text: string;\n    url: string;\n    type: 'image' | 'video';\n    timestamp: number;\n  }>;\n  onClose?: () => void;\n}\n\nexport default function ReadingMode({ content, visualizations, onClose }: ReadingModeProps) {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [fontSize, setFontSize] = useState(16);\n  const [lineHeight, setLineHeight] = useState(1.6);\n  const [isAutoScroll, setIsAutoScroll] = useState(false);\n  const [scrollSpeed, setScrollSpeed] = useState(50);\n  const [currentVisualization, setCurrentVisualization] = useState(0);\n  const [showSettings, setShowSettings] = useState(false);\n  const [readingProgress, setReadingProgress] = useState(0);\n  \n  const contentRef = useRef<HTMLDivElement>(null);\n  const scrollIntervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Auto-scroll functionality\n  useEffect(() => {\n    if (isAutoScroll && contentRef.current) {\n      scrollIntervalRef.current = setInterval(() => {\n        if (contentRef.current) {\n          const { scrollTop, scrollHeight, clientHeight } = contentRef.current;\n          const maxScroll = scrollHeight - clientHeight;\n          \n          if (scrollTop < maxScroll) {\n            contentRef.current.scrollTop += 1;\n            setReadingProgress((scrollTop / maxScroll) * 100);\n          } else {\n            setIsAutoScroll(false);\n          }\n        }\n      }, 100 - scrollSpeed);\n    } else {\n      if (scrollIntervalRef.current) {\n        clearInterval(scrollIntervalRef.current);\n        scrollIntervalRef.current = null;\n      }\n    }\n\n    return () => {\n      if (scrollIntervalRef.current) {\n        clearInterval(scrollIntervalRef.current);\n      }\n    };\n  }, [isAutoScroll, scrollSpeed]);\n\n  // Handle scroll progress\n  const handleScroll = () => {\n    if (contentRef.current && !isAutoScroll) {\n      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;\n      const maxScroll = scrollHeight - clientHeight;\n      setReadingProgress((scrollTop / maxScroll) * 100);\n    }\n  };\n\n  // Fullscreen toggle\n  const toggleFullscreen = () => {\n    if (!document.fullscreenElement) {\n      document.documentElement.requestFullscreen();\n      setIsFullscreen(true);\n    } else {\n      document.exitFullscreen();\n      setIsFullscreen(false);\n    }\n  };\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyPress = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        if (isFullscreen) {\n          toggleFullscreen();\n        } else {\n          onClose?.();\n        }\n      } else if (e.key === 'f' || e.key === 'F') {\n        toggleFullscreen();\n      } else if (e.key === 'd' || e.key === 'D') {\n        setIsDarkMode(!isDarkMode);\n      } else if (e.key === ' ') {\n        e.preventDefault();\n        setIsAutoScroll(!isAutoScroll);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, [isFullscreen, isDarkMode, isAutoScroll, onClose]);\n\n  const nextVisualization = () => {\n    setCurrentVisualization((prev) => \n      prev < visualizations.length - 1 ? prev + 1 : 0\n    );\n  };\n\n  const prevVisualization = () => {\n    setCurrentVisualization((prev) => \n      prev > 0 ? prev - 1 : visualizations.length - 1\n    );\n  };\n\n  return (\n    <div className={`fixed inset-0 z-50 ${isDarkMode ? 'bg-gray-900' : 'bg-white'} transition-colors`}>\n      {/* Header Controls */}\n      <div className={`flex items-center justify-between p-4 border-b ${\n        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'\n      }`}>\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={onClose}\n            className={`p-2 rounded-lg transition-colors ${\n              isDarkMode \n                ? 'hover:bg-gray-700 text-gray-300' \n                : 'hover:bg-gray-100 text-gray-600'\n            }`}\n          >\n            <BookOpen className=\"h-5 w-5\" />\n          </button>\n          \n          <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n            Modo Leitura Imersivo\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          {/* Reading Progress */}\n          <div className=\"flex items-center space-x-2\">\n            <div className={`w-32 h-2 rounded-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>\n              <div \n                className=\"h-2 bg-indigo-600 rounded-full transition-all\"\n                style={{ width: `${readingProgress}%` }}\n              />\n            </div>\n            <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n              {Math.round(readingProgress)}%\n            </span>\n          </div>\n\n          {/* Auto-scroll Controls */}\n          <button\n            onClick={() => setIsAutoScroll(!isAutoScroll)}\n            className={`p-2 rounded-lg transition-colors ${\n              isAutoScroll \n                ? 'bg-indigo-600 text-white' \n                : isDarkMode \n                  ? 'hover:bg-gray-700 text-gray-300' \n                  : 'hover:bg-gray-100 text-gray-600'\n            }`}\n          >\n            {isAutoScroll ? <Pause className=\"h-4 w-4\" /> : <Play className=\"h-4 w-4\" />}\n          </button>\n\n          {/* Dark Mode Toggle */}\n          <button\n            onClick={() => setIsDarkMode(!isDarkMode)}\n            className={`p-2 rounded-lg transition-colors ${\n              isDarkMode \n                ? 'hover:bg-gray-700 text-gray-300' \n                : 'hover:bg-gray-100 text-gray-600'\n            }`}\n          >\n            {isDarkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n          </button>\n\n          {/* Settings */}\n          <button\n            onClick={() => setShowSettings(!showSettings)}\n            className={`p-2 rounded-lg transition-colors ${\n              showSettings \n                ? 'bg-indigo-600 text-white' \n                : isDarkMode \n                  ? 'hover:bg-gray-700 text-gray-300' \n                  : 'hover:bg-gray-100 text-gray-600'\n            }`}\n          >\n            <Settings className=\"h-4 w-4\" />\n          </button>\n\n          {/* Fullscreen Toggle */}\n          <button\n            onClick={toggleFullscreen}\n            className={`p-2 rounded-lg transition-colors ${\n              isDarkMode \n                ? 'hover:bg-gray-700 text-gray-300' \n                : 'hover:bg-gray-100 text-gray-600'\n            }`}\n          >\n            {isFullscreen ? <Minimize2 className=\"h-4 w-4\" /> : <Maximize2 className=\"h-4 w-4\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Settings Panel */}\n      {showSettings && (\n        <div className={`p-4 border-b ${\n          isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'\n        }`}>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {/* Font Size */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${\n                isDarkMode ? 'text-gray-300' : 'text-gray-700'\n              }`}>\n                Tamanho da Fonte\n              </label>\n              <input\n                type=\"range\"\n                min=\"12\"\n                max=\"24\"\n                value={fontSize}\n                onChange={(e) => setFontSize(Number(e.target.value))}\n                className=\"w-full\"\n              />\n              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                {fontSize}px\n              </span>\n            </div>\n\n            {/* Line Height */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${\n                isDarkMode ? 'text-gray-300' : 'text-gray-700'\n              }`}>\n                Espaçamento\n              </label>\n              <input\n                type=\"range\"\n                min=\"1.2\"\n                max=\"2.0\"\n                step=\"0.1\"\n                value={lineHeight}\n                onChange={(e) => setLineHeight(Number(e.target.value))}\n                className=\"w-full\"\n              />\n              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                {lineHeight}x\n              </span>\n            </div>\n\n            {/* Scroll Speed */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${\n                isDarkMode ? 'text-gray-300' : 'text-gray-700'\n              }`}>\n                Velocidade Auto-scroll\n              </label>\n              <input\n                type=\"range\"\n                min=\"10\"\n                max=\"90\"\n                value={scrollSpeed}\n                onChange={(e) => setScrollSpeed(Number(e.target.value))}\n                className=\"w-full\"\n              />\n              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                {scrollSpeed}%\n              </span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"flex h-full\">\n        {/* Text Content */}\n        <div className=\"flex-1 flex flex-col\">\n          <div \n            ref={contentRef}\n            onScroll={handleScroll}\n            className={`flex-1 overflow-y-auto p-8 ${\n              isDarkMode ? 'text-gray-100' : 'text-gray-900'\n            }`}\n            style={{ \n              fontSize: `${fontSize}px`, \n              lineHeight: lineHeight,\n              maxWidth: '800px',\n              margin: '0 auto'\n            }}\n          >\n            <div \n              dangerouslySetInnerHTML={{ __html: content }}\n              className=\"prose prose-lg max-w-none\"\n            />\n          </div>\n        </div>\n\n        {/* Visualization Sidebar */}\n        {visualizations.length > 0 && (\n          <div className={`w-96 border-l ${\n            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'\n          }`}>\n            <div className=\"p-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className={`font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>\n                  Visualizações\n                </h3>\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={prevVisualization}\n                    className={`p-1 rounded transition-colors ${\n                      isDarkMode \n                        ? 'hover:bg-gray-700 text-gray-400' \n                        : 'hover:bg-gray-200 text-gray-600'\n                    }`}\n                  >\n                    <SkipBack className=\"h-4 w-4\" />\n                  </button>\n                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {currentVisualization + 1}/{visualizations.length}\n                  </span>\n                  <button\n                    onClick={nextVisualization}\n                    className={`p-1 rounded transition-colors ${\n                      isDarkMode \n                        ? 'hover:bg-gray-700 text-gray-400' \n                        : 'hover:bg-gray-200 text-gray-600'\n                    }`}\n                  >\n                    <SkipForward className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n\n              {visualizations[currentVisualization] && (\n                <div className=\"space-y-4\">\n                  <div className=\"aspect-video rounded-lg overflow-hidden bg-gray-200\">\n                    {visualizations[currentVisualization].type === 'video' ? (\n                      <video \n                        controls \n                        className=\"w-full h-full object-cover\"\n                        src={visualizations[currentVisualization].url}\n                      />\n                    ) : (\n                      <img \n                        src={visualizations[currentVisualization].url}\n                        alt={visualizations[currentVisualization].text}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    )}\n                  </div>\n                  \n                  <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>\n                    <strong>Conceito:</strong> {visualizations[currentVisualization].text}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Keyboard Shortcuts Help */}\n      <div className={`absolute bottom-4 left-4 text-xs ${\n        isDarkMode ? 'text-gray-500' : 'text-gray-400'\n      }`}>\n        <div>ESC: Sair | F: Tela cheia | D: Modo escuro | ESPAÇO: Auto-scroll</div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA+Be,SAAS,YAAY,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,EAAoB;;IACxF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAExD,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,gBAAgB,WAAW,OAAO,EAAE;gBACtC,kBAAkB,OAAO,GAAG;6CAAY;wBACtC,IAAI,WAAW,OAAO,EAAE;4BACtB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,WAAW,OAAO;4BACpE,MAAM,YAAY,eAAe;4BAEjC,IAAI,YAAY,WAAW;gCACzB,WAAW,OAAO,CAAC,SAAS,IAAI;gCAChC,mBAAmB,AAAC,YAAY,YAAa;4BAC/C,OAAO;gCACL,gBAAgB;4BAClB;wBACF;oBACF;4CAAG,MAAM;YACX,OAAO;gBACL,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,cAAc,kBAAkB,OAAO;oBACvC,kBAAkB,OAAO,GAAG;gBAC9B;YACF;YAEA;yCAAO;oBACL,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,cAAc,kBAAkB,OAAO;oBACzC;gBACF;;QACF;gCAAG;QAAC;QAAc;KAAY;IAE9B,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO,IAAI,CAAC,cAAc;YACvC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,WAAW,OAAO;YACpE,MAAM,YAAY,eAAe;YACjC,mBAAmB,AAAC,YAAY,YAAa;QAC/C;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,SAAS,eAAe,CAAC,iBAAiB;YAC1C,gBAAgB;QAClB,OAAO;YACL,SAAS,cAAc;YACvB,gBAAgB;QAClB;IACF;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;wDAAiB,CAAC;oBACtB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB,IAAI,cAAc;4BAChB;wBACF,OAAO;4BACL;wBACF;oBACF,OAAO,IAAI,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,KAAK,KAAK;wBACzC;oBACF,OAAO,IAAI,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,KAAK,KAAK;wBACzC,cAAc,CAAC;oBACjB,OAAO,IAAI,EAAE,GAAG,KAAK,KAAK;wBACxB,EAAE,cAAc;wBAChB,gBAAgB,CAAC;oBACnB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;yCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;gCAAG;QAAC;QAAc;QAAY;QAAc;KAAQ;IAEpD,MAAM,oBAAoB;QACxB,wBAAwB,CAAC,OACvB,OAAO,eAAe,MAAM,GAAG,IAAI,OAAO,IAAI;IAElD;IAEA,MAAM,oBAAoB;QACxB,wBAAwB,CAAC,OACvB,OAAO,IAAI,OAAO,IAAI,eAAe,MAAM,GAAG;IAElD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,mBAAmB,EAAE,aAAa,gBAAgB,WAAW,kBAAkB,CAAC;;0BAE/F,6LAAC;gBAAI,WAAW,CAAC,+CAA+C,EAC9D,aAAa,gCAAgC,4BAC7C;;kCACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAW,CAAC,iCAAiC,EAC3C,aACI,oCACA,mCACJ;0CAEF,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;0CAAE;;;;;;;;;;;;kCAK/E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,sBAAsB,EAAE,aAAa,gBAAgB,eAAe;kDACnF,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAG1C,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;4CACzE,KAAK,KAAK,CAAC;4CAAiB;;;;;;;;;;;;;0CAKjC,6LAAC;gCACC,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAW,CAAC,iCAAiC,EAC3C,eACI,6BACA,aACE,oCACA,mCACN;0CAED,6BAAe,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIlE,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAW,CAAC,iCAAiC,EAC3C,aACI,oCACA,mCACJ;0CAED,2BAAa,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAI9D,6LAAC;gCACC,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAW,CAAC,iCAAiC,EAC3C,eACI,6BACA,aACE,oCACA,mCACN;0CAEF,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAItB,6LAAC;gCACC,SAAS;gCACT,WAAW,CAAC,iCAAiC,EAC3C,aACI,oCACA,mCACJ;0CAED,6BAAe,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAAe,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM9E,8BACC,6LAAC;gBAAI,WAAW,CAAC,aAAa,EAC5B,aAAa,gCAAgC,8BAC7C;0BACA,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAM,WAAW,CAAC,+BAA+B,EAChD,aAAa,kBAAkB,iBAC/B;8CAAE;;;;;;8CAGJ,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;wCACzE;wCAAS;;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAW,CAAC,+BAA+B,EAChD,aAAa,kBAAkB,iBAC/B;8CAAE;;;;;;8CAGJ,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;oCACpD,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;wCACzE;wCAAW;;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAW,CAAC,+BAA+B,EAChD,aAAa,kBAAkB,iBAC/B;8CAAE;;;;;;8CAGJ,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;oCACrD,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;wCACzE;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK;4BACL,UAAU;4BACV,WAAW,CAAC,2BAA2B,EACrC,aAAa,kBAAkB,iBAC/B;4BACF,OAAO;gCACL,UAAU,GAAG,SAAS,EAAE,CAAC;gCACzB,YAAY;gCACZ,UAAU;gCACV,QAAQ;4BACV;sCAEA,cAAA,6LAAC;gCACC,yBAAyB;oCAAE,QAAQ;gCAAQ;gCAC3C,WAAU;;;;;;;;;;;;;;;;oBAMf,eAAe,MAAM,GAAG,mBACvB,6LAAC;wBAAI,WAAW,CAAC,cAAc,EAC7B,aAAa,gCAAgC,8BAC7C;kCACA,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,YAAY,EAAE,aAAa,kBAAkB,iBAAiB;sDAAE;;;;;;sDAGhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAW,CAAC,8BAA8B,EACxC,aACI,oCACA,mCACJ;8DAEF,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAK,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;wDACzE,uBAAuB;wDAAE;wDAAE,eAAe,MAAM;;;;;;;8DAEnD,6LAAC;oDACC,SAAS;oDACT,WAAW,CAAC,8BAA8B,EACxC,aACI,oCACA,mCACJ;8DAEF,cAAA,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gCAK5B,cAAc,CAAC,qBAAqB,kBACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,cAAc,CAAC,qBAAqB,CAAC,IAAI,KAAK,wBAC7C,6LAAC;gDACC,QAAQ;gDACR,WAAU;gDACV,KAAK,cAAc,CAAC,qBAAqB,CAAC,GAAG;;;;;qEAG/C,6LAAC;gDACC,KAAK,cAAc,CAAC,qBAAqB,CAAC,GAAG;gDAC7C,KAAK,cAAc,CAAC,qBAAqB,CAAC,IAAI;gDAC9C,WAAU;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;;8DACzE,6LAAC;8DAAO;;;;;;gDAAkB;gDAAE,cAAc,CAAC,qBAAqB,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnF,6LAAC;gBAAI,WAAW,CAAC,iCAAiC,EAChD,aAAa,kBAAkB,iBAC/B;0BACA,cAAA,6LAAC;8BAAI;;;;;;;;;;;;;;;;;AAIb;GArWwB;KAAA", "debugId": null}}, {"offset": {"line": 2579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/components/export/export-manager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Download, \n  FileImage, \n  FileVideo, \n  FileText, \n  Presentation,\n  Package,\n  Loader2,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react';\n\ninterface ExportItem {\n  id: string;\n  text: string;\n  url: string;\n  type: 'image' | 'video';\n  timestamp: number;\n}\n\ninterface ExportManagerProps {\n  visualizations: ExportItem[];\n  content: string;\n  onExport?: (format: string, items: ExportItem[]) => void;\n}\n\nexport default function ExportManager({ visualizations, content, onExport }: ExportManagerProps) {\n  const [selectedItems, setSelectedItems] = useState<string[]>([]);\n  const [exportFormat, setExportFormat] = useState<'individual' | 'zip' | 'presentation' | 'pdf'>('individual');\n  const [isExporting, setIsExporting] = useState(false);\n  const [exportStatus, setExportStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const toggleItemSelection = (id: string) => {\n    setSelectedItems(prev => \n      prev.includes(id) \n        ? prev.filter(item => item !== id)\n        : [...prev, id]\n    );\n  };\n\n  const selectAll = () => {\n    setSelectedItems(visualizations.map(item => item.id));\n  };\n\n  const clearSelection = () => {\n    setSelectedItems([]);\n  };\n\n  const downloadFile = async (url: string, filename: string) => {\n    try {\n      const response = await fetch(url);\n      const blob = await response.blob();\n      const downloadUrl = window.URL.createObjectURL(blob);\n      \n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = filename;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      \n      window.URL.revokeObjectURL(downloadUrl);\n    } catch (error) {\n      console.error('Download failed:', error);\n      throw error;\n    }\n  };\n\n  const exportIndividual = async () => {\n    const selectedVisualizations = visualizations.filter(item => \n      selectedItems.includes(item.id)\n    );\n\n    for (const item of selectedVisualizations) {\n      const extension = item.type === 'video' ? 'mp4' : 'jpg';\n      const filename = `${item.text.replace(/\\s+/g, '_').slice(0, 30)}_${item.timestamp}.${extension}`;\n      await downloadFile(item.url, filename);\n    }\n  };\n\n  const exportAsZip = async () => {\n    // For demo purposes, we'll simulate zip creation\n    // In production, you'd use a library like JSZip\n    const selectedVisualizations = visualizations.filter(item => \n      selectedItems.includes(item.id)\n    );\n\n    // Simulate zip creation delay\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // Create a simple text file with links for demo\n    const zipContent = selectedVisualizations.map(item => \n      `${item.text}: ${item.url}`\n    ).join('\\n');\n\n    const blob = new Blob([zipContent], { type: 'text/plain' });\n    const url = window.URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `visualizations_${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    window.URL.revokeObjectURL(url);\n  };\n\n  const exportAsPresentation = async () => {\n    // For demo purposes, create a simple HTML presentation\n    const selectedVisualizations = visualizations.filter(item => \n      selectedItems.includes(item.id)\n    );\n\n    const presentationHTML = `\n<!DOCTYPE html>\n<html>\n<head>\n    <title>StudyVision - Apresentação</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }\n        .slide { page-break-after: always; margin-bottom: 40px; }\n        .slide h2 { color: #4f46e5; border-bottom: 2px solid #4f46e5; padding-bottom: 10px; }\n        .slide img, .slide video { max-width: 100%; height: auto; margin: 20px 0; }\n        .slide p { line-height: 1.6; margin: 15px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"slide\">\n        <h1>StudyVision - Estudo Imersivo</h1>\n        <p>Apresentação gerada automaticamente</p>\n        <p>Data: ${new Date().toLocaleDateString('pt-BR')}</p>\n    </div>\n    \n    ${selectedVisualizations.map((item, index) => `\n    <div class=\"slide\">\n        <h2>Slide ${index + 1}: ${item.text}</h2>\n        ${item.type === 'video' \n          ? `<video controls><source src=\"${item.url}\" type=\"video/mp4\"></video>`\n          : `<img src=\"${item.url}\" alt=\"${item.text}\">`\n        }\n        <p><strong>Conceito:</strong> ${item.text}</p>\n        <p><strong>Tipo:</strong> ${item.type === 'video' ? 'Vídeo' : 'Imagem'}</p>\n    </div>\n    `).join('')}\n</body>\n</html>`;\n\n    const blob = new Blob([presentationHTML], { type: 'text/html' });\n    const url = window.URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `apresentacao_${Date.now()}.html`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    window.URL.revokeObjectURL(url);\n  };\n\n  const exportAsPDF = async () => {\n    // For demo purposes, create a simple text file\n    // In production, you'd use a library like jsPDF\n    const selectedVisualizations = visualizations.filter(item => \n      selectedItems.includes(item.id)\n    );\n\n    const pdfContent = `\nSTUDYVISION - RELATÓRIO DE ESTUDO\n================================\n\nData: ${new Date().toLocaleDateString('pt-BR')}\nVisualizações: ${selectedVisualizations.length}\n\nCONTEÚDO:\n${content.replace(/<[^>]*>/g, '').slice(0, 500)}...\n\nVISUALIZAÇÕES GERADAS:\n${selectedVisualizations.map((item, index) => `\n${index + 1}. ${item.text}\n   Tipo: ${item.type === 'video' ? 'Vídeo' : 'Imagem'}\n   URL: ${item.url}\n   Data: ${new Date(item.timestamp).toLocaleString('pt-BR')}\n`).join('')}\n\nGerado por StudyVision AI\n`;\n\n    const blob = new Blob([pdfContent], { type: 'text/plain' });\n    const url = window.URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `relatorio_estudo_${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    window.URL.revokeObjectURL(url);\n  };\n\n  const handleExport = async () => {\n    if (selectedItems.length === 0) {\n      alert('Selecione pelo menos uma visualização para exportar.');\n      return;\n    }\n\n    setIsExporting(true);\n    setExportStatus('idle');\n\n    try {\n      switch (exportFormat) {\n        case 'individual':\n          await exportIndividual();\n          break;\n        case 'zip':\n          await exportAsZip();\n          break;\n        case 'presentation':\n          await exportAsPresentation();\n          break;\n        case 'pdf':\n          await exportAsPDF();\n          break;\n      }\n\n      setExportStatus('success');\n      onExport?.(exportFormat, visualizations.filter(item => selectedItems.includes(item.id)));\n    } catch (error) {\n      console.error('Export failed:', error);\n      setExportStatus('error');\n    } finally {\n      setIsExporting(false);\n      setTimeout(() => setExportStatus('idle'), 3000);\n    }\n  };\n\n  if (visualizations.length === 0) {\n    return (\n      <div className=\"bg-white border rounded-lg p-6 text-center\">\n        <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Nenhuma Visualização</h3>\n        <p className=\"text-gray-600\">\n          Gere algumas visualizações primeiro para poder exportá-las.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white border rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Exportar Visualizações</h3>\n        <div className=\"flex items-center space-x-2\">\n          {exportStatus === 'success' && (\n            <div className=\"flex items-center text-green-600\">\n              <CheckCircle className=\"h-4 w-4 mr-1\" />\n              <span className=\"text-sm\">Exportado!</span>\n            </div>\n          )}\n          {exportStatus === 'error' && (\n            <div className=\"flex items-center text-red-600\">\n              <AlertCircle className=\"h-4 w-4 mr-1\" />\n              <span className=\"text-sm\">Erro na exportação</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Selection Controls */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={selectAll}\n            className=\"text-sm text-indigo-600 hover:text-indigo-700\"\n          >\n            Selecionar Todos\n          </button>\n          <span className=\"text-gray-300\">|</span>\n          <button\n            onClick={clearSelection}\n            className=\"text-sm text-gray-600 hover:text-gray-700\"\n          >\n            Limpar Seleção\n          </button>\n        </div>\n        <span className=\"text-sm text-gray-600\">\n          {selectedItems.length} de {visualizations.length} selecionados\n        </span>\n      </div>\n\n      {/* Visualization List */}\n      <div className=\"space-y-2 mb-6 max-h-48 overflow-y-auto\">\n        {visualizations.map((item) => (\n          <div\n            key={item.id}\n            className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${\n              selectedItems.includes(item.id)\n                ? 'border-indigo-300 bg-indigo-50'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n            onClick={() => toggleItemSelection(item.id)}\n          >\n            <input\n              type=\"checkbox\"\n              checked={selectedItems.includes(item.id)}\n              onChange={() => toggleItemSelection(item.id)}\n              className=\"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n            />\n            \n            <div className=\"flex-shrink-0\">\n              {item.type === 'video' ? (\n                <FileVideo className=\"h-5 w-5 text-purple-600\" />\n              ) : (\n                <FileImage className=\"h-5 w-5 text-blue-600\" />\n              )}\n            </div>\n            \n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">\n                {item.text}\n              </p>\n              <p className=\"text-xs text-gray-500\">\n                {new Date(item.timestamp).toLocaleString('pt-BR')}\n              </p>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Export Format Selection */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Formato de Exportação\n        </label>\n        <div className=\"grid grid-cols-2 gap-2\">\n          <button\n            onClick={() => setExportFormat('individual')}\n            className={`flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${\n              exportFormat === 'individual'\n                ? 'border-indigo-300 bg-indigo-50 text-indigo-700'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n          >\n            <Download className=\"h-4 w-4\" />\n            <span className=\"text-sm\">Individual</span>\n          </button>\n          \n          <button\n            onClick={() => setExportFormat('zip')}\n            className={`flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${\n              exportFormat === 'zip'\n                ? 'border-indigo-300 bg-indigo-50 text-indigo-700'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n          >\n            <Package className=\"h-4 w-4\" />\n            <span className=\"text-sm\">ZIP</span>\n          </button>\n          \n          <button\n            onClick={() => setExportFormat('presentation')}\n            className={`flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${\n              exportFormat === 'presentation'\n                ? 'border-indigo-300 bg-indigo-50 text-indigo-700'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n          >\n            <Presentation className=\"h-4 w-4\" />\n            <span className=\"text-sm\">Apresentação</span>\n          </button>\n          \n          <button\n            onClick={() => setExportFormat('pdf')}\n            className={`flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${\n              exportFormat === 'pdf'\n                ? 'border-indigo-300 bg-indigo-50 text-indigo-700'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n          >\n            <FileText className=\"h-4 w-4\" />\n            <span className=\"text-sm\">Relatório</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Export Button */}\n      <button\n        onClick={handleExport}\n        disabled={isExporting || selectedItems.length === 0}\n        className=\"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n      >\n        {isExporting ? (\n          <Loader2 className=\"h-4 w-4 animate-spin\" />\n        ) : (\n          <Download className=\"h-4 w-4\" />\n        )}\n        <span>\n          {isExporting \n            ? 'Exportando...' \n            : `Exportar ${selectedItems.length} item${selectedItems.length !== 1 ? 's' : ''}`\n          }\n        </span>\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA6Be,SAAS,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAsB;;IAC7F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiD;IAChG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,MAC7B;mBAAI;gBAAM;aAAG;IAErB;IAEA,MAAM,YAAY;QAChB,iBAAiB,eAAe,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;IACrD;IAEA,MAAM,iBAAiB;QACrB,iBAAiB,EAAE;IACrB;IAEA,MAAM,eAAe,OAAO,KAAa;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,cAAc,OAAO,GAAG,CAAC,eAAe,CAAC;YAE/C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG;YAChB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,yBAAyB,eAAe,MAAM,CAAC,CAAA,OACnD,cAAc,QAAQ,CAAC,KAAK,EAAE;QAGhC,KAAK,MAAM,QAAQ,uBAAwB;YACzC,MAAM,YAAY,KAAK,IAAI,KAAK,UAAU,QAAQ;YAClD,MAAM,WAAW,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,EAAE,WAAW;YAChG,MAAM,aAAa,KAAK,GAAG,EAAE;QAC/B;IACF;IAEA,MAAM,cAAc;QAClB,iDAAiD;QACjD,gDAAgD;QAChD,MAAM,yBAAyB,eAAe,MAAM,CAAC,CAAA,OACnD,cAAc,QAAQ,CAAC,KAAK,EAAE;QAGhC,8BAA8B;QAC9B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gDAAgD;QAChD,MAAM,aAAa,uBAAuB,GAAG,CAAC,CAAA,OAC5C,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,EAC3B,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAa;QACzD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QAEvC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,eAAe,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;QAClD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,uBAAuB;QAC3B,uDAAuD;QACvD,MAAM,yBAAyB,eAAe,MAAM,CAAC,CAAA,OACnD,cAAc,QAAQ,CAAC,KAAK,EAAE;QAGhC,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;;;;;iBAiBb,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;;IAGtD,EAAE,uBAAuB,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;;kBAEjC,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,IAAI,CAAC;QACpC,EAAE,KAAK,IAAI,KAAK,UACZ,CAAC,6BAA6B,EAAE,KAAK,GAAG,CAAC,2BAA2B,CAAC,GACrE,CAAC,UAAU,EAAE,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAC/C;sCAC6B,EAAE,KAAK,IAAI,CAAC;kCAChB,EAAE,KAAK,IAAI,KAAK,UAAU,UAAU,SAAS;;IAE3E,CAAC,EAAE,IAAI,CAAC,IAAI;;OAET,CAAC;QAEJ,MAAM,OAAO,IAAI,KAAK;YAAC;SAAiB,EAAE;YAAE,MAAM;QAAY;QAC9D,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QAEvC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;QACjD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,cAAc;QAClB,+CAA+C;QAC/C,gDAAgD;QAChD,MAAM,yBAAyB,eAAe,MAAM,CAAC,CAAA,OACnD,cAAc,QAAQ,CAAC,KAAK,EAAE;QAGhC,MAAM,aAAa,CAAC;;;;MAIlB,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;eAChC,EAAE,uBAAuB,MAAM,CAAC;;;AAG/C,EAAE,QAAQ,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC,GAAG,KAAK;;;AAGhD,EAAE,uBAAuB,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;AAC/C,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,IAAI,CAAC;SACjB,EAAE,KAAK,IAAI,KAAK,UAAU,UAAU,SAAS;QAC9C,EAAE,KAAK,GAAG,CAAC;SACV,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS;AAC5D,CAAC,EAAE,IAAI,CAAC,IAAI;;;AAGZ,CAAC;QAEG,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAa;QACzD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QAEvC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;QACpD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM;YACN;QACF;QAEA,eAAe;QACf,gBAAgB;QAEhB,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,MAAM;oBACN;gBACF,KAAK;oBACH,MAAM;oBACN;gBACF,KAAK;oBACH,MAAM;oBACN;gBACF,KAAK;oBACH,MAAM;oBACN;YACJ;YAEA,gBAAgB;YAChB,WAAW,cAAc,eAAe,MAAM,CAAC,CAAA,OAAQ,cAAc,QAAQ,CAAC,KAAK,EAAE;QACvF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,gBAAgB;QAClB,SAAU;YACR,eAAe;YACf,WAAW,IAAM,gBAAgB,SAAS;QAC5C;IACF;IAEA,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;4BACZ,iBAAiB,2BAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAG7B,iBAAiB,yBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAIH,6LAAC;wBAAK,WAAU;;4BACb,cAAc,MAAM;4BAAC;4BAAK,eAAe,MAAM;4BAAC;;;;;;;;;;;;;0BAKrD,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC;wBAEC,WAAW,CAAC,mFAAmF,EAC7F,cAAc,QAAQ,CAAC,KAAK,EAAE,IAC1B,mCACA,yCACJ;wBACF,SAAS,IAAM,oBAAoB,KAAK,EAAE;;0CAE1C,6LAAC;gCACC,MAAK;gCACL,SAAS,cAAc,QAAQ,CAAC,KAAK,EAAE;gCACvC,UAAU,IAAM,oBAAoB,KAAK,EAAE;gCAC3C,WAAU;;;;;;0CAGZ,6LAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI,KAAK,wBACb,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,KAAK,IAAI;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDACV,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;uBA5BxC,KAAK,EAAE;;;;;;;;;;0BAoClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8EAA8E,EACxF,iBAAiB,eACb,mDACA,yCACJ;;kDAEF,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8EAA8E,EACxF,iBAAiB,QACb,mDACA,yCACJ;;kDAEF,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8EAA8E,EACxF,iBAAiB,iBACb,mDACA,yCACJ;;kDAEF,6LAAC,qNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8EAA8E,EACxF,iBAAiB,QACb,mDACA,yCACJ;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,6LAAC;gBACC,SAAS;gBACT,UAAU,eAAe,cAAc,MAAM,KAAK;gBAClD,WAAU;;oBAET,4BACC,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;6CAEnB,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCAEtB,6LAAC;kCACE,cACG,kBACA,CAAC,SAAS,EAAE,cAAc,MAAM,CAAC,KAAK,EAAE,cAAc,MAAM,KAAK,IAAI,MAAM,IAAI;;;;;;;;;;;;;;;;;;AAM7F;GA7XwB;KAAA", "debugId": null}}, {"offset": {"line": 3197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Play, Download, Eye } from 'lucide-react';\nimport TextEditor from '@/components/editor/text-editor';\nimport VisualizationPanel from '@/components/visualization/visualization-panel';\nimport RealtimeStatus from '@/components/realtime/realtime-status';\nimport ReadingMode from '@/components/reading/reading-mode';\nimport ExportManager from '@/components/export/export-manager';\n\nexport default function Home() {\n  const [selectedText, setSelectedText] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [analysisData, setAnalysisData] = useState(null);\n  const [showReadingMode, setShowReadingMode] = useState(false);\n  const [editorContent, setEditorContent] = useState('');\n  const [visualizations, setVisualizations] = useState<Array<{\n    id: string;\n    text: string;\n    url: string;\n    type: 'image' | 'video';\n    timestamp: number;\n  }>>([]);\n\n  const handleTextSelect = (text: string) => {\n    setSelectedText(text);\n  };\n\n  const handleAnalysisComplete = (analysis: any) => {\n    setAnalysisData(analysis);\n    console.log('Analysis completed:', analysis);\n  };\n\n  const handleGenerate = (text: string, type: 'image' | 'video') => {\n    setIsGenerating(true);\n    // Here we'll integrate with media generation APIs\n    console.log('Generating', type, 'for:', text);\n  };\n\n  const handleContentChange = (content: string) => {\n    setEditorContent(content);\n    console.log('Content changed:', content);\n  };\n\n  const addVisualization = (text: string, url: string, type: 'image' | 'video') => {\n    const timestamp = Date.now();\n    const newVisualization = {\n      id: `viz-${timestamp}-${Math.random().toString(36).substr(2, 9)}`,\n      text,\n      url,\n      type,\n      timestamp\n    };\n    setVisualizations(prev => [newVisualization, ...prev]);\n  };\n\n  const enterReadingMode = () => {\n    setShowReadingMode(true);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-2\">\n              <BookOpen className=\"h-8 w-8 text-indigo-600\" />\n              <h1 className=\"text-2xl font-bold text-gray-900\">StudyVision AI</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={enterReadingMode}\n                className=\"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50\"\n              >\n                <Eye className=\"h-4 w-4\" />\n                <span>Modo Leitura</span>\n              </button>\n              <button className=\"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\">\n                Login\n              </button>\n              <button className=\"px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700\">\n                Começar Grátis\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Transforme Texto em Visualizações Imersivas\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Destaque qualquer conceito científico e veja-o ganhar vida através de\n            vídeos e imagens geradas por IA em tempo real.\n          </p>\n        </div>\n\n        {/* Demo Section */}\n        <div className=\"grid lg:grid-cols-3 gap-8 mb-12\">\n          {/* Text Editor */}\n          <div className=\"lg:col-span-2\">\n            <TextEditor\n              onTextSelect={handleTextSelect}\n              onContentChange={handleContentChange}\n              onAnalysisComplete={handleAnalysisComplete}\n            />\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Visualization Panel */}\n            <VisualizationPanel\n              selectedText={selectedText}\n              analysisData={analysisData}\n              onGenerate={handleGenerate}\n              onVisualizationGenerated={addVisualization}\n            />\n\n            {/* Realtime Status */}\n            <RealtimeStatus\n              onTextSelected={(data) => console.log('Text selected by user:', data)}\n              onAnalysisUpdate={(data) => console.log('Analysis update:', data)}\n              onGenerationUpdate={(data) => console.log('Generation update:', data)}\n            />\n\n            {/* Export Manager */}\n            <ExportManager\n              visualizations={visualizations}\n              content={editorContent}\n              onExport={(format, items) => console.log('Exported:', format, items)}\n            />\n          </div>\n        </div>\n\n        {/* Features */}\n        <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n          <div className=\"text-center\">\n            <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n              <Sparkles className=\"h-6 w-6 text-indigo-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">IA Avançada</h3>\n            <p className=\"text-gray-600\">\n              Powered by GPT-4 e VEO3 para gerar visualizações precisas e educativas\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n              <Play className=\"h-6 w-6 text-indigo-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Tempo Real</h3>\n            <p className=\"text-gray-600\">\n              Visualizações geradas instantaneamente conforme você estuda\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n              <Download className=\"h-6 w-6 text-indigo-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Exportação</h3>\n            <p className=\"text-gray-600\">\n              Baixe vídeos e imagens para usar em apresentações e estudos\n            </p>\n          </div>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center\">\n          <button className=\"px-8 py-4 bg-indigo-600 text-white text-lg font-semibold rounded-lg hover:bg-indigo-700 transition-colors\">\n            Começar Agora - Grátis\n          </button>\n          <p className=\"text-sm text-gray-600 mt-2\">\n            10 visualizações gratuitas • Sem cartão de crédito\n          </p>\n        </div>\n      </main>\n\n      {/* Reading Mode Overlay */}\n      {showReadingMode && (\n        <ReadingMode\n          content={editorContent || `\n            <h1>Exemplo de Conteúdo Científico</h1>\n            <p>A <mark>mitose</mark> é um processo fundamental da divisão celular onde uma célula se divide para formar duas células filhas geneticamente idênticas.</p>\n            <p>Durante a <mark>prófase</mark>, os cromossomos se condensam e tornam-se visíveis ao microscópio. O envelope nuclear começa a se desintegrar.</p>\n            <p>Na <mark>metáfase</mark>, os cromossomos se alinham no centro da célula, formando a placa metafásica. Este é um momento crucial para garantir que cada célula filha receba o número correto de cromossomos.</p>\n            <p>Durante a <mark>anáfase</mark>, as cromátides irmãs se separam e migram para polos opostos da célula.</p>\n            <p>Finalmente, na <mark>telófase</mark>, novos envelopes nucleares se formam ao redor de cada conjunto de cromossomos, completando o processo de divisão.</p>\n          `}\n          visualizations={visualizations}\n          onClose={() => setShowReadingMode(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAM/C,EAAE;IAEN,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;QAChB,QAAQ,GAAG,CAAC,uBAAuB;IACrC;IAEA,MAAM,iBAAiB,CAAC,MAAc;QACpC,gBAAgB;QAChB,kDAAkD;QAClD,QAAQ,GAAG,CAAC,cAAc,MAAM,QAAQ;IAC1C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;QACjB,QAAQ,GAAG,CAAC,oBAAoB;IAClC;IAEA,MAAM,mBAAmB,CAAC,MAAc,KAAa;QACnD,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,mBAAmB;YACvB,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACjE;YACA;YACA;YACA;QACF;QACA,kBAAkB,CAAA,OAAQ;gBAAC;mBAAqB;aAAK;IACvD;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAO,WAAU;kDAAkE;;;;;;kDAGpF,6LAAC;wCAAO,WAAU;kDAAwF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlH,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAOzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,UAAU;oCACT,cAAc;oCACd,iBAAiB;oCACjB,oBAAoB;;;;;;;;;;;0CAKxB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,gKAAA,CAAA,UAAkB;wCACjB,cAAc;wCACd,cAAc;wCACd,YAAY;wCACZ,0BAA0B;;;;;;kDAI5B,6LAAC,uJAAA,CAAA,UAAc;wCACb,gBAAgB,CAAC,OAAS,QAAQ,GAAG,CAAC,0BAA0B;wCAChE,kBAAkB,CAAC,OAAS,QAAQ,GAAG,CAAC,oBAAoB;wCAC5D,oBAAoB,CAAC,OAAS,QAAQ,GAAG,CAAC,sBAAsB;;;;;;kDAIlE,6LAAC,oJAAA,CAAA,UAAa;wCACZ,gBAAgB;wCAChB,SAAS;wCACT,UAAU,CAAC,QAAQ,QAAU,QAAQ,GAAG,CAAC,aAAa,QAAQ;;;;;;;;;;;;;;;;;;kCAMpE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAOjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAA4G;;;;;;0CAG9H,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;YAO7C,iCACC,6LAAC,mJAAA,CAAA,UAAW;gBACV,SAAS,iBAAiB,CAAC;;;;;;;UAO3B,CAAC;gBACD,gBAAgB;gBAChB,SAAS,IAAM,mBAAmB;;;;;;;;;;;;AAK5C;GA3LwB;KAAA", "debugId": null}}]}