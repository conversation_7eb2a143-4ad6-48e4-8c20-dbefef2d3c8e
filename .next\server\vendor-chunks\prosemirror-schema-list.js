"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-schema-list";
exports.ids = ["vendor-chunks/prosemirror-schema-list"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-schema-list/dist/index.js":
/*!************************************************************!*\
  !*** ./node_modules/prosemirror-schema-list/dist/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addListNodes: () => (/* binding */ addListNodes),\n/* harmony export */   bulletList: () => (/* binding */ bulletList),\n/* harmony export */   liftListItem: () => (/* binding */ liftListItem),\n/* harmony export */   listItem: () => (/* binding */ listItem),\n/* harmony export */   orderedList: () => (/* binding */ orderedList),\n/* harmony export */   sinkListItem: () => (/* binding */ sinkListItem),\n/* harmony export */   splitListItem: () => (/* binding */ splitListItem),\n/* harmony export */   splitListItemKeepMarks: () => (/* binding */ splitListItemKeepMarks),\n/* harmony export */   wrapInList: () => (/* binding */ wrapInList),\n/* harmony export */   wrapRangeInList: () => (/* binding */ wrapRangeInList)\n/* harmony export */ });\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\nconst olDOM = [\"ol\", 0], ulDOM = [\"ul\", 0], liDOM = [\"li\", 0];\n/**\nAn ordered list [node spec](https://prosemirror.net/docs/ref/#model.NodeSpec). Has a single\nattribute, `order`, which determines the number at which the list\nstarts counting, and defaults to 1. Represented as an `<ol>`\nelement.\n*/\nconst orderedList = {\n    attrs: { order: { default: 1, validate: \"number\" } },\n    parseDOM: [{ tag: \"ol\", getAttrs(dom) {\n                return { order: dom.hasAttribute(\"start\") ? +dom.getAttribute(\"start\") : 1 };\n            } }],\n    toDOM(node) {\n        return node.attrs.order == 1 ? olDOM : [\"ol\", { start: node.attrs.order }, 0];\n    }\n};\n/**\nA bullet list node spec, represented in the DOM as `<ul>`.\n*/\nconst bulletList = {\n    parseDOM: [{ tag: \"ul\" }],\n    toDOM() { return ulDOM; }\n};\n/**\nA list item (`<li>`) spec.\n*/\nconst listItem = {\n    parseDOM: [{ tag: \"li\" }],\n    toDOM() { return liDOM; },\n    defining: true\n};\nfunction add(obj, props) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    for (let prop in props)\n        copy[prop] = props[prop];\n    return copy;\n}\n/**\nConvenience function for adding list-related node types to a map\nspecifying the nodes for a schema. Adds\n[`orderedList`](https://prosemirror.net/docs/ref/#schema-list.orderedList) as `\"ordered_list\"`,\n[`bulletList`](https://prosemirror.net/docs/ref/#schema-list.bulletList) as `\"bullet_list\"`, and\n[`listItem`](https://prosemirror.net/docs/ref/#schema-list.listItem) as `\"list_item\"`.\n\n`itemContent` determines the content expression for the list items.\nIf you want the commands defined in this module to apply to your\nlist structure, it should have a shape like `\"paragraph block*\"` or\n`\"paragraph (ordered_list | bullet_list)*\"`. `listGroup` can be\ngiven to assign a group name to the list node types, for example\n`\"block\"`.\n*/\nfunction addListNodes(nodes, itemContent, listGroup) {\n    return nodes.append({\n        ordered_list: add(orderedList, { content: \"list_item+\", group: listGroup }),\n        bullet_list: add(bulletList, { content: \"list_item+\", group: listGroup }),\n        list_item: add(listItem, { content: itemContent })\n    });\n}\n/**\nReturns a command function that wraps the selection in a list with\nthe given type an attributes. If `dispatch` is null, only return a\nvalue to indicate whether this is possible, but don't actually\nperform the change.\n*/\nfunction wrapInList(listType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to);\n        if (!range)\n            return false;\n        let tr = dispatch ? state.tr : null;\n        if (!wrapRangeInList(tr, range, listType, attrs))\n            return false;\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nTry to wrap the given node range in a list of the given type.\nReturn `true` when this is possible, `false` otherwise. When `tr`\nis non-null, the wrapping is added to that transaction. When it is\n`null`, the function only queries whether the wrapping is\npossible.\n*/\nfunction wrapRangeInList(tr, range, listType, attrs = null) {\n    let doJoin = false, outerRange = range, doc = range.$from.doc;\n    // This is at the top of an existing list item\n    if (range.depth >= 2 && range.$from.node(range.depth - 1).type.compatibleContent(listType) && range.startIndex == 0) {\n        // Don't do anything if this is the top of the list\n        if (range.$from.index(range.depth - 1) == 0)\n            return false;\n        let $insert = doc.resolve(range.start - 2);\n        outerRange = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.NodeRange($insert, $insert, range.depth);\n        if (range.endIndex < range.parent.childCount)\n            range = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.NodeRange(range.$from, doc.resolve(range.$to.end(range.depth)), range.depth);\n        doJoin = true;\n    }\n    let wrap = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.findWrapping)(outerRange, listType, attrs, range);\n    if (!wrap)\n        return false;\n    if (tr)\n        doWrapInList(tr, range, wrap, doJoin, listType);\n    return true;\n}\nfunction doWrapInList(tr, range, wrappers, joinBefore, listType) {\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--)\n        content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(range.start - (joinBefore ? 2 : 0), range.end, range.start, range.end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, 0, 0), wrappers.length, true));\n    let found = 0;\n    for (let i = 0; i < wrappers.length; i++)\n        if (wrappers[i].type == listType)\n            found = i + 1;\n    let splitDepth = wrappers.length - found;\n    let splitPos = range.start + wrappers.length - (joinBefore ? 2 : 0), parent = range.parent;\n    for (let i = range.startIndex, e = range.endIndex, first = true; i < e; i++, first = false) {\n        if (!first && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.canSplit)(tr.doc, splitPos, splitDepth)) {\n            tr.split(splitPos, splitDepth);\n            splitPos += 2 * splitDepth;\n        }\n        splitPos += parent.child(i).nodeSize;\n    }\n    return tr;\n}\n/**\nBuild a command that splits a non-empty textblock at the top level\nof a list item by also splitting that list item.\n*/\nfunction splitListItem(itemType, itemAttrs) {\n    return function (state, dispatch) {\n        let { $from, $to, node } = state.selection;\n        if ((node && node.isBlock) || $from.depth < 2 || !$from.sameParent($to))\n            return false;\n        let grandParent = $from.node(-1);\n        if (grandParent.type != itemType)\n            return false;\n        if ($from.parent.content.size == 0 && $from.node(-1).childCount == $from.indexAfter(-1)) {\n            // In an empty block. If this is a nested list, the wrapping\n            // list item should be split. Otherwise, bail out and let next\n            // command handle lifting.\n            if ($from.depth == 3 || $from.node(-3).type != itemType ||\n                $from.index(-2) != $from.node(-2).childCount - 1)\n                return false;\n            if (dispatch) {\n                let wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n                let depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3;\n                // Build a fragment containing empty versions of the structure\n                // from the outer list item to the parent node of the cursor\n                for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d--)\n                    wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(d).copy(wrap));\n                let depthAfter = $from.indexAfter(-1) < $from.node(-2).childCount ? 1\n                    : $from.indexAfter(-2) < $from.node(-3).childCount ? 2 : 3;\n                // Add a second list item with an empty default start node\n                wrap = wrap.append(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(itemType.createAndFill()));\n                let start = $from.before($from.depth - (depthBefore - 1));\n                let tr = state.tr.replace(start, $from.after(-depthAfter), new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(wrap, 4 - depthBefore, 0));\n                let sel = -1;\n                tr.doc.nodesBetween(start, tr.doc.content.size, (node, pos) => {\n                    if (sel > -1)\n                        return false;\n                    if (node.isTextblock && node.content.size == 0)\n                        sel = pos + 1;\n                });\n                if (sel > -1)\n                    tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.Selection.near(tr.doc.resolve(sel)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n        let nextType = $to.pos == $from.end() ? grandParent.contentMatchAt(0).defaultType : null;\n        let tr = state.tr.delete($from.pos, $to.pos);\n        let types = nextType ? [itemAttrs ? { type: itemType, attrs: itemAttrs } : null, { type: nextType }] : undefined;\n        if (!(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.canSplit)(tr.doc, $from.pos, 2, types))\n            return false;\n        if (dispatch)\n            dispatch(tr.split($from.pos, 2, types).scrollIntoView());\n        return true;\n    };\n}\n/**\nActs like [`splitListItem`](https://prosemirror.net/docs/ref/#schema-list.splitListItem), but\nwithout resetting the set of active marks at the cursor.\n*/\nfunction splitListItemKeepMarks(itemType, itemAttrs) {\n    let split = splitListItem(itemType, itemAttrs);\n    return (state, dispatch) => {\n        return split(state, dispatch && (tr => {\n            let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n            if (marks)\n                tr.ensureMarks(marks);\n            dispatch(tr);\n        }));\n    };\n}\n/**\nCreate a command to lift the list item around the selection up into\na wrapping list.\n*/\nfunction liftListItem(itemType) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to, node => node.childCount > 0 && node.firstChild.type == itemType);\n        if (!range)\n            return false;\n        if (!dispatch)\n            return true;\n        if ($from.node(range.depth - 1).type == itemType) // Inside a parent list\n            return liftToOuterList(state, dispatch, itemType, range);\n        else // Outer list node\n            return liftOutOfList(state, dispatch, range);\n    };\n}\nfunction liftToOuterList(state, dispatch, itemType, range) {\n    let tr = state.tr, end = range.end, endOfList = range.$to.end(range.depth);\n    if (end < endOfList) {\n        // There are siblings after the lifted items, which must become\n        // children of the last item\n        tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(end - 1, endOfList, end, endOfList, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(itemType.create(null, range.parent.copy())), 1, 0), 1, true));\n        range = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.NodeRange(tr.doc.resolve(range.$from.pos), tr.doc.resolve(endOfList), range.depth);\n    }\n    const target = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.liftTarget)(range);\n    if (target == null)\n        return false;\n    tr.lift(range, target);\n    let $after = tr.doc.resolve(tr.mapping.map(end, -1) - 1);\n    if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.canJoin)(tr.doc, $after.pos) && $after.nodeBefore.type == $after.nodeAfter.type)\n        tr.join($after.pos);\n    dispatch(tr.scrollIntoView());\n    return true;\n}\nfunction liftOutOfList(state, dispatch, range) {\n    let tr = state.tr, list = range.parent;\n    // Merge the list items into a single big item\n    for (let pos = range.end, i = range.endIndex - 1, e = range.startIndex; i > e; i--) {\n        pos -= list.child(i).nodeSize;\n        tr.delete(pos - 1, pos + 1);\n    }\n    let $start = tr.doc.resolve(range.start), item = $start.nodeAfter;\n    if (tr.mapping.map(range.end) != range.start + $start.nodeAfter.nodeSize)\n        return false;\n    let atStart = range.startIndex == 0, atEnd = range.endIndex == list.childCount;\n    let parent = $start.node(-1), indexBefore = $start.index(-1);\n    if (!parent.canReplace(indexBefore + (atStart ? 0 : 1), indexBefore + 1, item.content.append(atEnd ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty : prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(list))))\n        return false;\n    let start = $start.pos, end = start + item.nodeSize;\n    // Strip off the surrounding list. At the sides where we're not at\n    // the end of the list, the existing list is closed. At sides where\n    // this is the end, it is overwritten to its end.\n    tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(start - (atStart ? 1 : 0), end + (atEnd ? 1 : 0), start + 1, end - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice((atStart ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty : prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(list.copy(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty)))\n        .append(atEnd ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty : prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(list.copy(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty))), atStart ? 0 : 1, atEnd ? 0 : 1), atStart ? 0 : 1));\n    dispatch(tr.scrollIntoView());\n    return true;\n}\n/**\nCreate a command to sink the list item around the selection down\ninto an inner list.\n*/\nfunction sinkListItem(itemType) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to, node => node.childCount > 0 && node.firstChild.type == itemType);\n        if (!range)\n            return false;\n        let startIndex = range.startIndex;\n        if (startIndex == 0)\n            return false;\n        let parent = range.parent, nodeBefore = parent.child(startIndex - 1);\n        if (nodeBefore.type != itemType)\n            return false;\n        if (dispatch) {\n            let nestedBefore = nodeBefore.lastChild && nodeBefore.lastChild.type == parent.type;\n            let inner = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(nestedBefore ? itemType.create() : null);\n            let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(itemType.create(null, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(parent.type.create(null, inner)))), nestedBefore ? 3 : 1, 0);\n            let before = range.start, after = range.end;\n            dispatch(state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(before - (nestedBefore ? 3 : 1), after, before, after, slice, 1, true))\n                .scrollIntoView());\n        }\n        return true;\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-schema-list/dist/index.js\n");

/***/ })

};
;