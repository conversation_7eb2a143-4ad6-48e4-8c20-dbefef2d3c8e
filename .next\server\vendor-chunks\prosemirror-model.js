"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-model";
exports.ids = ["vendor-chunks/prosemirror-model"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-model/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/prosemirror-model/dist/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentMatch: () => (/* binding */ ContentMatch),\n/* harmony export */   DOMParser: () => (/* binding */ DOMParser),\n/* harmony export */   DOMSerializer: () => (/* binding */ DOMSerializer),\n/* harmony export */   Fragment: () => (/* binding */ Fragment),\n/* harmony export */   Mark: () => (/* binding */ Mark),\n/* harmony export */   MarkType: () => (/* binding */ MarkType),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeRange: () => (/* binding */ NodeRange),\n/* harmony export */   NodeType: () => (/* binding */ NodeType),\n/* harmony export */   ReplaceError: () => (/* binding */ ReplaceError),\n/* harmony export */   ResolvedPos: () => (/* binding */ ResolvedPos),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   Slice: () => (/* binding */ Slice)\n/* harmony export */ });\n/* harmony import */ var orderedmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! orderedmap */ \"(ssr)/./node_modules/orderedmap/dist/index.js\");\n\n\nfunction findDiffStart(a, b, pos) {\n    for (let i = 0;; i++) {\n        if (i == a.childCount || i == b.childCount)\n            return a.childCount == b.childCount ? null : pos;\n        let childA = a.child(i), childB = b.child(i);\n        if (childA == childB) {\n            pos += childA.nodeSize;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return pos;\n        if (childA.isText && childA.text != childB.text) {\n            for (let j = 0; childA.text[j] == childB.text[j]; j++)\n                pos++;\n            return pos;\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffStart(childA.content, childB.content, pos + 1);\n            if (inner != null)\n                return inner;\n        }\n        pos += childA.nodeSize;\n    }\n}\nfunction findDiffEnd(a, b, posA, posB) {\n    for (let iA = a.childCount, iB = b.childCount;;) {\n        if (iA == 0 || iB == 0)\n            return iA == iB ? null : { a: posA, b: posB };\n        let childA = a.child(--iA), childB = b.child(--iB), size = childA.nodeSize;\n        if (childA == childB) {\n            posA -= size;\n            posB -= size;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return { a: posA, b: posB };\n        if (childA.isText && childA.text != childB.text) {\n            let same = 0, minSize = Math.min(childA.text.length, childB.text.length);\n            while (same < minSize && childA.text[childA.text.length - same - 1] == childB.text[childB.text.length - same - 1]) {\n                same++;\n                posA--;\n                posB--;\n            }\n            return { a: posA, b: posB };\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffEnd(childA.content, childB.content, posA - 1, posB - 1);\n            if (inner)\n                return inner;\n        }\n        posA -= size;\n        posB -= size;\n    }\n}\n\n/**\nA fragment represents a node's collection of child nodes.\n\nLike nodes, fragments are persistent data structures, and you\nshould not mutate them or their content. Rather, you create new\ninstances whenever needed. The API tries to make this easy.\n*/\nclass Fragment {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The child nodes in this fragment.\n    */\n    content, size) {\n        this.content = content;\n        this.size = size || 0;\n        if (size == null)\n            for (let i = 0; i < content.length; i++)\n                this.size += content[i].nodeSize;\n    }\n    /**\n    Invoke a callback for all descendant nodes between the given two\n    positions (relative to start of this fragment). Doesn't descend\n    into a node when the callback returns `false`.\n    */\n    nodesBetween(from, to, f, nodeStart = 0, parent) {\n        for (let i = 0, pos = 0; pos < to; i++) {\n            let child = this.content[i], end = pos + child.nodeSize;\n            if (end > from && f(child, nodeStart + pos, parent || null, i) !== false && child.content.size) {\n                let start = pos + 1;\n                child.nodesBetween(Math.max(0, from - start), Math.min(child.content.size, to - start), f, nodeStart + start);\n            }\n            pos = end;\n        }\n    }\n    /**\n    Call the given callback for every descendant node. `pos` will be\n    relative to the start of the fragment. The callback may return\n    `false` to prevent traversal of a given node's children.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.size, f);\n    }\n    /**\n    Extract the text between `from` and `to`. See the same method on\n    [`Node`](https://prosemirror.net/docs/ref/#model.Node.textBetween).\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        let text = \"\", first = true;\n        this.nodesBetween(from, to, (node, pos) => {\n            let nodeText = node.isText ? node.text.slice(Math.max(from, pos) - pos, to - pos)\n                : !node.isLeaf ? \"\"\n                    : leafText ? (typeof leafText === \"function\" ? leafText(node) : leafText)\n                        : node.type.spec.leafText ? node.type.spec.leafText(node)\n                            : \"\";\n            if (node.isBlock && (node.isLeaf && nodeText || node.isTextblock) && blockSeparator) {\n                if (first)\n                    first = false;\n                else\n                    text += blockSeparator;\n            }\n            text += nodeText;\n        }, 0);\n        return text;\n    }\n    /**\n    Create a new fragment containing the combined content of this\n    fragment and the other.\n    */\n    append(other) {\n        if (!other.size)\n            return this;\n        if (!this.size)\n            return other;\n        let last = this.lastChild, first = other.firstChild, content = this.content.slice(), i = 0;\n        if (last.isText && last.sameMarkup(first)) {\n            content[content.length - 1] = last.withText(last.text + first.text);\n            i = 1;\n        }\n        for (; i < other.content.length; i++)\n            content.push(other.content[i]);\n        return new Fragment(content, this.size + other.size);\n    }\n    /**\n    Cut out the sub-fragment between the two given positions.\n    */\n    cut(from, to = this.size) {\n        if (from == 0 && to == this.size)\n            return this;\n        let result = [], size = 0;\n        if (to > from)\n            for (let i = 0, pos = 0; pos < to; i++) {\n                let child = this.content[i], end = pos + child.nodeSize;\n                if (end > from) {\n                    if (pos < from || end > to) {\n                        if (child.isText)\n                            child = child.cut(Math.max(0, from - pos), Math.min(child.text.length, to - pos));\n                        else\n                            child = child.cut(Math.max(0, from - pos - 1), Math.min(child.content.size, to - pos - 1));\n                    }\n                    result.push(child);\n                    size += child.nodeSize;\n                }\n                pos = end;\n            }\n        return new Fragment(result, size);\n    }\n    /**\n    @internal\n    */\n    cutByIndex(from, to) {\n        if (from == to)\n            return Fragment.empty;\n        if (from == 0 && to == this.content.length)\n            return this;\n        return new Fragment(this.content.slice(from, to));\n    }\n    /**\n    Create a new fragment in which the node at the given index is\n    replaced by the given node.\n    */\n    replaceChild(index, node) {\n        let current = this.content[index];\n        if (current == node)\n            return this;\n        let copy = this.content.slice();\n        let size = this.size + node.nodeSize - current.nodeSize;\n        copy[index] = node;\n        return new Fragment(copy, size);\n    }\n    /**\n    Create a new fragment by prepending the given node to this\n    fragment.\n    */\n    addToStart(node) {\n        return new Fragment([node].concat(this.content), this.size + node.nodeSize);\n    }\n    /**\n    Create a new fragment by appending the given node to this\n    fragment.\n    */\n    addToEnd(node) {\n        return new Fragment(this.content.concat(node), this.size + node.nodeSize);\n    }\n    /**\n    Compare this fragment to another one.\n    */\n    eq(other) {\n        if (this.content.length != other.content.length)\n            return false;\n        for (let i = 0; i < this.content.length; i++)\n            if (!this.content[i].eq(other.content[i]))\n                return false;\n        return true;\n    }\n    /**\n    The first child of the fragment, or `null` if it is empty.\n    */\n    get firstChild() { return this.content.length ? this.content[0] : null; }\n    /**\n    The last child of the fragment, or `null` if it is empty.\n    */\n    get lastChild() { return this.content.length ? this.content[this.content.length - 1] : null; }\n    /**\n    The number of child nodes in this fragment.\n    */\n    get childCount() { return this.content.length; }\n    /**\n    Get the child node at the given index. Raise an error when the\n    index is out of range.\n    */\n    child(index) {\n        let found = this.content[index];\n        if (!found)\n            throw new RangeError(\"Index \" + index + \" out of range for \" + this);\n        return found;\n    }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) {\n        return this.content[index] || null;\n    }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) {\n        for (let i = 0, p = 0; i < this.content.length; i++) {\n            let child = this.content[i];\n            f(child, p, i);\n            p += child.nodeSize;\n        }\n    }\n    /**\n    Find the first position at which this fragment and another\n    fragment differ, or `null` if they are the same.\n    */\n    findDiffStart(other, pos = 0) {\n        return findDiffStart(this, other, pos);\n    }\n    /**\n    Find the first position, searching from the end, at which this\n    fragment and the given fragment differ, or `null` if they are\n    the same. Since this position will not be the same in both\n    nodes, an object with two separate positions is returned.\n    */\n    findDiffEnd(other, pos = this.size, otherPos = other.size) {\n        return findDiffEnd(this, other, pos, otherPos);\n    }\n    /**\n    Find the index and inner offset corresponding to a given relative\n    position in this fragment. The result object will be reused\n    (overwritten) the next time the function is called. @internal\n    */\n    findIndex(pos, round = -1) {\n        if (pos == 0)\n            return retIndex(0, pos);\n        if (pos == this.size)\n            return retIndex(this.content.length, pos);\n        if (pos > this.size || pos < 0)\n            throw new RangeError(`Position ${pos} outside of fragment (${this})`);\n        for (let i = 0, curPos = 0;; i++) {\n            let cur = this.child(i), end = curPos + cur.nodeSize;\n            if (end >= pos) {\n                if (end == pos || round > 0)\n                    return retIndex(i + 1, end);\n                return retIndex(i, curPos);\n            }\n            curPos = end;\n        }\n    }\n    /**\n    Return a debugging string that describes this fragment.\n    */\n    toString() { return \"<\" + this.toStringInner() + \">\"; }\n    /**\n    @internal\n    */\n    toStringInner() { return this.content.join(\", \"); }\n    /**\n    Create a JSON-serializeable representation of this fragment.\n    */\n    toJSON() {\n        return this.content.length ? this.content.map(n => n.toJSON()) : null;\n    }\n    /**\n    Deserialize a fragment from its JSON representation.\n    */\n    static fromJSON(schema, value) {\n        if (!value)\n            return Fragment.empty;\n        if (!Array.isArray(value))\n            throw new RangeError(\"Invalid input for Fragment.fromJSON\");\n        return new Fragment(value.map(schema.nodeFromJSON));\n    }\n    /**\n    Build a fragment from an array of nodes. Ensures that adjacent\n    text nodes with the same marks are joined together.\n    */\n    static fromArray(array) {\n        if (!array.length)\n            return Fragment.empty;\n        let joined, size = 0;\n        for (let i = 0; i < array.length; i++) {\n            let node = array[i];\n            size += node.nodeSize;\n            if (i && node.isText && array[i - 1].sameMarkup(node)) {\n                if (!joined)\n                    joined = array.slice(0, i);\n                joined[joined.length - 1] = node\n                    .withText(joined[joined.length - 1].text + node.text);\n            }\n            else if (joined) {\n                joined.push(node);\n            }\n        }\n        return new Fragment(joined || array, size);\n    }\n    /**\n    Create a fragment from something that can be interpreted as a\n    set of nodes. For `null`, it returns the empty fragment. For a\n    fragment, the fragment itself. For a node or array of nodes, a\n    fragment containing those nodes.\n    */\n    static from(nodes) {\n        if (!nodes)\n            return Fragment.empty;\n        if (nodes instanceof Fragment)\n            return nodes;\n        if (Array.isArray(nodes))\n            return this.fromArray(nodes);\n        if (nodes.attrs)\n            return new Fragment([nodes], nodes.nodeSize);\n        throw new RangeError(\"Can not convert \" + nodes + \" to a Fragment\" +\n            (nodes.nodesBetween ? \" (looks like multiple versions of prosemirror-model were loaded)\" : \"\"));\n    }\n}\n/**\nAn empty fragment. Intended to be reused whenever a node doesn't\ncontain anything (rather than allocating a new empty fragment for\neach leaf node).\n*/\nFragment.empty = new Fragment([], 0);\nconst found = { index: 0, offset: 0 };\nfunction retIndex(index, offset) {\n    found.index = index;\n    found.offset = offset;\n    return found;\n}\n\nfunction compareDeep(a, b) {\n    if (a === b)\n        return true;\n    if (!(a && typeof a == \"object\") ||\n        !(b && typeof b == \"object\"))\n        return false;\n    let array = Array.isArray(a);\n    if (Array.isArray(b) != array)\n        return false;\n    if (array) {\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!compareDeep(a[i], b[i]))\n                return false;\n    }\n    else {\n        for (let p in a)\n            if (!(p in b) || !compareDeep(a[p], b[p]))\n                return false;\n        for (let p in b)\n            if (!(p in a))\n                return false;\n    }\n    return true;\n}\n\n/**\nA mark is a piece of information that can be attached to a node,\nsuch as it being emphasized, in code font, or a link. It has a\ntype and optionally a set of attributes that provide further\ninformation (such as the target of the link). Marks are created\nthrough a `Schema`, which controls which types exist and which\nattributes they have.\n*/\nclass Mark {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of this mark.\n    */\n    type, \n    /**\n    The attributes associated with this mark.\n    */\n    attrs) {\n        this.type = type;\n        this.attrs = attrs;\n    }\n    /**\n    Given a set of marks, create a new set which contains this one as\n    well, in the right position. If this mark is already in the set,\n    the set itself is returned. If any marks that are set to be\n    [exclusive](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) with this mark are present,\n    those are replaced by this one.\n    */\n    addToSet(set) {\n        let copy, placed = false;\n        for (let i = 0; i < set.length; i++) {\n            let other = set[i];\n            if (this.eq(other))\n                return set;\n            if (this.type.excludes(other.type)) {\n                if (!copy)\n                    copy = set.slice(0, i);\n            }\n            else if (other.type.excludes(this.type)) {\n                return set;\n            }\n            else {\n                if (!placed && other.type.rank > this.type.rank) {\n                    if (!copy)\n                        copy = set.slice(0, i);\n                    copy.push(this);\n                    placed = true;\n                }\n                if (copy)\n                    copy.push(other);\n            }\n        }\n        if (!copy)\n            copy = set.slice();\n        if (!placed)\n            copy.push(this);\n        return copy;\n    }\n    /**\n    Remove this mark from the given set, returning a new set. If this\n    mark is not in the set, the set itself is returned.\n    */\n    removeFromSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return set.slice(0, i).concat(set.slice(i + 1));\n        return set;\n    }\n    /**\n    Test whether this mark is in the given set of marks.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return true;\n        return false;\n    }\n    /**\n    Test whether this mark has the same type and attributes as\n    another mark.\n    */\n    eq(other) {\n        return this == other ||\n            (this.type == other.type && compareDeep(this.attrs, other.attrs));\n    }\n    /**\n    Convert this mark to a JSON-serializeable representation.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        return obj;\n    }\n    /**\n    Deserialize a mark from JSON.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Mark.fromJSON\");\n        let type = schema.marks[json.type];\n        if (!type)\n            throw new RangeError(`There is no mark type ${json.type} in this schema`);\n        let mark = type.create(json.attrs);\n        type.checkAttrs(mark.attrs);\n        return mark;\n    }\n    /**\n    Test whether two sets of marks are identical.\n    */\n    static sameSet(a, b) {\n        if (a == b)\n            return true;\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!a[i].eq(b[i]))\n                return false;\n        return true;\n    }\n    /**\n    Create a properly sorted mark set from null, a single mark, or an\n    unsorted array of marks.\n    */\n    static setFrom(marks) {\n        if (!marks || Array.isArray(marks) && marks.length == 0)\n            return Mark.none;\n        if (marks instanceof Mark)\n            return [marks];\n        let copy = marks.slice();\n        copy.sort((a, b) => a.type.rank - b.type.rank);\n        return copy;\n    }\n}\n/**\nThe empty set of marks.\n*/\nMark.none = [];\n\n/**\nError type raised by [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) when\ngiven an invalid replacement.\n*/\nclass ReplaceError extends Error {\n}\n/*\nReplaceError = function(this: any, message: string) {\n  let err = Error.call(this, message)\n  ;(err as any).__proto__ = ReplaceError.prototype\n  return err\n} as any\n\nReplaceError.prototype = Object.create(Error.prototype)\nReplaceError.prototype.constructor = ReplaceError\nReplaceError.prototype.name = \"ReplaceError\"\n*/\n/**\nA slice represents a piece cut out of a larger document. It\nstores not only a fragment, but also the depth up to which nodes on\nboth side are ‘open’ (cut through).\n*/\nclass Slice {\n    /**\n    Create a slice. When specifying a non-zero open depth, you must\n    make sure that there are nodes of at least that depth at the\n    appropriate side of the fragment—i.e. if the fragment is an\n    empty paragraph node, `openStart` and `openEnd` can't be greater\n    than 1.\n    \n    It is not necessary for the content of open nodes to conform to\n    the schema's content constraints, though it should be a valid\n    start/end/middle for such a node, depending on which sides are\n    open.\n    */\n    constructor(\n    /**\n    The slice's content.\n    */\n    content, \n    /**\n    The open depth at the start of the fragment.\n    */\n    openStart, \n    /**\n    The open depth at the end.\n    */\n    openEnd) {\n        this.content = content;\n        this.openStart = openStart;\n        this.openEnd = openEnd;\n    }\n    /**\n    The size this slice would add when inserted into a document.\n    */\n    get size() {\n        return this.content.size - this.openStart - this.openEnd;\n    }\n    /**\n    @internal\n    */\n    insertAt(pos, fragment) {\n        let content = insertInto(this.content, pos + this.openStart, fragment);\n        return content && new Slice(content, this.openStart, this.openEnd);\n    }\n    /**\n    @internal\n    */\n    removeBetween(from, to) {\n        return new Slice(removeRange(this.content, from + this.openStart, to + this.openStart), this.openStart, this.openEnd);\n    }\n    /**\n    Tests whether this slice is equal to another slice.\n    */\n    eq(other) {\n        return this.content.eq(other.content) && this.openStart == other.openStart && this.openEnd == other.openEnd;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.content + \"(\" + this.openStart + \",\" + this.openEnd + \")\";\n    }\n    /**\n    Convert a slice to a JSON-serializable representation.\n    */\n    toJSON() {\n        if (!this.content.size)\n            return null;\n        let json = { content: this.content.toJSON() };\n        if (this.openStart > 0)\n            json.openStart = this.openStart;\n        if (this.openEnd > 0)\n            json.openEnd = this.openEnd;\n        return json;\n    }\n    /**\n    Deserialize a slice from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            return Slice.empty;\n        let openStart = json.openStart || 0, openEnd = json.openEnd || 0;\n        if (typeof openStart != \"number\" || typeof openEnd != \"number\")\n            throw new RangeError(\"Invalid input for Slice.fromJSON\");\n        return new Slice(Fragment.fromJSON(schema, json.content), openStart, openEnd);\n    }\n    /**\n    Create a slice from a fragment by taking the maximum possible\n    open value on both side of the fragment.\n    */\n    static maxOpen(fragment, openIsolating = true) {\n        let openStart = 0, openEnd = 0;\n        for (let n = fragment.firstChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.firstChild)\n            openStart++;\n        for (let n = fragment.lastChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.lastChild)\n            openEnd++;\n        return new Slice(fragment, openStart, openEnd);\n    }\n}\n/**\nThe empty slice.\n*/\nSlice.empty = new Slice(Fragment.empty, 0, 0);\nfunction removeRange(content, from, to) {\n    let { index, offset } = content.findIndex(from), child = content.maybeChild(index);\n    let { index: indexTo, offset: offsetTo } = content.findIndex(to);\n    if (offset == from || child.isText) {\n        if (offsetTo != to && !content.child(indexTo).isText)\n            throw new RangeError(\"Removing non-flat range\");\n        return content.cut(0, from).append(content.cut(to));\n    }\n    if (index != indexTo)\n        throw new RangeError(\"Removing non-flat range\");\n    return content.replaceChild(index, child.copy(removeRange(child.content, from - offset - 1, to - offset - 1)));\n}\nfunction insertInto(content, dist, insert, parent) {\n    let { index, offset } = content.findIndex(dist), child = content.maybeChild(index);\n    if (offset == dist || child.isText) {\n        if (parent && !parent.canReplace(index, index, insert))\n            return null;\n        return content.cut(0, dist).append(insert).append(content.cut(dist));\n    }\n    let inner = insertInto(child.content, dist - offset - 1, insert);\n    return inner && content.replaceChild(index, child.copy(inner));\n}\nfunction replace($from, $to, slice) {\n    if (slice.openStart > $from.depth)\n        throw new ReplaceError(\"Inserted content deeper than insertion position\");\n    if ($from.depth - slice.openStart != $to.depth - slice.openEnd)\n        throw new ReplaceError(\"Inconsistent open depths\");\n    return replaceOuter($from, $to, slice, 0);\n}\nfunction replaceOuter($from, $to, slice, depth) {\n    let index = $from.index(depth), node = $from.node(depth);\n    if (index == $to.index(depth) && depth < $from.depth - slice.openStart) {\n        let inner = replaceOuter($from, $to, slice, depth + 1);\n        return node.copy(node.content.replaceChild(index, inner));\n    }\n    else if (!slice.content.size) {\n        return close(node, replaceTwoWay($from, $to, depth));\n    }\n    else if (!slice.openStart && !slice.openEnd && $from.depth == depth && $to.depth == depth) { // Simple, flat case\n        let parent = $from.parent, content = parent.content;\n        return close(parent, content.cut(0, $from.parentOffset).append(slice.content).append(content.cut($to.parentOffset)));\n    }\n    else {\n        let { start, end } = prepareSliceForReplace(slice, $from);\n        return close(node, replaceThreeWay($from, start, end, $to, depth));\n    }\n}\nfunction checkJoin(main, sub) {\n    if (!sub.type.compatibleContent(main.type))\n        throw new ReplaceError(\"Cannot join \" + sub.type.name + \" onto \" + main.type.name);\n}\nfunction joinable($before, $after, depth) {\n    let node = $before.node(depth);\n    checkJoin(node, $after.node(depth));\n    return node;\n}\nfunction addNode(child, target) {\n    let last = target.length - 1;\n    if (last >= 0 && child.isText && child.sameMarkup(target[last]))\n        target[last] = child.withText(target[last].text + child.text);\n    else\n        target.push(child);\n}\nfunction addRange($start, $end, depth, target) {\n    let node = ($end || $start).node(depth);\n    let startIndex = 0, endIndex = $end ? $end.index(depth) : node.childCount;\n    if ($start) {\n        startIndex = $start.index(depth);\n        if ($start.depth > depth) {\n            startIndex++;\n        }\n        else if ($start.textOffset) {\n            addNode($start.nodeAfter, target);\n            startIndex++;\n        }\n    }\n    for (let i = startIndex; i < endIndex; i++)\n        addNode(node.child(i), target);\n    if ($end && $end.depth == depth && $end.textOffset)\n        addNode($end.nodeBefore, target);\n}\nfunction close(node, content) {\n    node.type.checkContent(content);\n    return node.copy(content);\n}\nfunction replaceThreeWay($from, $start, $end, $to, depth) {\n    let openStart = $from.depth > depth && joinable($from, $start, depth + 1);\n    let openEnd = $to.depth > depth && joinable($end, $to, depth + 1);\n    let content = [];\n    addRange(null, $from, depth, content);\n    if (openStart && openEnd && $start.index(depth) == $end.index(depth)) {\n        checkJoin(openStart, openEnd);\n        addNode(close(openStart, replaceThreeWay($from, $start, $end, $to, depth + 1)), content);\n    }\n    else {\n        if (openStart)\n            addNode(close(openStart, replaceTwoWay($from, $start, depth + 1)), content);\n        addRange($start, $end, depth, content);\n        if (openEnd)\n            addNode(close(openEnd, replaceTwoWay($end, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction replaceTwoWay($from, $to, depth) {\n    let content = [];\n    addRange(null, $from, depth, content);\n    if ($from.depth > depth) {\n        let type = joinable($from, $to, depth + 1);\n        addNode(close(type, replaceTwoWay($from, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction prepareSliceForReplace(slice, $along) {\n    let extra = $along.depth - slice.openStart, parent = $along.node(extra);\n    let node = parent.copy(slice.content);\n    for (let i = extra - 1; i >= 0; i--)\n        node = $along.node(i).copy(Fragment.from(node));\n    return { start: node.resolveNoCache(slice.openStart + extra),\n        end: node.resolveNoCache(node.content.size - slice.openEnd - extra) };\n}\n\n/**\nYou can [_resolve_](https://prosemirror.net/docs/ref/#model.Node.resolve) a position to get more\ninformation about it. Objects of this class represent such a\nresolved position, providing various pieces of context\ninformation, and some helper methods.\n\nThroughout this interface, methods that take an optional `depth`\nparameter will interpret undefined as `this.depth` and negative\nnumbers as `this.depth + value`.\n*/\nclass ResolvedPos {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position that was resolved.\n    */\n    pos, \n    /**\n    @internal\n    */\n    path, \n    /**\n    The offset this position has into its parent node.\n    */\n    parentOffset) {\n        this.pos = pos;\n        this.path = path;\n        this.parentOffset = parentOffset;\n        this.depth = path.length / 3 - 1;\n    }\n    /**\n    @internal\n    */\n    resolveDepth(val) {\n        if (val == null)\n            return this.depth;\n        if (val < 0)\n            return this.depth + val;\n        return val;\n    }\n    /**\n    The parent node that the position points into. Note that even if\n    a position points into a text node, that node is not considered\n    the parent—text nodes are ‘flat’ in this model, and have no content.\n    */\n    get parent() { return this.node(this.depth); }\n    /**\n    The root node in which the position was resolved.\n    */\n    get doc() { return this.node(0); }\n    /**\n    The ancestor node at the given level. `p.node(p.depth)` is the\n    same as `p.parent`.\n    */\n    node(depth) { return this.path[this.resolveDepth(depth) * 3]; }\n    /**\n    The index into the ancestor at the given level. If this points\n    at the 3rd node in the 2nd paragraph on the top level, for\n    example, `p.index(0)` is 1 and `p.index(1)` is 2.\n    */\n    index(depth) { return this.path[this.resolveDepth(depth) * 3 + 1]; }\n    /**\n    The index pointing after this position into the ancestor at the\n    given level.\n    */\n    indexAfter(depth) {\n        depth = this.resolveDepth(depth);\n        return this.index(depth) + (depth == this.depth && !this.textOffset ? 0 : 1);\n    }\n    /**\n    The (absolute) position at the start of the node at the given\n    level.\n    */\n    start(depth) {\n        depth = this.resolveDepth(depth);\n        return depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n    }\n    /**\n    The (absolute) position at the end of the node at the given\n    level.\n    */\n    end(depth) {\n        depth = this.resolveDepth(depth);\n        return this.start(depth) + this.node(depth).content.size;\n    }\n    /**\n    The (absolute) position directly before the wrapping node at the\n    given level, or, when `depth` is `this.depth + 1`, the original\n    position.\n    */\n    before(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position before the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1];\n    }\n    /**\n    The (absolute) position directly after the wrapping node at the\n    given level, or the original position when `depth` is `this.depth + 1`.\n    */\n    after(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position after the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1] + this.path[depth * 3].nodeSize;\n    }\n    /**\n    When this position points into a text node, this returns the\n    distance between the position and the start of the text node.\n    Will be zero for positions that point between nodes.\n    */\n    get textOffset() { return this.pos - this.path[this.path.length - 1]; }\n    /**\n    Get the node directly after the position, if any. If the position\n    points into a text node, only the part of that node after the\n    position is returned.\n    */\n    get nodeAfter() {\n        let parent = this.parent, index = this.index(this.depth);\n        if (index == parent.childCount)\n            return null;\n        let dOff = this.pos - this.path[this.path.length - 1], child = parent.child(index);\n        return dOff ? parent.child(index).cut(dOff) : child;\n    }\n    /**\n    Get the node directly before the position, if any. If the\n    position points into a text node, only the part of that node\n    before the position is returned.\n    */\n    get nodeBefore() {\n        let index = this.index(this.depth);\n        let dOff = this.pos - this.path[this.path.length - 1];\n        if (dOff)\n            return this.parent.child(index).cut(0, dOff);\n        return index == 0 ? null : this.parent.child(index - 1);\n    }\n    /**\n    Get the position at the given index in the parent node at the\n    given depth (which defaults to `this.depth`).\n    */\n    posAtIndex(index, depth) {\n        depth = this.resolveDepth(depth);\n        let node = this.path[depth * 3], pos = depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n        for (let i = 0; i < index; i++)\n            pos += node.child(i).nodeSize;\n        return pos;\n    }\n    /**\n    Get the marks at this position, factoring in the surrounding\n    marks' [`inclusive`](https://prosemirror.net/docs/ref/#model.MarkSpec.inclusive) property. If the\n    position is at the start of a non-empty node, the marks of the\n    node after it (if any) are returned.\n    */\n    marks() {\n        let parent = this.parent, index = this.index();\n        // In an empty parent, return the empty array\n        if (parent.content.size == 0)\n            return Mark.none;\n        // When inside a text node, just return the text node's marks\n        if (this.textOffset)\n            return parent.child(index).marks;\n        let main = parent.maybeChild(index - 1), other = parent.maybeChild(index);\n        // If the `after` flag is true of there is no node before, make\n        // the node after this position the main reference.\n        if (!main) {\n            let tmp = main;\n            main = other;\n            other = tmp;\n        }\n        // Use all marks in the main node, except those that have\n        // `inclusive` set to false and are not present in the other node.\n        let marks = main.marks;\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!other || !marks[i].isInSet(other.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    Get the marks after the current position, if any, except those\n    that are non-inclusive and not present at position `$end`. This\n    is mostly useful for getting the set of marks to preserve after a\n    deletion. Will return `null` if this position is at the end of\n    its parent node or its parent node isn't a textblock (in which\n    case no marks should be preserved).\n    */\n    marksAcross($end) {\n        let after = this.parent.maybeChild(this.index());\n        if (!after || !after.isInline)\n            return null;\n        let marks = after.marks, next = $end.parent.maybeChild($end.index());\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!next || !marks[i].isInSet(next.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    The depth up to which this position and the given (non-resolved)\n    position share the same parent nodes.\n    */\n    sharedDepth(pos) {\n        for (let depth = this.depth; depth > 0; depth--)\n            if (this.start(depth) <= pos && this.end(depth) >= pos)\n                return depth;\n        return 0;\n    }\n    /**\n    Returns a range based on the place where this position and the\n    given position diverge around block content. If both point into\n    the same textblock, for example, a range around that textblock\n    will be returned. If they point into different blocks, the range\n    around those blocks in their shared ancestor is returned. You can\n    pass in an optional predicate that will be called with a parent\n    node to see if a range into that parent is acceptable.\n    */\n    blockRange(other = this, pred) {\n        if (other.pos < this.pos)\n            return other.blockRange(this);\n        for (let d = this.depth - (this.parent.inlineContent || this.pos == other.pos ? 1 : 0); d >= 0; d--)\n            if (other.pos <= this.end(d) && (!pred || pred(this.node(d))))\n                return new NodeRange(this, other, d);\n        return null;\n    }\n    /**\n    Query whether the given position shares the same parent node.\n    */\n    sameParent(other) {\n        return this.pos - this.parentOffset == other.pos - other.parentOffset;\n    }\n    /**\n    Return the greater of this and the given position.\n    */\n    max(other) {\n        return other.pos > this.pos ? other : this;\n    }\n    /**\n    Return the smaller of this and the given position.\n    */\n    min(other) {\n        return other.pos < this.pos ? other : this;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let str = \"\";\n        for (let i = 1; i <= this.depth; i++)\n            str += (str ? \"/\" : \"\") + this.node(i).type.name + \"_\" + this.index(i - 1);\n        return str + \":\" + this.parentOffset;\n    }\n    /**\n    @internal\n    */\n    static resolve(doc, pos) {\n        if (!(pos >= 0 && pos <= doc.content.size))\n            throw new RangeError(\"Position \" + pos + \" out of range\");\n        let path = [];\n        let start = 0, parentOffset = pos;\n        for (let node = doc;;) {\n            let { index, offset } = node.content.findIndex(parentOffset);\n            let rem = parentOffset - offset;\n            path.push(node, index, start + offset);\n            if (!rem)\n                break;\n            node = node.child(index);\n            if (node.isText)\n                break;\n            parentOffset = rem - 1;\n            start += offset + 1;\n        }\n        return new ResolvedPos(pos, path, parentOffset);\n    }\n    /**\n    @internal\n    */\n    static resolveCached(doc, pos) {\n        let cache = resolveCache.get(doc);\n        if (cache) {\n            for (let i = 0; i < cache.elts.length; i++) {\n                let elt = cache.elts[i];\n                if (elt.pos == pos)\n                    return elt;\n            }\n        }\n        else {\n            resolveCache.set(doc, cache = new ResolveCache);\n        }\n        let result = cache.elts[cache.i] = ResolvedPos.resolve(doc, pos);\n        cache.i = (cache.i + 1) % resolveCacheSize;\n        return result;\n    }\n}\nclass ResolveCache {\n    constructor() {\n        this.elts = [];\n        this.i = 0;\n    }\n}\nconst resolveCacheSize = 12, resolveCache = new WeakMap();\n/**\nRepresents a flat range of content, i.e. one that starts and\nends in the same node.\n*/\nclass NodeRange {\n    /**\n    Construct a node range. `$from` and `$to` should point into the\n    same node until at least the given `depth`, since a node range\n    denotes an adjacent set of nodes in a single parent node.\n    */\n    constructor(\n    /**\n    A resolved position along the start of the content. May have a\n    `depth` greater than this object's `depth` property, since\n    these are the positions that were used to compute the range,\n    not re-resolved positions directly at its boundaries.\n    */\n    $from, \n    /**\n    A position along the end of the content. See\n    caveat for [`$from`](https://prosemirror.net/docs/ref/#model.NodeRange.$from).\n    */\n    $to, \n    /**\n    The depth of the node that this range points into.\n    */\n    depth) {\n        this.$from = $from;\n        this.$to = $to;\n        this.depth = depth;\n    }\n    /**\n    The position at the start of the range.\n    */\n    get start() { return this.$from.before(this.depth + 1); }\n    /**\n    The position at the end of the range.\n    */\n    get end() { return this.$to.after(this.depth + 1); }\n    /**\n    The parent node that the range points into.\n    */\n    get parent() { return this.$from.node(this.depth); }\n    /**\n    The start index of the range in the parent node.\n    */\n    get startIndex() { return this.$from.index(this.depth); }\n    /**\n    The end index of the range in the parent node.\n    */\n    get endIndex() { return this.$to.indexAfter(this.depth); }\n}\n\nconst emptyAttrs = Object.create(null);\n/**\nThis class represents a node in the tree that makes up a\nProseMirror document. So a document is an instance of `Node`, with\nchildren that are also instances of `Node`.\n\nNodes are persistent data structures. Instead of changing them, you\ncreate new ones with the content you want. Old ones keep pointing\nat the old document shape. This is made cheaper by sharing\nstructure between the old and new data as much as possible, which a\ntree shape like this (without back pointers) makes easy.\n\n**Do not** directly mutate the properties of a `Node` object. See\n[the guide](https://prosemirror.net/docs/guide/#doc) for more information.\n*/\nclass Node {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of node that this is.\n    */\n    type, \n    /**\n    An object mapping attribute names to values. The kind of\n    attributes allowed and required are\n    [determined](https://prosemirror.net/docs/ref/#model.NodeSpec.attrs) by the node type.\n    */\n    attrs, \n    // A fragment holding the node's children.\n    content, \n    /**\n    The marks (things like whether it is emphasized or part of a\n    link) applied to this node.\n    */\n    marks = Mark.none) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.content = content || Fragment.empty;\n    }\n    /**\n    The array of this node's child nodes.\n    */\n    get children() { return this.content.content; }\n    /**\n    The size of this node, as defined by the integer-based [indexing\n    scheme](https://prosemirror.net/docs/guide/#doc.indexing). For text nodes, this is the\n    amount of characters. For other leaf nodes, it is one. For\n    non-leaf nodes, it is the size of the content plus two (the\n    start and end token).\n    */\n    get nodeSize() { return this.isLeaf ? 1 : 2 + this.content.size; }\n    /**\n    The number of children that the node has.\n    */\n    get childCount() { return this.content.childCount; }\n    /**\n    Get the child node at the given index. Raises an error when the\n    index is out of range.\n    */\n    child(index) { return this.content.child(index); }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) { return this.content.maybeChild(index); }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) { this.content.forEach(f); }\n    /**\n    Invoke a callback for all descendant nodes recursively between\n    the given two positions that are relative to start of this\n    node's content. The callback is invoked with the node, its\n    position relative to the original node (method receiver),\n    its parent node, and its child index. When the callback returns\n    false for a given node, that node's children will not be\n    recursed over. The last parameter can be used to specify a\n    starting position to count from.\n    */\n    nodesBetween(from, to, f, startPos = 0) {\n        this.content.nodesBetween(from, to, f, startPos, this);\n    }\n    /**\n    Call the given callback for every descendant node. Doesn't\n    descend into a node when the callback returns `false`.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.content.size, f);\n    }\n    /**\n    Concatenates all the text nodes found in this fragment and its\n    children.\n    */\n    get textContent() {\n        return (this.isLeaf && this.type.spec.leafText)\n            ? this.type.spec.leafText(this)\n            : this.textBetween(0, this.content.size, \"\");\n    }\n    /**\n    Get all text between positions `from` and `to`. When\n    `blockSeparator` is given, it will be inserted to separate text\n    from different block nodes. If `leafText` is given, it'll be\n    inserted for every non-text leaf node encountered, otherwise\n    [`leafText`](https://prosemirror.net/docs/ref/#model.NodeSpec^leafText) will be used.\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        return this.content.textBetween(from, to, blockSeparator, leafText);\n    }\n    /**\n    Returns this node's first child, or `null` if there are no\n    children.\n    */\n    get firstChild() { return this.content.firstChild; }\n    /**\n    Returns this node's last child, or `null` if there are no\n    children.\n    */\n    get lastChild() { return this.content.lastChild; }\n    /**\n    Test whether two nodes represent the same piece of document.\n    */\n    eq(other) {\n        return this == other || (this.sameMarkup(other) && this.content.eq(other.content));\n    }\n    /**\n    Compare the markup (type, attributes, and marks) of this node to\n    those of another. Returns `true` if both have the same markup.\n    */\n    sameMarkup(other) {\n        return this.hasMarkup(other.type, other.attrs, other.marks);\n    }\n    /**\n    Check whether this node's markup correspond to the given type,\n    attributes, and marks.\n    */\n    hasMarkup(type, attrs, marks) {\n        return this.type == type &&\n            compareDeep(this.attrs, attrs || type.defaultAttrs || emptyAttrs) &&\n            Mark.sameSet(this.marks, marks || Mark.none);\n    }\n    /**\n    Create a new node with the same markup as this node, containing\n    the given content (or empty, if no content is given).\n    */\n    copy(content = null) {\n        if (content == this.content)\n            return this;\n        return new Node(this.type, this.attrs, content, this.marks);\n    }\n    /**\n    Create a copy of this node, with the given set of marks instead\n    of the node's own marks.\n    */\n    mark(marks) {\n        return marks == this.marks ? this : new Node(this.type, this.attrs, this.content, marks);\n    }\n    /**\n    Create a copy of this node with only the content between the\n    given positions. If `to` is not given, it defaults to the end of\n    the node.\n    */\n    cut(from, to = this.content.size) {\n        if (from == 0 && to == this.content.size)\n            return this;\n        return this.copy(this.content.cut(from, to));\n    }\n    /**\n    Cut out the part of the document between the given positions, and\n    return it as a `Slice` object.\n    */\n    slice(from, to = this.content.size, includeParents = false) {\n        if (from == to)\n            return Slice.empty;\n        let $from = this.resolve(from), $to = this.resolve(to);\n        let depth = includeParents ? 0 : $from.sharedDepth(to);\n        let start = $from.start(depth), node = $from.node(depth);\n        let content = node.content.cut($from.pos - start, $to.pos - start);\n        return new Slice(content, $from.depth - depth, $to.depth - depth);\n    }\n    /**\n    Replace the part of the document between the given positions with\n    the given slice. The slice must 'fit', meaning its open sides\n    must be able to connect to the surrounding content, and its\n    content nodes must be valid children for the node they are placed\n    into. If any of this is violated, an error of type\n    [`ReplaceError`](https://prosemirror.net/docs/ref/#model.ReplaceError) is thrown.\n    */\n    replace(from, to, slice) {\n        return replace(this.resolve(from), this.resolve(to), slice);\n    }\n    /**\n    Find the node directly after the given position.\n    */\n    nodeAt(pos) {\n        for (let node = this;;) {\n            let { index, offset } = node.content.findIndex(pos);\n            node = node.maybeChild(index);\n            if (!node)\n                return null;\n            if (offset == pos || node.isText)\n                return node;\n            pos -= offset + 1;\n        }\n    }\n    /**\n    Find the (direct) child node after the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childAfter(pos) {\n        let { index, offset } = this.content.findIndex(pos);\n        return { node: this.content.maybeChild(index), index, offset };\n    }\n    /**\n    Find the (direct) child node before the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childBefore(pos) {\n        if (pos == 0)\n            return { node: null, index: 0, offset: 0 };\n        let { index, offset } = this.content.findIndex(pos);\n        if (offset < pos)\n            return { node: this.content.child(index), index, offset };\n        let node = this.content.child(index - 1);\n        return { node, index: index - 1, offset: offset - node.nodeSize };\n    }\n    /**\n    Resolve the given position in the document, returning an\n    [object](https://prosemirror.net/docs/ref/#model.ResolvedPos) with information about its context.\n    */\n    resolve(pos) { return ResolvedPos.resolveCached(this, pos); }\n    /**\n    @internal\n    */\n    resolveNoCache(pos) { return ResolvedPos.resolve(this, pos); }\n    /**\n    Test whether a given mark or mark type occurs in this document\n    between the two given positions.\n    */\n    rangeHasMark(from, to, type) {\n        let found = false;\n        if (to > from)\n            this.nodesBetween(from, to, node => {\n                if (type.isInSet(node.marks))\n                    found = true;\n                return !found;\n            });\n        return found;\n    }\n    /**\n    True when this is a block (non-inline node)\n    */\n    get isBlock() { return this.type.isBlock; }\n    /**\n    True when this is a textblock node, a block node with inline\n    content.\n    */\n    get isTextblock() { return this.type.isTextblock; }\n    /**\n    True when this node allows inline content.\n    */\n    get inlineContent() { return this.type.inlineContent; }\n    /**\n    True when this is an inline node (a text node or a node that can\n    appear among text).\n    */\n    get isInline() { return this.type.isInline; }\n    /**\n    True when this is a text node.\n    */\n    get isText() { return this.type.isText; }\n    /**\n    True when this is a leaf node.\n    */\n    get isLeaf() { return this.type.isLeaf; }\n    /**\n    True when this is an atom, i.e. when it does not have directly\n    editable content. This is usually the same as `isLeaf`, but can\n    be configured with the [`atom` property](https://prosemirror.net/docs/ref/#model.NodeSpec.atom)\n    on a node's spec (typically used when the node is displayed as\n    an uneditable [node view](https://prosemirror.net/docs/ref/#view.NodeView)).\n    */\n    get isAtom() { return this.type.isAtom; }\n    /**\n    Return a string representation of this node for debugging\n    purposes.\n    */\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        let name = this.type.name;\n        if (this.content.size)\n            name += \"(\" + this.content.toStringInner() + \")\";\n        return wrapMarks(this.marks, name);\n    }\n    /**\n    Get the content match in this node at the given index.\n    */\n    contentMatchAt(index) {\n        let match = this.type.contentMatch.matchFragment(this.content, 0, index);\n        if (!match)\n            throw new Error(\"Called contentMatchAt on a node with invalid content\");\n        return match;\n    }\n    /**\n    Test whether replacing the range between `from` and `to` (by\n    child index) with the given replacement fragment (which defaults\n    to the empty fragment) would leave the node's content valid. You\n    can optionally pass `start` and `end` indices into the\n    replacement fragment.\n    */\n    canReplace(from, to, replacement = Fragment.empty, start = 0, end = replacement.childCount) {\n        let one = this.contentMatchAt(from).matchFragment(replacement, start, end);\n        let two = one && one.matchFragment(this.content, to);\n        if (!two || !two.validEnd)\n            return false;\n        for (let i = start; i < end; i++)\n            if (!this.type.allowsMarks(replacement.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Test whether replacing the range `from` to `to` (by index) with\n    a node of the given type would leave the node's content valid.\n    */\n    canReplaceWith(from, to, type, marks) {\n        if (marks && !this.type.allowsMarks(marks))\n            return false;\n        let start = this.contentMatchAt(from).matchType(type);\n        let end = start && start.matchFragment(this.content, to);\n        return end ? end.validEnd : false;\n    }\n    /**\n    Test whether the given node's content could be appended to this\n    node. If that node is empty, this will only return true if there\n    is at least one node type that can appear in both nodes (to avoid\n    merging completely incompatible nodes).\n    */\n    canAppend(other) {\n        if (other.content.size)\n            return this.canReplace(this.childCount, this.childCount, other.content);\n        else\n            return this.type.compatibleContent(other.type);\n    }\n    /**\n    Check whether this node and its descendants conform to the\n    schema, and raise an exception when they do not.\n    */\n    check() {\n        this.type.checkContent(this.content);\n        this.type.checkAttrs(this.attrs);\n        let copy = Mark.none;\n        for (let i = 0; i < this.marks.length; i++) {\n            let mark = this.marks[i];\n            mark.type.checkAttrs(mark.attrs);\n            copy = mark.addToSet(copy);\n        }\n        if (!Mark.sameSet(copy, this.marks))\n            throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(m => m.type.name)}`);\n        this.content.forEach(node => node.check());\n    }\n    /**\n    Return a JSON-serializeable representation of this node.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        if (this.content.size)\n            obj.content = this.content.toJSON();\n        if (this.marks.length)\n            obj.marks = this.marks.map(n => n.toJSON());\n        return obj;\n    }\n    /**\n    Deserialize a node from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Node.fromJSON\");\n        let marks = undefined;\n        if (json.marks) {\n            if (!Array.isArray(json.marks))\n                throw new RangeError(\"Invalid mark data for Node.fromJSON\");\n            marks = json.marks.map(schema.markFromJSON);\n        }\n        if (json.type == \"text\") {\n            if (typeof json.text != \"string\")\n                throw new RangeError(\"Invalid text node in JSON\");\n            return schema.text(json.text, marks);\n        }\n        let content = Fragment.fromJSON(schema, json.content);\n        let node = schema.nodeType(json.type).create(json.attrs, content, marks);\n        node.type.checkAttrs(node.attrs);\n        return node;\n    }\n}\nNode.prototype.text = undefined;\nclass TextNode extends Node {\n    /**\n    @internal\n    */\n    constructor(type, attrs, content, marks) {\n        super(type, attrs, null, marks);\n        if (!content)\n            throw new RangeError(\"Empty text nodes are not allowed\");\n        this.text = content;\n    }\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        return wrapMarks(this.marks, JSON.stringify(this.text));\n    }\n    get textContent() { return this.text; }\n    textBetween(from, to) { return this.text.slice(from, to); }\n    get nodeSize() { return this.text.length; }\n    mark(marks) {\n        return marks == this.marks ? this : new TextNode(this.type, this.attrs, this.text, marks);\n    }\n    withText(text) {\n        if (text == this.text)\n            return this;\n        return new TextNode(this.type, this.attrs, text, this.marks);\n    }\n    cut(from = 0, to = this.text.length) {\n        if (from == 0 && to == this.text.length)\n            return this;\n        return this.withText(this.text.slice(from, to));\n    }\n    eq(other) {\n        return this.sameMarkup(other) && this.text == other.text;\n    }\n    toJSON() {\n        let base = super.toJSON();\n        base.text = this.text;\n        return base;\n    }\n}\nfunction wrapMarks(marks, str) {\n    for (let i = marks.length - 1; i >= 0; i--)\n        str = marks[i].type.name + \"(\" + str + \")\";\n    return str;\n}\n\n/**\nInstances of this class represent a match state of a node type's\n[content expression](https://prosemirror.net/docs/ref/#model.NodeSpec.content), and can be used to\nfind out whether further content matches here, and whether a given\nposition is a valid end of the node.\n*/\nclass ContentMatch {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    True when this match state represents a valid end of the node.\n    */\n    validEnd) {\n        this.validEnd = validEnd;\n        /**\n        @internal\n        */\n        this.next = [];\n        /**\n        @internal\n        */\n        this.wrapCache = [];\n    }\n    /**\n    @internal\n    */\n    static parse(string, nodeTypes) {\n        let stream = new TokenStream(string, nodeTypes);\n        if (stream.next == null)\n            return ContentMatch.empty;\n        let expr = parseExpr(stream);\n        if (stream.next)\n            stream.err(\"Unexpected trailing text\");\n        let match = dfa(nfa(expr));\n        checkForDeadEnds(match, stream);\n        return match;\n    }\n    /**\n    Match a node type, returning a match after that node if\n    successful.\n    */\n    matchType(type) {\n        for (let i = 0; i < this.next.length; i++)\n            if (this.next[i].type == type)\n                return this.next[i].next;\n        return null;\n    }\n    /**\n    Try to match a fragment. Returns the resulting match when\n    successful.\n    */\n    matchFragment(frag, start = 0, end = frag.childCount) {\n        let cur = this;\n        for (let i = start; cur && i < end; i++)\n            cur = cur.matchType(frag.child(i).type);\n        return cur;\n    }\n    /**\n    @internal\n    */\n    get inlineContent() {\n        return this.next.length != 0 && this.next[0].type.isInline;\n    }\n    /**\n    Get the first matching node type at this match position that can\n    be generated.\n    */\n    get defaultType() {\n        for (let i = 0; i < this.next.length; i++) {\n            let { type } = this.next[i];\n            if (!(type.isText || type.hasRequiredAttrs()))\n                return type;\n        }\n        return null;\n    }\n    /**\n    @internal\n    */\n    compatible(other) {\n        for (let i = 0; i < this.next.length; i++)\n            for (let j = 0; j < other.next.length; j++)\n                if (this.next[i].type == other.next[j].type)\n                    return true;\n        return false;\n    }\n    /**\n    Try to match the given fragment, and if that fails, see if it can\n    be made to match by inserting nodes in front of it. When\n    successful, return a fragment of inserted nodes (which may be\n    empty if nothing had to be inserted). When `toEnd` is true, only\n    return a fragment if the resulting match goes to the end of the\n    content expression.\n    */\n    fillBefore(after, toEnd = false, startIndex = 0) {\n        let seen = [this];\n        function search(match, types) {\n            let finished = match.matchFragment(after, startIndex);\n            if (finished && (!toEnd || finished.validEnd))\n                return Fragment.from(types.map(tp => tp.createAndFill()));\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!(type.isText || type.hasRequiredAttrs()) && seen.indexOf(next) == -1) {\n                    seen.push(next);\n                    let found = search(next, types.concat(type));\n                    if (found)\n                        return found;\n                }\n            }\n            return null;\n        }\n        return search(this, []);\n    }\n    /**\n    Find a set of wrapping node types that would allow a node of the\n    given type to appear at this position. The result may be empty\n    (when it fits directly) and will be null when no such wrapping\n    exists.\n    */\n    findWrapping(target) {\n        for (let i = 0; i < this.wrapCache.length; i += 2)\n            if (this.wrapCache[i] == target)\n                return this.wrapCache[i + 1];\n        let computed = this.computeWrapping(target);\n        this.wrapCache.push(target, computed);\n        return computed;\n    }\n    /**\n    @internal\n    */\n    computeWrapping(target) {\n        let seen = Object.create(null), active = [{ match: this, type: null, via: null }];\n        while (active.length) {\n            let current = active.shift(), match = current.match;\n            if (match.matchType(target)) {\n                let result = [];\n                for (let obj = current; obj.type; obj = obj.via)\n                    result.push(obj.type);\n                return result.reverse();\n            }\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!type.isLeaf && !type.hasRequiredAttrs() && !(type.name in seen) && (!current.type || next.validEnd)) {\n                    active.push({ match: type.contentMatch, type, via: current });\n                    seen[type.name] = true;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n    The number of outgoing edges this node has in the finite\n    automaton that describes the content expression.\n    */\n    get edgeCount() {\n        return this.next.length;\n    }\n    /**\n    Get the _n_​th outgoing edge from this node in the finite\n    automaton that describes the content expression.\n    */\n    edge(n) {\n        if (n >= this.next.length)\n            throw new RangeError(`There's no ${n}th edge in this content match`);\n        return this.next[n];\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let seen = [];\n        function scan(m) {\n            seen.push(m);\n            for (let i = 0; i < m.next.length; i++)\n                if (seen.indexOf(m.next[i].next) == -1)\n                    scan(m.next[i].next);\n        }\n        scan(this);\n        return seen.map((m, i) => {\n            let out = i + (m.validEnd ? \"*\" : \" \") + \" \";\n            for (let i = 0; i < m.next.length; i++)\n                out += (i ? \", \" : \"\") + m.next[i].type.name + \"->\" + seen.indexOf(m.next[i].next);\n            return out;\n        }).join(\"\\n\");\n    }\n}\n/**\n@internal\n*/\nContentMatch.empty = new ContentMatch(true);\nclass TokenStream {\n    constructor(string, nodeTypes) {\n        this.string = string;\n        this.nodeTypes = nodeTypes;\n        this.inline = null;\n        this.pos = 0;\n        this.tokens = string.split(/\\s*(?=\\b|\\W|$)/);\n        if (this.tokens[this.tokens.length - 1] == \"\")\n            this.tokens.pop();\n        if (this.tokens[0] == \"\")\n            this.tokens.shift();\n    }\n    get next() { return this.tokens[this.pos]; }\n    eat(tok) { return this.next == tok && (this.pos++ || true); }\n    err(str) { throw new SyntaxError(str + \" (in content expression '\" + this.string + \"')\"); }\n}\nfunction parseExpr(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSeq(stream));\n    } while (stream.eat(\"|\"));\n    return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n}\nfunction parseExprSeq(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSubscript(stream));\n    } while (stream.next && stream.next != \")\" && stream.next != \"|\");\n    return exprs.length == 1 ? exprs[0] : { type: \"seq\", exprs };\n}\nfunction parseExprSubscript(stream) {\n    let expr = parseExprAtom(stream);\n    for (;;) {\n        if (stream.eat(\"+\"))\n            expr = { type: \"plus\", expr };\n        else if (stream.eat(\"*\"))\n            expr = { type: \"star\", expr };\n        else if (stream.eat(\"?\"))\n            expr = { type: \"opt\", expr };\n        else if (stream.eat(\"{\"))\n            expr = parseExprRange(stream, expr);\n        else\n            break;\n    }\n    return expr;\n}\nfunction parseNum(stream) {\n    if (/\\D/.test(stream.next))\n        stream.err(\"Expected number, got '\" + stream.next + \"'\");\n    let result = Number(stream.next);\n    stream.pos++;\n    return result;\n}\nfunction parseExprRange(stream, expr) {\n    let min = parseNum(stream), max = min;\n    if (stream.eat(\",\")) {\n        if (stream.next != \"}\")\n            max = parseNum(stream);\n        else\n            max = -1;\n    }\n    if (!stream.eat(\"}\"))\n        stream.err(\"Unclosed braced range\");\n    return { type: \"range\", min, max, expr };\n}\nfunction resolveName(stream, name) {\n    let types = stream.nodeTypes, type = types[name];\n    if (type)\n        return [type];\n    let result = [];\n    for (let typeName in types) {\n        let type = types[typeName];\n        if (type.isInGroup(name))\n            result.push(type);\n    }\n    if (result.length == 0)\n        stream.err(\"No node type or group '\" + name + \"' found\");\n    return result;\n}\nfunction parseExprAtom(stream) {\n    if (stream.eat(\"(\")) {\n        let expr = parseExpr(stream);\n        if (!stream.eat(\")\"))\n            stream.err(\"Missing closing paren\");\n        return expr;\n    }\n    else if (!/\\W/.test(stream.next)) {\n        let exprs = resolveName(stream, stream.next).map(type => {\n            if (stream.inline == null)\n                stream.inline = type.isInline;\n            else if (stream.inline != type.isInline)\n                stream.err(\"Mixing inline and block content\");\n            return { type: \"name\", value: type };\n        });\n        stream.pos++;\n        return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n    }\n    else {\n        stream.err(\"Unexpected token '\" + stream.next + \"'\");\n    }\n}\n// Construct an NFA from an expression as returned by the parser. The\n// NFA is represented as an array of states, which are themselves\n// arrays of edges, which are `{term, to}` objects. The first state is\n// the entry state and the last node is the success state.\n//\n// Note that unlike typical NFAs, the edge ordering in this one is\n// significant, in that it is used to contruct filler content when\n// necessary.\nfunction nfa(expr) {\n    let nfa = [[]];\n    connect(compile(expr, 0), node());\n    return nfa;\n    function node() { return nfa.push([]) - 1; }\n    function edge(from, to, term) {\n        let edge = { term, to };\n        nfa[from].push(edge);\n        return edge;\n    }\n    function connect(edges, to) {\n        edges.forEach(edge => edge.to = to);\n    }\n    function compile(expr, from) {\n        if (expr.type == \"choice\") {\n            return expr.exprs.reduce((out, expr) => out.concat(compile(expr, from)), []);\n        }\n        else if (expr.type == \"seq\") {\n            for (let i = 0;; i++) {\n                let next = compile(expr.exprs[i], from);\n                if (i == expr.exprs.length - 1)\n                    return next;\n                connect(next, from = node());\n            }\n        }\n        else if (expr.type == \"star\") {\n            let loop = node();\n            edge(from, loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"plus\") {\n            let loop = node();\n            connect(compile(expr.expr, from), loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"opt\") {\n            return [edge(from)].concat(compile(expr.expr, from));\n        }\n        else if (expr.type == \"range\") {\n            let cur = from;\n            for (let i = 0; i < expr.min; i++) {\n                let next = node();\n                connect(compile(expr.expr, cur), next);\n                cur = next;\n            }\n            if (expr.max == -1) {\n                connect(compile(expr.expr, cur), cur);\n            }\n            else {\n                for (let i = expr.min; i < expr.max; i++) {\n                    let next = node();\n                    edge(cur, next);\n                    connect(compile(expr.expr, cur), next);\n                    cur = next;\n                }\n            }\n            return [edge(cur)];\n        }\n        else if (expr.type == \"name\") {\n            return [edge(from, undefined, expr.value)];\n        }\n        else {\n            throw new Error(\"Unknown expr type\");\n        }\n    }\n}\nfunction cmp(a, b) { return b - a; }\n// Get the set of nodes reachable by null edges from `node`. Omit\n// nodes with only a single null-out-edge, since they may lead to\n// needless duplicated nodes.\nfunction nullFrom(nfa, node) {\n    let result = [];\n    scan(node);\n    return result.sort(cmp);\n    function scan(node) {\n        let edges = nfa[node];\n        if (edges.length == 1 && !edges[0].term)\n            return scan(edges[0].to);\n        result.push(node);\n        for (let i = 0; i < edges.length; i++) {\n            let { term, to } = edges[i];\n            if (!term && result.indexOf(to) == -1)\n                scan(to);\n        }\n    }\n}\n// Compiles an NFA as produced by `nfa` into a DFA, modeled as a set\n// of state objects (`ContentMatch` instances) with transitions\n// between them.\nfunction dfa(nfa) {\n    let labeled = Object.create(null);\n    return explore(nullFrom(nfa, 0));\n    function explore(states) {\n        let out = [];\n        states.forEach(node => {\n            nfa[node].forEach(({ term, to }) => {\n                if (!term)\n                    return;\n                let set;\n                for (let i = 0; i < out.length; i++)\n                    if (out[i][0] == term)\n                        set = out[i][1];\n                nullFrom(nfa, to).forEach(node => {\n                    if (!set)\n                        out.push([term, set = []]);\n                    if (set.indexOf(node) == -1)\n                        set.push(node);\n                });\n            });\n        });\n        let state = labeled[states.join(\",\")] = new ContentMatch(states.indexOf(nfa.length - 1) > -1);\n        for (let i = 0; i < out.length; i++) {\n            let states = out[i][1].sort(cmp);\n            state.next.push({ type: out[i][0], next: labeled[states.join(\",\")] || explore(states) });\n        }\n        return state;\n    }\n}\nfunction checkForDeadEnds(match, stream) {\n    for (let i = 0, work = [match]; i < work.length; i++) {\n        let state = work[i], dead = !state.validEnd, nodes = [];\n        for (let j = 0; j < state.next.length; j++) {\n            let { type, next } = state.next[j];\n            nodes.push(type.name);\n            if (dead && !(type.isText || type.hasRequiredAttrs()))\n                dead = false;\n            if (work.indexOf(next) == -1)\n                work.push(next);\n        }\n        if (dead)\n            stream.err(\"Only non-generatable nodes (\" + nodes.join(\", \") + \") in a required position (see https://prosemirror.net/docs/guide/#generatable)\");\n    }\n}\n\n// For node types where all attrs have a default value (or which don't\n// have any attributes), build up a single reusable default attribute\n// object, and use it for all nodes that don't specify specific\n// attributes.\nfunction defaultAttrs(attrs) {\n    let defaults = Object.create(null);\n    for (let attrName in attrs) {\n        let attr = attrs[attrName];\n        if (!attr.hasDefault)\n            return null;\n        defaults[attrName] = attr.default;\n    }\n    return defaults;\n}\nfunction computeAttrs(attrs, value) {\n    let built = Object.create(null);\n    for (let name in attrs) {\n        let given = value && value[name];\n        if (given === undefined) {\n            let attr = attrs[name];\n            if (attr.hasDefault)\n                given = attr.default;\n            else\n                throw new RangeError(\"No value supplied for attribute \" + name);\n        }\n        built[name] = given;\n    }\n    return built;\n}\nfunction checkAttrs(attrs, values, type, name) {\n    for (let name in values)\n        if (!(name in attrs))\n            throw new RangeError(`Unsupported attribute ${name} for ${type} of type ${name}`);\n    for (let name in attrs) {\n        let attr = attrs[name];\n        if (attr.validate)\n            attr.validate(values[name]);\n    }\n}\nfunction initAttrs(typeName, attrs) {\n    let result = Object.create(null);\n    if (attrs)\n        for (let name in attrs)\n            result[name] = new Attribute(typeName, name, attrs[name]);\n    return result;\n}\n/**\nNode types are objects allocated once per `Schema` and used to\n[tag](https://prosemirror.net/docs/ref/#model.Node.type) `Node` instances. They contain information\nabout the node type, such as its name and what kind of node it\nrepresents.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name the node type has in this schema.\n    */\n    name, \n    /**\n    A link back to the `Schema` the node type belongs to.\n    */\n    schema, \n    /**\n    The spec that this type is based on\n    */\n    spec) {\n        this.name = name;\n        this.schema = schema;\n        this.spec = spec;\n        /**\n        The set of marks allowed in this node. `null` means all marks\n        are allowed.\n        */\n        this.markSet = null;\n        this.groups = spec.group ? spec.group.split(\" \") : [];\n        this.attrs = initAttrs(name, spec.attrs);\n        this.defaultAttrs = defaultAttrs(this.attrs);\n        this.contentMatch = null;\n        this.inlineContent = null;\n        this.isBlock = !(spec.inline || name == \"text\");\n        this.isText = name == \"text\";\n    }\n    /**\n    True if this is an inline type.\n    */\n    get isInline() { return !this.isBlock; }\n    /**\n    True if this is a textblock type, a block that contains inline\n    content.\n    */\n    get isTextblock() { return this.isBlock && this.inlineContent; }\n    /**\n    True for node types that allow no content.\n    */\n    get isLeaf() { return this.contentMatch == ContentMatch.empty; }\n    /**\n    True when this node is an atom, i.e. when it does not have\n    directly editable content.\n    */\n    get isAtom() { return this.isLeaf || !!this.spec.atom; }\n    /**\n    Return true when this node type is part of the given\n    [group](https://prosemirror.net/docs/ref/#model.NodeSpec.group).\n    */\n    isInGroup(group) {\n        return this.groups.indexOf(group) > -1;\n    }\n    /**\n    The node type's [whitespace](https://prosemirror.net/docs/ref/#model.NodeSpec.whitespace) option.\n    */\n    get whitespace() {\n        return this.spec.whitespace || (this.spec.code ? \"pre\" : \"normal\");\n    }\n    /**\n    Tells you whether this node type has any required attributes.\n    */\n    hasRequiredAttrs() {\n        for (let n in this.attrs)\n            if (this.attrs[n].isRequired)\n                return true;\n        return false;\n    }\n    /**\n    Indicates whether this node allows some of the same content as\n    the given node type.\n    */\n    compatibleContent(other) {\n        return this == other || this.contentMatch.compatible(other.contentMatch);\n    }\n    /**\n    @internal\n    */\n    computeAttrs(attrs) {\n        if (!attrs && this.defaultAttrs)\n            return this.defaultAttrs;\n        else\n            return computeAttrs(this.attrs, attrs);\n    }\n    /**\n    Create a `Node` of this type. The given attributes are\n    checked and defaulted (you can pass `null` to use the type's\n    defaults entirely, if no required attributes exist). `content`\n    may be a `Fragment`, a node, an array of nodes, or\n    `null`. Similarly `marks` may be `null` to default to the empty\n    set of marks.\n    */\n    create(attrs = null, content, marks) {\n        if (this.isText)\n            throw new Error(\"NodeType.create can't construct text nodes\");\n        return new Node(this, this.computeAttrs(attrs), Fragment.from(content), Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but check the given content\n    against the node type's content restrictions, and throw an error\n    if it doesn't match.\n    */\n    createChecked(attrs = null, content, marks) {\n        content = Fragment.from(content);\n        this.checkContent(content);\n        return new Node(this, this.computeAttrs(attrs), content, Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but see if it is\n    necessary to add nodes to the start or end of the given fragment\n    to make it fit the node. If no fitting wrapping can be found,\n    return null. Note that, due to the fact that required nodes can\n    always be created, this will always succeed if you pass null or\n    `Fragment.empty` as content.\n    */\n    createAndFill(attrs = null, content, marks) {\n        attrs = this.computeAttrs(attrs);\n        content = Fragment.from(content);\n        if (content.size) {\n            let before = this.contentMatch.fillBefore(content);\n            if (!before)\n                return null;\n            content = before.append(content);\n        }\n        let matched = this.contentMatch.matchFragment(content);\n        let after = matched && matched.fillBefore(Fragment.empty, true);\n        if (!after)\n            return null;\n        return new Node(this, attrs, content.append(after), Mark.setFrom(marks));\n    }\n    /**\n    Returns true if the given fragment is valid content for this node\n    type.\n    */\n    validContent(content) {\n        let result = this.contentMatch.matchFragment(content);\n        if (!result || !result.validEnd)\n            return false;\n        for (let i = 0; i < content.childCount; i++)\n            if (!this.allowsMarks(content.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Throws a RangeError if the given fragment is not valid content for this\n    node type.\n    @internal\n    */\n    checkContent(content) {\n        if (!this.validContent(content))\n            throw new RangeError(`Invalid content for node ${this.name}: ${content.toString().slice(0, 50)}`);\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"node\", this.name);\n    }\n    /**\n    Check whether the given mark type is allowed in this node.\n    */\n    allowsMarkType(markType) {\n        return this.markSet == null || this.markSet.indexOf(markType) > -1;\n    }\n    /**\n    Test whether the given set of marks are allowed in this node.\n    */\n    allowsMarks(marks) {\n        if (this.markSet == null)\n            return true;\n        for (let i = 0; i < marks.length; i++)\n            if (!this.allowsMarkType(marks[i].type))\n                return false;\n        return true;\n    }\n    /**\n    Removes the marks that are not allowed in this node from the given set.\n    */\n    allowedMarks(marks) {\n        if (this.markSet == null)\n            return marks;\n        let copy;\n        for (let i = 0; i < marks.length; i++) {\n            if (!this.allowsMarkType(marks[i].type)) {\n                if (!copy)\n                    copy = marks.slice(0, i);\n            }\n            else if (copy) {\n                copy.push(marks[i]);\n            }\n        }\n        return !copy ? marks : copy.length ? copy : Mark.none;\n    }\n    /**\n    @internal\n    */\n    static compile(nodes, schema) {\n        let result = Object.create(null);\n        nodes.forEach((name, spec) => result[name] = new NodeType(name, schema, spec));\n        let topType = schema.spec.topNode || \"doc\";\n        if (!result[topType])\n            throw new RangeError(\"Schema is missing its top node type ('\" + topType + \"')\");\n        if (!result.text)\n            throw new RangeError(\"Every schema needs a 'text' type\");\n        for (let _ in result.text.attrs)\n            throw new RangeError(\"The text node type should not have attributes\");\n        return result;\n    }\n}\nfunction validateType(typeName, attrName, type) {\n    let types = type.split(\"|\");\n    return (value) => {\n        let name = value === null ? \"null\" : typeof value;\n        if (types.indexOf(name) < 0)\n            throw new RangeError(`Expected value of type ${types} for attribute ${attrName} on type ${typeName}, got ${name}`);\n    };\n}\n// Attribute descriptors\nclass Attribute {\n    constructor(typeName, attrName, options) {\n        this.hasDefault = Object.prototype.hasOwnProperty.call(options, \"default\");\n        this.default = options.default;\n        this.validate = typeof options.validate == \"string\" ? validateType(typeName, attrName, options.validate) : options.validate;\n    }\n    get isRequired() {\n        return !this.hasDefault;\n    }\n}\n// Marks\n/**\nLike nodes, marks (which are associated with nodes to signify\nthings like emphasis or being part of a link) are\n[tagged](https://prosemirror.net/docs/ref/#model.Mark.type) with type objects, which are\ninstantiated once per `Schema`.\n*/\nclass MarkType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the mark type.\n    */\n    name, \n    /**\n    @internal\n    */\n    rank, \n    /**\n    The schema that this mark type instance is part of.\n    */\n    schema, \n    /**\n    The spec on which the type is based.\n    */\n    spec) {\n        this.name = name;\n        this.rank = rank;\n        this.schema = schema;\n        this.spec = spec;\n        this.attrs = initAttrs(name, spec.attrs);\n        this.excluded = null;\n        let defaults = defaultAttrs(this.attrs);\n        this.instance = defaults ? new Mark(this, defaults) : null;\n    }\n    /**\n    Create a mark of this type. `attrs` may be `null` or an object\n    containing only some of the mark's attributes. The others, if\n    they have defaults, will be added.\n    */\n    create(attrs = null) {\n        if (!attrs && this.instance)\n            return this.instance;\n        return new Mark(this, computeAttrs(this.attrs, attrs));\n    }\n    /**\n    @internal\n    */\n    static compile(marks, schema) {\n        let result = Object.create(null), rank = 0;\n        marks.forEach((name, spec) => result[name] = new MarkType(name, rank++, schema, spec));\n        return result;\n    }\n    /**\n    When there is a mark of this type in the given set, a new set\n    without it is returned. Otherwise, the input set is returned.\n    */\n    removeFromSet(set) {\n        for (var i = 0; i < set.length; i++)\n            if (set[i].type == this) {\n                set = set.slice(0, i).concat(set.slice(i + 1));\n                i--;\n            }\n        return set;\n    }\n    /**\n    Tests whether there is a mark of this type in the given set.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (set[i].type == this)\n                return set[i];\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"mark\", this.name);\n    }\n    /**\n    Queries whether a given mark type is\n    [excluded](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) by this one.\n    */\n    excludes(other) {\n        return this.excluded.indexOf(other) > -1;\n    }\n}\n/**\nA document schema. Holds [node](https://prosemirror.net/docs/ref/#model.NodeType) and [mark\ntype](https://prosemirror.net/docs/ref/#model.MarkType) objects for the nodes and marks that may\noccur in conforming documents, and provides functionality for\ncreating and deserializing such documents.\n\nWhen given, the type parameters provide the names of the nodes and\nmarks in this schema.\n*/\nclass Schema {\n    /**\n    Construct a schema from a schema [specification](https://prosemirror.net/docs/ref/#model.SchemaSpec).\n    */\n    constructor(spec) {\n        /**\n        The [linebreak\n        replacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement) node defined\n        in this schema, if any.\n        */\n        this.linebreakReplacement = null;\n        /**\n        An object for storing whatever values modules may want to\n        compute and cache per schema. (If you want to store something\n        in it, try to use property names unlikely to clash.)\n        */\n        this.cached = Object.create(null);\n        let instanceSpec = this.spec = {};\n        for (let prop in spec)\n            instanceSpec[prop] = spec[prop];\n        instanceSpec.nodes = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.nodes),\n            instanceSpec.marks = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.marks || {}),\n            this.nodes = NodeType.compile(this.spec.nodes, this);\n        this.marks = MarkType.compile(this.spec.marks, this);\n        let contentExprCache = Object.create(null);\n        for (let prop in this.nodes) {\n            if (prop in this.marks)\n                throw new RangeError(prop + \" can not be both a node and a mark\");\n            let type = this.nodes[prop], contentExpr = type.spec.content || \"\", markExpr = type.spec.marks;\n            type.contentMatch = contentExprCache[contentExpr] ||\n                (contentExprCache[contentExpr] = ContentMatch.parse(contentExpr, this.nodes));\n            type.inlineContent = type.contentMatch.inlineContent;\n            if (type.spec.linebreakReplacement) {\n                if (this.linebreakReplacement)\n                    throw new RangeError(\"Multiple linebreak nodes defined\");\n                if (!type.isInline || !type.isLeaf)\n                    throw new RangeError(\"Linebreak replacement nodes must be inline leaf nodes\");\n                this.linebreakReplacement = type;\n            }\n            type.markSet = markExpr == \"_\" ? null :\n                markExpr ? gatherMarks(this, markExpr.split(\" \")) :\n                    markExpr == \"\" || !type.inlineContent ? [] : null;\n        }\n        for (let prop in this.marks) {\n            let type = this.marks[prop], excl = type.spec.excludes;\n            type.excluded = excl == null ? [type] : excl == \"\" ? [] : gatherMarks(this, excl.split(\" \"));\n        }\n        this.nodeFromJSON = this.nodeFromJSON.bind(this);\n        this.markFromJSON = this.markFromJSON.bind(this);\n        this.topNodeType = this.nodes[this.spec.topNode || \"doc\"];\n        this.cached.wrappings = Object.create(null);\n    }\n    /**\n    Create a node in this schema. The `type` may be a string or a\n    `NodeType` instance. Attributes will be extended with defaults,\n    `content` may be a `Fragment`, `null`, a `Node`, or an array of\n    nodes.\n    */\n    node(type, attrs = null, content, marks) {\n        if (typeof type == \"string\")\n            type = this.nodeType(type);\n        else if (!(type instanceof NodeType))\n            throw new RangeError(\"Invalid node type: \" + type);\n        else if (type.schema != this)\n            throw new RangeError(\"Node type from different schema used (\" + type.name + \")\");\n        return type.createChecked(attrs, content, marks);\n    }\n    /**\n    Create a text node in the schema. Empty text nodes are not\n    allowed.\n    */\n    text(text, marks) {\n        let type = this.nodes.text;\n        return new TextNode(type, type.defaultAttrs, text, Mark.setFrom(marks));\n    }\n    /**\n    Create a mark with the given type and attributes.\n    */\n    mark(type, attrs) {\n        if (typeof type == \"string\")\n            type = this.marks[type];\n        return type.create(attrs);\n    }\n    /**\n    Deserialize a node from its JSON representation. This method is\n    bound.\n    */\n    nodeFromJSON(json) {\n        return Node.fromJSON(this, json);\n    }\n    /**\n    Deserialize a mark from its JSON representation. This method is\n    bound.\n    */\n    markFromJSON(json) {\n        return Mark.fromJSON(this, json);\n    }\n    /**\n    @internal\n    */\n    nodeType(name) {\n        let found = this.nodes[name];\n        if (!found)\n            throw new RangeError(\"Unknown node type: \" + name);\n        return found;\n    }\n}\nfunction gatherMarks(schema, marks) {\n    let found = [];\n    for (let i = 0; i < marks.length; i++) {\n        let name = marks[i], mark = schema.marks[name], ok = mark;\n        if (mark) {\n            found.push(mark);\n        }\n        else {\n            for (let prop in schema.marks) {\n                let mark = schema.marks[prop];\n                if (name == \"_\" || (mark.spec.group && mark.spec.group.split(\" \").indexOf(name) > -1))\n                    found.push(ok = mark);\n            }\n        }\n        if (!ok)\n            throw new SyntaxError(\"Unknown mark type: '\" + marks[i] + \"'\");\n    }\n    return found;\n}\n\nfunction isTagRule(rule) { return rule.tag != null; }\nfunction isStyleRule(rule) { return rule.style != null; }\n/**\nA DOM parser represents a strategy for parsing DOM content into a\nProseMirror document conforming to a given schema. Its behavior is\ndefined by an array of [rules](https://prosemirror.net/docs/ref/#model.ParseRule).\n*/\nclass DOMParser {\n    /**\n    Create a parser that targets the given schema, using the given\n    parsing rules.\n    */\n    constructor(\n    /**\n    The schema into which the parser parses.\n    */\n    schema, \n    /**\n    The set of [parse rules](https://prosemirror.net/docs/ref/#model.ParseRule) that the parser\n    uses, in order of precedence.\n    */\n    rules) {\n        this.schema = schema;\n        this.rules = rules;\n        /**\n        @internal\n        */\n        this.tags = [];\n        /**\n        @internal\n        */\n        this.styles = [];\n        let matchedStyles = this.matchedStyles = [];\n        rules.forEach(rule => {\n            if (isTagRule(rule)) {\n                this.tags.push(rule);\n            }\n            else if (isStyleRule(rule)) {\n                let prop = /[^=]*/.exec(rule.style)[0];\n                if (matchedStyles.indexOf(prop) < 0)\n                    matchedStyles.push(prop);\n                this.styles.push(rule);\n            }\n        });\n        // Only normalize list elements when lists in the schema can't directly contain themselves\n        this.normalizeLists = !this.tags.some(r => {\n            if (!/^(ul|ol)\\b/.test(r.tag) || !r.node)\n                return false;\n            let node = schema.nodes[r.node];\n            return node.contentMatch.matchType(node);\n        });\n    }\n    /**\n    Parse a document from the content of a DOM node.\n    */\n    parse(dom, options = {}) {\n        let context = new ParseContext(this, options, false);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return context.finish();\n    }\n    /**\n    Parses the content of the given DOM node, like\n    [`parse`](https://prosemirror.net/docs/ref/#model.DOMParser.parse), and takes the same set of\n    options. But unlike that method, which produces a whole node,\n    this one returns a slice that is open at the sides, meaning that\n    the schema constraints aren't applied to the start of nodes to\n    the left of the input and the end of nodes at the end.\n    */\n    parseSlice(dom, options = {}) {\n        let context = new ParseContext(this, options, true);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return Slice.maxOpen(context.finish());\n    }\n    /**\n    @internal\n    */\n    matchTag(dom, context, after) {\n        for (let i = after ? this.tags.indexOf(after) + 1 : 0; i < this.tags.length; i++) {\n            let rule = this.tags[i];\n            if (matches(dom, rule.tag) &&\n                (rule.namespace === undefined || dom.namespaceURI == rule.namespace) &&\n                (!rule.context || context.matchesContext(rule.context))) {\n                if (rule.getAttrs) {\n                    let result = rule.getAttrs(dom);\n                    if (result === false)\n                        continue;\n                    rule.attrs = result || undefined;\n                }\n                return rule;\n            }\n        }\n    }\n    /**\n    @internal\n    */\n    matchStyle(prop, value, context, after) {\n        for (let i = after ? this.styles.indexOf(after) + 1 : 0; i < this.styles.length; i++) {\n            let rule = this.styles[i], style = rule.style;\n            if (style.indexOf(prop) != 0 ||\n                rule.context && !context.matchesContext(rule.context) ||\n                // Test that the style string either precisely matches the prop,\n                // or has an '=' sign after the prop, followed by the given\n                // value.\n                style.length > prop.length &&\n                    (style.charCodeAt(prop.length) != 61 || style.slice(prop.length + 1) != value))\n                continue;\n            if (rule.getAttrs) {\n                let result = rule.getAttrs(value);\n                if (result === false)\n                    continue;\n                rule.attrs = result || undefined;\n            }\n            return rule;\n        }\n    }\n    /**\n    @internal\n    */\n    static schemaRules(schema) {\n        let result = [];\n        function insert(rule) {\n            let priority = rule.priority == null ? 50 : rule.priority, i = 0;\n            for (; i < result.length; i++) {\n                let next = result[i], nextPriority = next.priority == null ? 50 : next.priority;\n                if (nextPriority < priority)\n                    break;\n            }\n            result.splice(i, 0, rule);\n        }\n        for (let name in schema.marks) {\n            let rules = schema.marks[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.mark || rule.ignore || rule.clearMark))\n                        rule.mark = name;\n                });\n        }\n        for (let name in schema.nodes) {\n            let rules = schema.nodes[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.node || rule.ignore || rule.mark))\n                        rule.node = name;\n                });\n        }\n        return result;\n    }\n    /**\n    Construct a DOM parser using the parsing rules listed in a\n    schema's [node specs](https://prosemirror.net/docs/ref/#model.NodeSpec.parseDOM), reordered by\n    [priority](https://prosemirror.net/docs/ref/#model.ParseRule.priority).\n    */\n    static fromSchema(schema) {\n        return schema.cached.domParser ||\n            (schema.cached.domParser = new DOMParser(schema, DOMParser.schemaRules(schema)));\n    }\n}\nconst blockTags = {\n    address: true, article: true, aside: true, blockquote: true, canvas: true,\n    dd: true, div: true, dl: true, fieldset: true, figcaption: true, figure: true,\n    footer: true, form: true, h1: true, h2: true, h3: true, h4: true, h5: true,\n    h6: true, header: true, hgroup: true, hr: true, li: true, noscript: true, ol: true,\n    output: true, p: true, pre: true, section: true, table: true, tfoot: true, ul: true\n};\nconst ignoreTags = {\n    head: true, noscript: true, object: true, script: true, style: true, title: true\n};\nconst listTags = { ol: true, ul: true };\n// Using a bitfield for node context options\nconst OPT_PRESERVE_WS = 1, OPT_PRESERVE_WS_FULL = 2, OPT_OPEN_LEFT = 4;\nfunction wsOptionsFor(type, preserveWhitespace, base) {\n    if (preserveWhitespace != null)\n        return (preserveWhitespace ? OPT_PRESERVE_WS : 0) |\n            (preserveWhitespace === \"full\" ? OPT_PRESERVE_WS_FULL : 0);\n    return type && type.whitespace == \"pre\" ? OPT_PRESERVE_WS | OPT_PRESERVE_WS_FULL : base & ~OPT_OPEN_LEFT;\n}\nclass NodeContext {\n    constructor(type, attrs, marks, solid, match, options) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.solid = solid;\n        this.options = options;\n        this.content = [];\n        // Marks applied to the node's children\n        this.activeMarks = Mark.none;\n        this.match = match || (options & OPT_OPEN_LEFT ? null : type.contentMatch);\n    }\n    findWrapping(node) {\n        if (!this.match) {\n            if (!this.type)\n                return [];\n            let fill = this.type.contentMatch.fillBefore(Fragment.from(node));\n            if (fill) {\n                this.match = this.type.contentMatch.matchFragment(fill);\n            }\n            else {\n                let start = this.type.contentMatch, wrap;\n                if (wrap = start.findWrapping(node.type)) {\n                    this.match = start;\n                    return wrap;\n                }\n                else {\n                    return null;\n                }\n            }\n        }\n        return this.match.findWrapping(node.type);\n    }\n    finish(openEnd) {\n        if (!(this.options & OPT_PRESERVE_WS)) { // Strip trailing whitespace\n            let last = this.content[this.content.length - 1], m;\n            if (last && last.isText && (m = /[ \\t\\r\\n\\u000c]+$/.exec(last.text))) {\n                let text = last;\n                if (last.text.length == m[0].length)\n                    this.content.pop();\n                else\n                    this.content[this.content.length - 1] = text.withText(text.text.slice(0, text.text.length - m[0].length));\n            }\n        }\n        let content = Fragment.from(this.content);\n        if (!openEnd && this.match)\n            content = content.append(this.match.fillBefore(Fragment.empty, true));\n        return this.type ? this.type.create(this.attrs, content, this.marks) : content;\n    }\n    inlineContext(node) {\n        if (this.type)\n            return this.type.inlineContent;\n        if (this.content.length)\n            return this.content[0].isInline;\n        return node.parentNode && !blockTags.hasOwnProperty(node.parentNode.nodeName.toLowerCase());\n    }\n}\nclass ParseContext {\n    constructor(\n    // The parser we are using.\n    parser, \n    // The options passed to this parse.\n    options, isOpen) {\n        this.parser = parser;\n        this.options = options;\n        this.isOpen = isOpen;\n        this.open = 0;\n        this.localPreserveWS = false;\n        let topNode = options.topNode, topContext;\n        let topOptions = wsOptionsFor(null, options.preserveWhitespace, 0) | (isOpen ? OPT_OPEN_LEFT : 0);\n        if (topNode)\n            topContext = new NodeContext(topNode.type, topNode.attrs, Mark.none, true, options.topMatch || topNode.type.contentMatch, topOptions);\n        else if (isOpen)\n            topContext = new NodeContext(null, null, Mark.none, true, null, topOptions);\n        else\n            topContext = new NodeContext(parser.schema.topNodeType, null, Mark.none, true, null, topOptions);\n        this.nodes = [topContext];\n        this.find = options.findPositions;\n        this.needsBlock = false;\n    }\n    get top() {\n        return this.nodes[this.open];\n    }\n    // Add a DOM node to the content. Text is inserted as text node,\n    // otherwise, the node is passed to `addElement` or, if it has a\n    // `style` attribute, `addElementWithStyles`.\n    addDOM(dom, marks) {\n        if (dom.nodeType == 3)\n            this.addTextNode(dom, marks);\n        else if (dom.nodeType == 1)\n            this.addElement(dom, marks);\n    }\n    addTextNode(dom, marks) {\n        let value = dom.nodeValue;\n        let top = this.top, preserveWS = (top.options & OPT_PRESERVE_WS_FULL) ? \"full\"\n            : this.localPreserveWS || (top.options & OPT_PRESERVE_WS) > 0;\n        if (preserveWS === \"full\" ||\n            top.inlineContext(dom) ||\n            /[^ \\t\\r\\n\\u000c]/.test(value)) {\n            if (!preserveWS) {\n                value = value.replace(/[ \\t\\r\\n\\u000c]+/g, \" \");\n                // If this starts with whitespace, and there is no node before it, or\n                // a hard break, or a text node that ends with whitespace, strip the\n                // leading space.\n                if (/^[ \\t\\r\\n\\u000c]/.test(value) && this.open == this.nodes.length - 1) {\n                    let nodeBefore = top.content[top.content.length - 1];\n                    let domNodeBefore = dom.previousSibling;\n                    if (!nodeBefore ||\n                        (domNodeBefore && domNodeBefore.nodeName == 'BR') ||\n                        (nodeBefore.isText && /[ \\t\\r\\n\\u000c]$/.test(nodeBefore.text)))\n                        value = value.slice(1);\n                }\n            }\n            else if (preserveWS !== \"full\") {\n                value = value.replace(/\\r?\\n|\\r/g, \" \");\n            }\n            else {\n                value = value.replace(/\\r\\n?/g, \"\\n\");\n            }\n            if (value)\n                this.insertNode(this.parser.schema.text(value), marks, !/\\S/.test(value));\n            this.findInText(dom);\n        }\n        else {\n            this.findInside(dom);\n        }\n    }\n    // Try to find a handler for the given tag and use that to parse. If\n    // none is found, the element's content nodes are added directly.\n    addElement(dom, marks, matchAfter) {\n        let outerWS = this.localPreserveWS, top = this.top;\n        if (dom.tagName == \"PRE\" || /pre/.test(dom.style && dom.style.whiteSpace))\n            this.localPreserveWS = true;\n        let name = dom.nodeName.toLowerCase(), ruleID;\n        if (listTags.hasOwnProperty(name) && this.parser.normalizeLists)\n            normalizeList(dom);\n        let rule = (this.options.ruleFromNode && this.options.ruleFromNode(dom)) ||\n            (ruleID = this.parser.matchTag(dom, this, matchAfter));\n        out: if (rule ? rule.ignore : ignoreTags.hasOwnProperty(name)) {\n            this.findInside(dom);\n            this.ignoreFallback(dom, marks);\n        }\n        else if (!rule || rule.skip || rule.closeParent) {\n            if (rule && rule.closeParent)\n                this.open = Math.max(0, this.open - 1);\n            else if (rule && rule.skip.nodeType)\n                dom = rule.skip;\n            let sync, oldNeedsBlock = this.needsBlock;\n            if (blockTags.hasOwnProperty(name)) {\n                if (top.content.length && top.content[0].isInline && this.open) {\n                    this.open--;\n                    top = this.top;\n                }\n                sync = true;\n                if (!top.type)\n                    this.needsBlock = true;\n            }\n            else if (!dom.firstChild) {\n                this.leafFallback(dom, marks);\n                break out;\n            }\n            let innerMarks = rule && rule.skip ? marks : this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addAll(dom, innerMarks);\n            if (sync)\n                this.sync(top);\n            this.needsBlock = oldNeedsBlock;\n        }\n        else {\n            let innerMarks = this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addElementByRule(dom, rule, innerMarks, rule.consuming === false ? ruleID : undefined);\n        }\n        this.localPreserveWS = outerWS;\n    }\n    // Called for leaf DOM nodes that would otherwise be ignored\n    leafFallback(dom, marks) {\n        if (dom.nodeName == \"BR\" && this.top.type && this.top.type.inlineContent)\n            this.addTextNode(dom.ownerDocument.createTextNode(\"\\n\"), marks);\n    }\n    // Called for ignored nodes\n    ignoreFallback(dom, marks) {\n        // Ignored BR nodes should at least create an inline context\n        if (dom.nodeName == \"BR\" && (!this.top.type || !this.top.type.inlineContent))\n            this.findPlace(this.parser.schema.text(\"-\"), marks, true);\n    }\n    // Run any style parser associated with the node's styles. Either\n    // return an updated array of marks, or null to indicate some of the\n    // styles had a rule with `ignore` set.\n    readStyles(dom, marks) {\n        let styles = dom.style;\n        // Because many properties will only show up in 'normalized' form\n        // in `style.item` (i.e. text-decoration becomes\n        // text-decoration-line, text-decoration-color, etc), we directly\n        // query the styles mentioned in our rules instead of iterating\n        // over the items.\n        if (styles && styles.length)\n            for (let i = 0; i < this.parser.matchedStyles.length; i++) {\n                let name = this.parser.matchedStyles[i], value = styles.getPropertyValue(name);\n                if (value)\n                    for (let after = undefined;;) {\n                        let rule = this.parser.matchStyle(name, value, this, after);\n                        if (!rule)\n                            break;\n                        if (rule.ignore)\n                            return null;\n                        if (rule.clearMark)\n                            marks = marks.filter(m => !rule.clearMark(m));\n                        else\n                            marks = marks.concat(this.parser.schema.marks[rule.mark].create(rule.attrs));\n                        if (rule.consuming === false)\n                            after = rule;\n                        else\n                            break;\n                    }\n            }\n        return marks;\n    }\n    // Look up a handler for the given node. If none are found, return\n    // false. Otherwise, apply it, use its return value to drive the way\n    // the node's content is wrapped, and return true.\n    addElementByRule(dom, rule, marks, continueAfter) {\n        let sync, nodeType;\n        if (rule.node) {\n            nodeType = this.parser.schema.nodes[rule.node];\n            if (!nodeType.isLeaf) {\n                let inner = this.enter(nodeType, rule.attrs || null, marks, rule.preserveWhitespace);\n                if (inner) {\n                    sync = true;\n                    marks = inner;\n                }\n            }\n            else if (!this.insertNode(nodeType.create(rule.attrs), marks, dom.nodeName == \"BR\")) {\n                this.leafFallback(dom, marks);\n            }\n        }\n        else {\n            let markType = this.parser.schema.marks[rule.mark];\n            marks = marks.concat(markType.create(rule.attrs));\n        }\n        let startIn = this.top;\n        if (nodeType && nodeType.isLeaf) {\n            this.findInside(dom);\n        }\n        else if (continueAfter) {\n            this.addElement(dom, marks, continueAfter);\n        }\n        else if (rule.getContent) {\n            this.findInside(dom);\n            rule.getContent(dom, this.parser.schema).forEach(node => this.insertNode(node, marks, false));\n        }\n        else {\n            let contentDOM = dom;\n            if (typeof rule.contentElement == \"string\")\n                contentDOM = dom.querySelector(rule.contentElement);\n            else if (typeof rule.contentElement == \"function\")\n                contentDOM = rule.contentElement(dom);\n            else if (rule.contentElement)\n                contentDOM = rule.contentElement;\n            this.findAround(dom, contentDOM, true);\n            this.addAll(contentDOM, marks);\n            this.findAround(dom, contentDOM, false);\n        }\n        if (sync && this.sync(startIn))\n            this.open--;\n    }\n    // Add all child nodes between `startIndex` and `endIndex` (or the\n    // whole node, if not given). If `sync` is passed, use it to\n    // synchronize after every block element.\n    addAll(parent, marks, startIndex, endIndex) {\n        let index = startIndex || 0;\n        for (let dom = startIndex ? parent.childNodes[startIndex] : parent.firstChild, end = endIndex == null ? null : parent.childNodes[endIndex]; dom != end; dom = dom.nextSibling, ++index) {\n            this.findAtPoint(parent, index);\n            this.addDOM(dom, marks);\n        }\n        this.findAtPoint(parent, index);\n    }\n    // Try to find a way to fit the given node type into the current\n    // context. May add intermediate wrappers and/or leave non-solid\n    // nodes that we're in.\n    findPlace(node, marks, cautious) {\n        let route, sync;\n        for (let depth = this.open, penalty = 0; depth >= 0; depth--) {\n            let cx = this.nodes[depth];\n            let found = cx.findWrapping(node);\n            if (found && (!route || route.length > found.length + penalty)) {\n                route = found;\n                sync = cx;\n                if (!found.length)\n                    break;\n            }\n            if (cx.solid) {\n                if (cautious)\n                    break;\n                penalty += 2;\n            }\n        }\n        if (!route)\n            return null;\n        this.sync(sync);\n        for (let i = 0; i < route.length; i++)\n            marks = this.enterInner(route[i], null, marks, false);\n        return marks;\n    }\n    // Try to insert the given node, adjusting the context when needed.\n    insertNode(node, marks, cautious) {\n        if (node.isInline && this.needsBlock && !this.top.type) {\n            let block = this.textblockFromContext();\n            if (block)\n                marks = this.enterInner(block, null, marks);\n        }\n        let innerMarks = this.findPlace(node, marks, cautious);\n        if (innerMarks) {\n            this.closeExtra();\n            let top = this.top;\n            if (top.match)\n                top.match = top.match.matchType(node.type);\n            let nodeMarks = Mark.none;\n            for (let m of innerMarks.concat(node.marks))\n                if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, node.type))\n                    nodeMarks = m.addToSet(nodeMarks);\n            top.content.push(node.mark(nodeMarks));\n            return true;\n        }\n        return false;\n    }\n    // Try to start a node of the given type, adjusting the context when\n    // necessary.\n    enter(type, attrs, marks, preserveWS) {\n        let innerMarks = this.findPlace(type.create(attrs), marks, false);\n        if (innerMarks)\n            innerMarks = this.enterInner(type, attrs, marks, true, preserveWS);\n        return innerMarks;\n    }\n    // Open a node of the given type\n    enterInner(type, attrs, marks, solid = false, preserveWS) {\n        this.closeExtra();\n        let top = this.top;\n        top.match = top.match && top.match.matchType(type);\n        let options = wsOptionsFor(type, preserveWS, top.options);\n        if ((top.options & OPT_OPEN_LEFT) && top.content.length == 0)\n            options |= OPT_OPEN_LEFT;\n        let applyMarks = Mark.none;\n        marks = marks.filter(m => {\n            if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, type)) {\n                applyMarks = m.addToSet(applyMarks);\n                return false;\n            }\n            return true;\n        });\n        this.nodes.push(new NodeContext(type, attrs, applyMarks, solid, null, options));\n        this.open++;\n        return marks;\n    }\n    // Make sure all nodes above this.open are finished and added to\n    // their parents\n    closeExtra(openEnd = false) {\n        let i = this.nodes.length - 1;\n        if (i > this.open) {\n            for (; i > this.open; i--)\n                this.nodes[i - 1].content.push(this.nodes[i].finish(openEnd));\n            this.nodes.length = this.open + 1;\n        }\n    }\n    finish() {\n        this.open = 0;\n        this.closeExtra(this.isOpen);\n        return this.nodes[0].finish(!!(this.isOpen || this.options.topOpen));\n    }\n    sync(to) {\n        for (let i = this.open; i >= 0; i--) {\n            if (this.nodes[i] == to) {\n                this.open = i;\n                return true;\n            }\n            else if (this.localPreserveWS) {\n                this.nodes[i].options |= OPT_PRESERVE_WS;\n            }\n        }\n        return false;\n    }\n    get currentPos() {\n        this.closeExtra();\n        let pos = 0;\n        for (let i = this.open; i >= 0; i--) {\n            let content = this.nodes[i].content;\n            for (let j = content.length - 1; j >= 0; j--)\n                pos += content[j].nodeSize;\n            if (i)\n                pos++;\n        }\n        return pos;\n    }\n    findAtPoint(parent, offset) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == parent && this.find[i].offset == offset)\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findInside(parent) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node))\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findAround(parent, content, before) {\n        if (parent != content && this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node)) {\n                    let pos = content.compareDocumentPosition(this.find[i].node);\n                    if (pos & (before ? 2 : 4))\n                        this.find[i].pos = this.currentPos;\n                }\n            }\n    }\n    findInText(textNode) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == textNode)\n                    this.find[i].pos = this.currentPos - (textNode.nodeValue.length - this.find[i].offset);\n            }\n    }\n    // Determines whether the given context string matches this context.\n    matchesContext(context) {\n        if (context.indexOf(\"|\") > -1)\n            return context.split(/\\s*\\|\\s*/).some(this.matchesContext, this);\n        let parts = context.split(\"/\");\n        let option = this.options.context;\n        let useRoot = !this.isOpen && (!option || option.parent.type == this.nodes[0].type);\n        let minDepth = -(option ? option.depth + 1 : 0) + (useRoot ? 0 : 1);\n        let match = (i, depth) => {\n            for (; i >= 0; i--) {\n                let part = parts[i];\n                if (part == \"\") {\n                    if (i == parts.length - 1 || i == 0)\n                        continue;\n                    for (; depth >= minDepth; depth--)\n                        if (match(i - 1, depth))\n                            return true;\n                    return false;\n                }\n                else {\n                    let next = depth > 0 || (depth == 0 && useRoot) ? this.nodes[depth].type\n                        : option && depth >= minDepth ? option.node(depth - minDepth).type\n                            : null;\n                    if (!next || (next.name != part && !next.isInGroup(part)))\n                        return false;\n                    depth--;\n                }\n            }\n            return true;\n        };\n        return match(parts.length - 1, this.open);\n    }\n    textblockFromContext() {\n        let $context = this.options.context;\n        if ($context)\n            for (let d = $context.depth; d >= 0; d--) {\n                let deflt = $context.node(d).contentMatchAt($context.indexAfter(d)).defaultType;\n                if (deflt && deflt.isTextblock && deflt.defaultAttrs)\n                    return deflt;\n            }\n        for (let name in this.parser.schema.nodes) {\n            let type = this.parser.schema.nodes[name];\n            if (type.isTextblock && type.defaultAttrs)\n                return type;\n        }\n    }\n}\n// Kludge to work around directly nested list nodes produced by some\n// tools and allowed by browsers to mean that the nested list is\n// actually part of the list item above it.\nfunction normalizeList(dom) {\n    for (let child = dom.firstChild, prevItem = null; child; child = child.nextSibling) {\n        let name = child.nodeType == 1 ? child.nodeName.toLowerCase() : null;\n        if (name && listTags.hasOwnProperty(name) && prevItem) {\n            prevItem.appendChild(child);\n            child = prevItem;\n        }\n        else if (name == \"li\") {\n            prevItem = child;\n        }\n        else if (name) {\n            prevItem = null;\n        }\n    }\n}\n// Apply a CSS selector.\nfunction matches(dom, selector) {\n    return (dom.matches || dom.msMatchesSelector || dom.webkitMatchesSelector || dom.mozMatchesSelector).call(dom, selector);\n}\nfunction copy(obj) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    return copy;\n}\n// Used when finding a mark at the top level of a fragment parse.\n// Checks whether it would be reasonable to apply a given mark type to\n// a given node, by looking at the way the mark occurs in the schema.\nfunction markMayApply(markType, nodeType) {\n    let nodes = nodeType.schema.nodes;\n    for (let name in nodes) {\n        let parent = nodes[name];\n        if (!parent.allowsMarkType(markType))\n            continue;\n        let seen = [], scan = (match) => {\n            seen.push(match);\n            for (let i = 0; i < match.edgeCount; i++) {\n                let { type, next } = match.edge(i);\n                if (type == nodeType)\n                    return true;\n                if (seen.indexOf(next) < 0 && scan(next))\n                    return true;\n            }\n        };\n        if (scan(parent.contentMatch))\n            return true;\n    }\n}\n\n/**\nA DOM serializer knows how to convert ProseMirror nodes and\nmarks of various types to DOM nodes.\n*/\nclass DOMSerializer {\n    /**\n    Create a serializer. `nodes` should map node names to functions\n    that take a node and return a description of the corresponding\n    DOM. `marks` does the same for mark names, but also gets an\n    argument that tells it whether the mark's content is block or\n    inline content (for typical use, it'll always be inline). A mark\n    serializer may be `null` to indicate that marks of that type\n    should not be serialized.\n    */\n    constructor(\n    /**\n    The node serialization functions.\n    */\n    nodes, \n    /**\n    The mark serialization functions.\n    */\n    marks) {\n        this.nodes = nodes;\n        this.marks = marks;\n    }\n    /**\n    Serialize the content of this fragment to a DOM fragment. When\n    not in the browser, the `document` option, containing a DOM\n    document, should be passed so that the serializer can create\n    nodes.\n    */\n    serializeFragment(fragment, options = {}, target) {\n        if (!target)\n            target = doc(options).createDocumentFragment();\n        let top = target, active = [];\n        fragment.forEach(node => {\n            if (active.length || node.marks.length) {\n                let keep = 0, rendered = 0;\n                while (keep < active.length && rendered < node.marks.length) {\n                    let next = node.marks[rendered];\n                    if (!this.marks[next.type.name]) {\n                        rendered++;\n                        continue;\n                    }\n                    if (!next.eq(active[keep][0]) || next.type.spec.spanning === false)\n                        break;\n                    keep++;\n                    rendered++;\n                }\n                while (keep < active.length)\n                    top = active.pop()[1];\n                while (rendered < node.marks.length) {\n                    let add = node.marks[rendered++];\n                    let markDOM = this.serializeMark(add, node.isInline, options);\n                    if (markDOM) {\n                        active.push([add, top]);\n                        top.appendChild(markDOM.dom);\n                        top = markDOM.contentDOM || markDOM.dom;\n                    }\n                }\n            }\n            top.appendChild(this.serializeNodeInner(node, options));\n        });\n        return target;\n    }\n    /**\n    @internal\n    */\n    serializeNodeInner(node, options) {\n        let { dom, contentDOM } = renderSpec(doc(options), this.nodes[node.type.name](node), null, node.attrs);\n        if (contentDOM) {\n            if (node.isLeaf)\n                throw new RangeError(\"Content hole not allowed in a leaf node spec\");\n            this.serializeFragment(node.content, options, contentDOM);\n        }\n        return dom;\n    }\n    /**\n    Serialize this node to a DOM node. This can be useful when you\n    need to serialize a part of a document, as opposed to the whole\n    document. To serialize a whole document, use\n    [`serializeFragment`](https://prosemirror.net/docs/ref/#model.DOMSerializer.serializeFragment) on\n    its [content](https://prosemirror.net/docs/ref/#model.Node.content).\n    */\n    serializeNode(node, options = {}) {\n        let dom = this.serializeNodeInner(node, options);\n        for (let i = node.marks.length - 1; i >= 0; i--) {\n            let wrap = this.serializeMark(node.marks[i], node.isInline, options);\n            if (wrap) {\n                (wrap.contentDOM || wrap.dom).appendChild(dom);\n                dom = wrap.dom;\n            }\n        }\n        return dom;\n    }\n    /**\n    @internal\n    */\n    serializeMark(mark, inline, options = {}) {\n        let toDOM = this.marks[mark.type.name];\n        return toDOM && renderSpec(doc(options), toDOM(mark, inline), null, mark.attrs);\n    }\n    static renderSpec(doc, structure, xmlNS = null, blockArraysIn) {\n        return renderSpec(doc, structure, xmlNS, blockArraysIn);\n    }\n    /**\n    Build a serializer using the [`toDOM`](https://prosemirror.net/docs/ref/#model.NodeSpec.toDOM)\n    properties in a schema's node and mark specs.\n    */\n    static fromSchema(schema) {\n        return schema.cached.domSerializer ||\n            (schema.cached.domSerializer = new DOMSerializer(this.nodesFromSchema(schema), this.marksFromSchema(schema)));\n    }\n    /**\n    Gather the serializers in a schema's node specs into an object.\n    This can be useful as a base to build a custom serializer from.\n    */\n    static nodesFromSchema(schema) {\n        let result = gatherToDOM(schema.nodes);\n        if (!result.text)\n            result.text = node => node.text;\n        return result;\n    }\n    /**\n    Gather the serializers in a schema's mark specs into an object.\n    */\n    static marksFromSchema(schema) {\n        return gatherToDOM(schema.marks);\n    }\n}\nfunction gatherToDOM(obj) {\n    let result = {};\n    for (let name in obj) {\n        let toDOM = obj[name].spec.toDOM;\n        if (toDOM)\n            result[name] = toDOM;\n    }\n    return result;\n}\nfunction doc(options) {\n    return options.document || window.document;\n}\nconst suspiciousAttributeCache = new WeakMap();\nfunction suspiciousAttributes(attrs) {\n    let value = suspiciousAttributeCache.get(attrs);\n    if (value === undefined)\n        suspiciousAttributeCache.set(attrs, value = suspiciousAttributesInner(attrs));\n    return value;\n}\nfunction suspiciousAttributesInner(attrs) {\n    let result = null;\n    function scan(value) {\n        if (value && typeof value == \"object\") {\n            if (Array.isArray(value)) {\n                if (typeof value[0] == \"string\") {\n                    if (!result)\n                        result = [];\n                    result.push(value);\n                }\n                else {\n                    for (let i = 0; i < value.length; i++)\n                        scan(value[i]);\n                }\n            }\n            else {\n                for (let prop in value)\n                    scan(value[prop]);\n            }\n        }\n    }\n    scan(attrs);\n    return result;\n}\nfunction renderSpec(doc, structure, xmlNS, blockArraysIn) {\n    if (typeof structure == \"string\")\n        return { dom: doc.createTextNode(structure) };\n    if (structure.nodeType != null)\n        return { dom: structure };\n    if (structure.dom && structure.dom.nodeType != null)\n        return structure;\n    let tagName = structure[0], suspicious;\n    if (typeof tagName != \"string\")\n        throw new RangeError(\"Invalid array passed to renderSpec\");\n    if (blockArraysIn && (suspicious = suspiciousAttributes(blockArraysIn)) &&\n        suspicious.indexOf(structure) > -1)\n        throw new RangeError(\"Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.\");\n    let space = tagName.indexOf(\" \");\n    if (space > 0) {\n        xmlNS = tagName.slice(0, space);\n        tagName = tagName.slice(space + 1);\n    }\n    let contentDOM;\n    let dom = (xmlNS ? doc.createElementNS(xmlNS, tagName) : doc.createElement(tagName));\n    let attrs = structure[1], start = 1;\n    if (attrs && typeof attrs == \"object\" && attrs.nodeType == null && !Array.isArray(attrs)) {\n        start = 2;\n        for (let name in attrs)\n            if (attrs[name] != null) {\n                let space = name.indexOf(\" \");\n                if (space > 0)\n                    dom.setAttributeNS(name.slice(0, space), name.slice(space + 1), attrs[name]);\n                else\n                    dom.setAttribute(name, attrs[name]);\n            }\n    }\n    for (let i = start; i < structure.length; i++) {\n        let child = structure[i];\n        if (child === 0) {\n            if (i < structure.length - 1 || i > start)\n                throw new RangeError(\"Content hole must be the only child of its parent node\");\n            return { dom, contentDOM: dom };\n        }\n        else {\n            let { dom: inner, contentDOM: innerContent } = renderSpec(doc, child, xmlNS, blockArraysIn);\n            dom.appendChild(inner);\n            if (innerContent) {\n                if (contentDOM)\n                    throw new RangeError(\"Multiple content holes\");\n                contentDOM = innerContent;\n            }\n        }\n    }\n    return { dom, contentDOM };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-model/dist/index.js\n");

/***/ })

};
;