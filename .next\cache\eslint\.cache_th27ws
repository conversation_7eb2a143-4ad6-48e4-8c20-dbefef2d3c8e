[{"C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\analyze\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\export\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\generate-media\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\editor\\text-editor.tsx": "6", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\export\\export-manager.tsx": "7", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\reading\\reading-mode.tsx": "8", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\realtime\\realtime-status.tsx": "9", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\ui\\button.tsx": "10", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\visualization\\visualization-panel.tsx": "11", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\hooks\\useAnalysis.ts": "12", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\hooks\\useMediaGeneration.ts": "13", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\hooks\\useWebSocket.ts": "14", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\media-generation.ts": "15", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\openai.ts": "16", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\utils.ts": "17", "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\websocket-server.ts": "18"}, {"size": 1836, "mtime": 1750181635219, "results": "19", "hashOfConfig": "20"}, {"size": 9553, "mtime": 1750184663102, "results": "21", "hashOfConfig": "20"}, {"size": 2165, "mtime": 1750181967852, "results": "22", "hashOfConfig": "20"}, {"size": 689, "mtime": 1749258357438, "results": "23", "hashOfConfig": "20"}, {"size": 8241, "mtime": 1750187702020, "results": "24", "hashOfConfig": "20"}, {"size": 8598, "mtime": 1750187682984, "results": "25", "hashOfConfig": "20"}, {"size": 13486, "mtime": 1750184588563, "results": "26", "hashOfConfig": "20"}, {"size": 13161, "mtime": 1750182447263, "results": "27", "hashOfConfig": "20"}, {"size": 5391, "mtime": 1750187607812, "results": "28", "hashOfConfig": "20"}, {"size": 1600, "mtime": 1750180672072, "results": "29", "hashOfConfig": "20"}, {"size": 11398, "mtime": 1750187629711, "results": "30", "hashOfConfig": "20"}, {"size": 2340, "mtime": 1750182198064, "results": "31", "hashOfConfig": "20"}, {"size": 2793, "mtime": 1750181987587, "results": "32", "hashOfConfig": "20"}, {"size": 5892, "mtime": 1750187469411, "results": "33", "hashOfConfig": "20"}, {"size": 6206, "mtime": 1750181951226, "results": "34", "hashOfConfig": "20"}, {"size": 4868, "mtime": 1750181619666, "results": "35", "hashOfConfig": "20"}, {"size": 166, "mtime": 1750180679406, "results": "36", "hashOfConfig": "20"}, {"size": 6579, "mtime": 1750182183895, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1nr2ova", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\analyze\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\export\\route.ts", ["92", "93", "94"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\generate-media\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\page.tsx", ["95", "96"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\editor\\text-editor.tsx", ["97", "98", "99", "100", "101"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\export\\export-manager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\reading\\reading-mode.tsx", ["102", "103", "104", "105"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\realtime\\realtime-status.tsx", ["106", "107", "108", "109"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\components\\visualization\\visualization-panel.tsx", ["110", "111", "112", "113", "114", "115"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\hooks\\useAnalysis.ts", ["116"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\hooks\\useMediaGeneration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\hooks\\useWebSocket.ts", ["117", "118", "119"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\media-generation.ts", ["120", "121"], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\openai.ts", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\lib\\websocket-server.ts", ["122", "123", "124", "125"], [], {"ruleId": "126", "severity": 2, "message": "127", "line": 39, "column": 48, "nodeType": "128", "messageId": "129", "endLine": 39, "endColumn": 51, "suggestions": "130"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 166, "column": 39, "nodeType": "128", "messageId": "129", "endLine": 166, "endColumn": 42, "suggestions": "131"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 244, "column": 39, "nodeType": "128", "messageId": "129", "endLine": 244, "endColumn": 42, "suggestions": "132"}, {"ruleId": "133", "severity": 2, "message": "134", "line": 13, "column": 10, "nodeType": null, "messageId": "135", "endLine": 13, "endColumn": 22}, {"ruleId": "126", "severity": 2, "message": "127", "line": 29, "column": 45, "nodeType": "128", "messageId": "129", "endLine": 29, "endColumn": 48, "suggestions": "136"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 15, "column": 35, "nodeType": "128", "messageId": "129", "endLine": 15, "endColumn": 38, "suggestions": "137"}, {"ruleId": "138", "severity": 2, "message": "139", "line": 98, "column": 58, "nodeType": "140", "messageId": "141", "suggestions": "142"}, {"ruleId": "138", "severity": 2, "message": "139", "line": 98, "column": 89, "nodeType": "140", "messageId": "141", "suggestions": "143"}, {"ruleId": "138", "severity": 2, "message": "139", "line": 192, "column": 50, "nodeType": "140", "messageId": "141", "suggestions": "144"}, {"ruleId": "138", "severity": 2, "message": "139", "line": 192, "column": 65, "nodeType": "140", "messageId": "141", "suggestions": "145"}, {"ruleId": "133", "severity": 2, "message": "146", "line": 8, "column": 3, "nodeType": null, "messageId": "135", "endLine": 8, "endColumn": 10}, {"ruleId": "133", "severity": 2, "message": "147", "line": 9, "column": 3, "nodeType": null, "messageId": "135", "endLine": 9, "endColumn": 10}, {"ruleId": "133", "severity": 2, "message": "148", "line": 15, "column": 3, "nodeType": null, "messageId": "135", "endLine": 15, "endColumn": 6}, {"ruleId": "149", "severity": 1, "message": "150", "line": 363, "column": 23, "nodeType": "151", "endLine": 367, "endColumn": 25}, {"ruleId": "126", "severity": 2, "message": "127", "line": 9, "column": 29, "nodeType": "128", "messageId": "129", "endLine": 9, "endColumn": 32, "suggestions": "152"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 10, "column": 31, "nodeType": "128", "messageId": "129", "endLine": 10, "endColumn": 34, "suggestions": "153"}, {"ruleId": "133", "severity": 2, "message": "154", "line": 34, "column": 5, "nodeType": null, "messageId": "135", "endLine": 34, "endColumn": 16}, {"ruleId": "133", "severity": 2, "message": "155", "line": 35, "column": 5, "nodeType": null, "messageId": "135", "endLine": 35, "endColumn": 17}, {"ruleId": "133", "severity": 2, "message": "156", "line": 3, "column": 20, "nodeType": null, "messageId": "135", "endLine": 3, "endColumn": 29}, {"ruleId": "126", "severity": 2, "message": "127", "line": 9, "column": 18, "nodeType": "128", "messageId": "129", "endLine": 9, "endColumn": 21, "suggestions": "157"}, {"ruleId": "138", "severity": 2, "message": "139", "line": 145, "column": 57, "nodeType": "140", "messageId": "141", "suggestions": "158"}, {"ruleId": "138", "severity": 2, "message": "139", "line": 145, "column": 88, "nodeType": "140", "messageId": "141", "suggestions": "159"}, {"ruleId": "149", "severity": 1, "message": "150", "line": 185, "column": 15, "nodeType": "151", "endLine": 189, "endColumn": 17}, {"ruleId": "149", "severity": 1, "message": "150", "line": 273, "column": 19, "nodeType": "151", "endLine": 277, "endColumn": 21}, {"ruleId": "133", "severity": 2, "message": "160", "line": 5, "column": 10, "nodeType": null, "messageId": "135", "endLine": 5, "endColumn": 22}, {"ruleId": "133", "severity": 2, "message": "161", "line": 4, "column": 10, "nodeType": null, "messageId": "135", "endLine": 4, "endColumn": 12}, {"ruleId": "126", "severity": 2, "message": "127", "line": 19, "column": 58, "nodeType": "128", "messageId": "129", "endLine": 19, "endColumn": 61, "suggestions": "162"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 23, "column": 60, "nodeType": "128", "messageId": "129", "endLine": 23, "endColumn": 63, "suggestions": "163"}, {"ruleId": "133", "severity": 2, "message": "164", "line": 1, "column": 8, "nodeType": null, "messageId": "135", "endLine": 1, "endColumn": 13}, {"ruleId": "133", "severity": 2, "message": "165", "line": 90, "column": 11, "nodeType": null, "messageId": "135", "endLine": 90, "endColumn": 19}, {"ruleId": "126", "severity": 2, "message": "127", "line": 19, "column": 58, "nodeType": "128", "messageId": "129", "endLine": 19, "endColumn": 61, "suggestions": "166"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 23, "column": 60, "nodeType": "128", "messageId": "129", "endLine": 23, "endColumn": 63, "suggestions": "167"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 174, "column": 82, "nodeType": "128", "messageId": "129", "endLine": 174, "endColumn": 85, "suggestions": "168"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 192, "column": 84, "nodeType": "128", "messageId": "129", "endLine": 192, "endColumn": 87, "suggestions": "169"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["170", "171"], ["172", "173"], ["174", "175"], "@typescript-eslint/no-unused-vars", "'isGenerating' is assigned a value but never used.", "unusedVar", ["176", "177"], ["178", "179"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["180", "181", "182", "183"], ["184", "185", "186", "187"], ["188", "189", "190", "191"], ["192", "193", "194", "195"], "'Volume2' is defined but never used.", "'VolumeX' is defined but never used.", "'Eye' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["196", "197"], ["198", "199"], "'joinSession' is assigned a value but never used.", "'leaveSession' is assigned a value but never used.", "'useEffect' is defined but never used.", ["200", "201"], ["202", "203", "204", "205"], ["206", "207", "208", "209"], "'useWebSocket' is defined but never used.", "'io' is defined but never used.", ["210", "211"], ["212", "213"], "'axios' is defined but never used.", "'duration' is assigned a value but never used.", ["214", "215"], ["216", "217"], ["218", "219"], ["220", "221"], {"messageId": "222", "fix": "223", "desc": "224"}, {"messageId": "225", "fix": "226", "desc": "227"}, {"messageId": "222", "fix": "228", "desc": "224"}, {"messageId": "225", "fix": "229", "desc": "227"}, {"messageId": "222", "fix": "230", "desc": "224"}, {"messageId": "225", "fix": "231", "desc": "227"}, {"messageId": "222", "fix": "232", "desc": "224"}, {"messageId": "225", "fix": "233", "desc": "227"}, {"messageId": "222", "fix": "234", "desc": "224"}, {"messageId": "225", "fix": "235", "desc": "227"}, {"messageId": "236", "data": "237", "fix": "238", "desc": "239"}, {"messageId": "236", "data": "240", "fix": "241", "desc": "242"}, {"messageId": "236", "data": "243", "fix": "244", "desc": "245"}, {"messageId": "236", "data": "246", "fix": "247", "desc": "248"}, {"messageId": "236", "data": "249", "fix": "250", "desc": "239"}, {"messageId": "236", "data": "251", "fix": "252", "desc": "242"}, {"messageId": "236", "data": "253", "fix": "254", "desc": "245"}, {"messageId": "236", "data": "255", "fix": "256", "desc": "248"}, {"messageId": "236", "data": "257", "fix": "258", "desc": "239"}, {"messageId": "236", "data": "259", "fix": "260", "desc": "242"}, {"messageId": "236", "data": "261", "fix": "262", "desc": "245"}, {"messageId": "236", "data": "263", "fix": "264", "desc": "248"}, {"messageId": "236", "data": "265", "fix": "266", "desc": "239"}, {"messageId": "236", "data": "267", "fix": "268", "desc": "242"}, {"messageId": "236", "data": "269", "fix": "270", "desc": "245"}, {"messageId": "236", "data": "271", "fix": "272", "desc": "248"}, {"messageId": "222", "fix": "273", "desc": "224"}, {"messageId": "225", "fix": "274", "desc": "227"}, {"messageId": "222", "fix": "275", "desc": "224"}, {"messageId": "225", "fix": "276", "desc": "227"}, {"messageId": "222", "fix": "277", "desc": "224"}, {"messageId": "225", "fix": "278", "desc": "227"}, {"messageId": "236", "data": "279", "fix": "280", "desc": "239"}, {"messageId": "236", "data": "281", "fix": "282", "desc": "242"}, {"messageId": "236", "data": "283", "fix": "284", "desc": "245"}, {"messageId": "236", "data": "285", "fix": "286", "desc": "248"}, {"messageId": "236", "data": "287", "fix": "288", "desc": "239"}, {"messageId": "236", "data": "289", "fix": "290", "desc": "242"}, {"messageId": "236", "data": "291", "fix": "292", "desc": "245"}, {"messageId": "236", "data": "293", "fix": "294", "desc": "248"}, {"messageId": "222", "fix": "295", "desc": "224"}, {"messageId": "225", "fix": "296", "desc": "227"}, {"messageId": "222", "fix": "297", "desc": "224"}, {"messageId": "225", "fix": "298", "desc": "227"}, {"messageId": "222", "fix": "299", "desc": "224"}, {"messageId": "225", "fix": "300", "desc": "227"}, {"messageId": "222", "fix": "301", "desc": "224"}, {"messageId": "225", "fix": "302", "desc": "227"}, {"messageId": "222", "fix": "303", "desc": "224"}, {"messageId": "225", "fix": "304", "desc": "227"}, {"messageId": "222", "fix": "305", "desc": "224"}, {"messageId": "225", "fix": "306", "desc": "227"}, "suggestUnknown", {"range": "307", "text": "308"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "309", "text": "310"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "311", "text": "308"}, {"range": "312", "text": "310"}, {"range": "313", "text": "308"}, {"range": "314", "text": "310"}, {"range": "315", "text": "308"}, {"range": "316", "text": "310"}, {"range": "317", "text": "308"}, {"range": "318", "text": "310"}, "replaceWithAlt", {"alt": "319"}, {"range": "320", "text": "319"}, "Replace with `&quot;`.", {"alt": "321"}, {"range": "322", "text": "321"}, "Replace with `&ldquo;`.", {"alt": "323"}, {"range": "324", "text": "323"}, "Replace with `&#34;`.", {"alt": "325"}, {"range": "326", "text": "325"}, "Replace with `&rdquo;`.", {"alt": "319"}, {"range": "327", "text": "328"}, {"alt": "321"}, {"range": "329", "text": "330"}, {"alt": "323"}, {"range": "331", "text": "332"}, {"alt": "325"}, {"range": "333", "text": "334"}, {"alt": "319"}, {"range": "335", "text": "319"}, {"alt": "321"}, {"range": "336", "text": "321"}, {"alt": "323"}, {"range": "337", "text": "323"}, {"alt": "325"}, {"range": "338", "text": "325"}, {"alt": "319"}, {"range": "339", "text": "319"}, {"alt": "321"}, {"range": "340", "text": "321"}, {"alt": "323"}, {"range": "341", "text": "323"}, {"alt": "325"}, {"range": "342", "text": "325"}, {"range": "343", "text": "308"}, {"range": "344", "text": "310"}, {"range": "345", "text": "308"}, {"range": "346", "text": "310"}, {"range": "347", "text": "308"}, {"range": "348", "text": "310"}, {"alt": "319"}, {"range": "349", "text": "319"}, {"alt": "321"}, {"range": "350", "text": "321"}, {"alt": "323"}, {"range": "351", "text": "323"}, {"alt": "325"}, {"range": "352", "text": "325"}, {"alt": "319"}, {"range": "353", "text": "328"}, {"alt": "321"}, {"range": "354", "text": "330"}, {"alt": "323"}, {"range": "355", "text": "332"}, {"alt": "325"}, {"range": "356", "text": "334"}, {"range": "357", "text": "308"}, {"range": "358", "text": "310"}, {"range": "359", "text": "308"}, {"range": "360", "text": "310"}, {"range": "361", "text": "308"}, {"range": "362", "text": "310"}, {"range": "363", "text": "308"}, {"range": "364", "text": "310"}, {"range": "365", "text": "308"}, {"range": "366", "text": "310"}, {"range": "367", "text": "308"}, {"range": "368", "text": "310"}, [981, 984], "unknown", [981, 984], "never", [5278, 5281], [5278, 5281], [8161, 8164], [8161, 8164], [1085, 1088], [1085, 1088], [601, 604], [601, 604], "&quot;", [4037, 4038], "&ldquo;", [4037, 4038], "&#34;", [4037, 4038], "&rdquo;", [4037, 4038], [4065, 4069], "...&quot;", [4065, 4069], "...&ldquo;", [4065, 4069], "...&#34;", [4065, 4069], "...&rdquo;", [7959, 7960], [7959, 7960], [7959, 7960], [7959, 7960], [7974, 7975], [7974, 7975], [7974, 7975], [7974, 7975], [338, 341], [338, 341], [382, 385], [382, 385], [309, 312], [309, 312], [5412, 5413], [5412, 5413], [5412, 5413], [5412, 5413], [5440, 5444], [5440, 5444], [5440, 5444], [5440, 5444], [778, 781], [778, 781], [1119, 1122], [1119, 1122], [1138, 1141], [1138, 1141], [1479, 1482], [1479, 1482], [5756, 5759], [5756, 5759], [6310, 6313], [6310, 6313]]