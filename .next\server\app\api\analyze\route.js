(()=>{var e={};e.id=786,e.ids=[786],e.modules={598:(e,t,s)=>{"use strict";let n,r,i,a;s.r(t),s.d(t,{patchFetch:()=>nu,routeModule:()=>na,serverHooks:()=>nc,workAsyncStorage:()=>no,workUnitAsyncStorage:()=>nl});var o,l,c,u,h,d,f,p,m,g,y,w,_,b,v,x,S,$,A,O,I,R,k,E,P,C,T,N,j,M,L,D,B,U,q,W,F,X,J,H,z,K,V,G,Q,Y,Z,ee,et,es,en,er,ei,ea,eo,el,ec,eu,eh,ed,ef,ep,em,eg,ey,ew,e_,eb,ev,ex,eS,e$,eA={};s.r(eA),s.d(eA,{OPTIONS:()=>ni,POST:()=>nr});var eO=s(6559),eI=s(8088),eR=s(7719),ek=s(2190);function eE(e,t,s,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s}function eP(e,t,s,n){if("a"===s&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}let eC=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eC=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function eT(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eN=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class ej extends Error{}class eM extends ej{constructor(e,t,s,n){super(`${eM.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.requestID=n?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new eD({message:s,cause:eN(t)});let r=t?.error;return 400===e?new eU(e,r,s,n):401===e?new eq(e,r,s,n):403===e?new eW(e,r,s,n):404===e?new eF(e,r,s,n):409===e?new eX(e,r,s,n):422===e?new eJ(e,r,s,n):429===e?new eH(e,r,s,n):e>=500?new ez(e,r,s,n):new eM(e,r,s,n)}}class eL extends eM{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eD extends eM{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eB extends eD{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eU extends eM{}class eq extends eM{}class eW extends eM{}class eF extends eM{}class eX extends eM{}class eJ extends eM{}class eH extends eM{}class ez extends eM{}class eK extends ej{constructor(){super("Could not parse response content as the length limit was reached")}}class eV extends ej{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let eG=/^[a-z][a-z0-9+.-]*:/i,eQ=e=>eG.test(e),eY=e=>(eY=Array.isArray)(e),eZ=eY;function e0(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let e1=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new ej(`${e} must be an integer`);if(t<0)throw new ej(`${e} must be a positive integer`);return t},e2=e=>{try{return JSON.parse(e)}catch(e){return}},e3=e=>new Promise(t=>setTimeout(t,e)),e4="5.5.1",e8=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,e5=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e4,"X-Stainless-OS":e9(Deno.build.os),"X-Stainless-Arch":e6(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e4,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e4,"X-Stainless-OS":e9(globalThis.process.platform??"unknown"),"X-Stainless-Arch":e6(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,n=s[2]||0,r=s[3]||0;return{browser:e,version:`${t}.${n}.${r}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e4,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e4,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},e6=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",e9=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",e7=()=>n??(n=e5());function te(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function tt(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return te({start(){},async pull(e){let{done:s,value:n}=await t.next();s?e.close():e.enqueue(n)},async cancel(){await t.return?.()}})}function ts(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tn(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let tr=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),ti="RFC3986",ta=e=>String(e),to={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:ta},tl=(e,t)=>(tl=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(e,t),tc=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function tu(e,t){if(eY(e)){let s=[];for(let n=0;n<e.length;n+=1)s.push(t(e[n]));return s}return t(e)}let th={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},td=function(e,t){Array.prototype.push.apply(e,eY(t)?t:[t])},tf={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,n,r)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===s)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,s=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===r&&(40===n||41===n)){s[s.length]=t.charAt(e);continue}if(n<128){s[s.length]=tc[n];continue}if(n<2048){s[s.length]=tc[192|n>>6]+tc[128|63&n];continue}if(n<55296||n>=57344){s[s.length]=tc[224|n>>12]+tc[128|n>>6&63]+tc[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),s[s.length]=tc[240|n>>18]+tc[128|n>>12&63]+tc[128|n>>6&63]+tc[128|63&n]}a+=s.join("")}return a},encodeValuesOnly:!1,format:ti,formatter:ta,indices:!1,serializeDate:e=>(r??(r=Function.prototype.call.bind(Date.prototype.toISOString)))(e),skipNulls:!1,strictNullHandling:!1},tp={};function tm(e){let t;return(i??(i=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function tg(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class ty{constructor(){o.set(this,void 0),l.set(this,void 0),eE(this,o,new Uint8Array,"f"),eE(this,l,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tm(e):e;eE(this,o,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),n=0;for(let t of e)s.set(t,n),n+=t.length;return s}([eP(this,o,"f"),s]),"f");let n=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(eP(this,o,"f"),eP(this,l,"f")));){if(t.carriage&&null==eP(this,l,"f")){eE(this,l,t.index,"f");continue}if(null!=eP(this,l,"f")&&(t.index!==eP(this,l,"f")+1||t.carriage)){n.push(tg(eP(this,o,"f").subarray(0,eP(this,l,"f")-1))),eE(this,o,eP(this,o,"f").subarray(eP(this,l,"f")),"f"),eE(this,l,null,"f");continue}let e=null!==eP(this,l,"f")?t.preceding-1:t.preceding,s=tg(eP(this,o,"f").subarray(0,e));n.push(s),eE(this,o,eP(this,o,"f").subarray(t.index),"f"),eE(this,l,null,"f")}return n}flush(){return eP(this,o,"f").length?this.decode("\n"):[]}}o=new WeakMap,l=new WeakMap,ty.NEWLINE_CHARS=new Set(["\n","\r"]),ty.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class tw{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*n(){if(s)throw new ej("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let n=!1;try{for await(let s of t_(e,t))if(!n){if(s.data.startsWith("[DONE]")){n=!0;continue}if(null===s.event||s.event.startsWith("response.")||s.event.startsWith("transcript.")){let t;try{t=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if(t&&t.error)throw new eM(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("error"==s.event)throw new eM(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}}n=!0}catch(e){if(eT(e))return;throw e}finally{n||t.abort()}}return new tw(n,t)}static fromReadableStream(e,t){let s=!1;async function*n(){let t=new ty;for await(let s of ts(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new tw(async function*(){if(s)throw new ej("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eT(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=s.next();e.push(n),t.push(n)}return n.shift()}});return[new tw(()=>n(e),this.controller),new tw(()=>n(t),this.controller)]}toReadableStream(){let e,t=this;return te({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:n}=await e.next();if(n)return t.close();let r=tm(JSON.stringify(s)+"\n");t.enqueue(r)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*t_(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new ej("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new ej("Attempted to iterate over a response with no body")}let s=new tv,n=new ty;for await(let t of tb(ts(e.body)))for(let e of n.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of n.flush()){let t=s.decode(e);t&&(yield t)}}async function*tb(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let n=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?tm(s):s,r=new Uint8Array(t.length+n.length);for(r.set(t),r.set(n,t.length),t=r;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tv{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}let tx={off:0,error:200,warn:300,info:400,debug:500},tS=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(tx,e))return e;tR(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(tx))}`)}};function t$(){}function tA(e,t,s){return!t||tx[e]>tx[s]?t$:t[e].bind(t)}let tO={error:t$,warn:t$,info:t$,debug:t$},tI=new WeakMap;function tR(e){let t=e.logger,s=e.logLevel??"off";if(!t)return tO;let n=tI.get(t);if(n&&n[0]===s)return n[1];let r={error:tA("error",t,s),warn:tA("warn",t,s),info:tA("info",t,s),debug:tA("debug",t,s)};return tI.set(t,[s,r]),r}let tk=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e);async function tE(e,t){let{response:s,requestLogID:n,retryOfRequestLogID:r,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(tR(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):tw.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let n=s.headers.get("content-type"),r=n?.split(";")[0]?.trim();return r?.includes("application/json")||r?.endsWith("+json")?tP(await s.json(),s):await s.text()})();return tR(e).debug(`[${n}] response parsed`,tk({retryOfRequestLogID:r,url:s.url,status:s.status,body:a,durationMs:Date.now()-i})),a}function tP(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tC extends Promise{constructor(e,t,s=tE){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,c.set(this,void 0),eE(this,c,e,"f")}_thenUnwrap(e){return new tC(eP(this,c,"f"),this.responsePromise,async(t,s)=>tP(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eP(this,c,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}c=new WeakMap;class tT{constructor(e,t,s,n){u.set(this,void 0),eE(this,u,e,"f"),this.options=n,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new ej("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eP(this,u,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(u=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tN extends tC{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await tE(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tj extends tT{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tM extends tT{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let tL=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tD(e,t,s){return tL(),new File(e,t??"unknown_file",s)}function tB(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tU=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tq=async(e,t)=>({...e,body:await tF(e.body,t)}),tW=new WeakMap,tF=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=tW.get(t);if(s)return s;let n=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return tW.set(t,n),n}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tz(s,e,t))),s},tX=e=>e instanceof Blob&&"name"in e,tJ=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tU(e)||tX(e)),tH=e=>{if(tJ(e))return!0;if(Array.isArray(e))return e.some(tH);if(e&&"object"==typeof e){for(let t in e)if(tH(e[t]))return!0}return!1},tz=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,tD([await s.blob()],tB(s)));else if(tU(s))e.append(t,tD([await new Response(tt(s)).blob()],tB(s)));else if(tX(s))e.append(t,s,tB(s));else if(Array.isArray(s))await Promise.all(s.map(s=>tz(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,n])=>tz(e,`${t}[${s}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},tK=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tV=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tK(e),tG=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function tQ(e,t,s){if(tL(),tV(e=await e))return e instanceof File?e:tD([await e.arrayBuffer()],e.name);if(tG(e)){let n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tD(await tY(n),t,s)}let n=await tY(e);if(t||(t=tB(e)),!s?.type){let e=n.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return tD(n,t,s)}async function tY(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tK(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tU(e))for await(let s of e)t.push(...await tY(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class tZ{constructor(e){this._client=e}}function t0(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let t1=((e=t0)=>function(t,...s){let n;if(1===t.length)return t[0];let r=!1,i=t.reduce((t,n,i)=>(/[?#]/.test(n)&&(r=!0),t+n+(i===s.length?"":(r?encodeURIComponent:e)(String(s[i])))),""),a=i.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(n=l.exec(a));)o.push({start:n.index,length:n[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let n=" ".repeat(s.start-e),r="^".repeat(s.length);return e=s.start+s.length,t+n+r},"");throw new ej(`Path parameters result in path with invalid segments:
${i}
${t}`)}return i})(t0);class t2 extends tZ{list(e,t={},s){return this._client.getAPIList(t1`/chat/completions/${e}/messages`,tM,{query:t,...s})}}let t3=e=>e?.role==="assistant",t4=e=>e?.role==="tool";class t8{constructor(){h.add(this),this.controller=new AbortController,d.set(this,void 0),f.set(this,()=>{}),p.set(this,()=>{}),m.set(this,void 0),g.set(this,()=>{}),y.set(this,()=>{}),w.set(this,{}),_.set(this,!1),b.set(this,!1),v.set(this,!1),x.set(this,!1),eE(this,d,new Promise((e,t)=>{eE(this,f,e,"f"),eE(this,p,t,"f")}),"f"),eE(this,m,new Promise((e,t)=>{eE(this,g,e,"f"),eE(this,y,t,"f")}),"f"),eP(this,d,"f").catch(()=>{}),eP(this,m,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},eP(this,h,"m",S).bind(this))},0)}_connected(){this.ended||(eP(this,f,"f").call(this),this._emit("connect"))}get ended(){return eP(this,_,"f")}get errored(){return eP(this,b,"f")}get aborted(){return eP(this,v,"f")}abort(){this.controller.abort()}on(e,t){return(eP(this,w,"f")[e]||(eP(this,w,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eP(this,w,"f")[e];if(!s)return this;let n=s.findIndex(e=>e.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(eP(this,w,"f")[e]||(eP(this,w,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{eE(this,x,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){eE(this,x,!0,"f"),await eP(this,m,"f")}_emit(e,...t){if(eP(this,_,"f"))return;"end"===e&&(eE(this,_,!0,"f"),eP(this,g,"f").call(this));let s=eP(this,w,"f")[e];if(s&&(eP(this,w,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eP(this,x,"f")||s?.length||Promise.reject(e),eP(this,p,"f").call(this,e),eP(this,y,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eP(this,x,"f")||s?.length||Promise.reject(e),eP(this,p,"f").call(this,e),eP(this,y,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function t5(e){return e?.$brand==="auto-parseable-response-format"}function t6(e){return e?.$brand==="auto-parseable-tool"}function t9(e,t){let s=e.choices.map(e=>{var s,n;if("length"===e.finish_reason)throw new eK;if("content_filter"===e.finish_reason)throw new eV;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:t6(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,n=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:s}}function t7(e){return!!t5(e.response_format)||(e.tools?.some(e=>t6(e)||"function"===e.type&&!0===e.function.strict)??!1)}d=new WeakMap,f=new WeakMap,p=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,b=new WeakMap,v=new WeakMap,x=new WeakMap,h=new WeakSet,S=function(e){if(eE(this,b,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new eL),e instanceof eL)return eE(this,v,!0,"f"),this._emit("abort",e);if(e instanceof ej)return this._emit("error",e);if(e instanceof Error){let t=new ej(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new ej(String(e)))};class se extends t8{constructor(){super(...arguments),$.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),t4(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(t3(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new ej("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),eP(this,$,"m",A).call(this)}async finalMessage(){return await this.done(),eP(this,$,"m",O).call(this)}async finalFunctionToolCall(){return await this.done(),eP(this,$,"m",I).call(this)}async finalFunctionToolCallResult(){return await this.done(),eP(this,$,"m",R).call(this)}async totalUsage(){return await this.done(),eP(this,$,"m",k).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=eP(this,$,"m",O).call(this);t&&this._emit("finalMessage",t);let s=eP(this,$,"m",A).call(this);s&&this._emit("finalContent",s);let n=eP(this,$,"m",I).call(this);n&&this._emit("finalFunctionToolCall",n);let r=eP(this,$,"m",R).call(this);null!=r&&this._emit("finalFunctionToolCallResult",r),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",eP(this,$,"m",k).call(this))}async _createChatCompletion(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eP(this,$,"m",E).call(this,t);let r=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(t9(r,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let n="tool",{tool_choice:r="auto",stream:i,...a}=t,o="string"!=typeof r&&r?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(t6(e)){if(!e.$callback)throw new ej("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...a,tool_choice:r,tools:h,messages:[...this.messages]},s),i=t.choices[0]?.message;if(!i)throw new ej("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:r,arguments:i}=e.function,a=u[r];if(a){if(o&&o!==r){let e=`Invalid tool_call: ${JSON.stringify(r)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(r)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}try{t="function"==typeof a.parse?await a.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:s,content:e});continue}let l=await a.function(t,this),c=eP(this,$,"m",P).call(this,l);if(this._addMessage({role:n,tool_call_id:s,content:c}),o)return}}}}$=new WeakSet,A=function(){return eP(this,$,"m",O).call(this).content??null},O=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(t3(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new ej("stream ended without producing a ChatCompletionMessage with role=assistant")},I=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t3(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},R=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t4(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},k=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},E=function(e){if(null!=e.n&&e.n>1)throw new ej("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},P=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class st extends se{static runTools(e,t,s){let n=new st,r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}_addMessage(e,t=!0){super._addMessage(e,t),t3(e)&&e.content&&this._emit("content",e.content)}}let ss={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class sn extends Error{}class sr extends Error{}let si=(e,t)=>{let s=e.length,n=0,r=e=>{throw new sn(`${e} at position ${n}`)},i=e=>{throw new sr(`${e} at position ${n}`)},a=()=>(h(),n>=s&&r("Unexpected end of input"),'"'===e[n])?o():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||ss.NULL&t&&s-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||ss.BOOL&t&&s-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||ss.BOOL&t&&s-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||ss.INFINITY&t&&s-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||ss.MINUS_INFINITY&t&&1<s-n&&s-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||ss.NAN&t&&s-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),o=()=>{let a=n,o=!1;for(n++;n<s&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(a,++n-Number(o)))}catch(e){i(String(e))}else if(ss.STR&t)try{return JSON.parse(e.substring(a,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{n++,h();let i={};try{for(;"}"!==e[n];){if(h(),n>=s&&ss.OBJ&t)return i;let r=o();h(),n++;try{let e=a();Object.defineProperty(i,r,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(ss.OBJ&t)return i;throw e}h(),","===e[n]&&n++}}catch(e){if(ss.OBJ&t)return i;r("Expected '}' at end of object")}return n++,i},c=()=>{n++;let s=[];try{for(;"]"!==e[n];)s.push(a()),h(),","===e[n]&&n++}catch(e){if(ss.ARR&t)return s;r("Expected ']' at end of array")}return n++,s},u=()=>{if(0===n){"-"===e&&ss.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(ss.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(s))}}let a=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=s||ss.NUM&t||r("Unterminated number literal");try{return JSON.parse(e.substring(a,n))}catch(s){"-"===e.substring(a,n)&&ss.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;n<s&&" \n\r	".includes(e[n]);)n++};return a()},sa=e=>(function(e,t=ss.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return si(e.trim(),t)})(e,ss.ALL^ss.NUM);class so extends se{constructor(e){super(),C.add(this),T.set(this,void 0),N.set(this,void 0),j.set(this,void 0),eE(this,T,e,"f"),eE(this,N,[],"f")}get currentChatCompletionSnapshot(){return eP(this,j,"f")}static fromReadableStream(e){let t=new so(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let n=new so(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,s){super._createChatCompletion;let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eP(this,C,"m",M).call(this);let r=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),r))eP(this,C,"m",D).call(this,e);if(r.controller.signal?.aborted)throw new eL;return this._addChatCompletion(eP(this,C,"m",q).call(this))}async _fromReadableStream(e,t){let s,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eP(this,C,"m",M).call(this),this._connected();let r=tw.fromReadableStream(e,this.controller);for await(let e of r)s&&s!==e.id&&this._addChatCompletion(eP(this,C,"m",q).call(this)),eP(this,C,"m",D).call(this,e),s=e.id;if(r.controller.signal?.aborted)throw new eL;return this._addChatCompletion(eP(this,C,"m",q).call(this))}[(T=new WeakMap,N=new WeakMap,j=new WeakMap,C=new WeakSet,M=function(){this.ended||eE(this,j,void 0,"f")},L=function(e){let t=eP(this,N,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},eP(this,N,"f")[e.index]=t),t},D=function(e){if(this.ended)return;let t=eP(this,C,"m",F).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=eP(this,C,"m",L).call(this,e);for(let t of(e.finish_reason&&(eP(this,C,"m",U).call(this,e),null!=n.current_tool_call_index&&eP(this,C,"m",B).call(this,e,n.current_tool_call_index)),s.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(eP(this,C,"m",U).call(this,e),null!=n.current_tool_call_index&&eP(this,C,"m",B).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},B=function(e,t){if(eP(this,C,"m",L).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=eP(this,T,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:t6(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},U=function(e){let t=eP(this,C,"m",L).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=eP(this,C,"m",W).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},q=function(){if(this.ended)throw new ej("stream has ended, this shouldn't happen");let e=eP(this,j,"f");if(!e)throw new ej("request ended without sending any chunks");return eE(this,j,void 0,"f"),eE(this,N,[],"f"),function(e,t){var s;let{id:n,choices:r,created:i,model:a,system_fingerprint:o,...l}=e;return s={...l,id:n,choices:r.map(({message:t,finish_reason:s,index:n,logprobs:r,...i})=>{if(!s)throw new ej(`missing finish_reason for choice ${n}`);let{content:a=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new ej(`missing role for choice ${n}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new ej(`missing function_call.arguments for choice ${n}`);if(!l)throw new ej(`missing function_call.name for choice ${n}`);return{...i,message:{content:a,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}return l?{...i,index:n,finish_reason:s,logprobs:r,message:{...c,role:u,content:a,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:r,type:i,id:a,...o}=t,{arguments:l,name:c,...u}=r||{};if(null==a)throw new ej(`missing choices[${n}].tool_calls[${s}].id
${sl(e)}`);if(null==i)throw new ej(`missing choices[${n}].tool_calls[${s}].type
${sl(e)}`);if(null==c)throw new ej(`missing choices[${n}].tool_calls[${s}].function.name
${sl(e)}`);if(null==l)throw new ej(`missing choices[${n}].tool_calls[${s}].function.arguments
${sl(e)}`);return{...o,id:a,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:a,role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}),created:i,model:a,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&t7(t)?t9(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,eP(this,T,"f"))},W=function(){let e=eP(this,T,"f")?.response_format;return t5(e)?e:null},F=function(e){var t,s,n,r;let i=eP(this,j,"f"),{choices:a,...o}=e;for(let{delta:a,finish_reason:l,index:c,logprobs:u=null,...h}of(i?Object.assign(i,o):i=eE(this,j,{...o,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...h}),u)if(e.logprobs){let{content:n,refusal:r,...i}=u;Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),r&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...r))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,eP(this,T,"f")&&t7(eP(this,T,"f")))){if("length"===l)throw new eK;if("content_filter"===l)throw new eV}if(Object.assign(e,h),!a)continue;let{content:o,refusal:d,function_call:f,role:p,tool_calls:m,...g}=a;if(Object.assign(e.message,g),d&&(e.message.refusal=(e.message.refusal||"")+d),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&eP(this,C,"m",W).call(this)&&(e.message.parsed=sa(e.message.content))),m)for(let{index:t,id:s,type:n,function:i,...a}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let o=(r=e.message.tool_calls)[t]??(r[t]={});Object.assign(o,a),s&&(o.id=s),n&&(o.type=n),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return t6(s)||s?.function.strict||!1}(eP(this,T,"f"),o)&&(o.function.parsed_arguments=sa(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tw(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function sl(e){return JSON.stringify(e)}class sc extends so{static fromReadableStream(e){let t=new sc(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let n=new sc(t),r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}}class su extends tZ{constructor(){super(...arguments),this.messages=new t2(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(t1`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(t1`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",tM,{query:e,...t})}delete(e,t){return this._client.delete(t1`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new ej(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new ej(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>t9(t,e))}runTools(e,t){return e.stream?sc.runTools(this._client,e,t):st.runTools(this._client,e,t)}stream(e,t){return so.createChatCompletion(this._client,e,t)}}su.Messages=t2;class sh extends tZ{constructor(){super(...arguments),this.completions=new su(this._client)}}sh.Completions=su;let sd=Symbol("brand.privateNullableHeaders"),sf=e=>{let t=new Headers,s=new Set;for(let n of e){let e=new Set;for(let[r,i]of function*(e){let t;if(!e)return;if(sd in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let n of(e instanceof Headers?t=e.entries():eZ(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=n[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=eZ(n[1])?n[1]:[n[1]],r=!1;for(let n of t)void 0!==n&&(s&&!r&&(r=!0,yield[e,null]),yield[e,n])}}(n)){let n=r.toLowerCase();e.has(n)||(t.delete(r),e.add(n)),null===i?(t.delete(r),s.add(n)):(t.append(r,i),s.delete(n))}}return{[sd]:!0,values:t,nulls:s}};class sp extends tZ{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:sf([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class sm extends tZ{create(e,t){return this._client.post("/audio/transcriptions",tq({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class sg extends tZ{create(e,t){return this._client.post("/audio/translations",tq({body:e,...t,__metadata:{model:e.model}},this._client))}}class sy extends tZ{constructor(){super(...arguments),this.transcriptions=new sm(this._client),this.translations=new sg(this._client),this.speech=new sp(this._client)}}sy.Transcriptions=sm,sy.Translations=sg,sy.Speech=sp;class sw extends tZ{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tM,{query:e,...t})}cancel(e,t){return this._client.post(t1`/batches/${e}/cancel`,t)}}class s_ extends tZ{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t1`/assistants/${e}`,{...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t1`/assistants/${e}`,{body:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tM,{query:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t1`/assistants/${e}`,{...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sb extends tZ{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sv extends tZ{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sx extends tZ{constructor(){super(...arguments),this.sessions=new sb(this._client),this.transcriptionSessions=new sv(this._client)}}sx.Sessions=sb,sx.TranscriptionSessions=sv;class sS extends tZ{create(e,t,s){return this._client.post(t1`/threads/${e}/messages`,{body:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t1`/threads/${n}/messages/${e}`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t1`/threads/${n}/messages/${e}`,{body:r,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t1`/threads/${e}/messages`,tM,{query:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:n}=t;return this._client.delete(t1`/threads/${n}/messages/${e}`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class s$ extends tZ{retrieve(e,t,s){let{thread_id:n,run_id:r,...i}=t;return this._client.get(t1`/threads/${n}/runs/${r}/steps/${e}`,{query:i,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:n,...r}=t;return this._client.getAPIList(t1`/threads/${n}/runs/${e}/steps`,tM,{query:r,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let sA=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,n=new Uint8Array(s);for(let e=0;e<s;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}},sO=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sI extends t8{constructor(){super(...arguments),X.add(this),H.set(this,[]),z.set(this,{}),K.set(this,{}),V.set(this,void 0),G.set(this,void 0),Q.set(this,void 0),Y.set(this,void 0),Z.set(this,void 0),ee.set(this,void 0),et.set(this,void 0),es.set(this,void 0),en.set(this,void 0)}[(H=new WeakMap,z=new WeakMap,K=new WeakMap,V=new WeakMap,G=new WeakMap,Q=new WeakMap,Y=new WeakMap,Z=new WeakMap,ee=new WeakMap,et=new WeakMap,es=new WeakMap,en=new WeakMap,X=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new J;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=tw.fromReadableStream(e,this.controller);for await(let e of n)eP(this,X,"m",er).call(this,e);if(n.controller.signal?.aborted)throw new eL;return this._addRun(eP(this,X,"m",ei).call(this))}toReadableStream(){return new tw(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,n){let r=new J;return r._run(()=>r._runToolAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createToolAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.submitToolOutputs(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eP(this,X,"m",er).call(this,e);if(a.controller.signal?.aborted)throw new eL;return this._addRun(eP(this,X,"m",ei).call(this))}static createThreadAssistantStream(e,t,s){let n=new J;return n._run(()=>n._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,s,n){let r=new J;return r._run(()=>r._runAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}currentEvent(){return eP(this,et,"f")}currentRun(){return eP(this,es,"f")}currentMessageSnapshot(){return eP(this,V,"f")}currentRunStepSnapshot(){return eP(this,en,"f")}async finalRunSteps(){return await this.done(),Object.values(eP(this,z,"f"))}async finalMessages(){return await this.done(),Object.values(eP(this,K,"f"))}async finalRun(){if(await this.done(),!eP(this,G,"f"))throw Error("Final run was not received.");return eP(this,G,"f")}async _createThreadAssistantStream(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let r={...t,stream:!0},i=await e.createAndRun(r,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))eP(this,X,"m",er).call(this,e);if(i.controller.signal?.aborted)throw new eL;return this._addRun(eP(this,X,"m",ei).call(this))}async _createAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.create(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eP(this,X,"m",er).call(this,e);if(a.controller.signal?.aborted)throw new eL;return this._addRun(eP(this,X,"m",ei).call(this))}static accumulateDelta(e,t){for(let[s,n]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=n;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(e0(t)&&e0(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!e0(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let n=t[s];null==n?t.push(e):t[s]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${n}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,n){return await this._createAssistantStream(t,e,s,n)}async _runToolAssistantStream(e,t,s,n){return await this._createToolAssistantStream(t,e,s,n)}}J=sI,er=function(e){if(!this.ended)switch(eE(this,et,e,"f"),eP(this,X,"m",el).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":eP(this,X,"m",ed).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eP(this,X,"m",eo).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":eP(this,X,"m",ea).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ei=function(){if(this.ended)throw new ej("stream has ended, this shouldn't happen");if(!eP(this,G,"f"))throw Error("Final run has not been received");return eP(this,G,"f")},ea=function(e){let[t,s]=eP(this,X,"m",eu).call(this,e,eP(this,V,"f"));for(let e of(eE(this,V,t,"f"),eP(this,K,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,n=t.content[s.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=eP(this,Q,"f")){if(eP(this,Y,"f"))switch(eP(this,Y,"f").type){case"text":this._emit("textDone",eP(this,Y,"f").text,eP(this,V,"f"));break;case"image_file":this._emit("imageFileDone",eP(this,Y,"f").image_file,eP(this,V,"f"))}eE(this,Q,s.index,"f")}eE(this,Y,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==eP(this,Q,"f")){let t=e.data.content[eP(this,Q,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,eP(this,V,"f"));break;case"text":this._emit("textDone",t.text,eP(this,V,"f"))}}eP(this,V,"f")&&this._emit("messageDone",e.data),eE(this,V,void 0,"f")}},eo=function(e){let t=eP(this,X,"m",ec).call(this,e);switch(eE(this,en,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==eP(this,Z,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(eP(this,ee,"f")&&this._emit("toolCallDone",eP(this,ee,"f")),eE(this,Z,e.index,"f"),eE(this,ee,t.step_details.tool_calls[e.index],"f"),eP(this,ee,"f")&&this._emit("toolCallCreated",eP(this,ee,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eE(this,en,void 0,"f"),"tool_calls"==e.data.step_details.type&&eP(this,ee,"f")&&(this._emit("toolCallDone",eP(this,ee,"f")),eE(this,ee,void 0,"f")),this._emit("runStepDone",e.data,t)}},el=function(e){eP(this,H,"f").push(e),this._emit("event",e)},ec=function(e){switch(e.event){case"thread.run.step.created":return eP(this,z,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=eP(this,z,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let n=J.accumulateDelta(t,s.delta);eP(this,z,"f")[e.data.id]=n}return eP(this,z,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":eP(this,z,"f")[e.data.id]=e.data}if(eP(this,z,"f")[e.data.id])return eP(this,z,"f")[e.data.id];throw Error("No snapshot available")},eu=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=eP(this,X,"m",eh).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eh=function(e,t){return J.accumulateDelta(t,e)},ed=function(e){switch(eE(this,es,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":eE(this,G,e.data,"f"),eP(this,ee,"f")&&(this._emit("toolCallDone",eP(this,ee,"f")),eE(this,ee,void 0,"f"))}};class sR extends tZ{constructor(){super(...arguments),this.steps=new s$(this._client)}create(e,t,s){let{include:n,...r}=t;return this._client.post(t1`/threads/${e}/runs`,{query:{include:n},body:r,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t1`/threads/${n}/runs/${e}`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t1`/threads/${n}/runs/${e}`,{body:r,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t1`/threads/${e}/runs`,tM,{query:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:n}=t;return this._client.post(t1`/threads/${n}/runs/${e}/cancel`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(n.id,{thread_id:e},s)}createAndStream(e,t,s){return sI.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let n=sf([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...n}}).withResponse();switch(r.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e3(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return r}}}stream(e,t,s){return sI.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t1`/threads/${n}/runs/${e}/submit_tool_outputs`,{body:r,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let n=await this.submitToolOutputs(e,t,s);return await this.poll(n.id,t,s)}submitToolOutputsStream(e,t,s){return sI.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sR.Steps=s$;class sk extends tZ{constructor(){super(...arguments),this.runs=new sR(this._client),this.messages=new sS(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t1`/threads/${e}`,{...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t1`/threads/${e}`,{body:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(t1`/threads/${e}`,{...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sI.createThreadAssistantStream(e,this._client.beta.threads,t)}}sk.Runs=sR,sk.Messages=sS;class sE extends tZ{constructor(){super(...arguments),this.realtime=new sx(this._client),this.assistants=new s_(this._client),this.threads=new sk(this._client)}}sE.Realtime=sx,sE.Assistants=s_,sE.Threads=sk;class sP extends tZ{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sC extends tZ{retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t1`/containers/${n}/files/${e}/content`,{...s,headers:sf([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sT extends tZ{constructor(){super(...arguments),this.content=new sC(this._client)}create(e,t,s){return this._client.post(t1`/containers/${e}/files`,tq({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t1`/containers/${n}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t1`/containers/${e}/files`,tM,{query:t,...s})}delete(e,t,s){let{container_id:n}=t;return this._client.delete(t1`/containers/${n}/files/${e}`,{...s,headers:sf([{Accept:"*/*"},s?.headers])})}}sT.Content=sC;class sN extends tZ{constructor(){super(...arguments),this.files=new sT(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tM,{query:e,...t})}delete(e,t){return this._client.delete(t1`/containers/${e}`,{...t,headers:sf([{Accept:"*/*"},t?.headers])})}}sN.Files=sT;class sj extends tZ{create(e,t){let s=!!e.encoding_format,n=s?e.encoding_format:"base64";s&&tR(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let r=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return s?r:(tR(this._client).debug("embeddings/decoding base64 embeddings from base64"),r._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=sA(t)}),e)))}}class sM extends tZ{retrieve(e,t,s){let{eval_id:n,run_id:r}=t;return this._client.get(t1`/evals/${n}/runs/${r}/output_items/${e}`,s)}list(e,t,s){let{eval_id:n,...r}=t;return this._client.getAPIList(t1`/evals/${n}/runs/${e}/output_items`,tM,{query:r,...s})}}class sL extends tZ{constructor(){super(...arguments),this.outputItems=new sM(this._client)}create(e,t,s){return this._client.post(t1`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:n}=t;return this._client.get(t1`/evals/${n}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t1`/evals/${e}/runs`,tM,{query:t,...s})}delete(e,t,s){let{eval_id:n}=t;return this._client.delete(t1`/evals/${n}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:n}=t;return this._client.post(t1`/evals/${n}/runs/${e}`,s)}}sL.OutputItems=sM;class sD extends tZ{constructor(){super(...arguments),this.runs=new sL(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/evals/${e}`,t)}update(e,t,s){return this._client.post(t1`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",tM,{query:e,...t})}delete(e,t){return this._client.delete(t1`/evals/${e}`,t)}}sD.Runs=sL;class sB extends tZ{create(e,t){return this._client.post("/files",tq({body:e,...t},this._client))}retrieve(e,t){return this._client.get(t1`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tM,{query:e,...t})}delete(e,t){return this._client.delete(t1`/files/${e}`,t)}content(e,t){return this._client.get(t1`/files/${e}/content`,{...t,headers:sf([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let n=new Set(["processed","error","deleted"]),r=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await e3(t),i=await this.retrieve(e),Date.now()-r>s)throw new eB({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return i}}class sU extends tZ{}class sq extends tZ{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class sW extends tZ{constructor(){super(...arguments),this.graders=new sq(this._client)}}sW.Graders=sq;class sF extends tZ{create(e,t,s){return this._client.getAPIList(t1`/fine_tuning/checkpoints/${e}/permissions`,tj,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.getAPIList(t1`/fine_tuning/checkpoints/${e}/permissions`,tM,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:n}=t;return this._client.delete(t1`/fine_tuning/checkpoints/${n}/permissions/${e}`,s)}}class sX extends tZ{constructor(){super(...arguments),this.permissions=new sF(this._client)}}sX.Permissions=sF;class sJ extends tZ{list(e,t={},s){return this._client.getAPIList(t1`/fine_tuning/jobs/${e}/checkpoints`,tM,{query:t,...s})}}class sH extends tZ{constructor(){super(...arguments),this.checkpoints=new sJ(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tM,{query:e,...t})}cancel(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(t1`/fine_tuning/jobs/${e}/events`,tM,{query:t,...s})}pause(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/resume`,t)}}sH.Checkpoints=sJ;class sz extends tZ{constructor(){super(...arguments),this.methods=new sU(this._client),this.jobs=new sH(this._client),this.checkpoints=new sX(this._client),this.alpha=new sW(this._client)}}sz.Methods=sU,sz.Jobs=sH,sz.Checkpoints=sX,sz.Alpha=sW;class sK extends tZ{}class sV extends tZ{constructor(){super(...arguments),this.graderModels=new sK(this._client)}}sV.GraderModels=sK;class sG extends tZ{createVariation(e,t){return this._client.post("/images/variations",tq({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",tq({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class sQ extends tZ{retrieve(e,t){return this._client.get(t1`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tj,e)}delete(e,t){return this._client.delete(t1`/models/${e}`,t)}}class sY extends tZ{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function sZ(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,n;return"output_text"===e.type?{...e,parsed:(s=t,n=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:s}}return e}),n=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||s0(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function s0(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class s1 extends t8{constructor(e){super(),ef.add(this),ep.set(this,void 0),em.set(this,void 0),eg.set(this,void 0),eE(this,ep,e,"f")}static createResponse(e,t,s){let n=new s1(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,s){let n,r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eP(this,ef,"m",ey).call(this);let i=null;for await(let r of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),n))eP(this,ef,"m",ew).call(this,r,i);if(n.controller.signal?.aborted)throw new eL;return eP(this,ef,"m",e_).call(this)}[(ep=new WeakMap,em=new WeakMap,eg=new WeakMap,ef=new WeakSet,ey=function(){this.ended||eE(this,em,void 0,"f")},ew=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},n=eP(this,ef,"m",eb).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new ej(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new ej(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new ej(`expected content to be 'output_text', got ${n.type}`);s("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new ej(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},e_=function(){if(this.ended)throw new ej("stream has ended, this shouldn't happen");let e=eP(this,em,"f");if(!e)throw new ej("request ended without sending any events");eE(this,em,void 0,"f");let t=function(e,t){var s;return t&&(s=t,t5(s.text?.format))?sZ(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,eP(this,ep,"f"));return eE(this,eg,t,"f"),t},eb=function(e){let t=eP(this,em,"f");if(!t){if("response.created"!==e.type)throw new ej(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return eE(this,em,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new ej(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new ej(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new ej(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new ej(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new ej(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":eE(this,em,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=eP(this,eg,"f");if(!e)throw new ej("stream ended without producing a ChatCompletion");return e}}class s2 extends tZ{list(e,t={},s){return this._client.getAPIList(t1`/responses/${e}/input_items`,tM,{query:t,...s})}}class s3 extends tZ{constructor(){super(...arguments),this.inputItems=new s2(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s0(e),e))}retrieve(e,t={},s){return this._client.get(t1`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})}delete(e,t){return this._client.delete(t1`/responses/${e}`,{...t,headers:sf([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>sZ(t,e))}stream(e,t){return s1.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(t1`/responses/${e}/cancel`,t)}}s3.InputItems=s2;class s4 extends tZ{create(e,t,s){return this._client.post(t1`/uploads/${e}/parts`,tq({body:t,...s},this._client))}}class s8 extends tZ{constructor(){super(...arguments),this.parts=new s4(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(t1`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(t1`/uploads/${e}/complete`,{body:t,...s})}}s8.Parts=s4;let s5=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class s6 extends tZ{create(e,t,s){return this._client.post(t1`/vector_stores/${e}/file_batches`,{body:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t1`/vector_stores/${n}/file_batches/${e}`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:n}=t;return this._client.post(t1`/vector_stores/${n}/file_batches/${e}/cancel`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t);return await this.poll(e,n.id,s)}listFiles(e,t,s){let{vector_store_id:n,...r}=t;return this._client.getAPIList(t1`/vector_stores/${n}/file_batches/${e}/files`,tM,{query:r,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let n=sf([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse();switch(r.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e3(a);break;case"failed":case"cancelled":case"completed":return r}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let r=Math.min(n?.maxConcurrency??5,t.length),i=this._client,a=t.values(),o=[...s];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);o.push(e.id)}}let c=Array(r).fill(a).map(l);return await s5(c),await this.createAndPoll(e,{file_ids:o})}}class s9 extends tZ{create(e,t,s){return this._client.post(t1`/vector_stores/${e}/files`,{body:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t1`/vector_stores/${n}/files/${e}`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:n,...r}=t;return this._client.post(t1`/vector_stores/${n}/files/${e}`,{body:r,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t1`/vector_stores/${e}/files`,tM,{query:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:n}=t;return this._client.delete(t1`/vector_stores/${n}/files/${e}`,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(e,n.id,s)}async poll(e,t,s){let n=sf([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let r=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse(),i=r.data;switch(i.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=r.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e3(a);break;case"failed":case"completed":return i}}}async upload(e,t,s){let n=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:n.id},s)}async uploadAndPoll(e,t,s){let n=await this.upload(e,t,s);return await this.poll(e,n.id,s)}content(e,t,s){let{vector_store_id:n}=t;return this._client.getAPIList(t1`/vector_stores/${n}/files/${e}/content`,tj,{...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class s7 extends tZ{constructor(){super(...arguments),this.files=new s9(this._client),this.fileBatches=new s6(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t1`/vector_stores/${e}`,{...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t1`/vector_stores/${e}`,{body:t,...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tM,{query:e,...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t1`/vector_stores/${e}`,{...t,headers:sf([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(t1`/vector_stores/${e}/search`,tj,{body:t,method:"post",...s,headers:sf([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}s7.Files=s9,s7.FileBatches=s6;class ne{constructor({baseURL:e=sO("OPENAI_BASE_URL"),apiKey:t=sO("OPENAI_API_KEY"),organization:s=sO("OPENAI_ORG_ID")??null,project:n=sO("OPENAI_PROJECT_ID")??null,...r}={}){if(ev.add(this),eS.set(this,void 0),this.completions=new sP(this),this.chat=new sh(this),this.embeddings=new sj(this),this.files=new sB(this),this.images=new sG(this),this.audio=new sy(this),this.moderations=new sY(this),this.models=new sQ(this),this.fineTuning=new sz(this),this.graders=new sV(this),this.vectorStores=new s7(this),this.beta=new sE(this),this.batches=new sw(this),this.uploads=new s8(this),this.responses=new s3(this),this.evals=new sD(this),this.containers=new sN(this),void 0===t)throw new ej("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let i={apiKey:t,organization:s,project:n,...r,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&e8())throw new ej("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=i.baseURL,this.timeout=i.timeout??ex.DEFAULT_TIMEOUT,this.logger=i.logger??console;let a="warn";this.logLevel=a,this.logLevel=tS(i.logLevel,"ClientOptions.logLevel",this)??tS(sO("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??a,this.fetchOptions=i.fetchOptions,this.maxRetries=i.maxRetries??2,this.fetch=i.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),eE(this,eS,tr,"f"),this._options=i,this.apiKey=t,this.organization=s,this.project=n}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return sf([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,n,r=e,i=function(e=tf){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||tf.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=ti;if(void 0!==e.format){if(!tl(to,e.format))throw TypeError("Unknown format option provided.");n=e.format}let r=to[n],i=tf.filter;if(("function"==typeof e.filter||eY(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in th?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":tf.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let a=void 0===e.allowDots?!0==!!e.encodeDotInKeys||tf.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:tf.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:tf.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:tf.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?tf.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:tf.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:tf.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:tf.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:tf.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:tf.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:tf.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:tf.strictNullHandling}}(t);"function"==typeof i.filter?r=(0,i.filter)("",r):eY(i.filter)&&(s=i.filter);let a=[];if("object"!=typeof r||null===r)return"";let o=th[i.arrayFormat],l="comma"===o&&i.commaRoundTrip;s||(s=Object.keys(r)),i.sort&&s.sort(i.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];i.skipNulls&&null===r[t]||td(a,function e(t,s,n,r,i,a,o,l,c,u,h,d,f,p,m,g,y,w){var _,b;let v,x=t,S=w,$=0,A=!1;for(;void 0!==(S=S.get(tp))&&!A;){let e=S.get(t);if($+=1,void 0!==e)if(e===$)throw RangeError("Cyclic object value");else A=!0;void 0===S.get(tp)&&($=0)}if("function"==typeof u?x=u(s,x):x instanceof Date?x=f?.(x):"comma"===n&&eY(x)&&(x=tu(x,function(e){return e instanceof Date?f?.(e):e})),null===x){if(a)return c&&!g?c(s,tf.encoder,y,"key",p):s;x=""}if("string"==typeof(_=x)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(b=x)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(c){let e=g?s:c(s,tf.encoder,y,"key",p);return[m?.(e)+"="+m?.(c(x,tf.encoder,y,"value",p))]}return[m?.(s)+"="+m?.(String(x))]}let O=[];if(void 0===x)return O;if("comma"===n&&eY(x))g&&c&&(x=tu(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(eY(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let I=l?String(s).replace(/\./g,"%2E"):String(s),R=r&&eY(x)&&1===x.length?I+"[]":I;if(i&&eY(x)&&0===x.length)return R+"[]";for(let s=0;s<v.length;++s){let _=v[s],b="object"==typeof _&&void 0!==_.value?_.value:x[_];if(o&&null===b)continue;let S=d&&l?_.replace(/\./g,"%2E"):_,A=eY(x)?"function"==typeof n?n(R,S):R:R+(d?"."+S:"["+S+"]");w.set(t,$);let I=new WeakMap;I.set(tp,w),td(O,e(b,A,n,r,i,a,o,l,"comma"===n&&g&&eY(x)?null:c,u,h,d,f,p,m,g,y,I))}return O}(r[t],t,o,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=a.join(i.delimiter),h=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),u.length>0?h+u:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${e4}`}defaultIdempotencyKey(){return`stainless-node-retry-${eC()}`}makeStatusError(e,t,s,n){return eM.generate(e,t,s,n)}buildURL(e,t,s){let n=!eP(this,ev,"m",e$).call(this)&&s||this.baseURL,r=new URL(eQ(e)?e:n+(n.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),i=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(i)&&(t={...i,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new tC(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let n=await e,r=n.maxRetries??this.maxRetries;null==t&&(t=r),await this.prepareOptions(n);let{req:i,url:a,timeout:o}=this.buildRequest(n,{retryCount:r-t});await this.prepareRequest(i,{url:a,options:n});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(tR(this).debug(`[${l}] sending request`,tk({retryOfRequestLogID:s,method:n.method,url:a,options:n,headers:i.headers})),n.signal?.aborted)throw new eL;let h=new AbortController,d=await this.fetchWithTimeout(a,i,o,h).catch(eN),f=Date.now();if(d instanceof Error){let e=`retrying, ${t} attempts remaining`;if(n.signal?.aborted)throw new eL;let r=eT(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return tR(this).info(`[${l}] connection ${r?"timed out":"failed"} - ${e}`),tR(this).debug(`[${l}] connection ${r?"timed out":"failed"} (${e})`,tk({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),this.retryRequest(n,t,s??l);if(tR(this).info(`[${l}] connection ${r?"timed out":"failed"} - error; no more retries left`),tR(this).debug(`[${l}] connection ${r?"timed out":"failed"} (error; no more retries left)`,tk({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),r)throw new eB;throw new eD({cause:d})}let p=[...d.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${p}] ${i.method} ${a} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${f-u}ms`;if(!d.ok){let e=this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tn(d.body),tR(this).info(`${m} - ${e}`),tR(this).debug(`[${l}] response error (${e})`,tk({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),this.retryRequest(n,t,s??l,d.headers)}let r=e?"error; no more retries left":"error; not retryable";tR(this).info(`${m} - ${r}`);let i=await d.text().catch(e=>eN(e).message),a=e2(i),o=a?void 0:i;throw tR(this).debug(`[${l}] response error (${r})`,tk({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,a,o,d.headers)}return tR(this).info(m),tR(this).debug(`[${l}] response start`,tk({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),{response:d,options:n,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new tN(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,n){let{signal:r,method:i,...a}=t||{};r&&r.addEventListener("abort",()=>n.abort());let o=setTimeout(()=>n.abort(),s),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:n.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,n){let r,i=n?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(r=e)}let a=n?.get("retry-after");if(a&&!r){let e=parseFloat(a);r=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(r&&0<=r&&r<6e4)){let s=e.maxRetries??this.maxRetries;r=this.calculateDefaultRetryTimeoutMillis(t,s)}return await e3(r),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:n,path:r,query:i,defaultBaseURL:a}=s,o=this.buildURL(r,i,a);"timeout"in s&&e1("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:l,body:c}=this.buildBody({options:s}),u=this.buildHeaders({options:e,method:n,bodyHeaders:l,retryCount:t});return{req:{method:n,headers:u,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&c instanceof globalThis.ReadableStream&&{duplex:"half"},...c&&{body:c},...this.fetchOptions??{},...s.fetchOptions??{}},url:o,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:n}){let r={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),r[this.idempotencyHeader]=e.idempotencyKey);let i=sf([r,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...e7(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=sf([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:tt(e)}:eP(this,eS,"f").call(this,{body:e,headers:s})}}ex=ne,eS=new WeakMap,ev=new WeakSet,e$=function(){return"https://api.openai.com/v1"!==this.baseURL},ne.OpenAI=ex,ne.DEFAULT_TIMEOUT=6e5,ne.OpenAIError=ej,ne.APIError=eM,ne.APIConnectionError=eD,ne.APIConnectionTimeoutError=eB,ne.APIUserAbortError=eL,ne.NotFoundError=eF,ne.ConflictError=eX,ne.RateLimitError=eH,ne.BadRequestError=eU,ne.AuthenticationError=eq,ne.InternalServerError=ez,ne.PermissionDeniedError=eW,ne.UnprocessableEntityError=eJ,ne.toFile=tQ,ne.Completions=sP,ne.Chat=sh,ne.Embeddings=sj,ne.Files=sB,ne.Images=sG,ne.Audio=sy,ne.Moderations=sY,ne.Models=sQ,ne.FineTuning=sz,ne.Graders=sV,ne.VectorStores=s7,ne.Beta=sE,ne.Batches=sw,ne.Uploads=s8,ne.Responses=s3,ne.Evals=sD,ne.Containers=sN;let nt=new ne({apiKey:process.env.OPENAI_API_KEY});async function ns(e,t="",s="image"){try{let n=`
Voc\xea \xe9 um especialista em educa\xe7\xe3o cient\xedfica e visualiza\xe7\xe3o de conceitos complexos. 
Sua tarefa \xe9 analisar termos cient\xedficos e gerar prompts detalhados para criar visualiza\xe7\xf5es educativas.

Instru\xe7\xf5es:
1. Identifique o conceito cient\xedfico principal
2. Forne\xe7a uma defini\xe7\xe3o clara e educativa
3. Crie um prompt detalhado para gerar uma visualiza\xe7\xe3o ${"video"===s?"em v\xeddeo":"em imagem"}
4. Sugira conceitos relacionados
5. Determine o n\xedvel de dificuldade

O prompt de visualiza\xe7\xe3o deve ser:
- Cientificamente preciso
- Visualmente descritivo
- Educativo e envolvente
- Adequado para ${"video"===s?"anima\xe7\xe3o":"ilustra\xe7\xe3o est\xe1tica"}

Responda APENAS em formato JSON v\xe1lido.
`,r=`
Texto selecionado: "${e}"
Contexto adicional: "${t}"
Tipo de visualiza\xe7\xe3o: ${s}

Analise este conceito cient\xedfico e gere uma resposta no seguinte formato JSON:
{
  "concept": "nome do conceito",
  "definition": "defini\xe7\xe3o clara e educativa",
  "visualizationPrompt": {
    "originalText": "texto original",
    "enhancedPrompt": "prompt detalhado para visualiza\xe7\xe3o",
    "visualizationType": "${s}",
    "scientificContext": "contexto cient\xedfico",
    "educationalLevel": "basic|intermediate|advanced"
  },
  "relatedConcepts": ["conceito1", "conceito2", "conceito3"],
  "difficulty": "basic|intermediate|advanced"
}
`,i=await nt.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:n},{role:"user",content:r}],temperature:.7,max_tokens:1e3}),a=i.choices[0]?.message?.content;if(!a)throw Error("No response from OpenAI");return JSON.parse(a)}catch(t){return console.error("Error analyzing scientific text:",t),{concept:e,definition:`${e} \xe9 um conceito cient\xedfico importante que merece visualiza\xe7\xe3o detalhada.`,visualizationPrompt:{originalText:e,enhancedPrompt:`Crie uma ${"video"===s?"anima\xe7\xe3o educativa":"ilustra\xe7\xe3o cient\xedfica detalhada"} mostrando ${e} de forma clara e educativa, com cores vibrantes e elementos visuais que facilitem o entendimento.`,visualizationType:s,scientificContext:"Educa\xe7\xe3o cient\xedfica geral",educationalLevel:"intermediate"},relatedConcepts:[],difficulty:"intermediate"}}}async function nn(e,t){try{let s=await nt.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:"Voc\xea \xe9 um especialista em educa\xe7\xe3o cient\xedfica. Gere sugest\xf5es de atividades educativas baseadas no conceito fornecido."},{role:"user",content:`Conceito: ${e}
N\xedvel: ${t}

Gere 5 sugest\xf5es de atividades educativas em formato de lista JSON.`}],temperature:.8,max_tokens:500}),n=s.choices[0]?.message?.content;if(n)return JSON.parse(n);return[]}catch(e){return console.error("Error generating educational suggestions:",e),["Criar um mapa mental do conceito","Fazer um experimento pr\xe1tico relacionado","Pesquisar aplica\xe7\xf5es no mundo real","Criar uma apresenta\xe7\xe3o explicativa","Discutir com colegas e professores"]}}async function nr(e){try{let{selectedText:t,context:s,visualizationType:n}=await e.json();if(!t||0===t.trim().length)return ek.NextResponse.json({error:"Selected text is required"},{status:400});if(t.length>500)return ek.NextResponse.json({error:"Selected text is too long (max 500 characters)"},{status:400});let r=await ns(t,s||"",n||"image"),i=await nn(r.concept,r.difficulty);return ek.NextResponse.json({success:!0,data:{...r,educationalSuggestions:i,timestamp:new Date().toISOString()}})}catch(e){return console.error("Error in analyze API:",e),ek.NextResponse.json({error:"Failed to analyze text",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function ni(){return new ek.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let na=new eO.AppRouteRouteModule({definition:{kind:eI.RouteKind.APP_ROUTE,page:"/api/analyze/route",pathname:"/api/analyze",filename:"route",bundlePath:"app/api/analyze/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\analyze\\route.ts",nextConfigOutput:"",userland:eA}),{workAsyncStorage:no,workUnitAsyncStorage:nl,serverHooks:nc}=na;function nu(){return(0,eR.patchFetch)({workAsyncStorage:no,workUnitAsyncStorage:nl})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[447,580],()=>s(598));module.exports=n})();