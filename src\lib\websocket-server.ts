import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';

export interface WebSocketEvents {
  // Client to Server
  'join-session': (sessionId: string) => void;
  'leave-session': (sessionId: string) => void;
  'text-selection': (data: { sessionId: string; selectedText: string; timestamp: number }) => void;
  'analysis-request': (data: { sessionId: string; text: string; type: 'image' | 'video' }) => void;
  'generation-request': (data: { sessionId: string; prompt: string; type: 'image' | 'video' }) => void;

  // Server to Client
  'session-joined': (data: { sessionId: string; userId: string }) => void;
  'user-joined': (data: { userId: string; sessionId: string }) => void;
  'user-left': (data: { userId: string; sessionId: string }) => void;
  'text-selected': (data: { userId: string; selectedText: string; timestamp: number }) => void;
  'analysis-started': (data: { userId: string; text: string; type: 'image' | 'video' }) => void;
  'analysis-progress': (data: { userId: string; progress: number; stage: string }) => void;
  'analysis-completed': (data: { userId: string; result: any }) => void;
  'analysis-error': (data: { userId: string; error: string }) => void;
  'generation-started': (data: { userId: string; prompt: string; type: 'image' | 'video' }) => void;
  'generation-progress': (data: { userId: string; progress: number; stage: string }) => void;
  'generation-completed': (data: { userId: string; result: any }) => void;
  'generation-error': (data: { userId: string; error: string }) => void;
}

let io: SocketIOServer | null = null;

export function initializeWebSocketServer(httpServer: HTTPServer): SocketIOServer {
  if (io) {
    return io;
  }

  io = new SocketIOServer(httpServer, {
    cors: {
      origin: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
      methods: ["GET", "POST"]
    },
    transports: ['websocket', 'polling']
  });

  // Store active sessions and users
  const sessions = new Map<string, Set<string>>();
  const userSessions = new Map<string, string>();

  io.on('connection', (socket) => {
    console.log('User connected:', socket.id);

    // Join a session
    socket.on('join-session', (sessionId: string) => {
      const previousSession = userSessions.get(socket.id);
      
      // Leave previous session if exists
      if (previousSession) {
        socket.leave(previousSession);
        const sessionUsers = sessions.get(previousSession);
        if (sessionUsers) {
          sessionUsers.delete(socket.id);
          if (sessionUsers.size === 0) {
            sessions.delete(previousSession);
          } else {
            socket.to(previousSession).emit('user-left', {
              userId: socket.id,
              sessionId: previousSession
            });
          }
        }
      }

      // Join new session
      socket.join(sessionId);
      userSessions.set(socket.id, sessionId);
      
      if (!sessions.has(sessionId)) {
        sessions.set(sessionId, new Set());
      }
      sessions.get(sessionId)!.add(socket.id);

      // Notify user and others in session
      socket.emit('session-joined', { sessionId, userId: socket.id });
      socket.to(sessionId).emit('user-joined', { userId: socket.id, sessionId });

      console.log(`User ${socket.id} joined session ${sessionId}`);
    });

    // Leave a session
    socket.on('leave-session', (sessionId: string) => {
      socket.leave(sessionId);
      userSessions.delete(socket.id);
      
      const sessionUsers = sessions.get(sessionId);
      if (sessionUsers) {
        sessionUsers.delete(socket.id);
        if (sessionUsers.size === 0) {
          sessions.delete(sessionId);
        } else {
          socket.to(sessionId).emit('user-left', {
            userId: socket.id,
            sessionId
          });
        }
      }

      console.log(`User ${socket.id} left session ${sessionId}`);
    });

    // Handle text selection
    socket.on('text-selection', (data) => {
      const { sessionId, selectedText, timestamp } = data;
      socket.to(sessionId).emit('text-selected', {
        userId: socket.id,
        selectedText,
        timestamp
      });
    });

    // Handle analysis request
    socket.on('analysis-request', (data) => {
      const { sessionId, text, type } = data;
      socket.to(sessionId).emit('analysis-started', {
        userId: socket.id,
        text,
        type
      });
    });

    // Handle generation request
    socket.on('generation-request', (data) => {
      const { sessionId, prompt, type } = data;
      socket.to(sessionId).emit('generation-started', {
        userId: socket.id,
        prompt,
        type
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      const sessionId = userSessions.get(socket.id);
      if (sessionId) {
        const sessionUsers = sessions.get(sessionId);
        if (sessionUsers) {
          sessionUsers.delete(socket.id);
          if (sessionUsers.size === 0) {
            sessions.delete(sessionId);
          } else {
            socket.to(sessionId).emit('user-left', {
              userId: socket.id,
              sessionId
            });
          }
        }
        userSessions.delete(socket.id);
      }
      
      console.log('User disconnected:', socket.id);
    });
  });

  return io;
}

export function getWebSocketServer(): SocketIOServer | null {
  return io;
}

// Helper functions to emit events
export function emitAnalysisProgress(sessionId: string, userId: string, progress: number, stage: string) {
  if (io) {
    io.to(sessionId).emit('analysis-progress', { userId, progress, stage });
  }
}

export function emitAnalysisCompleted(sessionId: string, userId: string, result: any) {
  if (io) {
    io.to(sessionId).emit('analysis-completed', { userId, result });
  }
}

export function emitAnalysisError(sessionId: string, userId: string, error: string) {
  if (io) {
    io.to(sessionId).emit('analysis-error', { userId, error });
  }
}

export function emitGenerationProgress(sessionId: string, userId: string, progress: number, stage: string) {
  if (io) {
    io.to(sessionId).emit('generation-progress', { userId, progress, stage });
  }
}

export function emitGenerationCompleted(sessionId: string, userId: string, result: any) {
  if (io) {
    io.to(sessionId).emit('generation-completed', { userId, result });
  }
}

export function emitGenerationError(sessionId: string, userId: string, error: string) {
  if (io) {
    io.to(sessionId).emit('generation-error', { userId, error });
  }
}
