'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

export interface WebSocketState {
  connected: boolean;
  sessionId: string | null;
  users: string[];
  error: string | null;
}

export interface WebSocketEvents {
  onTextSelected?: (data: { userId: string; selectedText: string; timestamp: number }) => void;
  onUserJoined?: (data: { userId: string; sessionId: string }) => void;
  onUserLeft?: (data: { userId: string; sessionId: string }) => void;
  onAnalysisStarted?: (data: { userId: string; text: string; type: 'image' | 'video' }) => void;
  onAnalysisProgress?: (data: { userId: string; progress: number; stage: string }) => void;
  onAnalysisCompleted?: (data: { userId: string; result: any }) => void;
  onAnalysisError?: (data: { userId: string; error: string }) => void;
  onGenerationStarted?: (data: { userId: string; prompt: string; type: 'image' | 'video' }) => void;
  onGenerationProgress?: (data: { userId: string; progress: number; stage: string }) => void;
  onGenerationCompleted?: (data: { userId: string; result: any }) => void;
  onGenerationError?: (data: { userId: string; error: string }) => void;
}

export function useWebSocket(events?: WebSocketEvents) {
  const socketRef = useRef<Socket | null>(null);
  const [state, setState] = useState<WebSocketState>({
    connected: false,
    sessionId: null,
    users: [],
    error: null,
  });

  // Initialize socket connection
  useEffect(() => {
    // For development, we'll simulate WebSocket functionality
    // In production with custom server, this would connect to actual WebSocket
    console.log('WebSocket simulation mode - custom server not running');

    // Simulate connection
    setState(prev => ({ ...prev, connected: true, error: null }));

    return () => {
      console.log('WebSocket simulation disconnected');
    };

    /*
    // Real WebSocket implementation (uncomment when using custom server)
    const socket = io(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', {
      transports: ['websocket', 'polling'],
      autoConnect: true,
    });

    socketRef.current = socket;

    // Connection events
    socket.on('connect', () => {
      setState(prev => ({ ...prev, connected: true, error: null }));
      console.log('WebSocket connected');
    });

    socket.on('disconnect', () => {
      setState(prev => ({ ...prev, connected: false }));
      console.log('WebSocket disconnected');
    });

    socket.on('connect_error', (error) => {
      setState(prev => ({ ...prev, error: error.message }));
      console.error('WebSocket connection error:', error);
    });

    // Session events
    socket.on('session-joined', (data) => {
      setState(prev => ({ ...prev, sessionId: data.sessionId }));
      console.log('Joined session:', data.sessionId);
    });

    socket.on('user-joined', (data) => {
      setState(prev => ({ 
        ...prev, 
        users: [...prev.users.filter(id => id !== data.userId), data.userId]
      }));
      events?.onUserJoined?.(data);
    });

    socket.on('user-left', (data) => {
      setState(prev => ({ 
        ...prev, 
        users: prev.users.filter(id => id !== data.userId)
      }));
      events?.onUserLeft?.(data);
    });

    // Text selection events
    socket.on('text-selected', (data) => {
      events?.onTextSelected?.(data);
    });

    // Analysis events
    socket.on('analysis-started', (data) => {
      events?.onAnalysisStarted?.(data);
    });

    socket.on('analysis-progress', (data) => {
      events?.onAnalysisProgress?.(data);
    });

    socket.on('analysis-completed', (data) => {
      events?.onAnalysisCompleted?.(data);
    });

    socket.on('analysis-error', (data) => {
      events?.onAnalysisError?.(data);
    });

    // Generation events
    socket.on('generation-started', (data) => {
      events?.onGenerationStarted?.(data);
    });

    socket.on('generation-progress', (data) => {
      events?.onGenerationProgress?.(data);
    });

    socket.on('generation-completed', (data) => {
      events?.onGenerationCompleted?.(data);
    });

    socket.on('generation-error', (data) => {
      events?.onGenerationError?.(data);
    });

    return () => {
      socket.disconnect();
    };
    */
  }, [events]);

  // Join a session
  const joinSession = useCallback((sessionId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('join-session', sessionId);
    }
  }, []);

  // Leave current session
  const leaveSession = useCallback(() => {
    if (socketRef.current && state.sessionId) {
      socketRef.current.emit('leave-session', state.sessionId);
      setState(prev => ({ ...prev, sessionId: null, users: [] }));
    }
  }, [state.sessionId]);

  // Emit text selection
  const emitTextSelection = useCallback((selectedText: string) => {
    if (socketRef.current && state.sessionId) {
      socketRef.current.emit('text-selection', {
        sessionId: state.sessionId,
        selectedText,
        timestamp: Date.now()
      });
    }
  }, [state.sessionId]);

  // Emit analysis request
  const emitAnalysisRequest = useCallback((text: string, type: 'image' | 'video') => {
    if (socketRef.current && state.sessionId) {
      socketRef.current.emit('analysis-request', {
        sessionId: state.sessionId,
        text,
        type
      });
    }
  }, [state.sessionId]);

  // Emit generation request
  const emitGenerationRequest = useCallback((prompt: string, type: 'image' | 'video') => {
    if (socketRef.current && state.sessionId) {
      socketRef.current.emit('generation-request', {
        sessionId: state.sessionId,
        prompt,
        type
      });
    }
  }, [state.sessionId]);

  return {
    ...state,
    joinSession,
    leaveSession,
    emitTextSelection,
    emitAnalysisRequest,
    emitGenerationRequest,
  };
}
