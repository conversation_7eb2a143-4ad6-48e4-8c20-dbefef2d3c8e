{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/node_modules/prosemirror-view/dist/index.js"], "sourcesContent": ["import { TextSelection, NodeSelection, AllSelection, Selection } from 'prosemirror-state';\nimport { DOMSerializer, Fragment, Mark, Slice, DOMParser } from 'prosemirror-model';\nimport { dropPoint } from 'prosemirror-transform';\n\nconst domIndex = function (node) {\n    for (var index = 0;; index++) {\n        node = node.previousSibling;\n        if (!node)\n            return index;\n    }\n};\nconst parentNode = function (node) {\n    let parent = node.assignedSlot || node.parentNode;\n    return parent && parent.nodeType == 11 ? parent.host : parent;\n};\nlet reusedRange = null;\n// Note that this will always return the same range, because DOM range\n// objects are every expensive, and keep slowing down subsequent DOM\n// updates, for some reason.\nconst textRange = function (node, from, to) {\n    let range = reusedRange || (reusedRange = document.createRange());\n    range.setEnd(node, to == null ? node.nodeValue.length : to);\n    range.setStart(node, from || 0);\n    return range;\n};\nconst clearReusedRange = function () {\n    reusedRange = null;\n};\n// Scans forward and backward through DOM positions equivalent to the\n// given one to see if the two are in the same place (i.e. after a\n// text node vs at the end of that text node)\nconst isEquivalentPosition = function (node, off, targetNode, targetOff) {\n    return targetNode && (scanFor(node, off, targetNode, targetOff, -1) ||\n        scanFor(node, off, targetNode, targetOff, 1));\n};\nconst atomElements = /^(img|br|input|textarea|hr)$/i;\nfunction scanFor(node, off, targetNode, targetOff, dir) {\n    var _a;\n    for (;;) {\n        if (node == targetNode && off == targetOff)\n            return true;\n        if (off == (dir < 0 ? 0 : nodeSize(node))) {\n            let parent = node.parentNode;\n            if (!parent || parent.nodeType != 1 || hasBlockDesc(node) || atomElements.test(node.nodeName) ||\n                node.contentEditable == \"false\")\n                return false;\n            off = domIndex(node) + (dir < 0 ? 0 : 1);\n            node = parent;\n        }\n        else if (node.nodeType == 1) {\n            let child = node.childNodes[off + (dir < 0 ? -1 : 0)];\n            if (child.nodeType == 1 && child.contentEditable == \"false\") {\n                if ((_a = child.pmViewDesc) === null || _a === void 0 ? void 0 : _a.ignoreForSelection)\n                    off += dir;\n                else\n                    return false;\n            }\n            else {\n                node = child;\n                off = dir < 0 ? nodeSize(node) : 0;\n            }\n        }\n        else {\n            return false;\n        }\n    }\n}\nfunction nodeSize(node) {\n    return node.nodeType == 3 ? node.nodeValue.length : node.childNodes.length;\n}\nfunction textNodeBefore$1(node, offset) {\n    for (;;) {\n        if (node.nodeType == 3 && offset)\n            return node;\n        if (node.nodeType == 1 && offset > 0) {\n            if (node.contentEditable == \"false\")\n                return null;\n            node = node.childNodes[offset - 1];\n            offset = nodeSize(node);\n        }\n        else if (node.parentNode && !hasBlockDesc(node)) {\n            offset = domIndex(node);\n            node = node.parentNode;\n        }\n        else {\n            return null;\n        }\n    }\n}\nfunction textNodeAfter$1(node, offset) {\n    for (;;) {\n        if (node.nodeType == 3 && offset < node.nodeValue.length)\n            return node;\n        if (node.nodeType == 1 && offset < node.childNodes.length) {\n            if (node.contentEditable == \"false\")\n                return null;\n            node = node.childNodes[offset];\n            offset = 0;\n        }\n        else if (node.parentNode && !hasBlockDesc(node)) {\n            offset = domIndex(node) + 1;\n            node = node.parentNode;\n        }\n        else {\n            return null;\n        }\n    }\n}\nfunction isOnEdge(node, offset, parent) {\n    for (let atStart = offset == 0, atEnd = offset == nodeSize(node); atStart || atEnd;) {\n        if (node == parent)\n            return true;\n        let index = domIndex(node);\n        node = node.parentNode;\n        if (!node)\n            return false;\n        atStart = atStart && index == 0;\n        atEnd = atEnd && index == nodeSize(node);\n    }\n}\nfunction hasBlockDesc(dom) {\n    let desc;\n    for (let cur = dom; cur; cur = cur.parentNode)\n        if (desc = cur.pmViewDesc)\n            break;\n    return desc && desc.node && desc.node.isBlock && (desc.dom == dom || desc.contentDOM == dom);\n}\n// Work around Chrome issue https://bugs.chromium.org/p/chromium/issues/detail?id=447523\n// (isCollapsed inappropriately returns true in shadow dom)\nconst selectionCollapsed = function (domSel) {\n    return domSel.focusNode && isEquivalentPosition(domSel.focusNode, domSel.focusOffset, domSel.anchorNode, domSel.anchorOffset);\n};\nfunction keyEvent(keyCode, key) {\n    let event = document.createEvent(\"Event\");\n    event.initEvent(\"keydown\", true, true);\n    event.keyCode = keyCode;\n    event.key = event.code = key;\n    return event;\n}\nfunction deepActiveElement(doc) {\n    let elt = doc.activeElement;\n    while (elt && elt.shadowRoot)\n        elt = elt.shadowRoot.activeElement;\n    return elt;\n}\nfunction caretFromPoint(doc, x, y) {\n    if (doc.caretPositionFromPoint) {\n        try { // Firefox throws for this call in hard-to-predict circumstances (#994)\n            let pos = doc.caretPositionFromPoint(x, y);\n            // Clip the offset, because Chrome will return a text offset\n            // into <input> nodes, which can't be treated as a regular DOM\n            // offset\n            if (pos)\n                return { node: pos.offsetNode, offset: Math.min(nodeSize(pos.offsetNode), pos.offset) };\n        }\n        catch (_) { }\n    }\n    if (doc.caretRangeFromPoint) {\n        let range = doc.caretRangeFromPoint(x, y);\n        if (range)\n            return { node: range.startContainer, offset: Math.min(nodeSize(range.startContainer), range.startOffset) };\n    }\n}\n\nconst nav = typeof navigator != \"undefined\" ? navigator : null;\nconst doc = typeof document != \"undefined\" ? document : null;\nconst agent = (nav && nav.userAgent) || \"\";\nconst ie_edge = /Edge\\/(\\d+)/.exec(agent);\nconst ie_upto10 = /MSIE \\d/.exec(agent);\nconst ie_11up = /Trident\\/(?:[7-9]|\\d{2,})\\..*rv:(\\d+)/.exec(agent);\nconst ie = !!(ie_upto10 || ie_11up || ie_edge);\nconst ie_version = ie_upto10 ? document.documentMode : ie_11up ? +ie_11up[1] : ie_edge ? +ie_edge[1] : 0;\nconst gecko = !ie && /gecko\\/(\\d+)/i.test(agent);\ngecko && +(/Firefox\\/(\\d+)/.exec(agent) || [0, 0])[1];\nconst _chrome = !ie && /Chrome\\/(\\d+)/.exec(agent);\nconst chrome = !!_chrome;\nconst chrome_version = _chrome ? +_chrome[1] : 0;\nconst safari = !ie && !!nav && /Apple Computer/.test(nav.vendor);\n// Is true for both iOS and iPadOS for convenience\nconst ios = safari && (/Mobile\\/\\w+/.test(agent) || !!nav && nav.maxTouchPoints > 2);\nconst mac = ios || (nav ? /Mac/.test(nav.platform) : false);\nconst windows = nav ? /Win/.test(nav.platform) : false;\nconst android = /Android \\d/.test(agent);\nconst webkit = !!doc && \"webkitFontSmoothing\" in doc.documentElement.style;\nconst webkit_version = webkit ? +(/\\bAppleWebKit\\/(\\d+)/.exec(navigator.userAgent) || [0, 0])[1] : 0;\n\nfunction windowRect(doc) {\n    let vp = doc.defaultView && doc.defaultView.visualViewport;\n    if (vp)\n        return {\n            left: 0, right: vp.width,\n            top: 0, bottom: vp.height\n        };\n    return { left: 0, right: doc.documentElement.clientWidth,\n        top: 0, bottom: doc.documentElement.clientHeight };\n}\nfunction getSide(value, side) {\n    return typeof value == \"number\" ? value : value[side];\n}\nfunction clientRect(node) {\n    let rect = node.getBoundingClientRect();\n    // Adjust for elements with style \"transform: scale()\"\n    let scaleX = (rect.width / node.offsetWidth) || 1;\n    let scaleY = (rect.height / node.offsetHeight) || 1;\n    // Make sure scrollbar width isn't included in the rectangle\n    return { left: rect.left, right: rect.left + node.clientWidth * scaleX,\n        top: rect.top, bottom: rect.top + node.clientHeight * scaleY };\n}\nfunction scrollRectIntoView(view, rect, startDOM) {\n    let scrollThreshold = view.someProp(\"scrollThreshold\") || 0, scrollMargin = view.someProp(\"scrollMargin\") || 5;\n    let doc = view.dom.ownerDocument;\n    for (let parent = startDOM || view.dom;;) {\n        if (!parent)\n            break;\n        if (parent.nodeType != 1) {\n            parent = parentNode(parent);\n            continue;\n        }\n        let elt = parent;\n        let atTop = elt == doc.body;\n        let bounding = atTop ? windowRect(doc) : clientRect(elt);\n        let moveX = 0, moveY = 0;\n        if (rect.top < bounding.top + getSide(scrollThreshold, \"top\"))\n            moveY = -(bounding.top - rect.top + getSide(scrollMargin, \"top\"));\n        else if (rect.bottom > bounding.bottom - getSide(scrollThreshold, \"bottom\"))\n            moveY = rect.bottom - rect.top > bounding.bottom - bounding.top\n                ? rect.top + getSide(scrollMargin, \"top\") - bounding.top\n                : rect.bottom - bounding.bottom + getSide(scrollMargin, \"bottom\");\n        if (rect.left < bounding.left + getSide(scrollThreshold, \"left\"))\n            moveX = -(bounding.left - rect.left + getSide(scrollMargin, \"left\"));\n        else if (rect.right > bounding.right - getSide(scrollThreshold, \"right\"))\n            moveX = rect.right - bounding.right + getSide(scrollMargin, \"right\");\n        if (moveX || moveY) {\n            if (atTop) {\n                doc.defaultView.scrollBy(moveX, moveY);\n            }\n            else {\n                let startX = elt.scrollLeft, startY = elt.scrollTop;\n                if (moveY)\n                    elt.scrollTop += moveY;\n                if (moveX)\n                    elt.scrollLeft += moveX;\n                let dX = elt.scrollLeft - startX, dY = elt.scrollTop - startY;\n                rect = { left: rect.left - dX, top: rect.top - dY, right: rect.right - dX, bottom: rect.bottom - dY };\n            }\n        }\n        let pos = atTop ? \"fixed\" : getComputedStyle(parent).position;\n        if (/^(fixed|sticky)$/.test(pos))\n            break;\n        parent = pos == \"absolute\" ? parent.offsetParent : parentNode(parent);\n    }\n}\n// Store the scroll position of the editor's parent nodes, along with\n// the top position of an element near the top of the editor, which\n// will be used to make sure the visible viewport remains stable even\n// when the size of the content above changes.\nfunction storeScrollPos(view) {\n    let rect = view.dom.getBoundingClientRect(), startY = Math.max(0, rect.top);\n    let refDOM, refTop;\n    for (let x = (rect.left + rect.right) / 2, y = startY + 1; y < Math.min(innerHeight, rect.bottom); y += 5) {\n        let dom = view.root.elementFromPoint(x, y);\n        if (!dom || dom == view.dom || !view.dom.contains(dom))\n            continue;\n        let localRect = dom.getBoundingClientRect();\n        if (localRect.top >= startY - 20) {\n            refDOM = dom;\n            refTop = localRect.top;\n            break;\n        }\n    }\n    return { refDOM: refDOM, refTop: refTop, stack: scrollStack(view.dom) };\n}\nfunction scrollStack(dom) {\n    let stack = [], doc = dom.ownerDocument;\n    for (let cur = dom; cur; cur = parentNode(cur)) {\n        stack.push({ dom: cur, top: cur.scrollTop, left: cur.scrollLeft });\n        if (dom == doc)\n            break;\n    }\n    return stack;\n}\n// Reset the scroll position of the editor's parent nodes to that what\n// it was before, when storeScrollPos was called.\nfunction resetScrollPos({ refDOM, refTop, stack }) {\n    let newRefTop = refDOM ? refDOM.getBoundingClientRect().top : 0;\n    restoreScrollStack(stack, newRefTop == 0 ? 0 : newRefTop - refTop);\n}\nfunction restoreScrollStack(stack, dTop) {\n    for (let i = 0; i < stack.length; i++) {\n        let { dom, top, left } = stack[i];\n        if (dom.scrollTop != top + dTop)\n            dom.scrollTop = top + dTop;\n        if (dom.scrollLeft != left)\n            dom.scrollLeft = left;\n    }\n}\nlet preventScrollSupported = null;\n// Feature-detects support for .focus({preventScroll: true}), and uses\n// a fallback kludge when not supported.\nfunction focusPreventScroll(dom) {\n    if (dom.setActive)\n        return dom.setActive(); // in IE\n    if (preventScrollSupported)\n        return dom.focus(preventScrollSupported);\n    let stored = scrollStack(dom);\n    dom.focus(preventScrollSupported == null ? {\n        get preventScroll() {\n            preventScrollSupported = { preventScroll: true };\n            return true;\n        }\n    } : undefined);\n    if (!preventScrollSupported) {\n        preventScrollSupported = false;\n        restoreScrollStack(stored, 0);\n    }\n}\nfunction findOffsetInNode(node, coords) {\n    let closest, dxClosest = 2e8, coordsClosest, offset = 0;\n    let rowBot = coords.top, rowTop = coords.top;\n    let firstBelow, coordsBelow;\n    for (let child = node.firstChild, childIndex = 0; child; child = child.nextSibling, childIndex++) {\n        let rects;\n        if (child.nodeType == 1)\n            rects = child.getClientRects();\n        else if (child.nodeType == 3)\n            rects = textRange(child).getClientRects();\n        else\n            continue;\n        for (let i = 0; i < rects.length; i++) {\n            let rect = rects[i];\n            if (rect.top <= rowBot && rect.bottom >= rowTop) {\n                rowBot = Math.max(rect.bottom, rowBot);\n                rowTop = Math.min(rect.top, rowTop);\n                let dx = rect.left > coords.left ? rect.left - coords.left\n                    : rect.right < coords.left ? coords.left - rect.right : 0;\n                if (dx < dxClosest) {\n                    closest = child;\n                    dxClosest = dx;\n                    coordsClosest = dx && closest.nodeType == 3 ? {\n                        left: rect.right < coords.left ? rect.right : rect.left,\n                        top: coords.top\n                    } : coords;\n                    if (child.nodeType == 1 && dx)\n                        offset = childIndex + (coords.left >= (rect.left + rect.right) / 2 ? 1 : 0);\n                    continue;\n                }\n            }\n            else if (rect.top > coords.top && !firstBelow && rect.left <= coords.left && rect.right >= coords.left) {\n                firstBelow = child;\n                coordsBelow = { left: Math.max(rect.left, Math.min(rect.right, coords.left)), top: rect.top };\n            }\n            if (!closest && (coords.left >= rect.right && coords.top >= rect.top ||\n                coords.left >= rect.left && coords.top >= rect.bottom))\n                offset = childIndex + 1;\n        }\n    }\n    if (!closest && firstBelow) {\n        closest = firstBelow;\n        coordsClosest = coordsBelow;\n        dxClosest = 0;\n    }\n    if (closest && closest.nodeType == 3)\n        return findOffsetInText(closest, coordsClosest);\n    if (!closest || (dxClosest && closest.nodeType == 1))\n        return { node, offset };\n    return findOffsetInNode(closest, coordsClosest);\n}\nfunction findOffsetInText(node, coords) {\n    let len = node.nodeValue.length;\n    let range = document.createRange();\n    for (let i = 0; i < len; i++) {\n        range.setEnd(node, i + 1);\n        range.setStart(node, i);\n        let rect = singleRect(range, 1);\n        if (rect.top == rect.bottom)\n            continue;\n        if (inRect(coords, rect))\n            return { node, offset: i + (coords.left >= (rect.left + rect.right) / 2 ? 1 : 0) };\n    }\n    return { node, offset: 0 };\n}\nfunction inRect(coords, rect) {\n    return coords.left >= rect.left - 1 && coords.left <= rect.right + 1 &&\n        coords.top >= rect.top - 1 && coords.top <= rect.bottom + 1;\n}\nfunction targetKludge(dom, coords) {\n    let parent = dom.parentNode;\n    if (parent && /^li$/i.test(parent.nodeName) && coords.left < dom.getBoundingClientRect().left)\n        return parent;\n    return dom;\n}\nfunction posFromElement(view, elt, coords) {\n    let { node, offset } = findOffsetInNode(elt, coords), bias = -1;\n    if (node.nodeType == 1 && !node.firstChild) {\n        let rect = node.getBoundingClientRect();\n        bias = rect.left != rect.right && coords.left > (rect.left + rect.right) / 2 ? 1 : -1;\n    }\n    return view.docView.posFromDOM(node, offset, bias);\n}\nfunction posFromCaret(view, node, offset, coords) {\n    // Browser (in caretPosition/RangeFromPoint) will agressively\n    // normalize towards nearby inline nodes. Since we are interested in\n    // positions between block nodes too, we first walk up the hierarchy\n    // of nodes to see if there are block nodes that the coordinates\n    // fall outside of. If so, we take the position before/after that\n    // block. If not, we call `posFromDOM` on the raw node/offset.\n    let outsideBlock = -1;\n    for (let cur = node, sawBlock = false;;) {\n        if (cur == view.dom)\n            break;\n        let desc = view.docView.nearestDesc(cur, true), rect;\n        if (!desc)\n            return null;\n        if (desc.dom.nodeType == 1 && (desc.node.isBlock && desc.parent || !desc.contentDOM) &&\n            // Ignore elements with zero-size bounding rectangles\n            ((rect = desc.dom.getBoundingClientRect()).width || rect.height)) {\n            if (desc.node.isBlock && desc.parent) {\n                // Only apply the horizontal test to the innermost block. Vertical for any parent.\n                if (!sawBlock && rect.left > coords.left || rect.top > coords.top)\n                    outsideBlock = desc.posBefore;\n                else if (!sawBlock && rect.right < coords.left || rect.bottom < coords.top)\n                    outsideBlock = desc.posAfter;\n                sawBlock = true;\n            }\n            if (!desc.contentDOM && outsideBlock < 0 && !desc.node.isText) {\n                // If we are inside a leaf, return the side of the leaf closer to the coords\n                let before = desc.node.isBlock ? coords.top < (rect.top + rect.bottom) / 2\n                    : coords.left < (rect.left + rect.right) / 2;\n                return before ? desc.posBefore : desc.posAfter;\n            }\n        }\n        cur = desc.dom.parentNode;\n    }\n    return outsideBlock > -1 ? outsideBlock : view.docView.posFromDOM(node, offset, -1);\n}\nfunction elementFromPoint(element, coords, box) {\n    let len = element.childNodes.length;\n    if (len && box.top < box.bottom) {\n        for (let startI = Math.max(0, Math.min(len - 1, Math.floor(len * (coords.top - box.top) / (box.bottom - box.top)) - 2)), i = startI;;) {\n            let child = element.childNodes[i];\n            if (child.nodeType == 1) {\n                let rects = child.getClientRects();\n                for (let j = 0; j < rects.length; j++) {\n                    let rect = rects[j];\n                    if (inRect(coords, rect))\n                        return elementFromPoint(child, coords, rect);\n                }\n            }\n            if ((i = (i + 1) % len) == startI)\n                break;\n        }\n    }\n    return element;\n}\n// Given an x,y position on the editor, get the position in the document.\nfunction posAtCoords(view, coords) {\n    let doc = view.dom.ownerDocument, node, offset = 0;\n    let caret = caretFromPoint(doc, coords.left, coords.top);\n    if (caret)\n        ({ node, offset } = caret);\n    let elt = (view.root.elementFromPoint ? view.root : doc)\n        .elementFromPoint(coords.left, coords.top);\n    let pos;\n    if (!elt || !view.dom.contains(elt.nodeType != 1 ? elt.parentNode : elt)) {\n        let box = view.dom.getBoundingClientRect();\n        if (!inRect(coords, box))\n            return null;\n        elt = elementFromPoint(view.dom, coords, box);\n        if (!elt)\n            return null;\n    }\n    // Safari's caretRangeFromPoint returns nonsense when on a draggable element\n    if (safari) {\n        for (let p = elt; node && p; p = parentNode(p))\n            if (p.draggable)\n                node = undefined;\n    }\n    elt = targetKludge(elt, coords);\n    if (node) {\n        if (gecko && node.nodeType == 1) {\n            // Firefox will sometimes return offsets into <input> nodes, which\n            // have no actual children, from caretPositionFromPoint (#953)\n            offset = Math.min(offset, node.childNodes.length);\n            // It'll also move the returned position before image nodes,\n            // even if those are behind it.\n            if (offset < node.childNodes.length) {\n                let next = node.childNodes[offset], box;\n                if (next.nodeName == \"IMG\" && (box = next.getBoundingClientRect()).right <= coords.left &&\n                    box.bottom > coords.top)\n                    offset++;\n            }\n        }\n        let prev;\n        // When clicking above the right side of an uneditable node, Chrome will report a cursor position after that node.\n        if (webkit && offset && node.nodeType == 1 && (prev = node.childNodes[offset - 1]).nodeType == 1 &&\n            prev.contentEditable == \"false\" && prev.getBoundingClientRect().top >= coords.top)\n            offset--;\n        // Suspiciously specific kludge to work around caret*FromPoint\n        // never returning a position at the end of the document\n        if (node == view.dom && offset == node.childNodes.length - 1 && node.lastChild.nodeType == 1 &&\n            coords.top > node.lastChild.getBoundingClientRect().bottom)\n            pos = view.state.doc.content.size;\n        // Ignore positions directly after a BR, since caret*FromPoint\n        // 'round up' positions that would be more accurately placed\n        // before the BR node.\n        else if (offset == 0 || node.nodeType != 1 || node.childNodes[offset - 1].nodeName != \"BR\")\n            pos = posFromCaret(view, node, offset, coords);\n    }\n    if (pos == null)\n        pos = posFromElement(view, elt, coords);\n    let desc = view.docView.nearestDesc(elt, true);\n    return { pos, inside: desc ? desc.posAtStart - desc.border : -1 };\n}\nfunction nonZero(rect) {\n    return rect.top < rect.bottom || rect.left < rect.right;\n}\nfunction singleRect(target, bias) {\n    let rects = target.getClientRects();\n    if (rects.length) {\n        let first = rects[bias < 0 ? 0 : rects.length - 1];\n        if (nonZero(first))\n            return first;\n    }\n    return Array.prototype.find.call(rects, nonZero) || target.getBoundingClientRect();\n}\nconst BIDI = /[\\u0590-\\u05f4\\u0600-\\u06ff\\u0700-\\u08ac]/;\n// Given a position in the document model, get a bounding box of the\n// character at that position, relative to the window.\nfunction coordsAtPos(view, pos, side) {\n    let { node, offset, atom } = view.docView.domFromPos(pos, side < 0 ? -1 : 1);\n    let supportEmptyRange = webkit || gecko;\n    if (node.nodeType == 3) {\n        // These browsers support querying empty text ranges. Prefer that in\n        // bidi context or when at the end of a node.\n        if (supportEmptyRange && (BIDI.test(node.nodeValue) || (side < 0 ? !offset : offset == node.nodeValue.length))) {\n            let rect = singleRect(textRange(node, offset, offset), side);\n            // Firefox returns bad results (the position before the space)\n            // when querying a position directly after line-broken\n            // whitespace. Detect this situation and and kludge around it\n            if (gecko && offset && /\\s/.test(node.nodeValue[offset - 1]) && offset < node.nodeValue.length) {\n                let rectBefore = singleRect(textRange(node, offset - 1, offset - 1), -1);\n                if (rectBefore.top == rect.top) {\n                    let rectAfter = singleRect(textRange(node, offset, offset + 1), -1);\n                    if (rectAfter.top != rect.top)\n                        return flattenV(rectAfter, rectAfter.left < rectBefore.left);\n                }\n            }\n            return rect;\n        }\n        else {\n            let from = offset, to = offset, takeSide = side < 0 ? 1 : -1;\n            if (side < 0 && !offset) {\n                to++;\n                takeSide = -1;\n            }\n            else if (side >= 0 && offset == node.nodeValue.length) {\n                from--;\n                takeSide = 1;\n            }\n            else if (side < 0) {\n                from--;\n            }\n            else {\n                to++;\n            }\n            return flattenV(singleRect(textRange(node, from, to), takeSide), takeSide < 0);\n        }\n    }\n    let $dom = view.state.doc.resolve(pos - (atom || 0));\n    // Return a horizontal line in block context\n    if (!$dom.parent.inlineContent) {\n        if (atom == null && offset && (side < 0 || offset == nodeSize(node))) {\n            let before = node.childNodes[offset - 1];\n            if (before.nodeType == 1)\n                return flattenH(before.getBoundingClientRect(), false);\n        }\n        if (atom == null && offset < nodeSize(node)) {\n            let after = node.childNodes[offset];\n            if (after.nodeType == 1)\n                return flattenH(after.getBoundingClientRect(), true);\n        }\n        return flattenH(node.getBoundingClientRect(), side >= 0);\n    }\n    // Inline, not in text node (this is not Bidi-safe)\n    if (atom == null && offset && (side < 0 || offset == nodeSize(node))) {\n        let before = node.childNodes[offset - 1];\n        let target = before.nodeType == 3 ? textRange(before, nodeSize(before) - (supportEmptyRange ? 0 : 1))\n            // BR nodes tend to only return the rectangle before them.\n            // Only use them if they are the last element in their parent\n            : before.nodeType == 1 && (before.nodeName != \"BR\" || !before.nextSibling) ? before : null;\n        if (target)\n            return flattenV(singleRect(target, 1), false);\n    }\n    if (atom == null && offset < nodeSize(node)) {\n        let after = node.childNodes[offset];\n        while (after.pmViewDesc && after.pmViewDesc.ignoreForCoords)\n            after = after.nextSibling;\n        let target = !after ? null : after.nodeType == 3 ? textRange(after, 0, (supportEmptyRange ? 0 : 1))\n            : after.nodeType == 1 ? after : null;\n        if (target)\n            return flattenV(singleRect(target, -1), true);\n    }\n    // All else failed, just try to get a rectangle for the target node\n    return flattenV(singleRect(node.nodeType == 3 ? textRange(node) : node, -side), side >= 0);\n}\nfunction flattenV(rect, left) {\n    if (rect.width == 0)\n        return rect;\n    let x = left ? rect.left : rect.right;\n    return { top: rect.top, bottom: rect.bottom, left: x, right: x };\n}\nfunction flattenH(rect, top) {\n    if (rect.height == 0)\n        return rect;\n    let y = top ? rect.top : rect.bottom;\n    return { top: y, bottom: y, left: rect.left, right: rect.right };\n}\nfunction withFlushedState(view, state, f) {\n    let viewState = view.state, active = view.root.activeElement;\n    if (viewState != state)\n        view.updateState(state);\n    if (active != view.dom)\n        view.focus();\n    try {\n        return f();\n    }\n    finally {\n        if (viewState != state)\n            view.updateState(viewState);\n        if (active != view.dom && active)\n            active.focus();\n    }\n}\n// Whether vertical position motion in a given direction\n// from a position would leave a text block.\nfunction endOfTextblockVertical(view, state, dir) {\n    let sel = state.selection;\n    let $pos = dir == \"up\" ? sel.$from : sel.$to;\n    return withFlushedState(view, state, () => {\n        let { node: dom } = view.docView.domFromPos($pos.pos, dir == \"up\" ? -1 : 1);\n        for (;;) {\n            let nearest = view.docView.nearestDesc(dom, true);\n            if (!nearest)\n                break;\n            if (nearest.node.isBlock) {\n                dom = nearest.contentDOM || nearest.dom;\n                break;\n            }\n            dom = nearest.dom.parentNode;\n        }\n        let coords = coordsAtPos(view, $pos.pos, 1);\n        for (let child = dom.firstChild; child; child = child.nextSibling) {\n            let boxes;\n            if (child.nodeType == 1)\n                boxes = child.getClientRects();\n            else if (child.nodeType == 3)\n                boxes = textRange(child, 0, child.nodeValue.length).getClientRects();\n            else\n                continue;\n            for (let i = 0; i < boxes.length; i++) {\n                let box = boxes[i];\n                if (box.bottom > box.top + 1 &&\n                    (dir == \"up\" ? coords.top - box.top > (box.bottom - coords.top) * 2\n                        : box.bottom - coords.bottom > (coords.bottom - box.top) * 2))\n                    return false;\n            }\n        }\n        return true;\n    });\n}\nconst maybeRTL = /[\\u0590-\\u08ac]/;\nfunction endOfTextblockHorizontal(view, state, dir) {\n    let { $head } = state.selection;\n    if (!$head.parent.isTextblock)\n        return false;\n    let offset = $head.parentOffset, atStart = !offset, atEnd = offset == $head.parent.content.size;\n    let sel = view.domSelection();\n    if (!sel)\n        return $head.pos == $head.start() || $head.pos == $head.end();\n    // If the textblock is all LTR, or the browser doesn't support\n    // Selection.modify (Edge), fall back to a primitive approach\n    if (!maybeRTL.test($head.parent.textContent) || !sel.modify)\n        return dir == \"left\" || dir == \"backward\" ? atStart : atEnd;\n    return withFlushedState(view, state, () => {\n        // This is a huge hack, but appears to be the best we can\n        // currently do: use `Selection.modify` to move the selection by\n        // one character, and see if that moves the cursor out of the\n        // textblock (or doesn't move it at all, when at the start/end of\n        // the document).\n        let { focusNode: oldNode, focusOffset: oldOff, anchorNode, anchorOffset } = view.domSelectionRange();\n        let oldBidiLevel = sel.caretBidiLevel // Only for Firefox\n        ;\n        sel.modify(\"move\", dir, \"character\");\n        let parentDOM = $head.depth ? view.docView.domAfterPos($head.before()) : view.dom;\n        let { focusNode: newNode, focusOffset: newOff } = view.domSelectionRange();\n        let result = newNode && !parentDOM.contains(newNode.nodeType == 1 ? newNode : newNode.parentNode) ||\n            (oldNode == newNode && oldOff == newOff);\n        // Restore the previous selection\n        try {\n            sel.collapse(anchorNode, anchorOffset);\n            if (oldNode && (oldNode != anchorNode || oldOff != anchorOffset) && sel.extend)\n                sel.extend(oldNode, oldOff);\n        }\n        catch (_) { }\n        if (oldBidiLevel != null)\n            sel.caretBidiLevel = oldBidiLevel;\n        return result;\n    });\n}\nlet cachedState = null;\nlet cachedDir = null;\nlet cachedResult = false;\nfunction endOfTextblock(view, state, dir) {\n    if (cachedState == state && cachedDir == dir)\n        return cachedResult;\n    cachedState = state;\n    cachedDir = dir;\n    return cachedResult = dir == \"up\" || dir == \"down\"\n        ? endOfTextblockVertical(view, state, dir)\n        : endOfTextblockHorizontal(view, state, dir);\n}\n\n// View descriptions are data structures that describe the DOM that is\n// used to represent the editor's content. They are used for:\n//\n// - Incremental redrawing when the document changes\n//\n// - Figuring out what part of the document a given DOM position\n//   corresponds to\n//\n// - Wiring in custom implementations of the editing interface for a\n//   given node\n//\n// They form a doubly-linked mutable tree, starting at `view.docView`.\nconst NOT_DIRTY = 0, CHILD_DIRTY = 1, CONTENT_DIRTY = 2, NODE_DIRTY = 3;\n// Superclass for the various kinds of descriptions. Defines their\n// basic structure and shared methods.\nclass ViewDesc {\n    constructor(parent, children, dom, \n    // This is the node that holds the child views. It may be null for\n    // descs that don't have children.\n    contentDOM) {\n        this.parent = parent;\n        this.children = children;\n        this.dom = dom;\n        this.contentDOM = contentDOM;\n        this.dirty = NOT_DIRTY;\n        // An expando property on the DOM node provides a link back to its\n        // description.\n        dom.pmViewDesc = this;\n    }\n    // Used to check whether a given description corresponds to a\n    // widget/mark/node.\n    matchesWidget(widget) { return false; }\n    matchesMark(mark) { return false; }\n    matchesNode(node, outerDeco, innerDeco) { return false; }\n    matchesHack(nodeName) { return false; }\n    // When parsing in-editor content (in domchange.js), we allow\n    // descriptions to determine the parse rules that should be used to\n    // parse them.\n    parseRule() { return null; }\n    // Used by the editor's event handler to ignore events that come\n    // from certain descs.\n    stopEvent(event) { return false; }\n    // The size of the content represented by this desc.\n    get size() {\n        let size = 0;\n        for (let i = 0; i < this.children.length; i++)\n            size += this.children[i].size;\n        return size;\n    }\n    // For block nodes, this represents the space taken up by their\n    // start/end tokens.\n    get border() { return 0; }\n    destroy() {\n        this.parent = undefined;\n        if (this.dom.pmViewDesc == this)\n            this.dom.pmViewDesc = undefined;\n        for (let i = 0; i < this.children.length; i++)\n            this.children[i].destroy();\n    }\n    posBeforeChild(child) {\n        for (let i = 0, pos = this.posAtStart;; i++) {\n            let cur = this.children[i];\n            if (cur == child)\n                return pos;\n            pos += cur.size;\n        }\n    }\n    get posBefore() {\n        return this.parent.posBeforeChild(this);\n    }\n    get posAtStart() {\n        return this.parent ? this.parent.posBeforeChild(this) + this.border : 0;\n    }\n    get posAfter() {\n        return this.posBefore + this.size;\n    }\n    get posAtEnd() {\n        return this.posAtStart + this.size - 2 * this.border;\n    }\n    localPosFromDOM(dom, offset, bias) {\n        // If the DOM position is in the content, use the child desc after\n        // it to figure out a position.\n        if (this.contentDOM && this.contentDOM.contains(dom.nodeType == 1 ? dom : dom.parentNode)) {\n            if (bias < 0) {\n                let domBefore, desc;\n                if (dom == this.contentDOM) {\n                    domBefore = dom.childNodes[offset - 1];\n                }\n                else {\n                    while (dom.parentNode != this.contentDOM)\n                        dom = dom.parentNode;\n                    domBefore = dom.previousSibling;\n                }\n                while (domBefore && !((desc = domBefore.pmViewDesc) && desc.parent == this))\n                    domBefore = domBefore.previousSibling;\n                return domBefore ? this.posBeforeChild(desc) + desc.size : this.posAtStart;\n            }\n            else {\n                let domAfter, desc;\n                if (dom == this.contentDOM) {\n                    domAfter = dom.childNodes[offset];\n                }\n                else {\n                    while (dom.parentNode != this.contentDOM)\n                        dom = dom.parentNode;\n                    domAfter = dom.nextSibling;\n                }\n                while (domAfter && !((desc = domAfter.pmViewDesc) && desc.parent == this))\n                    domAfter = domAfter.nextSibling;\n                return domAfter ? this.posBeforeChild(desc) : this.posAtEnd;\n            }\n        }\n        // Otherwise, use various heuristics, falling back on the bias\n        // parameter, to determine whether to return the position at the\n        // start or at the end of this view desc.\n        let atEnd;\n        if (dom == this.dom && this.contentDOM) {\n            atEnd = offset > domIndex(this.contentDOM);\n        }\n        else if (this.contentDOM && this.contentDOM != this.dom && this.dom.contains(this.contentDOM)) {\n            atEnd = dom.compareDocumentPosition(this.contentDOM) & 2;\n        }\n        else if (this.dom.firstChild) {\n            if (offset == 0)\n                for (let search = dom;; search = search.parentNode) {\n                    if (search == this.dom) {\n                        atEnd = false;\n                        break;\n                    }\n                    if (search.previousSibling)\n                        break;\n                }\n            if (atEnd == null && offset == dom.childNodes.length)\n                for (let search = dom;; search = search.parentNode) {\n                    if (search == this.dom) {\n                        atEnd = true;\n                        break;\n                    }\n                    if (search.nextSibling)\n                        break;\n                }\n        }\n        return (atEnd == null ? bias > 0 : atEnd) ? this.posAtEnd : this.posAtStart;\n    }\n    nearestDesc(dom, onlyNodes = false) {\n        for (let first = true, cur = dom; cur; cur = cur.parentNode) {\n            let desc = this.getDesc(cur), nodeDOM;\n            if (desc && (!onlyNodes || desc.node)) {\n                // If dom is outside of this desc's nodeDOM, don't count it.\n                if (first && (nodeDOM = desc.nodeDOM) &&\n                    !(nodeDOM.nodeType == 1 ? nodeDOM.contains(dom.nodeType == 1 ? dom : dom.parentNode) : nodeDOM == dom))\n                    first = false;\n                else\n                    return desc;\n            }\n        }\n    }\n    getDesc(dom) {\n        let desc = dom.pmViewDesc;\n        for (let cur = desc; cur; cur = cur.parent)\n            if (cur == this)\n                return desc;\n    }\n    posFromDOM(dom, offset, bias) {\n        for (let scan = dom; scan; scan = scan.parentNode) {\n            let desc = this.getDesc(scan);\n            if (desc)\n                return desc.localPosFromDOM(dom, offset, bias);\n        }\n        return -1;\n    }\n    // Find the desc for the node after the given pos, if any. (When a\n    // parent node overrode rendering, there might not be one.)\n    descAt(pos) {\n        for (let i = 0, offset = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (offset == pos && end != offset) {\n                while (!child.border && child.children.length) {\n                    for (let i = 0; i < child.children.length; i++) {\n                        let inner = child.children[i];\n                        if (inner.size) {\n                            child = inner;\n                            break;\n                        }\n                    }\n                }\n                return child;\n            }\n            if (pos < end)\n                return child.descAt(pos - offset - child.border);\n            offset = end;\n        }\n    }\n    domFromPos(pos, side) {\n        if (!this.contentDOM)\n            return { node: this.dom, offset: 0, atom: pos + 1 };\n        // First find the position in the child array\n        let i = 0, offset = 0;\n        for (let curPos = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = curPos + child.size;\n            if (end > pos || child instanceof TrailingHackViewDesc) {\n                offset = pos - curPos;\n                break;\n            }\n            curPos = end;\n        }\n        // If this points into the middle of a child, call through\n        if (offset)\n            return this.children[i].domFromPos(offset - this.children[i].border, side);\n        // Go back if there were any zero-length widgets with side >= 0 before this point\n        for (let prev; i && !(prev = this.children[i - 1]).size && prev instanceof WidgetViewDesc && prev.side >= 0; i--) { }\n        // Scan towards the first useable node\n        if (side <= 0) {\n            let prev, enter = true;\n            for (;; i--, enter = false) {\n                prev = i ? this.children[i - 1] : null;\n                if (!prev || prev.dom.parentNode == this.contentDOM)\n                    break;\n            }\n            if (prev && side && enter && !prev.border && !prev.domAtom)\n                return prev.domFromPos(prev.size, side);\n            return { node: this.contentDOM, offset: prev ? domIndex(prev.dom) + 1 : 0 };\n        }\n        else {\n            let next, enter = true;\n            for (;; i++, enter = false) {\n                next = i < this.children.length ? this.children[i] : null;\n                if (!next || next.dom.parentNode == this.contentDOM)\n                    break;\n            }\n            if (next && enter && !next.border && !next.domAtom)\n                return next.domFromPos(0, side);\n            return { node: this.contentDOM, offset: next ? domIndex(next.dom) : this.contentDOM.childNodes.length };\n        }\n    }\n    // Used to find a DOM range in a single parent for a given changed\n    // range.\n    parseRange(from, to, base = 0) {\n        if (this.children.length == 0)\n            return { node: this.contentDOM, from, to, fromOffset: 0, toOffset: this.contentDOM.childNodes.length };\n        let fromOffset = -1, toOffset = -1;\n        for (let offset = base, i = 0;; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (fromOffset == -1 && from <= end) {\n                let childBase = offset + child.border;\n                // FIXME maybe descend mark views to parse a narrower range?\n                if (from >= childBase && to <= end - child.border && child.node &&\n                    child.contentDOM && this.contentDOM.contains(child.contentDOM))\n                    return child.parseRange(from, to, childBase);\n                from = offset;\n                for (let j = i; j > 0; j--) {\n                    let prev = this.children[j - 1];\n                    if (prev.size && prev.dom.parentNode == this.contentDOM && !prev.emptyChildAt(1)) {\n                        fromOffset = domIndex(prev.dom) + 1;\n                        break;\n                    }\n                    from -= prev.size;\n                }\n                if (fromOffset == -1)\n                    fromOffset = 0;\n            }\n            if (fromOffset > -1 && (end > to || i == this.children.length - 1)) {\n                to = end;\n                for (let j = i + 1; j < this.children.length; j++) {\n                    let next = this.children[j];\n                    if (next.size && next.dom.parentNode == this.contentDOM && !next.emptyChildAt(-1)) {\n                        toOffset = domIndex(next.dom);\n                        break;\n                    }\n                    to += next.size;\n                }\n                if (toOffset == -1)\n                    toOffset = this.contentDOM.childNodes.length;\n                break;\n            }\n            offset = end;\n        }\n        return { node: this.contentDOM, from, to, fromOffset, toOffset };\n    }\n    emptyChildAt(side) {\n        if (this.border || !this.contentDOM || !this.children.length)\n            return false;\n        let child = this.children[side < 0 ? 0 : this.children.length - 1];\n        return child.size == 0 || child.emptyChildAt(side);\n    }\n    domAfterPos(pos) {\n        let { node, offset } = this.domFromPos(pos, 0);\n        if (node.nodeType != 1 || offset == node.childNodes.length)\n            throw new RangeError(\"No node after pos \" + pos);\n        return node.childNodes[offset];\n    }\n    // View descs are responsible for setting any selection that falls\n    // entirely inside of them, so that custom implementations can do\n    // custom things with the selection. Note that this falls apart when\n    // a selection starts in such a node and ends in another, in which\n    // case we just use whatever domFromPos produces as a best effort.\n    setSelection(anchor, head, view, force = false) {\n        // If the selection falls entirely in a child, give it to that child\n        let from = Math.min(anchor, head), to = Math.max(anchor, head);\n        for (let i = 0, offset = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (from > offset && to < end)\n                return child.setSelection(anchor - offset - child.border, head - offset - child.border, view, force);\n            offset = end;\n        }\n        let anchorDOM = this.domFromPos(anchor, anchor ? -1 : 1);\n        let headDOM = head == anchor ? anchorDOM : this.domFromPos(head, head ? -1 : 1);\n        let domSel = view.root.getSelection();\n        let selRange = view.domSelectionRange();\n        let brKludge = false;\n        // On Firefox, using Selection.collapse to put the cursor after a\n        // BR node for some reason doesn't always work (#1073). On Safari,\n        // the cursor sometimes inexplicable visually lags behind its\n        // reported position in such situations (#1092).\n        if ((gecko || safari) && anchor == head) {\n            let { node, offset } = anchorDOM;\n            if (node.nodeType == 3) {\n                brKludge = !!(offset && node.nodeValue[offset - 1] == \"\\n\");\n                // Issue #1128\n                if (brKludge && offset == node.nodeValue.length) {\n                    for (let scan = node, after; scan; scan = scan.parentNode) {\n                        if (after = scan.nextSibling) {\n                            if (after.nodeName == \"BR\")\n                                anchorDOM = headDOM = { node: after.parentNode, offset: domIndex(after) + 1 };\n                            break;\n                        }\n                        let desc = scan.pmViewDesc;\n                        if (desc && desc.node && desc.node.isBlock)\n                            break;\n                    }\n                }\n            }\n            else {\n                let prev = node.childNodes[offset - 1];\n                brKludge = prev && (prev.nodeName == \"BR\" || prev.contentEditable == \"false\");\n            }\n        }\n        // Firefox can act strangely when the selection is in front of an\n        // uneditable node. See #1163 and https://bugzilla.mozilla.org/show_bug.cgi?id=1709536\n        if (gecko && selRange.focusNode && selRange.focusNode != headDOM.node && selRange.focusNode.nodeType == 1) {\n            let after = selRange.focusNode.childNodes[selRange.focusOffset];\n            if (after && after.contentEditable == \"false\")\n                force = true;\n        }\n        if (!(force || brKludge && safari) &&\n            isEquivalentPosition(anchorDOM.node, anchorDOM.offset, selRange.anchorNode, selRange.anchorOffset) &&\n            isEquivalentPosition(headDOM.node, headDOM.offset, selRange.focusNode, selRange.focusOffset))\n            return;\n        // Selection.extend can be used to create an 'inverted' selection\n        // (one where the focus is before the anchor), but not all\n        // browsers support it yet.\n        let domSelExtended = false;\n        if ((domSel.extend || anchor == head) && !brKludge) {\n            domSel.collapse(anchorDOM.node, anchorDOM.offset);\n            try {\n                if (anchor != head)\n                    domSel.extend(headDOM.node, headDOM.offset);\n                domSelExtended = true;\n            }\n            catch (_) {\n                // In some cases with Chrome the selection is empty after calling\n                // collapse, even when it should be valid. This appears to be a bug, but\n                // it is difficult to isolate. If this happens fallback to the old path\n                // without using extend.\n                // Similarly, this could crash on Safari if the editor is hidden, and\n                // there was no selection.\n            }\n        }\n        if (!domSelExtended) {\n            if (anchor > head) {\n                let tmp = anchorDOM;\n                anchorDOM = headDOM;\n                headDOM = tmp;\n            }\n            let range = document.createRange();\n            range.setEnd(headDOM.node, headDOM.offset);\n            range.setStart(anchorDOM.node, anchorDOM.offset);\n            domSel.removeAllRanges();\n            domSel.addRange(range);\n        }\n    }\n    ignoreMutation(mutation) {\n        return !this.contentDOM && mutation.type != \"selection\";\n    }\n    get contentLost() {\n        return this.contentDOM && this.contentDOM != this.dom && !this.dom.contains(this.contentDOM);\n    }\n    // Remove a subtree of the element tree that has been touched\n    // by a DOM change, so that the next update will redraw it.\n    markDirty(from, to) {\n        for (let offset = 0, i = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (offset == end ? from <= end && to >= offset : from < end && to > offset) {\n                let startInside = offset + child.border, endInside = end - child.border;\n                if (from >= startInside && to <= endInside) {\n                    this.dirty = from == offset || to == end ? CONTENT_DIRTY : CHILD_DIRTY;\n                    if (from == startInside && to == endInside &&\n                        (child.contentLost || child.dom.parentNode != this.contentDOM))\n                        child.dirty = NODE_DIRTY;\n                    else\n                        child.markDirty(from - startInside, to - startInside);\n                    return;\n                }\n                else {\n                    child.dirty = child.dom == child.contentDOM && child.dom.parentNode == this.contentDOM && !child.children.length\n                        ? CONTENT_DIRTY : NODE_DIRTY;\n                }\n            }\n            offset = end;\n        }\n        this.dirty = CONTENT_DIRTY;\n    }\n    markParentsDirty() {\n        let level = 1;\n        for (let node = this.parent; node; node = node.parent, level++) {\n            let dirty = level == 1 ? CONTENT_DIRTY : CHILD_DIRTY;\n            if (node.dirty < dirty)\n                node.dirty = dirty;\n        }\n    }\n    get domAtom() { return false; }\n    get ignoreForCoords() { return false; }\n    get ignoreForSelection() { return false; }\n    isText(text) { return false; }\n}\n// A widget desc represents a widget decoration, which is a DOM node\n// drawn between the document nodes.\nclass WidgetViewDesc extends ViewDesc {\n    constructor(parent, widget, view, pos) {\n        let self, dom = widget.type.toDOM;\n        if (typeof dom == \"function\")\n            dom = dom(view, () => {\n                if (!self)\n                    return pos;\n                if (self.parent)\n                    return self.parent.posBeforeChild(self);\n            });\n        if (!widget.type.spec.raw) {\n            if (dom.nodeType != 1) {\n                let wrap = document.createElement(\"span\");\n                wrap.appendChild(dom);\n                dom = wrap;\n            }\n            dom.contentEditable = \"false\";\n            dom.classList.add(\"ProseMirror-widget\");\n        }\n        super(parent, [], dom, null);\n        this.widget = widget;\n        this.widget = widget;\n        self = this;\n    }\n    matchesWidget(widget) {\n        return this.dirty == NOT_DIRTY && widget.type.eq(this.widget.type);\n    }\n    parseRule() { return { ignore: true }; }\n    stopEvent(event) {\n        let stop = this.widget.spec.stopEvent;\n        return stop ? stop(event) : false;\n    }\n    ignoreMutation(mutation) {\n        return mutation.type != \"selection\" || this.widget.spec.ignoreSelection;\n    }\n    destroy() {\n        this.widget.type.destroy(this.dom);\n        super.destroy();\n    }\n    get domAtom() { return true; }\n    get ignoreForSelection() { return !!this.widget.type.spec.relaxedSide; }\n    get side() { return this.widget.type.side; }\n}\nclass CompositionViewDesc extends ViewDesc {\n    constructor(parent, dom, textDOM, text) {\n        super(parent, [], dom, null);\n        this.textDOM = textDOM;\n        this.text = text;\n    }\n    get size() { return this.text.length; }\n    localPosFromDOM(dom, offset) {\n        if (dom != this.textDOM)\n            return this.posAtStart + (offset ? this.size : 0);\n        return this.posAtStart + offset;\n    }\n    domFromPos(pos) {\n        return { node: this.textDOM, offset: pos };\n    }\n    ignoreMutation(mut) {\n        return mut.type === 'characterData' && mut.target.nodeValue == mut.oldValue;\n    }\n}\n// A mark desc represents a mark. May have multiple children,\n// depending on how the mark is split. Note that marks are drawn using\n// a fixed nesting order, for simplicity and predictability, so in\n// some cases they will be split more often than would appear\n// necessary.\nclass MarkViewDesc extends ViewDesc {\n    constructor(parent, mark, dom, contentDOM, spec) {\n        super(parent, [], dom, contentDOM);\n        this.mark = mark;\n        this.spec = spec;\n    }\n    static create(parent, mark, inline, view) {\n        let custom = view.nodeViews[mark.type.name];\n        let spec = custom && custom(mark, view, inline);\n        if (!spec || !spec.dom)\n            spec = DOMSerializer.renderSpec(document, mark.type.spec.toDOM(mark, inline), null, mark.attrs);\n        return new MarkViewDesc(parent, mark, spec.dom, spec.contentDOM || spec.dom, spec);\n    }\n    parseRule() {\n        if ((this.dirty & NODE_DIRTY) || this.mark.type.spec.reparseInView)\n            return null;\n        return { mark: this.mark.type.name, attrs: this.mark.attrs, contentElement: this.contentDOM };\n    }\n    matchesMark(mark) { return this.dirty != NODE_DIRTY && this.mark.eq(mark); }\n    markDirty(from, to) {\n        super.markDirty(from, to);\n        // Move dirty info to nearest node view\n        if (this.dirty != NOT_DIRTY) {\n            let parent = this.parent;\n            while (!parent.node)\n                parent = parent.parent;\n            if (parent.dirty < this.dirty)\n                parent.dirty = this.dirty;\n            this.dirty = NOT_DIRTY;\n        }\n    }\n    slice(from, to, view) {\n        let copy = MarkViewDesc.create(this.parent, this.mark, true, view);\n        let nodes = this.children, size = this.size;\n        if (to < size)\n            nodes = replaceNodes(nodes, to, size, view);\n        if (from > 0)\n            nodes = replaceNodes(nodes, 0, from, view);\n        for (let i = 0; i < nodes.length; i++)\n            nodes[i].parent = copy;\n        copy.children = nodes;\n        return copy;\n    }\n    ignoreMutation(mutation) {\n        return this.spec.ignoreMutation ? this.spec.ignoreMutation(mutation) : super.ignoreMutation(mutation);\n    }\n    destroy() {\n        if (this.spec.destroy)\n            this.spec.destroy();\n        super.destroy();\n    }\n}\n// Node view descs are the main, most common type of view desc, and\n// correspond to an actual node in the document. Unlike mark descs,\n// they populate their child array themselves.\nclass NodeViewDesc extends ViewDesc {\n    constructor(parent, node, outerDeco, innerDeco, dom, contentDOM, nodeDOM, view, pos) {\n        super(parent, [], dom, contentDOM);\n        this.node = node;\n        this.outerDeco = outerDeco;\n        this.innerDeco = innerDeco;\n        this.nodeDOM = nodeDOM;\n    }\n    // By default, a node is rendered using the `toDOM` method from the\n    // node type spec. But client code can use the `nodeViews` spec to\n    // supply a custom node view, which can influence various aspects of\n    // the way the node works.\n    //\n    // (Using subclassing for this was intentionally decided against,\n    // since it'd require exposing a whole slew of finicky\n    // implementation details to the user code that they probably will\n    // never need.)\n    static create(parent, node, outerDeco, innerDeco, view, pos) {\n        let custom = view.nodeViews[node.type.name], descObj;\n        let spec = custom && custom(node, view, () => {\n            // (This is a function that allows the custom view to find its\n            // own position)\n            if (!descObj)\n                return pos;\n            if (descObj.parent)\n                return descObj.parent.posBeforeChild(descObj);\n        }, outerDeco, innerDeco);\n        let dom = spec && spec.dom, contentDOM = spec && spec.contentDOM;\n        if (node.isText) {\n            if (!dom)\n                dom = document.createTextNode(node.text);\n            else if (dom.nodeType != 3)\n                throw new RangeError(\"Text must be rendered as a DOM text node\");\n        }\n        else if (!dom) {\n            let spec = DOMSerializer.renderSpec(document, node.type.spec.toDOM(node), null, node.attrs);\n            ({ dom, contentDOM } = spec);\n        }\n        if (!contentDOM && !node.isText && dom.nodeName != \"BR\") { // Chrome gets confused by <br contenteditable=false>\n            if (!dom.hasAttribute(\"contenteditable\"))\n                dom.contentEditable = \"false\";\n            if (node.type.spec.draggable)\n                dom.draggable = true;\n        }\n        let nodeDOM = dom;\n        dom = applyOuterDeco(dom, outerDeco, node);\n        if (spec)\n            return descObj = new CustomNodeViewDesc(parent, node, outerDeco, innerDeco, dom, contentDOM || null, nodeDOM, spec, view, pos + 1);\n        else if (node.isText)\n            return new TextViewDesc(parent, node, outerDeco, innerDeco, dom, nodeDOM, view);\n        else\n            return new NodeViewDesc(parent, node, outerDeco, innerDeco, dom, contentDOM || null, nodeDOM, view, pos + 1);\n    }\n    parseRule() {\n        // Experimental kludge to allow opt-in re-parsing of nodes\n        if (this.node.type.spec.reparseInView)\n            return null;\n        // FIXME the assumption that this can always return the current\n        // attrs means that if the user somehow manages to change the\n        // attrs in the dom, that won't be picked up. Not entirely sure\n        // whether this is a problem\n        let rule = { node: this.node.type.name, attrs: this.node.attrs };\n        if (this.node.type.whitespace == \"pre\")\n            rule.preserveWhitespace = \"full\";\n        if (!this.contentDOM) {\n            rule.getContent = () => this.node.content;\n        }\n        else if (!this.contentLost) {\n            rule.contentElement = this.contentDOM;\n        }\n        else {\n            // Chrome likes to randomly recreate parent nodes when\n            // backspacing things. When that happens, this tries to find the\n            // new parent.\n            for (let i = this.children.length - 1; i >= 0; i--) {\n                let child = this.children[i];\n                if (this.dom.contains(child.dom.parentNode)) {\n                    rule.contentElement = child.dom.parentNode;\n                    break;\n                }\n            }\n            if (!rule.contentElement)\n                rule.getContent = () => Fragment.empty;\n        }\n        return rule;\n    }\n    matchesNode(node, outerDeco, innerDeco) {\n        return this.dirty == NOT_DIRTY && node.eq(this.node) &&\n            sameOuterDeco(outerDeco, this.outerDeco) && innerDeco.eq(this.innerDeco);\n    }\n    get size() { return this.node.nodeSize; }\n    get border() { return this.node.isLeaf ? 0 : 1; }\n    // Syncs `this.children` to match `this.node.content` and the local\n    // decorations, possibly introducing nesting for marks. Then, in a\n    // separate step, syncs the DOM inside `this.contentDOM` to\n    // `this.children`.\n    updateChildren(view, pos) {\n        let inline = this.node.inlineContent, off = pos;\n        let composition = view.composing ? this.localCompositionInfo(view, pos) : null;\n        let localComposition = composition && composition.pos > -1 ? composition : null;\n        let compositionInChild = composition && composition.pos < 0;\n        let updater = new ViewTreeUpdater(this, localComposition && localComposition.node, view);\n        iterDeco(this.node, this.innerDeco, (widget, i, insideNode) => {\n            if (widget.spec.marks)\n                updater.syncToMarks(widget.spec.marks, inline, view);\n            else if (widget.type.side >= 0 && !insideNode)\n                updater.syncToMarks(i == this.node.childCount ? Mark.none : this.node.child(i).marks, inline, view);\n            // If the next node is a desc matching this widget, reuse it,\n            // otherwise insert the widget as a new view desc.\n            updater.placeWidget(widget, view, off);\n        }, (child, outerDeco, innerDeco, i) => {\n            // Make sure the wrapping mark descs match the node's marks.\n            updater.syncToMarks(child.marks, inline, view);\n            // Try several strategies for drawing this node\n            let compIndex;\n            if (updater.findNodeMatch(child, outerDeco, innerDeco, i)) ;\n            else if (compositionInChild && view.state.selection.from > off &&\n                view.state.selection.to < off + child.nodeSize &&\n                (compIndex = updater.findIndexWithChild(composition.node)) > -1 &&\n                updater.updateNodeAt(child, outerDeco, innerDeco, compIndex, view)) ;\n            else if (updater.updateNextNode(child, outerDeco, innerDeco, view, i, off)) ;\n            else {\n                // Add it as a new view\n                updater.addNode(child, outerDeco, innerDeco, view, off);\n            }\n            off += child.nodeSize;\n        });\n        // Drop all remaining descs after the current position.\n        updater.syncToMarks([], inline, view);\n        if (this.node.isTextblock)\n            updater.addTextblockHacks();\n        updater.destroyRest();\n        // Sync the DOM if anything changed\n        if (updater.changed || this.dirty == CONTENT_DIRTY) {\n            // May have to protect focused DOM from being changed if a composition is active\n            if (localComposition)\n                this.protectLocalComposition(view, localComposition);\n            renderDescs(this.contentDOM, this.children, view);\n            if (ios)\n                iosHacks(this.dom);\n        }\n    }\n    localCompositionInfo(view, pos) {\n        // Only do something if both the selection and a focused text node\n        // are inside of this node\n        let { from, to } = view.state.selection;\n        if (!(view.state.selection instanceof TextSelection) || from < pos || to > pos + this.node.content.size)\n            return null;\n        let textNode = view.input.compositionNode;\n        if (!textNode || !this.dom.contains(textNode.parentNode))\n            return null;\n        if (this.node.inlineContent) {\n            // Find the text in the focused node in the node, stop if it's not\n            // there (may have been modified through other means, in which\n            // case it should overwritten)\n            let text = textNode.nodeValue;\n            let textPos = findTextInFragment(this.node.content, text, from - pos, to - pos);\n            return textPos < 0 ? null : { node: textNode, pos: textPos, text };\n        }\n        else {\n            return { node: textNode, pos: -1, text: \"\" };\n        }\n    }\n    protectLocalComposition(view, { node, pos, text }) {\n        // The node is already part of a local view desc, leave it there\n        if (this.getDesc(node))\n            return;\n        // Create a composition view for the orphaned nodes\n        let topNode = node;\n        for (;; topNode = topNode.parentNode) {\n            if (topNode.parentNode == this.contentDOM)\n                break;\n            while (topNode.previousSibling)\n                topNode.parentNode.removeChild(topNode.previousSibling);\n            while (topNode.nextSibling)\n                topNode.parentNode.removeChild(topNode.nextSibling);\n            if (topNode.pmViewDesc)\n                topNode.pmViewDesc = undefined;\n        }\n        let desc = new CompositionViewDesc(this, topNode, node, text);\n        view.input.compositionNodes.push(desc);\n        // Patch up this.children to contain the composition view\n        this.children = replaceNodes(this.children, pos, pos + text.length, view, desc);\n    }\n    // If this desc must be updated to match the given node decoration,\n    // do so and return true.\n    update(node, outerDeco, innerDeco, view) {\n        if (this.dirty == NODE_DIRTY ||\n            !node.sameMarkup(this.node))\n            return false;\n        this.updateInner(node, outerDeco, innerDeco, view);\n        return true;\n    }\n    updateInner(node, outerDeco, innerDeco, view) {\n        this.updateOuterDeco(outerDeco);\n        this.node = node;\n        this.innerDeco = innerDeco;\n        if (this.contentDOM)\n            this.updateChildren(view, this.posAtStart);\n        this.dirty = NOT_DIRTY;\n    }\n    updateOuterDeco(outerDeco) {\n        if (sameOuterDeco(outerDeco, this.outerDeco))\n            return;\n        let needsWrap = this.nodeDOM.nodeType != 1;\n        let oldDOM = this.dom;\n        this.dom = patchOuterDeco(this.dom, this.nodeDOM, computeOuterDeco(this.outerDeco, this.node, needsWrap), computeOuterDeco(outerDeco, this.node, needsWrap));\n        if (this.dom != oldDOM) {\n            oldDOM.pmViewDesc = undefined;\n            this.dom.pmViewDesc = this;\n        }\n        this.outerDeco = outerDeco;\n    }\n    // Mark this node as being the selected node.\n    selectNode() {\n        if (this.nodeDOM.nodeType == 1)\n            this.nodeDOM.classList.add(\"ProseMirror-selectednode\");\n        if (this.contentDOM || !this.node.type.spec.draggable)\n            this.dom.draggable = true;\n    }\n    // Remove selected node marking from this node.\n    deselectNode() {\n        if (this.nodeDOM.nodeType == 1) {\n            this.nodeDOM.classList.remove(\"ProseMirror-selectednode\");\n            if (this.contentDOM || !this.node.type.spec.draggable)\n                this.dom.removeAttribute(\"draggable\");\n        }\n    }\n    get domAtom() { return this.node.isAtom; }\n}\n// Create a view desc for the top-level document node, to be exported\n// and used by the view class.\nfunction docViewDesc(doc, outerDeco, innerDeco, dom, view) {\n    applyOuterDeco(dom, outerDeco, doc);\n    let docView = new NodeViewDesc(undefined, doc, outerDeco, innerDeco, dom, dom, dom, view, 0);\n    if (docView.contentDOM)\n        docView.updateChildren(view, 0);\n    return docView;\n}\nclass TextViewDesc extends NodeViewDesc {\n    constructor(parent, node, outerDeco, innerDeco, dom, nodeDOM, view) {\n        super(parent, node, outerDeco, innerDeco, dom, null, nodeDOM, view, 0);\n    }\n    parseRule() {\n        let skip = this.nodeDOM.parentNode;\n        while (skip && skip != this.dom && !skip.pmIsDeco)\n            skip = skip.parentNode;\n        return { skip: (skip || true) };\n    }\n    update(node, outerDeco, innerDeco, view) {\n        if (this.dirty == NODE_DIRTY || (this.dirty != NOT_DIRTY && !this.inParent()) ||\n            !node.sameMarkup(this.node))\n            return false;\n        this.updateOuterDeco(outerDeco);\n        if ((this.dirty != NOT_DIRTY || node.text != this.node.text) && node.text != this.nodeDOM.nodeValue) {\n            this.nodeDOM.nodeValue = node.text;\n            if (view.trackWrites == this.nodeDOM)\n                view.trackWrites = null;\n        }\n        this.node = node;\n        this.dirty = NOT_DIRTY;\n        return true;\n    }\n    inParent() {\n        let parentDOM = this.parent.contentDOM;\n        for (let n = this.nodeDOM; n; n = n.parentNode)\n            if (n == parentDOM)\n                return true;\n        return false;\n    }\n    domFromPos(pos) {\n        return { node: this.nodeDOM, offset: pos };\n    }\n    localPosFromDOM(dom, offset, bias) {\n        if (dom == this.nodeDOM)\n            return this.posAtStart + Math.min(offset, this.node.text.length);\n        return super.localPosFromDOM(dom, offset, bias);\n    }\n    ignoreMutation(mutation) {\n        return mutation.type != \"characterData\" && mutation.type != \"selection\";\n    }\n    slice(from, to, view) {\n        let node = this.node.cut(from, to), dom = document.createTextNode(node.text);\n        return new TextViewDesc(this.parent, node, this.outerDeco, this.innerDeco, dom, dom, view);\n    }\n    markDirty(from, to) {\n        super.markDirty(from, to);\n        if (this.dom != this.nodeDOM && (from == 0 || to == this.nodeDOM.nodeValue.length))\n            this.dirty = NODE_DIRTY;\n    }\n    get domAtom() { return false; }\n    isText(text) { return this.node.text == text; }\n}\n// A dummy desc used to tag trailing BR or IMG nodes created to work\n// around contentEditable terribleness.\nclass TrailingHackViewDesc extends ViewDesc {\n    parseRule() { return { ignore: true }; }\n    matchesHack(nodeName) { return this.dirty == NOT_DIRTY && this.dom.nodeName == nodeName; }\n    get domAtom() { return true; }\n    get ignoreForCoords() { return this.dom.nodeName == \"IMG\"; }\n}\n// A separate subclass is used for customized node views, so that the\n// extra checks only have to be made for nodes that are actually\n// customized.\nclass CustomNodeViewDesc extends NodeViewDesc {\n    constructor(parent, node, outerDeco, innerDeco, dom, contentDOM, nodeDOM, spec, view, pos) {\n        super(parent, node, outerDeco, innerDeco, dom, contentDOM, nodeDOM, view, pos);\n        this.spec = spec;\n    }\n    // A custom `update` method gets to decide whether the update goes\n    // through. If it does, and there's a `contentDOM` node, our logic\n    // updates the children.\n    update(node, outerDeco, innerDeco, view) {\n        if (this.dirty == NODE_DIRTY)\n            return false;\n        if (this.spec.update && (this.node.type == node.type || this.spec.multiType)) {\n            let result = this.spec.update(node, outerDeco, innerDeco);\n            if (result)\n                this.updateInner(node, outerDeco, innerDeco, view);\n            return result;\n        }\n        else if (!this.contentDOM && !node.isLeaf) {\n            return false;\n        }\n        else {\n            return super.update(node, outerDeco, innerDeco, view);\n        }\n    }\n    selectNode() {\n        this.spec.selectNode ? this.spec.selectNode() : super.selectNode();\n    }\n    deselectNode() {\n        this.spec.deselectNode ? this.spec.deselectNode() : super.deselectNode();\n    }\n    setSelection(anchor, head, view, force) {\n        this.spec.setSelection ? this.spec.setSelection(anchor, head, view.root)\n            : super.setSelection(anchor, head, view, force);\n    }\n    destroy() {\n        if (this.spec.destroy)\n            this.spec.destroy();\n        super.destroy();\n    }\n    stopEvent(event) {\n        return this.spec.stopEvent ? this.spec.stopEvent(event) : false;\n    }\n    ignoreMutation(mutation) {\n        return this.spec.ignoreMutation ? this.spec.ignoreMutation(mutation) : super.ignoreMutation(mutation);\n    }\n}\n// Sync the content of the given DOM node with the nodes associated\n// with the given array of view descs, recursing into mark descs\n// because this should sync the subtree for a whole node at a time.\nfunction renderDescs(parentDOM, descs, view) {\n    let dom = parentDOM.firstChild, written = false;\n    for (let i = 0; i < descs.length; i++) {\n        let desc = descs[i], childDOM = desc.dom;\n        if (childDOM.parentNode == parentDOM) {\n            while (childDOM != dom) {\n                dom = rm(dom);\n                written = true;\n            }\n            dom = dom.nextSibling;\n        }\n        else {\n            written = true;\n            parentDOM.insertBefore(childDOM, dom);\n        }\n        if (desc instanceof MarkViewDesc) {\n            let pos = dom ? dom.previousSibling : parentDOM.lastChild;\n            renderDescs(desc.contentDOM, desc.children, view);\n            dom = pos ? pos.nextSibling : parentDOM.firstChild;\n        }\n    }\n    while (dom) {\n        dom = rm(dom);\n        written = true;\n    }\n    if (written && view.trackWrites == parentDOM)\n        view.trackWrites = null;\n}\nconst OuterDecoLevel = function (nodeName) {\n    if (nodeName)\n        this.nodeName = nodeName;\n};\nOuterDecoLevel.prototype = Object.create(null);\nconst noDeco = [new OuterDecoLevel];\nfunction computeOuterDeco(outerDeco, node, needsWrap) {\n    if (outerDeco.length == 0)\n        return noDeco;\n    let top = needsWrap ? noDeco[0] : new OuterDecoLevel, result = [top];\n    for (let i = 0; i < outerDeco.length; i++) {\n        let attrs = outerDeco[i].type.attrs;\n        if (!attrs)\n            continue;\n        if (attrs.nodeName)\n            result.push(top = new OuterDecoLevel(attrs.nodeName));\n        for (let name in attrs) {\n            let val = attrs[name];\n            if (val == null)\n                continue;\n            if (needsWrap && result.length == 1)\n                result.push(top = new OuterDecoLevel(node.isInline ? \"span\" : \"div\"));\n            if (name == \"class\")\n                top.class = (top.class ? top.class + \" \" : \"\") + val;\n            else if (name == \"style\")\n                top.style = (top.style ? top.style + \";\" : \"\") + val;\n            else if (name != \"nodeName\")\n                top[name] = val;\n        }\n    }\n    return result;\n}\nfunction patchOuterDeco(outerDOM, nodeDOM, prevComputed, curComputed) {\n    // Shortcut for trivial case\n    if (prevComputed == noDeco && curComputed == noDeco)\n        return nodeDOM;\n    let curDOM = nodeDOM;\n    for (let i = 0; i < curComputed.length; i++) {\n        let deco = curComputed[i], prev = prevComputed[i];\n        if (i) {\n            let parent;\n            if (prev && prev.nodeName == deco.nodeName && curDOM != outerDOM &&\n                (parent = curDOM.parentNode) && parent.nodeName.toLowerCase() == deco.nodeName) {\n                curDOM = parent;\n            }\n            else {\n                parent = document.createElement(deco.nodeName);\n                parent.pmIsDeco = true;\n                parent.appendChild(curDOM);\n                prev = noDeco[0];\n                curDOM = parent;\n            }\n        }\n        patchAttributes(curDOM, prev || noDeco[0], deco);\n    }\n    return curDOM;\n}\nfunction patchAttributes(dom, prev, cur) {\n    for (let name in prev)\n        if (name != \"class\" && name != \"style\" && name != \"nodeName\" && !(name in cur))\n            dom.removeAttribute(name);\n    for (let name in cur)\n        if (name != \"class\" && name != \"style\" && name != \"nodeName\" && cur[name] != prev[name])\n            dom.setAttribute(name, cur[name]);\n    if (prev.class != cur.class) {\n        let prevList = prev.class ? prev.class.split(\" \").filter(Boolean) : [];\n        let curList = cur.class ? cur.class.split(\" \").filter(Boolean) : [];\n        for (let i = 0; i < prevList.length; i++)\n            if (curList.indexOf(prevList[i]) == -1)\n                dom.classList.remove(prevList[i]);\n        for (let i = 0; i < curList.length; i++)\n            if (prevList.indexOf(curList[i]) == -1)\n                dom.classList.add(curList[i]);\n        if (dom.classList.length == 0)\n            dom.removeAttribute(\"class\");\n    }\n    if (prev.style != cur.style) {\n        if (prev.style) {\n            let prop = /\\s*([\\w\\-\\xa1-\\uffff]+)\\s*:(?:\"(?:\\\\.|[^\"])*\"|'(?:\\\\.|[^'])*'|\\(.*?\\)|[^;])*/g, m;\n            while (m = prop.exec(prev.style))\n                dom.style.removeProperty(m[1]);\n        }\n        if (cur.style)\n            dom.style.cssText += cur.style;\n    }\n}\nfunction applyOuterDeco(dom, deco, node) {\n    return patchOuterDeco(dom, dom, noDeco, computeOuterDeco(deco, node, dom.nodeType != 1));\n}\nfunction sameOuterDeco(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (!a[i].type.eq(b[i].type))\n            return false;\n    return true;\n}\n// Remove a DOM node and return its next sibling.\nfunction rm(dom) {\n    let next = dom.nextSibling;\n    dom.parentNode.removeChild(dom);\n    return next;\n}\n// Helper class for incrementally updating a tree of mark descs and\n// the widget and node descs inside of them.\nclass ViewTreeUpdater {\n    constructor(top, lock, view) {\n        this.lock = lock;\n        this.view = view;\n        // Index into `this.top`'s child array, represents the current\n        // update position.\n        this.index = 0;\n        // When entering a mark, the current top and index are pushed\n        // onto this.\n        this.stack = [];\n        // Tracks whether anything was changed\n        this.changed = false;\n        this.top = top;\n        this.preMatch = preMatch(top.node.content, top);\n    }\n    // Destroy and remove the children between the given indices in\n    // `this.top`.\n    destroyBetween(start, end) {\n        if (start == end)\n            return;\n        for (let i = start; i < end; i++)\n            this.top.children[i].destroy();\n        this.top.children.splice(start, end - start);\n        this.changed = true;\n    }\n    // Destroy all remaining children in `this.top`.\n    destroyRest() {\n        this.destroyBetween(this.index, this.top.children.length);\n    }\n    // Sync the current stack of mark descs with the given array of\n    // marks, reusing existing mark descs when possible.\n    syncToMarks(marks, inline, view) {\n        let keep = 0, depth = this.stack.length >> 1;\n        let maxKeep = Math.min(depth, marks.length);\n        while (keep < maxKeep &&\n            (keep == depth - 1 ? this.top : this.stack[(keep + 1) << 1])\n                .matchesMark(marks[keep]) && marks[keep].type.spec.spanning !== false)\n            keep++;\n        while (keep < depth) {\n            this.destroyRest();\n            this.top.dirty = NOT_DIRTY;\n            this.index = this.stack.pop();\n            this.top = this.stack.pop();\n            depth--;\n        }\n        while (depth < marks.length) {\n            this.stack.push(this.top, this.index + 1);\n            let found = -1;\n            for (let i = this.index; i < Math.min(this.index + 3, this.top.children.length); i++) {\n                let next = this.top.children[i];\n                if (next.matchesMark(marks[depth]) && !this.isLocked(next.dom)) {\n                    found = i;\n                    break;\n                }\n            }\n            if (found > -1) {\n                if (found > this.index) {\n                    this.changed = true;\n                    this.destroyBetween(this.index, found);\n                }\n                this.top = this.top.children[this.index];\n            }\n            else {\n                let markDesc = MarkViewDesc.create(this.top, marks[depth], inline, view);\n                this.top.children.splice(this.index, 0, markDesc);\n                this.top = markDesc;\n                this.changed = true;\n            }\n            this.index = 0;\n            depth++;\n        }\n    }\n    // Try to find a node desc matching the given data. Skip over it and\n    // return true when successful.\n    findNodeMatch(node, outerDeco, innerDeco, index) {\n        let found = -1, targetDesc;\n        if (index >= this.preMatch.index &&\n            (targetDesc = this.preMatch.matches[index - this.preMatch.index]).parent == this.top &&\n            targetDesc.matchesNode(node, outerDeco, innerDeco)) {\n            found = this.top.children.indexOf(targetDesc, this.index);\n        }\n        else {\n            for (let i = this.index, e = Math.min(this.top.children.length, i + 5); i < e; i++) {\n                let child = this.top.children[i];\n                if (child.matchesNode(node, outerDeco, innerDeco) && !this.preMatch.matched.has(child)) {\n                    found = i;\n                    break;\n                }\n            }\n        }\n        if (found < 0)\n            return false;\n        this.destroyBetween(this.index, found);\n        this.index++;\n        return true;\n    }\n    updateNodeAt(node, outerDeco, innerDeco, index, view) {\n        let child = this.top.children[index];\n        if (child.dirty == NODE_DIRTY && child.dom == child.contentDOM)\n            child.dirty = CONTENT_DIRTY;\n        if (!child.update(node, outerDeco, innerDeco, view))\n            return false;\n        this.destroyBetween(this.index, index);\n        this.index++;\n        return true;\n    }\n    findIndexWithChild(domNode) {\n        for (;;) {\n            let parent = domNode.parentNode;\n            if (!parent)\n                return -1;\n            if (parent == this.top.contentDOM) {\n                let desc = domNode.pmViewDesc;\n                if (desc)\n                    for (let i = this.index; i < this.top.children.length; i++) {\n                        if (this.top.children[i] == desc)\n                            return i;\n                    }\n                return -1;\n            }\n            domNode = parent;\n        }\n    }\n    // Try to update the next node, if any, to the given data. Checks\n    // pre-matches to avoid overwriting nodes that could still be used.\n    updateNextNode(node, outerDeco, innerDeco, view, index, pos) {\n        for (let i = this.index; i < this.top.children.length; i++) {\n            let next = this.top.children[i];\n            if (next instanceof NodeViewDesc) {\n                let preMatch = this.preMatch.matched.get(next);\n                if (preMatch != null && preMatch != index)\n                    return false;\n                let nextDOM = next.dom, updated;\n                // Can't update if nextDOM is or contains this.lock, except if\n                // it's a text node whose content already matches the new text\n                // and whose decorations match the new ones.\n                let locked = this.isLocked(nextDOM) &&\n                    !(node.isText && next.node && next.node.isText && next.nodeDOM.nodeValue == node.text &&\n                        next.dirty != NODE_DIRTY && sameOuterDeco(outerDeco, next.outerDeco));\n                if (!locked && next.update(node, outerDeco, innerDeco, view)) {\n                    this.destroyBetween(this.index, i);\n                    if (next.dom != nextDOM)\n                        this.changed = true;\n                    this.index++;\n                    return true;\n                }\n                else if (!locked && (updated = this.recreateWrapper(next, node, outerDeco, innerDeco, view, pos))) {\n                    this.destroyBetween(this.index, i);\n                    this.top.children[this.index] = updated;\n                    if (updated.contentDOM) {\n                        updated.dirty = CONTENT_DIRTY;\n                        updated.updateChildren(view, pos + 1);\n                        updated.dirty = NOT_DIRTY;\n                    }\n                    this.changed = true;\n                    this.index++;\n                    return true;\n                }\n                break;\n            }\n        }\n        return false;\n    }\n    // When a node with content is replaced by a different node with\n    // identical content, move over its children.\n    recreateWrapper(next, node, outerDeco, innerDeco, view, pos) {\n        if (next.dirty || node.isAtom || !next.children.length ||\n            !next.node.content.eq(node.content) ||\n            !sameOuterDeco(outerDeco, next.outerDeco) || !innerDeco.eq(next.innerDeco))\n            return null;\n        let wrapper = NodeViewDesc.create(this.top, node, outerDeco, innerDeco, view, pos);\n        if (wrapper.contentDOM) {\n            wrapper.children = next.children;\n            next.children = [];\n            for (let ch of wrapper.children)\n                ch.parent = wrapper;\n        }\n        next.destroy();\n        return wrapper;\n    }\n    // Insert the node as a newly created node desc.\n    addNode(node, outerDeco, innerDeco, view, pos) {\n        let desc = NodeViewDesc.create(this.top, node, outerDeco, innerDeco, view, pos);\n        if (desc.contentDOM)\n            desc.updateChildren(view, pos + 1);\n        this.top.children.splice(this.index++, 0, desc);\n        this.changed = true;\n    }\n    placeWidget(widget, view, pos) {\n        let next = this.index < this.top.children.length ? this.top.children[this.index] : null;\n        if (next && next.matchesWidget(widget) &&\n            (widget == next.widget || !next.widget.type.toDOM.parentNode)) {\n            this.index++;\n        }\n        else {\n            let desc = new WidgetViewDesc(this.top, widget, view, pos);\n            this.top.children.splice(this.index++, 0, desc);\n            this.changed = true;\n        }\n    }\n    // Make sure a textblock looks and behaves correctly in\n    // contentEditable.\n    addTextblockHacks() {\n        let lastChild = this.top.children[this.index - 1], parent = this.top;\n        while (lastChild instanceof MarkViewDesc) {\n            parent = lastChild;\n            lastChild = parent.children[parent.children.length - 1];\n        }\n        if (!lastChild || // Empty textblock\n            !(lastChild instanceof TextViewDesc) ||\n            /\\n$/.test(lastChild.node.text) ||\n            (this.view.requiresGeckoHackNode && /\\s$/.test(lastChild.node.text))) {\n            // Avoid bugs in Safari's cursor drawing (#1165) and Chrome's mouse selection (#1152)\n            if ((safari || chrome) && lastChild && lastChild.dom.contentEditable == \"false\")\n                this.addHackNode(\"IMG\", parent);\n            this.addHackNode(\"BR\", this.top);\n        }\n    }\n    addHackNode(nodeName, parent) {\n        if (parent == this.top && this.index < parent.children.length && parent.children[this.index].matchesHack(nodeName)) {\n            this.index++;\n        }\n        else {\n            let dom = document.createElement(nodeName);\n            if (nodeName == \"IMG\") {\n                dom.className = \"ProseMirror-separator\";\n                dom.alt = \"\";\n            }\n            if (nodeName == \"BR\")\n                dom.className = \"ProseMirror-trailingBreak\";\n            let hack = new TrailingHackViewDesc(this.top, [], dom, null);\n            if (parent != this.top)\n                parent.children.push(hack);\n            else\n                parent.children.splice(this.index++, 0, hack);\n            this.changed = true;\n        }\n    }\n    isLocked(node) {\n        return this.lock && (node == this.lock || node.nodeType == 1 && node.contains(this.lock.parentNode));\n    }\n}\n// Iterate from the end of the fragment and array of descs to find\n// directly matching ones, in order to avoid overeagerly reusing those\n// for other nodes. Returns the fragment index of the first node that\n// is part of the sequence of matched nodes at the end of the\n// fragment.\nfunction preMatch(frag, parentDesc) {\n    let curDesc = parentDesc, descI = curDesc.children.length;\n    let fI = frag.childCount, matched = new Map, matches = [];\n    outer: while (fI > 0) {\n        let desc;\n        for (;;) {\n            if (descI) {\n                let next = curDesc.children[descI - 1];\n                if (next instanceof MarkViewDesc) {\n                    curDesc = next;\n                    descI = next.children.length;\n                }\n                else {\n                    desc = next;\n                    descI--;\n                    break;\n                }\n            }\n            else if (curDesc == parentDesc) {\n                break outer;\n            }\n            else {\n                // FIXME\n                descI = curDesc.parent.children.indexOf(curDesc);\n                curDesc = curDesc.parent;\n            }\n        }\n        let node = desc.node;\n        if (!node)\n            continue;\n        if (node != frag.child(fI - 1))\n            break;\n        --fI;\n        matched.set(desc, fI);\n        matches.push(desc);\n    }\n    return { index: fI, matched, matches: matches.reverse() };\n}\nfunction compareSide(a, b) {\n    return a.type.side - b.type.side;\n}\n// This function abstracts iterating over the nodes and decorations in\n// a fragment. Calls `onNode` for each node, with its local and child\n// decorations. Splits text nodes when there is a decoration starting\n// or ending inside of them. Calls `onWidget` for each widget.\nfunction iterDeco(parent, deco, onWidget, onNode) {\n    let locals = deco.locals(parent), offset = 0;\n    // Simple, cheap variant for when there are no local decorations\n    if (locals.length == 0) {\n        for (let i = 0; i < parent.childCount; i++) {\n            let child = parent.child(i);\n            onNode(child, locals, deco.forChild(offset, child), i);\n            offset += child.nodeSize;\n        }\n        return;\n    }\n    let decoIndex = 0, active = [], restNode = null;\n    for (let parentIndex = 0;;) {\n        let widget, widgets;\n        while (decoIndex < locals.length && locals[decoIndex].to == offset) {\n            let next = locals[decoIndex++];\n            if (next.widget) {\n                if (!widget)\n                    widget = next;\n                else\n                    (widgets || (widgets = [widget])).push(next);\n            }\n        }\n        if (widget) {\n            if (widgets) {\n                widgets.sort(compareSide);\n                for (let i = 0; i < widgets.length; i++)\n                    onWidget(widgets[i], parentIndex, !!restNode);\n            }\n            else {\n                onWidget(widget, parentIndex, !!restNode);\n            }\n        }\n        let child, index;\n        if (restNode) {\n            index = -1;\n            child = restNode;\n            restNode = null;\n        }\n        else if (parentIndex < parent.childCount) {\n            index = parentIndex;\n            child = parent.child(parentIndex++);\n        }\n        else {\n            break;\n        }\n        for (let i = 0; i < active.length; i++)\n            if (active[i].to <= offset)\n                active.splice(i--, 1);\n        while (decoIndex < locals.length && locals[decoIndex].from <= offset && locals[decoIndex].to > offset)\n            active.push(locals[decoIndex++]);\n        let end = offset + child.nodeSize;\n        if (child.isText) {\n            let cutAt = end;\n            if (decoIndex < locals.length && locals[decoIndex].from < cutAt)\n                cutAt = locals[decoIndex].from;\n            for (let i = 0; i < active.length; i++)\n                if (active[i].to < cutAt)\n                    cutAt = active[i].to;\n            if (cutAt < end) {\n                restNode = child.cut(cutAt - offset);\n                child = child.cut(0, cutAt - offset);\n                end = cutAt;\n                index = -1;\n            }\n        }\n        else {\n            while (decoIndex < locals.length && locals[decoIndex].to < end)\n                decoIndex++;\n        }\n        let outerDeco = child.isInline && !child.isLeaf ? active.filter(d => !d.inline) : active.slice();\n        onNode(child, outerDeco, deco.forChild(offset, child), index);\n        offset = end;\n    }\n}\n// List markers in Mobile Safari will mysteriously disappear\n// sometimes. This works around that.\nfunction iosHacks(dom) {\n    if (dom.nodeName == \"UL\" || dom.nodeName == \"OL\") {\n        let oldCSS = dom.style.cssText;\n        dom.style.cssText = oldCSS + \"; list-style: square !important\";\n        window.getComputedStyle(dom).listStyle;\n        dom.style.cssText = oldCSS;\n    }\n}\n// Find a piece of text in an inline fragment, overlapping from-to\nfunction findTextInFragment(frag, text, from, to) {\n    for (let i = 0, pos = 0; i < frag.childCount && pos <= to;) {\n        let child = frag.child(i++), childStart = pos;\n        pos += child.nodeSize;\n        if (!child.isText)\n            continue;\n        let str = child.text;\n        while (i < frag.childCount) {\n            let next = frag.child(i++);\n            pos += next.nodeSize;\n            if (!next.isText)\n                break;\n            str += next.text;\n        }\n        if (pos >= from) {\n            if (pos >= to && str.slice(to - text.length - childStart, to - childStart) == text)\n                return to - text.length;\n            let found = childStart < to ? str.lastIndexOf(text, to - childStart - 1) : -1;\n            if (found >= 0 && found + text.length + childStart >= from)\n                return childStart + found;\n            if (from == to && str.length >= (to + text.length) - childStart &&\n                str.slice(to - childStart, to - childStart + text.length) == text)\n                return to;\n        }\n    }\n    return -1;\n}\n// Replace range from-to in an array of view descs with replacement\n// (may be null to just delete). This goes very much against the grain\n// of the rest of this code, which tends to create nodes with the\n// right shape in one go, rather than messing with them after\n// creation, but is necessary in the composition hack.\nfunction replaceNodes(nodes, from, to, view, replacement) {\n    let result = [];\n    for (let i = 0, off = 0; i < nodes.length; i++) {\n        let child = nodes[i], start = off, end = off += child.size;\n        if (start >= to || end <= from) {\n            result.push(child);\n        }\n        else {\n            if (start < from)\n                result.push(child.slice(0, from - start, view));\n            if (replacement) {\n                result.push(replacement);\n                replacement = undefined;\n            }\n            if (end > to)\n                result.push(child.slice(to - start, child.size, view));\n        }\n    }\n    return result;\n}\n\nfunction selectionFromDOM(view, origin = null) {\n    let domSel = view.domSelectionRange(), doc = view.state.doc;\n    if (!domSel.focusNode)\n        return null;\n    let nearestDesc = view.docView.nearestDesc(domSel.focusNode), inWidget = nearestDesc && nearestDesc.size == 0;\n    let head = view.docView.posFromDOM(domSel.focusNode, domSel.focusOffset, 1);\n    if (head < 0)\n        return null;\n    let $head = doc.resolve(head), anchor, selection;\n    if (selectionCollapsed(domSel)) {\n        anchor = head;\n        while (nearestDesc && !nearestDesc.node)\n            nearestDesc = nearestDesc.parent;\n        let nearestDescNode = nearestDesc.node;\n        if (nearestDesc && nearestDescNode.isAtom && NodeSelection.isSelectable(nearestDescNode) && nearestDesc.parent\n            && !(nearestDescNode.isInline && isOnEdge(domSel.focusNode, domSel.focusOffset, nearestDesc.dom))) {\n            let pos = nearestDesc.posBefore;\n            selection = new NodeSelection(head == pos ? $head : doc.resolve(pos));\n        }\n    }\n    else {\n        if (domSel instanceof view.dom.ownerDocument.defaultView.Selection && domSel.rangeCount > 1) {\n            let min = head, max = head;\n            for (let i = 0; i < domSel.rangeCount; i++) {\n                let range = domSel.getRangeAt(i);\n                min = Math.min(min, view.docView.posFromDOM(range.startContainer, range.startOffset, 1));\n                max = Math.max(max, view.docView.posFromDOM(range.endContainer, range.endOffset, -1));\n            }\n            if (min < 0)\n                return null;\n            [anchor, head] = max == view.state.selection.anchor ? [max, min] : [min, max];\n            $head = doc.resolve(head);\n        }\n        else {\n            anchor = view.docView.posFromDOM(domSel.anchorNode, domSel.anchorOffset, 1);\n        }\n        if (anchor < 0)\n            return null;\n    }\n    let $anchor = doc.resolve(anchor);\n    if (!selection) {\n        let bias = origin == \"pointer\" || (view.state.selection.head < $head.pos && !inWidget) ? 1 : -1;\n        selection = selectionBetween(view, $anchor, $head, bias);\n    }\n    return selection;\n}\nfunction editorOwnsSelection(view) {\n    return view.editable ? view.hasFocus() :\n        hasSelection(view) && document.activeElement && document.activeElement.contains(view.dom);\n}\nfunction selectionToDOM(view, force = false) {\n    let sel = view.state.selection;\n    syncNodeSelection(view, sel);\n    if (!editorOwnsSelection(view))\n        return;\n    // The delayed drag selection causes issues with Cell Selections\n    // in Safari. And the drag selection delay is to workarond issues\n    // which only present in Chrome.\n    if (!force && view.input.mouseDown && view.input.mouseDown.allowDefault && chrome) {\n        let domSel = view.domSelectionRange(), curSel = view.domObserver.currentSelection;\n        if (domSel.anchorNode && curSel.anchorNode &&\n            isEquivalentPosition(domSel.anchorNode, domSel.anchorOffset, curSel.anchorNode, curSel.anchorOffset)) {\n            view.input.mouseDown.delayedSelectionSync = true;\n            view.domObserver.setCurSelection();\n            return;\n        }\n    }\n    view.domObserver.disconnectSelection();\n    if (view.cursorWrapper) {\n        selectCursorWrapper(view);\n    }\n    else {\n        let { anchor, head } = sel, resetEditableFrom, resetEditableTo;\n        if (brokenSelectBetweenUneditable && !(sel instanceof TextSelection)) {\n            if (!sel.$from.parent.inlineContent)\n                resetEditableFrom = temporarilyEditableNear(view, sel.from);\n            if (!sel.empty && !sel.$from.parent.inlineContent)\n                resetEditableTo = temporarilyEditableNear(view, sel.to);\n        }\n        view.docView.setSelection(anchor, head, view, force);\n        if (brokenSelectBetweenUneditable) {\n            if (resetEditableFrom)\n                resetEditable(resetEditableFrom);\n            if (resetEditableTo)\n                resetEditable(resetEditableTo);\n        }\n        if (sel.visible) {\n            view.dom.classList.remove(\"ProseMirror-hideselection\");\n        }\n        else {\n            view.dom.classList.add(\"ProseMirror-hideselection\");\n            if (\"onselectionchange\" in document)\n                removeClassOnSelectionChange(view);\n        }\n    }\n    view.domObserver.setCurSelection();\n    view.domObserver.connectSelection();\n}\n// Kludge to work around Webkit not allowing a selection to start/end\n// between non-editable block nodes. We briefly make something\n// editable, set the selection, then set it uneditable again.\nconst brokenSelectBetweenUneditable = safari || chrome && chrome_version < 63;\nfunction temporarilyEditableNear(view, pos) {\n    let { node, offset } = view.docView.domFromPos(pos, 0);\n    let after = offset < node.childNodes.length ? node.childNodes[offset] : null;\n    let before = offset ? node.childNodes[offset - 1] : null;\n    if (safari && after && after.contentEditable == \"false\")\n        return setEditable(after);\n    if ((!after || after.contentEditable == \"false\") &&\n        (!before || before.contentEditable == \"false\")) {\n        if (after)\n            return setEditable(after);\n        else if (before)\n            return setEditable(before);\n    }\n}\nfunction setEditable(element) {\n    element.contentEditable = \"true\";\n    if (safari && element.draggable) {\n        element.draggable = false;\n        element.wasDraggable = true;\n    }\n    return element;\n}\nfunction resetEditable(element) {\n    element.contentEditable = \"false\";\n    if (element.wasDraggable) {\n        element.draggable = true;\n        element.wasDraggable = null;\n    }\n}\nfunction removeClassOnSelectionChange(view) {\n    let doc = view.dom.ownerDocument;\n    doc.removeEventListener(\"selectionchange\", view.input.hideSelectionGuard);\n    let domSel = view.domSelectionRange();\n    let node = domSel.anchorNode, offset = domSel.anchorOffset;\n    doc.addEventListener(\"selectionchange\", view.input.hideSelectionGuard = () => {\n        if (domSel.anchorNode != node || domSel.anchorOffset != offset) {\n            doc.removeEventListener(\"selectionchange\", view.input.hideSelectionGuard);\n            setTimeout(() => {\n                if (!editorOwnsSelection(view) || view.state.selection.visible)\n                    view.dom.classList.remove(\"ProseMirror-hideselection\");\n            }, 20);\n        }\n    });\n}\nfunction selectCursorWrapper(view) {\n    let domSel = view.domSelection(), range = document.createRange();\n    if (!domSel)\n        return;\n    let node = view.cursorWrapper.dom, img = node.nodeName == \"IMG\";\n    if (img)\n        range.setStart(node.parentNode, domIndex(node) + 1);\n    else\n        range.setStart(node, 0);\n    range.collapse(true);\n    domSel.removeAllRanges();\n    domSel.addRange(range);\n    // Kludge to kill 'control selection' in IE11 when selecting an\n    // invisible cursor wrapper, since that would result in those weird\n    // resize handles and a selection that considers the absolutely\n    // positioned wrapper, rather than the root editable node, the\n    // focused element.\n    if (!img && !view.state.selection.visible && ie && ie_version <= 11) {\n        node.disabled = true;\n        node.disabled = false;\n    }\n}\nfunction syncNodeSelection(view, sel) {\n    if (sel instanceof NodeSelection) {\n        let desc = view.docView.descAt(sel.from);\n        if (desc != view.lastSelectedViewDesc) {\n            clearNodeSelection(view);\n            if (desc)\n                desc.selectNode();\n            view.lastSelectedViewDesc = desc;\n        }\n    }\n    else {\n        clearNodeSelection(view);\n    }\n}\n// Clear all DOM statefulness of the last node selection.\nfunction clearNodeSelection(view) {\n    if (view.lastSelectedViewDesc) {\n        if (view.lastSelectedViewDesc.parent)\n            view.lastSelectedViewDesc.deselectNode();\n        view.lastSelectedViewDesc = undefined;\n    }\n}\nfunction selectionBetween(view, $anchor, $head, bias) {\n    return view.someProp(\"createSelectionBetween\", f => f(view, $anchor, $head))\n        || TextSelection.between($anchor, $head, bias);\n}\nfunction hasFocusAndSelection(view) {\n    if (view.editable && !view.hasFocus())\n        return false;\n    return hasSelection(view);\n}\nfunction hasSelection(view) {\n    let sel = view.domSelectionRange();\n    if (!sel.anchorNode)\n        return false;\n    try {\n        // Firefox will raise 'permission denied' errors when accessing\n        // properties of `sel.anchorNode` when it's in a generated CSS\n        // element.\n        return view.dom.contains(sel.anchorNode.nodeType == 3 ? sel.anchorNode.parentNode : sel.anchorNode) &&\n            (view.editable || view.dom.contains(sel.focusNode.nodeType == 3 ? sel.focusNode.parentNode : sel.focusNode));\n    }\n    catch (_) {\n        return false;\n    }\n}\nfunction anchorInRightPlace(view) {\n    let anchorDOM = view.docView.domFromPos(view.state.selection.anchor, 0);\n    let domSel = view.domSelectionRange();\n    return isEquivalentPosition(anchorDOM.node, anchorDOM.offset, domSel.anchorNode, domSel.anchorOffset);\n}\n\nfunction moveSelectionBlock(state, dir) {\n    let { $anchor, $head } = state.selection;\n    let $side = dir > 0 ? $anchor.max($head) : $anchor.min($head);\n    let $start = !$side.parent.inlineContent ? $side : $side.depth ? state.doc.resolve(dir > 0 ? $side.after() : $side.before()) : null;\n    return $start && Selection.findFrom($start, dir);\n}\nfunction apply(view, sel) {\n    view.dispatch(view.state.tr.setSelection(sel).scrollIntoView());\n    return true;\n}\nfunction selectHorizontally(view, dir, mods) {\n    let sel = view.state.selection;\n    if (sel instanceof TextSelection) {\n        if (mods.indexOf(\"s\") > -1) {\n            let { $head } = sel, node = $head.textOffset ? null : dir < 0 ? $head.nodeBefore : $head.nodeAfter;\n            if (!node || node.isText || !node.isLeaf)\n                return false;\n            let $newHead = view.state.doc.resolve($head.pos + node.nodeSize * (dir < 0 ? -1 : 1));\n            return apply(view, new TextSelection(sel.$anchor, $newHead));\n        }\n        else if (!sel.empty) {\n            return false;\n        }\n        else if (view.endOfTextblock(dir > 0 ? \"forward\" : \"backward\")) {\n            let next = moveSelectionBlock(view.state, dir);\n            if (next && (next instanceof NodeSelection))\n                return apply(view, next);\n            return false;\n        }\n        else if (!(mac && mods.indexOf(\"m\") > -1)) {\n            let $head = sel.$head, node = $head.textOffset ? null : dir < 0 ? $head.nodeBefore : $head.nodeAfter, desc;\n            if (!node || node.isText)\n                return false;\n            let nodePos = dir < 0 ? $head.pos - node.nodeSize : $head.pos;\n            if (!(node.isAtom || (desc = view.docView.descAt(nodePos)) && !desc.contentDOM))\n                return false;\n            if (NodeSelection.isSelectable(node)) {\n                return apply(view, new NodeSelection(dir < 0 ? view.state.doc.resolve($head.pos - node.nodeSize) : $head));\n            }\n            else if (webkit) {\n                // Chrome and Safari will introduce extra pointless cursor\n                // positions around inline uneditable nodes, so we have to\n                // take over and move the cursor past them (#937)\n                return apply(view, new TextSelection(view.state.doc.resolve(dir < 0 ? nodePos : nodePos + node.nodeSize)));\n            }\n            else {\n                return false;\n            }\n        }\n    }\n    else if (sel instanceof NodeSelection && sel.node.isInline) {\n        return apply(view, new TextSelection(dir > 0 ? sel.$to : sel.$from));\n    }\n    else {\n        let next = moveSelectionBlock(view.state, dir);\n        if (next)\n            return apply(view, next);\n        return false;\n    }\n}\nfunction nodeLen(node) {\n    return node.nodeType == 3 ? node.nodeValue.length : node.childNodes.length;\n}\nfunction isIgnorable(dom, dir) {\n    let desc = dom.pmViewDesc;\n    return desc && desc.size == 0 && (dir < 0 || dom.nextSibling || dom.nodeName != \"BR\");\n}\nfunction skipIgnoredNodes(view, dir) {\n    return dir < 0 ? skipIgnoredNodesBefore(view) : skipIgnoredNodesAfter(view);\n}\n// Make sure the cursor isn't directly after one or more ignored\n// nodes, which will confuse the browser's cursor motion logic.\nfunction skipIgnoredNodesBefore(view) {\n    let sel = view.domSelectionRange();\n    let node = sel.focusNode, offset = sel.focusOffset;\n    if (!node)\n        return;\n    let moveNode, moveOffset, force = false;\n    // Gecko will do odd things when the selection is directly in front\n    // of a non-editable node, so in that case, move it into the next\n    // node if possible. Issue prosemirror/prosemirror#832.\n    if (gecko && node.nodeType == 1 && offset < nodeLen(node) && isIgnorable(node.childNodes[offset], -1))\n        force = true;\n    for (;;) {\n        if (offset > 0) {\n            if (node.nodeType != 1) {\n                break;\n            }\n            else {\n                let before = node.childNodes[offset - 1];\n                if (isIgnorable(before, -1)) {\n                    moveNode = node;\n                    moveOffset = --offset;\n                }\n                else if (before.nodeType == 3) {\n                    node = before;\n                    offset = node.nodeValue.length;\n                }\n                else\n                    break;\n            }\n        }\n        else if (isBlockNode(node)) {\n            break;\n        }\n        else {\n            let prev = node.previousSibling;\n            while (prev && isIgnorable(prev, -1)) {\n                moveNode = node.parentNode;\n                moveOffset = domIndex(prev);\n                prev = prev.previousSibling;\n            }\n            if (!prev) {\n                node = node.parentNode;\n                if (node == view.dom)\n                    break;\n                offset = 0;\n            }\n            else {\n                node = prev;\n                offset = nodeLen(node);\n            }\n        }\n    }\n    if (force)\n        setSelFocus(view, node, offset);\n    else if (moveNode)\n        setSelFocus(view, moveNode, moveOffset);\n}\n// Make sure the cursor isn't directly before one or more ignored\n// nodes.\nfunction skipIgnoredNodesAfter(view) {\n    let sel = view.domSelectionRange();\n    let node = sel.focusNode, offset = sel.focusOffset;\n    if (!node)\n        return;\n    let len = nodeLen(node);\n    let moveNode, moveOffset;\n    for (;;) {\n        if (offset < len) {\n            if (node.nodeType != 1)\n                break;\n            let after = node.childNodes[offset];\n            if (isIgnorable(after, 1)) {\n                moveNode = node;\n                moveOffset = ++offset;\n            }\n            else\n                break;\n        }\n        else if (isBlockNode(node)) {\n            break;\n        }\n        else {\n            let next = node.nextSibling;\n            while (next && isIgnorable(next, 1)) {\n                moveNode = next.parentNode;\n                moveOffset = domIndex(next) + 1;\n                next = next.nextSibling;\n            }\n            if (!next) {\n                node = node.parentNode;\n                if (node == view.dom)\n                    break;\n                offset = len = 0;\n            }\n            else {\n                node = next;\n                offset = 0;\n                len = nodeLen(node);\n            }\n        }\n    }\n    if (moveNode)\n        setSelFocus(view, moveNode, moveOffset);\n}\nfunction isBlockNode(dom) {\n    let desc = dom.pmViewDesc;\n    return desc && desc.node && desc.node.isBlock;\n}\nfunction textNodeAfter(node, offset) {\n    while (node && offset == node.childNodes.length && !hasBlockDesc(node)) {\n        offset = domIndex(node) + 1;\n        node = node.parentNode;\n    }\n    while (node && offset < node.childNodes.length) {\n        let next = node.childNodes[offset];\n        if (next.nodeType == 3)\n            return next;\n        if (next.nodeType == 1 && next.contentEditable == \"false\")\n            break;\n        node = next;\n        offset = 0;\n    }\n}\nfunction textNodeBefore(node, offset) {\n    while (node && !offset && !hasBlockDesc(node)) {\n        offset = domIndex(node);\n        node = node.parentNode;\n    }\n    while (node && offset) {\n        let next = node.childNodes[offset - 1];\n        if (next.nodeType == 3)\n            return next;\n        if (next.nodeType == 1 && next.contentEditable == \"false\")\n            break;\n        node = next;\n        offset = node.childNodes.length;\n    }\n}\nfunction setSelFocus(view, node, offset) {\n    if (node.nodeType != 3) {\n        let before, after;\n        if (after = textNodeAfter(node, offset)) {\n            node = after;\n            offset = 0;\n        }\n        else if (before = textNodeBefore(node, offset)) {\n            node = before;\n            offset = before.nodeValue.length;\n        }\n    }\n    let sel = view.domSelection();\n    if (!sel)\n        return;\n    if (selectionCollapsed(sel)) {\n        let range = document.createRange();\n        range.setEnd(node, offset);\n        range.setStart(node, offset);\n        sel.removeAllRanges();\n        sel.addRange(range);\n    }\n    else if (sel.extend) {\n        sel.extend(node, offset);\n    }\n    view.domObserver.setCurSelection();\n    let { state } = view;\n    // If no state update ends up happening, reset the selection.\n    setTimeout(() => {\n        if (view.state == state)\n            selectionToDOM(view);\n    }, 50);\n}\nfunction findDirection(view, pos) {\n    let $pos = view.state.doc.resolve(pos);\n    if (!(chrome || windows) && $pos.parent.inlineContent) {\n        let coords = view.coordsAtPos(pos);\n        if (pos > $pos.start()) {\n            let before = view.coordsAtPos(pos - 1);\n            let mid = (before.top + before.bottom) / 2;\n            if (mid > coords.top && mid < coords.bottom && Math.abs(before.left - coords.left) > 1)\n                return before.left < coords.left ? \"ltr\" : \"rtl\";\n        }\n        if (pos < $pos.end()) {\n            let after = view.coordsAtPos(pos + 1);\n            let mid = (after.top + after.bottom) / 2;\n            if (mid > coords.top && mid < coords.bottom && Math.abs(after.left - coords.left) > 1)\n                return after.left > coords.left ? \"ltr\" : \"rtl\";\n        }\n    }\n    let computed = getComputedStyle(view.dom).direction;\n    return computed == \"rtl\" ? \"rtl\" : \"ltr\";\n}\n// Check whether vertical selection motion would involve node\n// selections. If so, apply it (if not, the result is left to the\n// browser)\nfunction selectVertically(view, dir, mods) {\n    let sel = view.state.selection;\n    if (sel instanceof TextSelection && !sel.empty || mods.indexOf(\"s\") > -1)\n        return false;\n    if (mac && mods.indexOf(\"m\") > -1)\n        return false;\n    let { $from, $to } = sel;\n    if (!$from.parent.inlineContent || view.endOfTextblock(dir < 0 ? \"up\" : \"down\")) {\n        let next = moveSelectionBlock(view.state, dir);\n        if (next && (next instanceof NodeSelection))\n            return apply(view, next);\n    }\n    if (!$from.parent.inlineContent) {\n        let side = dir < 0 ? $from : $to;\n        let beyond = sel instanceof AllSelection ? Selection.near(side, dir) : Selection.findFrom(side, dir);\n        return beyond ? apply(view, beyond) : false;\n    }\n    return false;\n}\nfunction stopNativeHorizontalDelete(view, dir) {\n    if (!(view.state.selection instanceof TextSelection))\n        return true;\n    let { $head, $anchor, empty } = view.state.selection;\n    if (!$head.sameParent($anchor))\n        return true;\n    if (!empty)\n        return false;\n    if (view.endOfTextblock(dir > 0 ? \"forward\" : \"backward\"))\n        return true;\n    let nextNode = !$head.textOffset && (dir < 0 ? $head.nodeBefore : $head.nodeAfter);\n    if (nextNode && !nextNode.isText) {\n        let tr = view.state.tr;\n        if (dir < 0)\n            tr.delete($head.pos - nextNode.nodeSize, $head.pos);\n        else\n            tr.delete($head.pos, $head.pos + nextNode.nodeSize);\n        view.dispatch(tr);\n        return true;\n    }\n    return false;\n}\nfunction switchEditable(view, node, state) {\n    view.domObserver.stop();\n    node.contentEditable = state;\n    view.domObserver.start();\n}\n// Issue #867 / #1090 / https://bugs.chromium.org/p/chromium/issues/detail?id=903821\n// In which Safari (and at some point in the past, Chrome) does really\n// wrong things when the down arrow is pressed when the cursor is\n// directly at the start of a textblock and has an uneditable node\n// after it\nfunction safariDownArrowBug(view) {\n    if (!safari || view.state.selection.$head.parentOffset > 0)\n        return false;\n    let { focusNode, focusOffset } = view.domSelectionRange();\n    if (focusNode && focusNode.nodeType == 1 && focusOffset == 0 &&\n        focusNode.firstChild && focusNode.firstChild.contentEditable == \"false\") {\n        let child = focusNode.firstChild;\n        switchEditable(view, child, \"true\");\n        setTimeout(() => switchEditable(view, child, \"false\"), 20);\n    }\n    return false;\n}\n// A backdrop key mapping used to make sure we always suppress keys\n// that have a dangerous default effect, even if the commands they are\n// bound to return false, and to make sure that cursor-motion keys\n// find a cursor (as opposed to a node selection) when pressed. For\n// cursor-motion keys, the code in the handlers also takes care of\n// block selections.\nfunction getMods(event) {\n    let result = \"\";\n    if (event.ctrlKey)\n        result += \"c\";\n    if (event.metaKey)\n        result += \"m\";\n    if (event.altKey)\n        result += \"a\";\n    if (event.shiftKey)\n        result += \"s\";\n    return result;\n}\nfunction captureKeyDown(view, event) {\n    let code = event.keyCode, mods = getMods(event);\n    if (code == 8 || (mac && code == 72 && mods == \"c\")) { // Backspace, Ctrl-h on Mac\n        return stopNativeHorizontalDelete(view, -1) || skipIgnoredNodes(view, -1);\n    }\n    else if ((code == 46 && !event.shiftKey) || (mac && code == 68 && mods == \"c\")) { // Delete, Ctrl-d on Mac\n        return stopNativeHorizontalDelete(view, 1) || skipIgnoredNodes(view, 1);\n    }\n    else if (code == 13 || code == 27) { // Enter, Esc\n        return true;\n    }\n    else if (code == 37 || (mac && code == 66 && mods == \"c\")) { // Left arrow, Ctrl-b on Mac\n        let dir = code == 37 ? (findDirection(view, view.state.selection.from) == \"ltr\" ? -1 : 1) : -1;\n        return selectHorizontally(view, dir, mods) || skipIgnoredNodes(view, dir);\n    }\n    else if (code == 39 || (mac && code == 70 && mods == \"c\")) { // Right arrow, Ctrl-f on Mac\n        let dir = code == 39 ? (findDirection(view, view.state.selection.from) == \"ltr\" ? 1 : -1) : 1;\n        return selectHorizontally(view, dir, mods) || skipIgnoredNodes(view, dir);\n    }\n    else if (code == 38 || (mac && code == 80 && mods == \"c\")) { // Up arrow, Ctrl-p on Mac\n        return selectVertically(view, -1, mods) || skipIgnoredNodes(view, -1);\n    }\n    else if (code == 40 || (mac && code == 78 && mods == \"c\")) { // Down arrow, Ctrl-n on Mac\n        return safariDownArrowBug(view) || selectVertically(view, 1, mods) || skipIgnoredNodes(view, 1);\n    }\n    else if (mods == (mac ? \"m\" : \"c\") &&\n        (code == 66 || code == 73 || code == 89 || code == 90)) { // Mod-[biyz]\n        return true;\n    }\n    return false;\n}\n\nfunction serializeForClipboard(view, slice) {\n    view.someProp(\"transformCopied\", f => { slice = f(slice, view); });\n    let context = [], { content, openStart, openEnd } = slice;\n    while (openStart > 1 && openEnd > 1 && content.childCount == 1 && content.firstChild.childCount == 1) {\n        openStart--;\n        openEnd--;\n        let node = content.firstChild;\n        context.push(node.type.name, node.attrs != node.type.defaultAttrs ? node.attrs : null);\n        content = node.content;\n    }\n    let serializer = view.someProp(\"clipboardSerializer\") || DOMSerializer.fromSchema(view.state.schema);\n    let doc = detachedDoc(), wrap = doc.createElement(\"div\");\n    wrap.appendChild(serializer.serializeFragment(content, { document: doc }));\n    let firstChild = wrap.firstChild, needsWrap, wrappers = 0;\n    while (firstChild && firstChild.nodeType == 1 && (needsWrap = wrapMap[firstChild.nodeName.toLowerCase()])) {\n        for (let i = needsWrap.length - 1; i >= 0; i--) {\n            let wrapper = doc.createElement(needsWrap[i]);\n            while (wrap.firstChild)\n                wrapper.appendChild(wrap.firstChild);\n            wrap.appendChild(wrapper);\n            wrappers++;\n        }\n        firstChild = wrap.firstChild;\n    }\n    if (firstChild && firstChild.nodeType == 1)\n        firstChild.setAttribute(\"data-pm-slice\", `${openStart} ${openEnd}${wrappers ? ` -${wrappers}` : \"\"} ${JSON.stringify(context)}`);\n    let text = view.someProp(\"clipboardTextSerializer\", f => f(slice, view)) ||\n        slice.content.textBetween(0, slice.content.size, \"\\n\\n\");\n    return { dom: wrap, text, slice };\n}\n// Read a slice of content from the clipboard (or drop data).\nfunction parseFromClipboard(view, text, html, plainText, $context) {\n    let inCode = $context.parent.type.spec.code;\n    let dom, slice;\n    if (!html && !text)\n        return null;\n    let asText = text && (plainText || inCode || !html);\n    if (asText) {\n        view.someProp(\"transformPastedText\", f => { text = f(text, inCode || plainText, view); });\n        if (inCode)\n            return text ? new Slice(Fragment.from(view.state.schema.text(text.replace(/\\r\\n?/g, \"\\n\"))), 0, 0) : Slice.empty;\n        let parsed = view.someProp(\"clipboardTextParser\", f => f(text, $context, plainText, view));\n        if (parsed) {\n            slice = parsed;\n        }\n        else {\n            let marks = $context.marks();\n            let { schema } = view.state, serializer = DOMSerializer.fromSchema(schema);\n            dom = document.createElement(\"div\");\n            text.split(/(?:\\r\\n?|\\n)+/).forEach(block => {\n                let p = dom.appendChild(document.createElement(\"p\"));\n                if (block)\n                    p.appendChild(serializer.serializeNode(schema.text(block, marks)));\n            });\n        }\n    }\n    else {\n        view.someProp(\"transformPastedHTML\", f => { html = f(html, view); });\n        dom = readHTML(html);\n        if (webkit)\n            restoreReplacedSpaces(dom);\n    }\n    let contextNode = dom && dom.querySelector(\"[data-pm-slice]\");\n    let sliceData = contextNode && /^(\\d+) (\\d+)(?: -(\\d+))? (.*)/.exec(contextNode.getAttribute(\"data-pm-slice\") || \"\");\n    if (sliceData && sliceData[3])\n        for (let i = +sliceData[3]; i > 0; i--) {\n            let child = dom.firstChild;\n            while (child && child.nodeType != 1)\n                child = child.nextSibling;\n            if (!child)\n                break;\n            dom = child;\n        }\n    if (!slice) {\n        let parser = view.someProp(\"clipboardParser\") || view.someProp(\"domParser\") || DOMParser.fromSchema(view.state.schema);\n        slice = parser.parseSlice(dom, {\n            preserveWhitespace: !!(asText || sliceData),\n            context: $context,\n            ruleFromNode(dom) {\n                if (dom.nodeName == \"BR\" && !dom.nextSibling &&\n                    dom.parentNode && !inlineParents.test(dom.parentNode.nodeName))\n                    return { ignore: true };\n                return null;\n            }\n        });\n    }\n    if (sliceData) {\n        slice = addContext(closeSlice(slice, +sliceData[1], +sliceData[2]), sliceData[4]);\n    }\n    else { // HTML wasn't created by ProseMirror. Make sure top-level siblings are coherent\n        slice = Slice.maxOpen(normalizeSiblings(slice.content, $context), true);\n        if (slice.openStart || slice.openEnd) {\n            let openStart = 0, openEnd = 0;\n            for (let node = slice.content.firstChild; openStart < slice.openStart && !node.type.spec.isolating; openStart++, node = node.firstChild) { }\n            for (let node = slice.content.lastChild; openEnd < slice.openEnd && !node.type.spec.isolating; openEnd++, node = node.lastChild) { }\n            slice = closeSlice(slice, openStart, openEnd);\n        }\n    }\n    view.someProp(\"transformPasted\", f => { slice = f(slice, view); });\n    return slice;\n}\nconst inlineParents = /^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;\n// Takes a slice parsed with parseSlice, which means there hasn't been\n// any content-expression checking done on the top nodes, tries to\n// find a parent node in the current context that might fit the nodes,\n// and if successful, rebuilds the slice so that it fits into that parent.\n//\n// This addresses the problem that Transform.replace expects a\n// coherent slice, and will fail to place a set of siblings that don't\n// fit anywhere in the schema.\nfunction normalizeSiblings(fragment, $context) {\n    if (fragment.childCount < 2)\n        return fragment;\n    for (let d = $context.depth; d >= 0; d--) {\n        let parent = $context.node(d);\n        let match = parent.contentMatchAt($context.index(d));\n        let lastWrap, result = [];\n        fragment.forEach(node => {\n            if (!result)\n                return;\n            let wrap = match.findWrapping(node.type), inLast;\n            if (!wrap)\n                return result = null;\n            if (inLast = result.length && lastWrap.length && addToSibling(wrap, lastWrap, node, result[result.length - 1], 0)) {\n                result[result.length - 1] = inLast;\n            }\n            else {\n                if (result.length)\n                    result[result.length - 1] = closeRight(result[result.length - 1], lastWrap.length);\n                let wrapped = withWrappers(node, wrap);\n                result.push(wrapped);\n                match = match.matchType(wrapped.type);\n                lastWrap = wrap;\n            }\n        });\n        if (result)\n            return Fragment.from(result);\n    }\n    return fragment;\n}\nfunction withWrappers(node, wrap, from = 0) {\n    for (let i = wrap.length - 1; i >= from; i--)\n        node = wrap[i].create(null, Fragment.from(node));\n    return node;\n}\n// Used to group adjacent nodes wrapped in similar parents by\n// normalizeSiblings into the same parent node\nfunction addToSibling(wrap, lastWrap, node, sibling, depth) {\n    if (depth < wrap.length && depth < lastWrap.length && wrap[depth] == lastWrap[depth]) {\n        let inner = addToSibling(wrap, lastWrap, node, sibling.lastChild, depth + 1);\n        if (inner)\n            return sibling.copy(sibling.content.replaceChild(sibling.childCount - 1, inner));\n        let match = sibling.contentMatchAt(sibling.childCount);\n        if (match.matchType(depth == wrap.length - 1 ? node.type : wrap[depth + 1]))\n            return sibling.copy(sibling.content.append(Fragment.from(withWrappers(node, wrap, depth + 1))));\n    }\n}\nfunction closeRight(node, depth) {\n    if (depth == 0)\n        return node;\n    let fragment = node.content.replaceChild(node.childCount - 1, closeRight(node.lastChild, depth - 1));\n    let fill = node.contentMatchAt(node.childCount).fillBefore(Fragment.empty, true);\n    return node.copy(fragment.append(fill));\n}\nfunction closeRange(fragment, side, from, to, depth, openEnd) {\n    let node = side < 0 ? fragment.firstChild : fragment.lastChild, inner = node.content;\n    if (fragment.childCount > 1)\n        openEnd = 0;\n    if (depth < to - 1)\n        inner = closeRange(inner, side, from, to, depth + 1, openEnd);\n    if (depth >= from)\n        inner = side < 0 ? node.contentMatchAt(0).fillBefore(inner, openEnd <= depth).append(inner)\n            : inner.append(node.contentMatchAt(node.childCount).fillBefore(Fragment.empty, true));\n    return fragment.replaceChild(side < 0 ? 0 : fragment.childCount - 1, node.copy(inner));\n}\nfunction closeSlice(slice, openStart, openEnd) {\n    if (openStart < slice.openStart)\n        slice = new Slice(closeRange(slice.content, -1, openStart, slice.openStart, 0, slice.openEnd), openStart, slice.openEnd);\n    if (openEnd < slice.openEnd)\n        slice = new Slice(closeRange(slice.content, 1, openEnd, slice.openEnd, 0, 0), slice.openStart, openEnd);\n    return slice;\n}\n// Trick from jQuery -- some elements must be wrapped in other\n// elements for innerHTML to work. I.e. if you do `div.innerHTML =\n// \"<td>..</td>\"` the table cells are ignored.\nconst wrapMap = {\n    thead: [\"table\"],\n    tbody: [\"table\"],\n    tfoot: [\"table\"],\n    caption: [\"table\"],\n    colgroup: [\"table\"],\n    col: [\"table\", \"colgroup\"],\n    tr: [\"table\", \"tbody\"],\n    td: [\"table\", \"tbody\", \"tr\"],\n    th: [\"table\", \"tbody\", \"tr\"]\n};\nlet _detachedDoc = null;\nfunction detachedDoc() {\n    return _detachedDoc || (_detachedDoc = document.implementation.createHTMLDocument(\"title\"));\n}\nlet _policy = null;\nfunction maybeWrapTrusted(html) {\n    let trustedTypes = window.trustedTypes;\n    if (!trustedTypes)\n        return html;\n    // With the require-trusted-types-for CSP, Chrome will block\n    // innerHTML, even on a detached document. This wraps the string in\n    // a way that makes the browser allow us to use its parser again.\n    if (!_policy)\n        _policy = trustedTypes.defaultPolicy || trustedTypes.createPolicy(\"ProseMirrorClipboard\", { createHTML: (s) => s });\n    return _policy.createHTML(html);\n}\nfunction readHTML(html) {\n    let metas = /^(\\s*<meta [^>]*>)*/.exec(html);\n    if (metas)\n        html = html.slice(metas[0].length);\n    let elt = detachedDoc().createElement(\"div\");\n    let firstTag = /<([a-z][^>\\s]+)/i.exec(html), wrap;\n    if (wrap = firstTag && wrapMap[firstTag[1].toLowerCase()])\n        html = wrap.map(n => \"<\" + n + \">\").join(\"\") + html + wrap.map(n => \"</\" + n + \">\").reverse().join(\"\");\n    elt.innerHTML = maybeWrapTrusted(html);\n    if (wrap)\n        for (let i = 0; i < wrap.length; i++)\n            elt = elt.querySelector(wrap[i]) || elt;\n    return elt;\n}\n// Webkit browsers do some hard-to-predict replacement of regular\n// spaces with non-breaking spaces when putting content on the\n// clipboard. This tries to convert such non-breaking spaces (which\n// will be wrapped in a plain span on Chrome, a span with class\n// Apple-converted-space on Safari) back to regular spaces.\nfunction restoreReplacedSpaces(dom) {\n    let nodes = dom.querySelectorAll(chrome ? \"span:not([class]):not([style])\" : \"span.Apple-converted-space\");\n    for (let i = 0; i < nodes.length; i++) {\n        let node = nodes[i];\n        if (node.childNodes.length == 1 && node.textContent == \"\\u00a0\" && node.parentNode)\n            node.parentNode.replaceChild(dom.ownerDocument.createTextNode(\" \"), node);\n    }\n}\nfunction addContext(slice, context) {\n    if (!slice.size)\n        return slice;\n    let schema = slice.content.firstChild.type.schema, array;\n    try {\n        array = JSON.parse(context);\n    }\n    catch (e) {\n        return slice;\n    }\n    let { content, openStart, openEnd } = slice;\n    for (let i = array.length - 2; i >= 0; i -= 2) {\n        let type = schema.nodes[array[i]];\n        if (!type || type.hasRequiredAttrs())\n            break;\n        content = Fragment.from(type.create(array[i + 1], content));\n        openStart++;\n        openEnd++;\n    }\n    return new Slice(content, openStart, openEnd);\n}\n\n// A collection of DOM events that occur within the editor, and callback functions\n// to invoke when the event fires.\nconst handlers = {};\nconst editHandlers = {};\nconst passiveHandlers = { touchstart: true, touchmove: true };\nclass InputState {\n    constructor() {\n        this.shiftKey = false;\n        this.mouseDown = null;\n        this.lastKeyCode = null;\n        this.lastKeyCodeTime = 0;\n        this.lastClick = { time: 0, x: 0, y: 0, type: \"\", button: 0 };\n        this.lastSelectionOrigin = null;\n        this.lastSelectionTime = 0;\n        this.lastIOSEnter = 0;\n        this.lastIOSEnterFallbackTimeout = -1;\n        this.lastFocus = 0;\n        this.lastTouch = 0;\n        this.lastChromeDelete = 0;\n        this.composing = false;\n        this.compositionNode = null;\n        this.composingTimeout = -1;\n        this.compositionNodes = [];\n        this.compositionEndedAt = -2e8;\n        this.compositionID = 1;\n        // Set to a composition ID when there are pending changes at compositionend\n        this.compositionPendingChanges = 0;\n        this.domChangeCount = 0;\n        this.eventHandlers = Object.create(null);\n        this.hideSelectionGuard = null;\n    }\n}\nfunction initInput(view) {\n    for (let event in handlers) {\n        let handler = handlers[event];\n        view.dom.addEventListener(event, view.input.eventHandlers[event] = (event) => {\n            if (eventBelongsToView(view, event) && !runCustomHandler(view, event) &&\n                (view.editable || !(event.type in editHandlers)))\n                handler(view, event);\n        }, passiveHandlers[event] ? { passive: true } : undefined);\n    }\n    // On Safari, for reasons beyond my understanding, adding an input\n    // event handler makes an issue where the composition vanishes when\n    // you press enter go away.\n    if (safari)\n        view.dom.addEventListener(\"input\", () => null);\n    ensureListeners(view);\n}\nfunction setSelectionOrigin(view, origin) {\n    view.input.lastSelectionOrigin = origin;\n    view.input.lastSelectionTime = Date.now();\n}\nfunction destroyInput(view) {\n    view.domObserver.stop();\n    for (let type in view.input.eventHandlers)\n        view.dom.removeEventListener(type, view.input.eventHandlers[type]);\n    clearTimeout(view.input.composingTimeout);\n    clearTimeout(view.input.lastIOSEnterFallbackTimeout);\n}\nfunction ensureListeners(view) {\n    view.someProp(\"handleDOMEvents\", currentHandlers => {\n        for (let type in currentHandlers)\n            if (!view.input.eventHandlers[type])\n                view.dom.addEventListener(type, view.input.eventHandlers[type] = event => runCustomHandler(view, event));\n    });\n}\nfunction runCustomHandler(view, event) {\n    return view.someProp(\"handleDOMEvents\", handlers => {\n        let handler = handlers[event.type];\n        return handler ? handler(view, event) || event.defaultPrevented : false;\n    });\n}\nfunction eventBelongsToView(view, event) {\n    if (!event.bubbles)\n        return true;\n    if (event.defaultPrevented)\n        return false;\n    for (let node = event.target; node != view.dom; node = node.parentNode)\n        if (!node || node.nodeType == 11 ||\n            (node.pmViewDesc && node.pmViewDesc.stopEvent(event)))\n            return false;\n    return true;\n}\nfunction dispatchEvent(view, event) {\n    if (!runCustomHandler(view, event) && handlers[event.type] &&\n        (view.editable || !(event.type in editHandlers)))\n        handlers[event.type](view, event);\n}\neditHandlers.keydown = (view, _event) => {\n    let event = _event;\n    view.input.shiftKey = event.keyCode == 16 || event.shiftKey;\n    if (inOrNearComposition(view, event))\n        return;\n    view.input.lastKeyCode = event.keyCode;\n    view.input.lastKeyCodeTime = Date.now();\n    // Suppress enter key events on Chrome Android, because those tend\n    // to be part of a confused sequence of composition events fired,\n    // and handling them eagerly tends to corrupt the input.\n    if (android && chrome && event.keyCode == 13)\n        return;\n    if (event.keyCode != 229)\n        view.domObserver.forceFlush();\n    // On iOS, if we preventDefault enter key presses, the virtual\n    // keyboard gets confused. So the hack here is to set a flag that\n    // makes the DOM change code recognize that what just happens should\n    // be replaced by whatever the Enter key handlers do.\n    if (ios && event.keyCode == 13 && !event.ctrlKey && !event.altKey && !event.metaKey) {\n        let now = Date.now();\n        view.input.lastIOSEnter = now;\n        view.input.lastIOSEnterFallbackTimeout = setTimeout(() => {\n            if (view.input.lastIOSEnter == now) {\n                view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\")));\n                view.input.lastIOSEnter = 0;\n            }\n        }, 200);\n    }\n    else if (view.someProp(\"handleKeyDown\", f => f(view, event)) || captureKeyDown(view, event)) {\n        event.preventDefault();\n    }\n    else {\n        setSelectionOrigin(view, \"key\");\n    }\n};\neditHandlers.keyup = (view, event) => {\n    if (event.keyCode == 16)\n        view.input.shiftKey = false;\n};\neditHandlers.keypress = (view, _event) => {\n    let event = _event;\n    if (inOrNearComposition(view, event) || !event.charCode ||\n        event.ctrlKey && !event.altKey || mac && event.metaKey)\n        return;\n    if (view.someProp(\"handleKeyPress\", f => f(view, event))) {\n        event.preventDefault();\n        return;\n    }\n    let sel = view.state.selection;\n    if (!(sel instanceof TextSelection) || !sel.$from.sameParent(sel.$to)) {\n        let text = String.fromCharCode(event.charCode);\n        let deflt = () => view.state.tr.insertText(text).scrollIntoView();\n        if (!/[\\r\\n]/.test(text) && !view.someProp(\"handleTextInput\", f => f(view, sel.$from.pos, sel.$to.pos, text, deflt)))\n            view.dispatch(deflt());\n        event.preventDefault();\n    }\n};\nfunction eventCoords(event) { return { left: event.clientX, top: event.clientY }; }\nfunction isNear(event, click) {\n    let dx = click.x - event.clientX, dy = click.y - event.clientY;\n    return dx * dx + dy * dy < 100;\n}\nfunction runHandlerOnContext(view, propName, pos, inside, event) {\n    if (inside == -1)\n        return false;\n    let $pos = view.state.doc.resolve(inside);\n    for (let i = $pos.depth + 1; i > 0; i--) {\n        if (view.someProp(propName, f => i > $pos.depth ? f(view, pos, $pos.nodeAfter, $pos.before(i), event, true)\n            : f(view, pos, $pos.node(i), $pos.before(i), event, false)))\n            return true;\n    }\n    return false;\n}\nfunction updateSelection(view, selection, origin) {\n    if (!view.focused)\n        view.focus();\n    if (view.state.selection.eq(selection))\n        return;\n    let tr = view.state.tr.setSelection(selection);\n    if (origin == \"pointer\")\n        tr.setMeta(\"pointer\", true);\n    view.dispatch(tr);\n}\nfunction selectClickedLeaf(view, inside) {\n    if (inside == -1)\n        return false;\n    let $pos = view.state.doc.resolve(inside), node = $pos.nodeAfter;\n    if (node && node.isAtom && NodeSelection.isSelectable(node)) {\n        updateSelection(view, new NodeSelection($pos), \"pointer\");\n        return true;\n    }\n    return false;\n}\nfunction selectClickedNode(view, inside) {\n    if (inside == -1)\n        return false;\n    let sel = view.state.selection, selectedNode, selectAt;\n    if (sel instanceof NodeSelection)\n        selectedNode = sel.node;\n    let $pos = view.state.doc.resolve(inside);\n    for (let i = $pos.depth + 1; i > 0; i--) {\n        let node = i > $pos.depth ? $pos.nodeAfter : $pos.node(i);\n        if (NodeSelection.isSelectable(node)) {\n            if (selectedNode && sel.$from.depth > 0 &&\n                i >= sel.$from.depth && $pos.before(sel.$from.depth + 1) == sel.$from.pos)\n                selectAt = $pos.before(sel.$from.depth);\n            else\n                selectAt = $pos.before(i);\n            break;\n        }\n    }\n    if (selectAt != null) {\n        updateSelection(view, NodeSelection.create(view.state.doc, selectAt), \"pointer\");\n        return true;\n    }\n    else {\n        return false;\n    }\n}\nfunction handleSingleClick(view, pos, inside, event, selectNode) {\n    return runHandlerOnContext(view, \"handleClickOn\", pos, inside, event) ||\n        view.someProp(\"handleClick\", f => f(view, pos, event)) ||\n        (selectNode ? selectClickedNode(view, inside) : selectClickedLeaf(view, inside));\n}\nfunction handleDoubleClick(view, pos, inside, event) {\n    return runHandlerOnContext(view, \"handleDoubleClickOn\", pos, inside, event) ||\n        view.someProp(\"handleDoubleClick\", f => f(view, pos, event));\n}\nfunction handleTripleClick(view, pos, inside, event) {\n    return runHandlerOnContext(view, \"handleTripleClickOn\", pos, inside, event) ||\n        view.someProp(\"handleTripleClick\", f => f(view, pos, event)) ||\n        defaultTripleClick(view, inside, event);\n}\nfunction defaultTripleClick(view, inside, event) {\n    if (event.button != 0)\n        return false;\n    let doc = view.state.doc;\n    if (inside == -1) {\n        if (doc.inlineContent) {\n            updateSelection(view, TextSelection.create(doc, 0, doc.content.size), \"pointer\");\n            return true;\n        }\n        return false;\n    }\n    let $pos = doc.resolve(inside);\n    for (let i = $pos.depth + 1; i > 0; i--) {\n        let node = i > $pos.depth ? $pos.nodeAfter : $pos.node(i);\n        let nodePos = $pos.before(i);\n        if (node.inlineContent)\n            updateSelection(view, TextSelection.create(doc, nodePos + 1, nodePos + 1 + node.content.size), \"pointer\");\n        else if (NodeSelection.isSelectable(node))\n            updateSelection(view, NodeSelection.create(doc, nodePos), \"pointer\");\n        else\n            continue;\n        return true;\n    }\n}\nfunction forceDOMFlush(view) {\n    return endComposition(view);\n}\nconst selectNodeModifier = mac ? \"metaKey\" : \"ctrlKey\";\nhandlers.mousedown = (view, _event) => {\n    let event = _event;\n    view.input.shiftKey = event.shiftKey;\n    let flushed = forceDOMFlush(view);\n    let now = Date.now(), type = \"singleClick\";\n    if (now - view.input.lastClick.time < 500 && isNear(event, view.input.lastClick) && !event[selectNodeModifier] &&\n        view.input.lastClick.button == event.button) {\n        if (view.input.lastClick.type == \"singleClick\")\n            type = \"doubleClick\";\n        else if (view.input.lastClick.type == \"doubleClick\")\n            type = \"tripleClick\";\n    }\n    view.input.lastClick = { time: now, x: event.clientX, y: event.clientY, type, button: event.button };\n    let pos = view.posAtCoords(eventCoords(event));\n    if (!pos)\n        return;\n    if (type == \"singleClick\") {\n        if (view.input.mouseDown)\n            view.input.mouseDown.done();\n        view.input.mouseDown = new MouseDown(view, pos, event, !!flushed);\n    }\n    else if ((type == \"doubleClick\" ? handleDoubleClick : handleTripleClick)(view, pos.pos, pos.inside, event)) {\n        event.preventDefault();\n    }\n    else {\n        setSelectionOrigin(view, \"pointer\");\n    }\n};\nclass MouseDown {\n    constructor(view, pos, event, flushed) {\n        this.view = view;\n        this.pos = pos;\n        this.event = event;\n        this.flushed = flushed;\n        this.delayedSelectionSync = false;\n        this.mightDrag = null;\n        this.startDoc = view.state.doc;\n        this.selectNode = !!event[selectNodeModifier];\n        this.allowDefault = event.shiftKey;\n        let targetNode, targetPos;\n        if (pos.inside > -1) {\n            targetNode = view.state.doc.nodeAt(pos.inside);\n            targetPos = pos.inside;\n        }\n        else {\n            let $pos = view.state.doc.resolve(pos.pos);\n            targetNode = $pos.parent;\n            targetPos = $pos.depth ? $pos.before() : 0;\n        }\n        const target = flushed ? null : event.target;\n        const targetDesc = target ? view.docView.nearestDesc(target, true) : null;\n        this.target = targetDesc && targetDesc.dom.nodeType == 1 ? targetDesc.dom : null;\n        let { selection } = view.state;\n        if (event.button == 0 &&\n            targetNode.type.spec.draggable && targetNode.type.spec.selectable !== false ||\n            selection instanceof NodeSelection && selection.from <= targetPos && selection.to > targetPos)\n            this.mightDrag = {\n                node: targetNode,\n                pos: targetPos,\n                addAttr: !!(this.target && !this.target.draggable),\n                setUneditable: !!(this.target && gecko && !this.target.hasAttribute(\"contentEditable\"))\n            };\n        if (this.target && this.mightDrag && (this.mightDrag.addAttr || this.mightDrag.setUneditable)) {\n            this.view.domObserver.stop();\n            if (this.mightDrag.addAttr)\n                this.target.draggable = true;\n            if (this.mightDrag.setUneditable)\n                setTimeout(() => {\n                    if (this.view.input.mouseDown == this)\n                        this.target.setAttribute(\"contentEditable\", \"false\");\n                }, 20);\n            this.view.domObserver.start();\n        }\n        view.root.addEventListener(\"mouseup\", this.up = this.up.bind(this));\n        view.root.addEventListener(\"mousemove\", this.move = this.move.bind(this));\n        setSelectionOrigin(view, \"pointer\");\n    }\n    done() {\n        this.view.root.removeEventListener(\"mouseup\", this.up);\n        this.view.root.removeEventListener(\"mousemove\", this.move);\n        if (this.mightDrag && this.target) {\n            this.view.domObserver.stop();\n            if (this.mightDrag.addAttr)\n                this.target.removeAttribute(\"draggable\");\n            if (this.mightDrag.setUneditable)\n                this.target.removeAttribute(\"contentEditable\");\n            this.view.domObserver.start();\n        }\n        if (this.delayedSelectionSync)\n            setTimeout(() => selectionToDOM(this.view));\n        this.view.input.mouseDown = null;\n    }\n    up(event) {\n        this.done();\n        if (!this.view.dom.contains(event.target))\n            return;\n        let pos = this.pos;\n        if (this.view.state.doc != this.startDoc)\n            pos = this.view.posAtCoords(eventCoords(event));\n        this.updateAllowDefault(event);\n        if (this.allowDefault || !pos) {\n            setSelectionOrigin(this.view, \"pointer\");\n        }\n        else if (handleSingleClick(this.view, pos.pos, pos.inside, event, this.selectNode)) {\n            event.preventDefault();\n        }\n        else if (event.button == 0 &&\n            (this.flushed ||\n                // Safari ignores clicks on draggable elements\n                (safari && this.mightDrag && !this.mightDrag.node.isAtom) ||\n                // Chrome will sometimes treat a node selection as a\n                // cursor, but still report that the node is selected\n                // when asked through getSelection. You'll then get a\n                // situation where clicking at the point where that\n                // (hidden) cursor is doesn't change the selection, and\n                // thus doesn't get a reaction from ProseMirror. This\n                // works around that.\n                (chrome && !this.view.state.selection.visible &&\n                    Math.min(Math.abs(pos.pos - this.view.state.selection.from), Math.abs(pos.pos - this.view.state.selection.to)) <= 2))) {\n            updateSelection(this.view, Selection.near(this.view.state.doc.resolve(pos.pos)), \"pointer\");\n            event.preventDefault();\n        }\n        else {\n            setSelectionOrigin(this.view, \"pointer\");\n        }\n    }\n    move(event) {\n        this.updateAllowDefault(event);\n        setSelectionOrigin(this.view, \"pointer\");\n        if (event.buttons == 0)\n            this.done();\n    }\n    updateAllowDefault(event) {\n        if (!this.allowDefault && (Math.abs(this.event.x - event.clientX) > 4 ||\n            Math.abs(this.event.y - event.clientY) > 4))\n            this.allowDefault = true;\n    }\n}\nhandlers.touchstart = view => {\n    view.input.lastTouch = Date.now();\n    forceDOMFlush(view);\n    setSelectionOrigin(view, \"pointer\");\n};\nhandlers.touchmove = view => {\n    view.input.lastTouch = Date.now();\n    setSelectionOrigin(view, \"pointer\");\n};\nhandlers.contextmenu = view => forceDOMFlush(view);\nfunction inOrNearComposition(view, event) {\n    if (view.composing)\n        return true;\n    // See https://www.stum.de/2016/06/24/handling-ime-events-in-javascript/.\n    // On Japanese input method editors (IMEs), the Enter key is used to confirm character\n    // selection. On Safari, when Enter is pressed, compositionend and keydown events are\n    // emitted. The keydown event triggers newline insertion, which we don't want.\n    // This method returns true if the keydown event should be ignored.\n    // We only ignore it once, as pressing Enter a second time *should* insert a newline.\n    // Furthermore, the keydown event timestamp must be close to the compositionEndedAt timestamp.\n    // This guards against the case where compositionend is triggered without the keyboard\n    // (e.g. character confirmation may be done with the mouse), and keydown is triggered\n    // afterwards- we wouldn't want to ignore the keydown event in this case.\n    if (safari && Math.abs(event.timeStamp - view.input.compositionEndedAt) < 500) {\n        view.input.compositionEndedAt = -2e8;\n        return true;\n    }\n    return false;\n}\n// Drop active composition after 5 seconds of inactivity on Android\nconst timeoutComposition = android ? 5000 : -1;\neditHandlers.compositionstart = editHandlers.compositionupdate = view => {\n    if (!view.composing) {\n        view.domObserver.flush();\n        let { state } = view, $pos = state.selection.$to;\n        if (state.selection instanceof TextSelection &&\n            (state.storedMarks ||\n                (!$pos.textOffset && $pos.parentOffset && $pos.nodeBefore.marks.some(m => m.type.spec.inclusive === false)))) {\n            // Need to wrap the cursor in mark nodes different from the ones in the DOM context\n            view.markCursor = view.state.storedMarks || $pos.marks();\n            endComposition(view, true);\n            view.markCursor = null;\n        }\n        else {\n            endComposition(view, !state.selection.empty);\n            // In firefox, if the cursor is after but outside a marked node,\n            // the inserted text won't inherit the marks. So this moves it\n            // inside if necessary.\n            if (gecko && state.selection.empty && $pos.parentOffset && !$pos.textOffset && $pos.nodeBefore.marks.length) {\n                let sel = view.domSelectionRange();\n                for (let node = sel.focusNode, offset = sel.focusOffset; node && node.nodeType == 1 && offset != 0;) {\n                    let before = offset < 0 ? node.lastChild : node.childNodes[offset - 1];\n                    if (!before)\n                        break;\n                    if (before.nodeType == 3) {\n                        let sel = view.domSelection();\n                        if (sel)\n                            sel.collapse(before, before.nodeValue.length);\n                        break;\n                    }\n                    else {\n                        node = before;\n                        offset = -1;\n                    }\n                }\n            }\n        }\n        view.input.composing = true;\n    }\n    scheduleComposeEnd(view, timeoutComposition);\n};\neditHandlers.compositionend = (view, event) => {\n    if (view.composing) {\n        view.input.composing = false;\n        view.input.compositionEndedAt = event.timeStamp;\n        view.input.compositionPendingChanges = view.domObserver.pendingRecords().length ? view.input.compositionID : 0;\n        view.input.compositionNode = null;\n        if (view.input.compositionPendingChanges)\n            Promise.resolve().then(() => view.domObserver.flush());\n        view.input.compositionID++;\n        scheduleComposeEnd(view, 20);\n    }\n};\nfunction scheduleComposeEnd(view, delay) {\n    clearTimeout(view.input.composingTimeout);\n    if (delay > -1)\n        view.input.composingTimeout = setTimeout(() => endComposition(view), delay);\n}\nfunction clearComposition(view) {\n    if (view.composing) {\n        view.input.composing = false;\n        view.input.compositionEndedAt = timestampFromCustomEvent();\n    }\n    while (view.input.compositionNodes.length > 0)\n        view.input.compositionNodes.pop().markParentsDirty();\n}\nfunction findCompositionNode(view) {\n    let sel = view.domSelectionRange();\n    if (!sel.focusNode)\n        return null;\n    let textBefore = textNodeBefore$1(sel.focusNode, sel.focusOffset);\n    let textAfter = textNodeAfter$1(sel.focusNode, sel.focusOffset);\n    if (textBefore && textAfter && textBefore != textAfter) {\n        let descAfter = textAfter.pmViewDesc, lastChanged = view.domObserver.lastChangedTextNode;\n        if (textBefore == lastChanged || textAfter == lastChanged)\n            return lastChanged;\n        if (!descAfter || !descAfter.isText(textAfter.nodeValue)) {\n            return textAfter;\n        }\n        else if (view.input.compositionNode == textAfter) {\n            let descBefore = textBefore.pmViewDesc;\n            if (!(!descBefore || !descBefore.isText(textBefore.nodeValue)))\n                return textAfter;\n        }\n    }\n    return textBefore || textAfter;\n}\nfunction timestampFromCustomEvent() {\n    let event = document.createEvent(\"Event\");\n    event.initEvent(\"event\", true, true);\n    return event.timeStamp;\n}\n/**\n@internal\n*/\nfunction endComposition(view, restarting = false) {\n    if (android && view.domObserver.flushingSoon >= 0)\n        return;\n    view.domObserver.forceFlush();\n    clearComposition(view);\n    if (restarting || view.docView && view.docView.dirty) {\n        let sel = selectionFromDOM(view), cur = view.state.selection;\n        if (sel && !sel.eq(cur))\n            view.dispatch(view.state.tr.setSelection(sel));\n        else if ((view.markCursor || restarting) && !cur.$from.node(cur.$from.sharedDepth(cur.to)).inlineContent)\n            view.dispatch(view.state.tr.deleteSelection());\n        else\n            view.updateState(view.state);\n        return true;\n    }\n    return false;\n}\nfunction captureCopy(view, dom) {\n    // The extra wrapper is somehow necessary on IE/Edge to prevent the\n    // content from being mangled when it is put onto the clipboard\n    if (!view.dom.parentNode)\n        return;\n    let wrap = view.dom.parentNode.appendChild(document.createElement(\"div\"));\n    wrap.appendChild(dom);\n    wrap.style.cssText = \"position: fixed; left: -10000px; top: 10px\";\n    let sel = getSelection(), range = document.createRange();\n    range.selectNodeContents(dom);\n    // Done because IE will fire a selectionchange moving the selection\n    // to its start when removeAllRanges is called and the editor still\n    // has focus (which will mess up the editor's selection state).\n    view.dom.blur();\n    sel.removeAllRanges();\n    sel.addRange(range);\n    setTimeout(() => {\n        if (wrap.parentNode)\n            wrap.parentNode.removeChild(wrap);\n        view.focus();\n    }, 50);\n}\n// This is very crude, but unfortunately both these browsers _pretend_\n// that they have a clipboard API—all the objects and methods are\n// there, they just don't work, and they are hard to test.\nconst brokenClipboardAPI = (ie && ie_version < 15) ||\n    (ios && webkit_version < 604);\nhandlers.copy = editHandlers.cut = (view, _event) => {\n    let event = _event;\n    let sel = view.state.selection, cut = event.type == \"cut\";\n    if (sel.empty)\n        return;\n    // IE and Edge's clipboard interface is completely broken\n    let data = brokenClipboardAPI ? null : event.clipboardData;\n    let slice = sel.content(), { dom, text } = serializeForClipboard(view, slice);\n    if (data) {\n        event.preventDefault();\n        data.clearData();\n        data.setData(\"text/html\", dom.innerHTML);\n        data.setData(\"text/plain\", text);\n    }\n    else {\n        captureCopy(view, dom);\n    }\n    if (cut)\n        view.dispatch(view.state.tr.deleteSelection().scrollIntoView().setMeta(\"uiEvent\", \"cut\"));\n};\nfunction sliceSingleNode(slice) {\n    return slice.openStart == 0 && slice.openEnd == 0 && slice.content.childCount == 1 ? slice.content.firstChild : null;\n}\nfunction capturePaste(view, event) {\n    if (!view.dom.parentNode)\n        return;\n    let plainText = view.input.shiftKey || view.state.selection.$from.parent.type.spec.code;\n    let target = view.dom.parentNode.appendChild(document.createElement(plainText ? \"textarea\" : \"div\"));\n    if (!plainText)\n        target.contentEditable = \"true\";\n    target.style.cssText = \"position: fixed; left: -10000px; top: 10px\";\n    target.focus();\n    let plain = view.input.shiftKey && view.input.lastKeyCode != 45;\n    setTimeout(() => {\n        view.focus();\n        if (target.parentNode)\n            target.parentNode.removeChild(target);\n        if (plainText)\n            doPaste(view, target.value, null, plain, event);\n        else\n            doPaste(view, target.textContent, target.innerHTML, plain, event);\n    }, 50);\n}\nfunction doPaste(view, text, html, preferPlain, event) {\n    let slice = parseFromClipboard(view, text, html, preferPlain, view.state.selection.$from);\n    if (view.someProp(\"handlePaste\", f => f(view, event, slice || Slice.empty)))\n        return true;\n    if (!slice)\n        return false;\n    let singleNode = sliceSingleNode(slice);\n    let tr = singleNode\n        ? view.state.tr.replaceSelectionWith(singleNode, preferPlain)\n        : view.state.tr.replaceSelection(slice);\n    view.dispatch(tr.scrollIntoView().setMeta(\"paste\", true).setMeta(\"uiEvent\", \"paste\"));\n    return true;\n}\nfunction getText(clipboardData) {\n    let text = clipboardData.getData(\"text/plain\") || clipboardData.getData(\"Text\");\n    if (text)\n        return text;\n    let uris = clipboardData.getData(\"text/uri-list\");\n    return uris ? uris.replace(/\\r?\\n/g, \" \") : \"\";\n}\neditHandlers.paste = (view, _event) => {\n    let event = _event;\n    // Handling paste from JavaScript during composition is very poorly\n    // handled by browsers, so as a dodgy but preferable kludge, we just\n    // let the browser do its native thing there, except on Android,\n    // where the editor is almost always composing.\n    if (view.composing && !android)\n        return;\n    let data = brokenClipboardAPI ? null : event.clipboardData;\n    let plain = view.input.shiftKey && view.input.lastKeyCode != 45;\n    if (data && doPaste(view, getText(data), data.getData(\"text/html\"), plain, event))\n        event.preventDefault();\n    else\n        capturePaste(view, event);\n};\nclass Dragging {\n    constructor(slice, move, node) {\n        this.slice = slice;\n        this.move = move;\n        this.node = node;\n    }\n}\nconst dragCopyModifier = mac ? \"altKey\" : \"ctrlKey\";\nfunction dragMoves(view, event) {\n    let moves = view.someProp(\"dragCopies\", test => !test(event));\n    return moves != null ? moves : !event[dragCopyModifier];\n}\nhandlers.dragstart = (view, _event) => {\n    let event = _event;\n    let mouseDown = view.input.mouseDown;\n    if (mouseDown)\n        mouseDown.done();\n    if (!event.dataTransfer)\n        return;\n    let sel = view.state.selection;\n    let pos = sel.empty ? null : view.posAtCoords(eventCoords(event));\n    let node;\n    if (pos && pos.pos >= sel.from && pos.pos <= (sel instanceof NodeSelection ? sel.to - 1 : sel.to)) ;\n    else if (mouseDown && mouseDown.mightDrag) {\n        node = NodeSelection.create(view.state.doc, mouseDown.mightDrag.pos);\n    }\n    else if (event.target && event.target.nodeType == 1) {\n        let desc = view.docView.nearestDesc(event.target, true);\n        if (desc && desc.node.type.spec.draggable && desc != view.docView)\n            node = NodeSelection.create(view.state.doc, desc.posBefore);\n    }\n    let draggedSlice = (node || view.state.selection).content();\n    let { dom, text, slice } = serializeForClipboard(view, draggedSlice);\n    // Pre-120 Chrome versions clear files when calling `clearData` (#1472)\n    if (!event.dataTransfer.files.length || !chrome || chrome_version > 120)\n        event.dataTransfer.clearData();\n    event.dataTransfer.setData(brokenClipboardAPI ? \"Text\" : \"text/html\", dom.innerHTML);\n    // See https://github.com/ProseMirror/prosemirror/issues/1156\n    event.dataTransfer.effectAllowed = \"copyMove\";\n    if (!brokenClipboardAPI)\n        event.dataTransfer.setData(\"text/plain\", text);\n    view.dragging = new Dragging(slice, dragMoves(view, event), node);\n};\nhandlers.dragend = view => {\n    let dragging = view.dragging;\n    window.setTimeout(() => {\n        if (view.dragging == dragging)\n            view.dragging = null;\n    }, 50);\n};\neditHandlers.dragover = editHandlers.dragenter = (_, e) => e.preventDefault();\neditHandlers.drop = (view, _event) => {\n    let event = _event;\n    let dragging = view.dragging;\n    view.dragging = null;\n    if (!event.dataTransfer)\n        return;\n    let eventPos = view.posAtCoords(eventCoords(event));\n    if (!eventPos)\n        return;\n    let $mouse = view.state.doc.resolve(eventPos.pos);\n    let slice = dragging && dragging.slice;\n    if (slice) {\n        view.someProp(\"transformPasted\", f => { slice = f(slice, view); });\n    }\n    else {\n        slice = parseFromClipboard(view, getText(event.dataTransfer), brokenClipboardAPI ? null : event.dataTransfer.getData(\"text/html\"), false, $mouse);\n    }\n    let move = !!(dragging && dragMoves(view, event));\n    if (view.someProp(\"handleDrop\", f => f(view, event, slice || Slice.empty, move))) {\n        event.preventDefault();\n        return;\n    }\n    if (!slice)\n        return;\n    event.preventDefault();\n    let insertPos = slice ? dropPoint(view.state.doc, $mouse.pos, slice) : $mouse.pos;\n    if (insertPos == null)\n        insertPos = $mouse.pos;\n    let tr = view.state.tr;\n    if (move) {\n        let { node } = dragging;\n        if (node)\n            node.replace(tr);\n        else\n            tr.deleteSelection();\n    }\n    let pos = tr.mapping.map(insertPos);\n    let isNode = slice.openStart == 0 && slice.openEnd == 0 && slice.content.childCount == 1;\n    let beforeInsert = tr.doc;\n    if (isNode)\n        tr.replaceRangeWith(pos, pos, slice.content.firstChild);\n    else\n        tr.replaceRange(pos, pos, slice);\n    if (tr.doc.eq(beforeInsert))\n        return;\n    let $pos = tr.doc.resolve(pos);\n    if (isNode && NodeSelection.isSelectable(slice.content.firstChild) &&\n        $pos.nodeAfter && $pos.nodeAfter.sameMarkup(slice.content.firstChild)) {\n        tr.setSelection(new NodeSelection($pos));\n    }\n    else {\n        let end = tr.mapping.map(insertPos);\n        tr.mapping.maps[tr.mapping.maps.length - 1].forEach((_from, _to, _newFrom, newTo) => end = newTo);\n        tr.setSelection(selectionBetween(view, $pos, tr.doc.resolve(end)));\n    }\n    view.focus();\n    view.dispatch(tr.setMeta(\"uiEvent\", \"drop\"));\n};\nhandlers.focus = view => {\n    view.input.lastFocus = Date.now();\n    if (!view.focused) {\n        view.domObserver.stop();\n        view.dom.classList.add(\"ProseMirror-focused\");\n        view.domObserver.start();\n        view.focused = true;\n        setTimeout(() => {\n            if (view.docView && view.hasFocus() && !view.domObserver.currentSelection.eq(view.domSelectionRange()))\n                selectionToDOM(view);\n        }, 20);\n    }\n};\nhandlers.blur = (view, _event) => {\n    let event = _event;\n    if (view.focused) {\n        view.domObserver.stop();\n        view.dom.classList.remove(\"ProseMirror-focused\");\n        view.domObserver.start();\n        if (event.relatedTarget && view.dom.contains(event.relatedTarget))\n            view.domObserver.currentSelection.clear();\n        view.focused = false;\n    }\n};\nhandlers.beforeinput = (view, _event) => {\n    let event = _event;\n    // We should probably do more with beforeinput events, but support\n    // is so spotty that I'm still waiting to see where they are going.\n    // Very specific hack to deal with backspace sometimes failing on\n    // Chrome Android when after an uneditable node.\n    if (chrome && android && event.inputType == \"deleteContentBackward\") {\n        view.domObserver.flushSoon();\n        let { domChangeCount } = view.input;\n        setTimeout(() => {\n            if (view.input.domChangeCount != domChangeCount)\n                return; // Event already had some effect\n            // This bug tends to close the virtual keyboard, so we refocus\n            view.dom.blur();\n            view.focus();\n            if (view.someProp(\"handleKeyDown\", f => f(view, keyEvent(8, \"Backspace\"))))\n                return;\n            let { $cursor } = view.state.selection;\n            // Crude approximation of backspace behavior when no command handled it\n            if ($cursor && $cursor.pos > 0)\n                view.dispatch(view.state.tr.delete($cursor.pos - 1, $cursor.pos).scrollIntoView());\n        }, 50);\n    }\n};\n// Make sure all handlers get registered\nfor (let prop in editHandlers)\n    handlers[prop] = editHandlers[prop];\n\nfunction compareObjs(a, b) {\n    if (a == b)\n        return true;\n    for (let p in a)\n        if (a[p] !== b[p])\n            return false;\n    for (let p in b)\n        if (!(p in a))\n            return false;\n    return true;\n}\nclass WidgetType {\n    constructor(toDOM, spec) {\n        this.toDOM = toDOM;\n        this.spec = spec || noSpec;\n        this.side = this.spec.side || 0;\n    }\n    map(mapping, span, offset, oldOffset) {\n        let { pos, deleted } = mapping.mapResult(span.from + oldOffset, this.side < 0 ? -1 : 1);\n        return deleted ? null : new Decoration(pos - offset, pos - offset, this);\n    }\n    valid() { return true; }\n    eq(other) {\n        return this == other ||\n            (other instanceof WidgetType &&\n                (this.spec.key && this.spec.key == other.spec.key ||\n                    this.toDOM == other.toDOM && compareObjs(this.spec, other.spec)));\n    }\n    destroy(node) {\n        if (this.spec.destroy)\n            this.spec.destroy(node);\n    }\n}\nclass InlineType {\n    constructor(attrs, spec) {\n        this.attrs = attrs;\n        this.spec = spec || noSpec;\n    }\n    map(mapping, span, offset, oldOffset) {\n        let from = mapping.map(span.from + oldOffset, this.spec.inclusiveStart ? -1 : 1) - offset;\n        let to = mapping.map(span.to + oldOffset, this.spec.inclusiveEnd ? 1 : -1) - offset;\n        return from >= to ? null : new Decoration(from, to, this);\n    }\n    valid(_, span) { return span.from < span.to; }\n    eq(other) {\n        return this == other ||\n            (other instanceof InlineType && compareObjs(this.attrs, other.attrs) &&\n                compareObjs(this.spec, other.spec));\n    }\n    static is(span) { return span.type instanceof InlineType; }\n    destroy() { }\n}\nclass NodeType {\n    constructor(attrs, spec) {\n        this.attrs = attrs;\n        this.spec = spec || noSpec;\n    }\n    map(mapping, span, offset, oldOffset) {\n        let from = mapping.mapResult(span.from + oldOffset, 1);\n        if (from.deleted)\n            return null;\n        let to = mapping.mapResult(span.to + oldOffset, -1);\n        if (to.deleted || to.pos <= from.pos)\n            return null;\n        return new Decoration(from.pos - offset, to.pos - offset, this);\n    }\n    valid(node, span) {\n        let { index, offset } = node.content.findIndex(span.from), child;\n        return offset == span.from && !(child = node.child(index)).isText && offset + child.nodeSize == span.to;\n    }\n    eq(other) {\n        return this == other ||\n            (other instanceof NodeType && compareObjs(this.attrs, other.attrs) &&\n                compareObjs(this.spec, other.spec));\n    }\n    destroy() { }\n}\n/**\nDecoration objects can be provided to the view through the\n[`decorations` prop](https://prosemirror.net/docs/ref/#view.EditorProps.decorations). They come in\nseveral variants—see the static members of this class for details.\n*/\nclass Decoration {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The start position of the decoration.\n    */\n    from, \n    /**\n    The end position. Will be the same as `from` for [widget\n    decorations](https://prosemirror.net/docs/ref/#view.Decoration^widget).\n    */\n    to, \n    /**\n    @internal\n    */\n    type) {\n        this.from = from;\n        this.to = to;\n        this.type = type;\n    }\n    /**\n    @internal\n    */\n    copy(from, to) {\n        return new Decoration(from, to, this.type);\n    }\n    /**\n    @internal\n    */\n    eq(other, offset = 0) {\n        return this.type.eq(other.type) && this.from + offset == other.from && this.to + offset == other.to;\n    }\n    /**\n    @internal\n    */\n    map(mapping, offset, oldOffset) {\n        return this.type.map(mapping, this, offset, oldOffset);\n    }\n    /**\n    Creates a widget decoration, which is a DOM node that's shown in\n    the document at the given position. It is recommended that you\n    delay rendering the widget by passing a function that will be\n    called when the widget is actually drawn in a view, but you can\n    also directly pass a DOM node. `getPos` can be used to find the\n    widget's current document position.\n    */\n    static widget(pos, toDOM, spec) {\n        return new Decoration(pos, pos, new WidgetType(toDOM, spec));\n    }\n    /**\n    Creates an inline decoration, which adds the given attributes to\n    each inline node between `from` and `to`.\n    */\n    static inline(from, to, attrs, spec) {\n        return new Decoration(from, to, new InlineType(attrs, spec));\n    }\n    /**\n    Creates a node decoration. `from` and `to` should point precisely\n    before and after a node in the document. That node, and only that\n    node, will receive the given attributes.\n    */\n    static node(from, to, attrs, spec) {\n        return new Decoration(from, to, new NodeType(attrs, spec));\n    }\n    /**\n    The spec provided when creating this decoration. Can be useful\n    if you've stored extra information in that object.\n    */\n    get spec() { return this.type.spec; }\n    /**\n    @internal\n    */\n    get inline() { return this.type instanceof InlineType; }\n    /**\n    @internal\n    */\n    get widget() { return this.type instanceof WidgetType; }\n}\nconst none = [], noSpec = {};\n/**\nA collection of [decorations](https://prosemirror.net/docs/ref/#view.Decoration), organized in such\na way that the drawing algorithm can efficiently use and compare\nthem. This is a persistent data structure—it is not modified,\nupdates create a new value.\n*/\nclass DecorationSet {\n    /**\n    @internal\n    */\n    constructor(local, children) {\n        this.local = local.length ? local : none;\n        this.children = children.length ? children : none;\n    }\n    /**\n    Create a set of decorations, using the structure of the given\n    document. This will consume (modify) the `decorations` array, so\n    you must make a copy if you want need to preserve that.\n    */\n    static create(doc, decorations) {\n        return decorations.length ? buildTree(decorations, doc, 0, noSpec) : empty;\n    }\n    /**\n    Find all decorations in this set which touch the given range\n    (including decorations that start or end directly at the\n    boundaries) and match the given predicate on their spec. When\n    `start` and `end` are omitted, all decorations in the set are\n    considered. When `predicate` isn't given, all decorations are\n    assumed to match.\n    */\n    find(start, end, predicate) {\n        let result = [];\n        this.findInner(start == null ? 0 : start, end == null ? 1e9 : end, result, 0, predicate);\n        return result;\n    }\n    findInner(start, end, result, offset, predicate) {\n        for (let i = 0; i < this.local.length; i++) {\n            let span = this.local[i];\n            if (span.from <= end && span.to >= start && (!predicate || predicate(span.spec)))\n                result.push(span.copy(span.from + offset, span.to + offset));\n        }\n        for (let i = 0; i < this.children.length; i += 3) {\n            if (this.children[i] < end && this.children[i + 1] > start) {\n                let childOff = this.children[i] + 1;\n                this.children[i + 2].findInner(start - childOff, end - childOff, result, offset + childOff, predicate);\n            }\n        }\n    }\n    /**\n    Map the set of decorations in response to a change in the\n    document.\n    */\n    map(mapping, doc, options) {\n        if (this == empty || mapping.maps.length == 0)\n            return this;\n        return this.mapInner(mapping, doc, 0, 0, options || noSpec);\n    }\n    /**\n    @internal\n    */\n    mapInner(mapping, node, offset, oldOffset, options) {\n        let newLocal;\n        for (let i = 0; i < this.local.length; i++) {\n            let mapped = this.local[i].map(mapping, offset, oldOffset);\n            if (mapped && mapped.type.valid(node, mapped))\n                (newLocal || (newLocal = [])).push(mapped);\n            else if (options.onRemove)\n                options.onRemove(this.local[i].spec);\n        }\n        if (this.children.length)\n            return mapChildren(this.children, newLocal || [], mapping, node, offset, oldOffset, options);\n        else\n            return newLocal ? new DecorationSet(newLocal.sort(byPos), none) : empty;\n    }\n    /**\n    Add the given array of decorations to the ones in the set,\n    producing a new set. Consumes the `decorations` array. Needs\n    access to the current document to create the appropriate tree\n    structure.\n    */\n    add(doc, decorations) {\n        if (!decorations.length)\n            return this;\n        if (this == empty)\n            return DecorationSet.create(doc, decorations);\n        return this.addInner(doc, decorations, 0);\n    }\n    addInner(doc, decorations, offset) {\n        let children, childIndex = 0;\n        doc.forEach((childNode, childOffset) => {\n            let baseOffset = childOffset + offset, found;\n            if (!(found = takeSpansForNode(decorations, childNode, baseOffset)))\n                return;\n            if (!children)\n                children = this.children.slice();\n            while (childIndex < children.length && children[childIndex] < childOffset)\n                childIndex += 3;\n            if (children[childIndex] == childOffset)\n                children[childIndex + 2] = children[childIndex + 2].addInner(childNode, found, baseOffset + 1);\n            else\n                children.splice(childIndex, 0, childOffset, childOffset + childNode.nodeSize, buildTree(found, childNode, baseOffset + 1, noSpec));\n            childIndex += 3;\n        });\n        let local = moveSpans(childIndex ? withoutNulls(decorations) : decorations, -offset);\n        for (let i = 0; i < local.length; i++)\n            if (!local[i].type.valid(doc, local[i]))\n                local.splice(i--, 1);\n        return new DecorationSet(local.length ? this.local.concat(local).sort(byPos) : this.local, children || this.children);\n    }\n    /**\n    Create a new set that contains the decorations in this set, minus\n    the ones in the given array.\n    */\n    remove(decorations) {\n        if (decorations.length == 0 || this == empty)\n            return this;\n        return this.removeInner(decorations, 0);\n    }\n    removeInner(decorations, offset) {\n        let children = this.children, local = this.local;\n        for (let i = 0; i < children.length; i += 3) {\n            let found;\n            let from = children[i] + offset, to = children[i + 1] + offset;\n            for (let j = 0, span; j < decorations.length; j++)\n                if (span = decorations[j]) {\n                    if (span.from > from && span.to < to) {\n                        decorations[j] = null;\n                        (found || (found = [])).push(span);\n                    }\n                }\n            if (!found)\n                continue;\n            if (children == this.children)\n                children = this.children.slice();\n            let removed = children[i + 2].removeInner(found, from + 1);\n            if (removed != empty) {\n                children[i + 2] = removed;\n            }\n            else {\n                children.splice(i, 3);\n                i -= 3;\n            }\n        }\n        if (local.length)\n            for (let i = 0, span; i < decorations.length; i++)\n                if (span = decorations[i]) {\n                    for (let j = 0; j < local.length; j++)\n                        if (local[j].eq(span, offset)) {\n                            if (local == this.local)\n                                local = this.local.slice();\n                            local.splice(j--, 1);\n                        }\n                }\n        if (children == this.children && local == this.local)\n            return this;\n        return local.length || children.length ? new DecorationSet(local, children) : empty;\n    }\n    forChild(offset, node) {\n        if (this == empty)\n            return this;\n        if (node.isLeaf)\n            return DecorationSet.empty;\n        let child, local;\n        for (let i = 0; i < this.children.length; i += 3)\n            if (this.children[i] >= offset) {\n                if (this.children[i] == offset)\n                    child = this.children[i + 2];\n                break;\n            }\n        let start = offset + 1, end = start + node.content.size;\n        for (let i = 0; i < this.local.length; i++) {\n            let dec = this.local[i];\n            if (dec.from < end && dec.to > start && (dec.type instanceof InlineType)) {\n                let from = Math.max(start, dec.from) - start, to = Math.min(end, dec.to) - start;\n                if (from < to)\n                    (local || (local = [])).push(dec.copy(from, to));\n            }\n        }\n        if (local) {\n            let localSet = new DecorationSet(local.sort(byPos), none);\n            return child ? new DecorationGroup([localSet, child]) : localSet;\n        }\n        return child || empty;\n    }\n    /**\n    @internal\n    */\n    eq(other) {\n        if (this == other)\n            return true;\n        if (!(other instanceof DecorationSet) ||\n            this.local.length != other.local.length ||\n            this.children.length != other.children.length)\n            return false;\n        for (let i = 0; i < this.local.length; i++)\n            if (!this.local[i].eq(other.local[i]))\n                return false;\n        for (let i = 0; i < this.children.length; i += 3)\n            if (this.children[i] != other.children[i] ||\n                this.children[i + 1] != other.children[i + 1] ||\n                !this.children[i + 2].eq(other.children[i + 2]))\n                return false;\n        return true;\n    }\n    /**\n    @internal\n    */\n    locals(node) {\n        return removeOverlap(this.localsInner(node));\n    }\n    /**\n    @internal\n    */\n    localsInner(node) {\n        if (this == empty)\n            return none;\n        if (node.inlineContent || !this.local.some(InlineType.is))\n            return this.local;\n        let result = [];\n        for (let i = 0; i < this.local.length; i++) {\n            if (!(this.local[i].type instanceof InlineType))\n                result.push(this.local[i]);\n        }\n        return result;\n    }\n    forEachSet(f) { f(this); }\n}\n/**\nThe empty set of decorations.\n*/\nDecorationSet.empty = new DecorationSet([], []);\n/**\n@internal\n*/\nDecorationSet.removeOverlap = removeOverlap;\nconst empty = DecorationSet.empty;\n// An abstraction that allows the code dealing with decorations to\n// treat multiple DecorationSet objects as if it were a single object\n// with (a subset of) the same interface.\nclass DecorationGroup {\n    constructor(members) {\n        this.members = members;\n    }\n    map(mapping, doc) {\n        const mappedDecos = this.members.map(member => member.map(mapping, doc, noSpec));\n        return DecorationGroup.from(mappedDecos);\n    }\n    forChild(offset, child) {\n        if (child.isLeaf)\n            return DecorationSet.empty;\n        let found = [];\n        for (let i = 0; i < this.members.length; i++) {\n            let result = this.members[i].forChild(offset, child);\n            if (result == empty)\n                continue;\n            if (result instanceof DecorationGroup)\n                found = found.concat(result.members);\n            else\n                found.push(result);\n        }\n        return DecorationGroup.from(found);\n    }\n    eq(other) {\n        if (!(other instanceof DecorationGroup) ||\n            other.members.length != this.members.length)\n            return false;\n        for (let i = 0; i < this.members.length; i++)\n            if (!this.members[i].eq(other.members[i]))\n                return false;\n        return true;\n    }\n    locals(node) {\n        let result, sorted = true;\n        for (let i = 0; i < this.members.length; i++) {\n            let locals = this.members[i].localsInner(node);\n            if (!locals.length)\n                continue;\n            if (!result) {\n                result = locals;\n            }\n            else {\n                if (sorted) {\n                    result = result.slice();\n                    sorted = false;\n                }\n                for (let j = 0; j < locals.length; j++)\n                    result.push(locals[j]);\n            }\n        }\n        return result ? removeOverlap(sorted ? result : result.sort(byPos)) : none;\n    }\n    // Create a group for the given array of decoration sets, or return\n    // a single set when possible.\n    static from(members) {\n        switch (members.length) {\n            case 0: return empty;\n            case 1: return members[0];\n            default: return new DecorationGroup(members.every(m => m instanceof DecorationSet) ? members :\n                members.reduce((r, m) => r.concat(m instanceof DecorationSet ? m : m.members), []));\n        }\n    }\n    forEachSet(f) {\n        for (let i = 0; i < this.members.length; i++)\n            this.members[i].forEachSet(f);\n    }\n}\nfunction mapChildren(oldChildren, newLocal, mapping, node, offset, oldOffset, options) {\n    let children = oldChildren.slice();\n    // Mark the children that are directly touched by changes, and\n    // move those that are after the changes.\n    for (let i = 0, baseOffset = oldOffset; i < mapping.maps.length; i++) {\n        let moved = 0;\n        mapping.maps[i].forEach((oldStart, oldEnd, newStart, newEnd) => {\n            let dSize = (newEnd - newStart) - (oldEnd - oldStart);\n            for (let i = 0; i < children.length; i += 3) {\n                let end = children[i + 1];\n                if (end < 0 || oldStart > end + baseOffset - moved)\n                    continue;\n                let start = children[i] + baseOffset - moved;\n                if (oldEnd >= start) {\n                    children[i + 1] = oldStart <= start ? -2 : -1;\n                }\n                else if (oldStart >= baseOffset && dSize) {\n                    children[i] += dSize;\n                    children[i + 1] += dSize;\n                }\n            }\n            moved += dSize;\n        });\n        baseOffset = mapping.maps[i].map(baseOffset, -1);\n    }\n    // Find the child nodes that still correspond to a single node,\n    // recursively call mapInner on them and update their positions.\n    let mustRebuild = false;\n    for (let i = 0; i < children.length; i += 3)\n        if (children[i + 1] < 0) { // Touched nodes\n            if (children[i + 1] == -2) {\n                mustRebuild = true;\n                children[i + 1] = -1;\n                continue;\n            }\n            let from = mapping.map(oldChildren[i] + oldOffset), fromLocal = from - offset;\n            if (fromLocal < 0 || fromLocal >= node.content.size) {\n                mustRebuild = true;\n                continue;\n            }\n            // Must read oldChildren because children was tagged with -1\n            let to = mapping.map(oldChildren[i + 1] + oldOffset, -1), toLocal = to - offset;\n            let { index, offset: childOffset } = node.content.findIndex(fromLocal);\n            let childNode = node.maybeChild(index);\n            if (childNode && childOffset == fromLocal && childOffset + childNode.nodeSize == toLocal) {\n                let mapped = children[i + 2]\n                    .mapInner(mapping, childNode, from + 1, oldChildren[i] + oldOffset + 1, options);\n                if (mapped != empty) {\n                    children[i] = fromLocal;\n                    children[i + 1] = toLocal;\n                    children[i + 2] = mapped;\n                }\n                else {\n                    children[i + 1] = -2;\n                    mustRebuild = true;\n                }\n            }\n            else {\n                mustRebuild = true;\n            }\n        }\n    // Remaining children must be collected and rebuilt into the appropriate structure\n    if (mustRebuild) {\n        let decorations = mapAndGatherRemainingDecorations(children, oldChildren, newLocal, mapping, offset, oldOffset, options);\n        let built = buildTree(decorations, node, 0, options);\n        newLocal = built.local;\n        for (let i = 0; i < children.length; i += 3)\n            if (children[i + 1] < 0) {\n                children.splice(i, 3);\n                i -= 3;\n            }\n        for (let i = 0, j = 0; i < built.children.length; i += 3) {\n            let from = built.children[i];\n            while (j < children.length && children[j] < from)\n                j += 3;\n            children.splice(j, 0, built.children[i], built.children[i + 1], built.children[i + 2]);\n        }\n    }\n    return new DecorationSet(newLocal.sort(byPos), children);\n}\nfunction moveSpans(spans, offset) {\n    if (!offset || !spans.length)\n        return spans;\n    let result = [];\n    for (let i = 0; i < spans.length; i++) {\n        let span = spans[i];\n        result.push(new Decoration(span.from + offset, span.to + offset, span.type));\n    }\n    return result;\n}\nfunction mapAndGatherRemainingDecorations(children, oldChildren, decorations, mapping, offset, oldOffset, options) {\n    // Gather all decorations from the remaining marked children\n    function gather(set, oldOffset) {\n        for (let i = 0; i < set.local.length; i++) {\n            let mapped = set.local[i].map(mapping, offset, oldOffset);\n            if (mapped)\n                decorations.push(mapped);\n            else if (options.onRemove)\n                options.onRemove(set.local[i].spec);\n        }\n        for (let i = 0; i < set.children.length; i += 3)\n            gather(set.children[i + 2], set.children[i] + oldOffset + 1);\n    }\n    for (let i = 0; i < children.length; i += 3)\n        if (children[i + 1] == -1)\n            gather(children[i + 2], oldChildren[i] + oldOffset + 1);\n    return decorations;\n}\nfunction takeSpansForNode(spans, node, offset) {\n    if (node.isLeaf)\n        return null;\n    let end = offset + node.nodeSize, found = null;\n    for (let i = 0, span; i < spans.length; i++) {\n        if ((span = spans[i]) && span.from > offset && span.to < end) {\n            (found || (found = [])).push(span);\n            spans[i] = null;\n        }\n    }\n    return found;\n}\nfunction withoutNulls(array) {\n    let result = [];\n    for (let i = 0; i < array.length; i++)\n        if (array[i] != null)\n            result.push(array[i]);\n    return result;\n}\n// Build up a tree that corresponds to a set of decorations. `offset`\n// is a base offset that should be subtracted from the `from` and `to`\n// positions in the spans (so that we don't have to allocate new spans\n// for recursive calls).\nfunction buildTree(spans, node, offset, options) {\n    let children = [], hasNulls = false;\n    node.forEach((childNode, localStart) => {\n        let found = takeSpansForNode(spans, childNode, localStart + offset);\n        if (found) {\n            hasNulls = true;\n            let subtree = buildTree(found, childNode, offset + localStart + 1, options);\n            if (subtree != empty)\n                children.push(localStart, localStart + childNode.nodeSize, subtree);\n        }\n    });\n    let locals = moveSpans(hasNulls ? withoutNulls(spans) : spans, -offset).sort(byPos);\n    for (let i = 0; i < locals.length; i++)\n        if (!locals[i].type.valid(node, locals[i])) {\n            if (options.onRemove)\n                options.onRemove(locals[i].spec);\n            locals.splice(i--, 1);\n        }\n    return locals.length || children.length ? new DecorationSet(locals, children) : empty;\n}\n// Used to sort decorations so that ones with a low start position\n// come first, and within a set with the same start position, those\n// with an smaller end position come first.\nfunction byPos(a, b) {\n    return a.from - b.from || a.to - b.to;\n}\n// Scan a sorted array of decorations for partially overlapping spans,\n// and split those so that only fully overlapping spans are left (to\n// make subsequent rendering easier). Will return the input array if\n// no partially overlapping spans are found (the common case).\nfunction removeOverlap(spans) {\n    let working = spans;\n    for (let i = 0; i < working.length - 1; i++) {\n        let span = working[i];\n        if (span.from != span.to)\n            for (let j = i + 1; j < working.length; j++) {\n                let next = working[j];\n                if (next.from == span.from) {\n                    if (next.to != span.to) {\n                        if (working == spans)\n                            working = spans.slice();\n                        // Followed by a partially overlapping larger span. Split that\n                        // span.\n                        working[j] = next.copy(next.from, span.to);\n                        insertAhead(working, j + 1, next.copy(span.to, next.to));\n                    }\n                    continue;\n                }\n                else {\n                    if (next.from < span.to) {\n                        if (working == spans)\n                            working = spans.slice();\n                        // The end of this one overlaps with a subsequent span. Split\n                        // this one.\n                        working[i] = span.copy(span.from, next.from);\n                        insertAhead(working, j, span.copy(next.from, span.to));\n                    }\n                    break;\n                }\n            }\n    }\n    return working;\n}\nfunction insertAhead(array, i, deco) {\n    while (i < array.length && byPos(deco, array[i]) > 0)\n        i++;\n    array.splice(i, 0, deco);\n}\n// Get the decorations associated with the current props of a view.\nfunction viewDecorations(view) {\n    let found = [];\n    view.someProp(\"decorations\", f => {\n        let result = f(view.state);\n        if (result && result != empty)\n            found.push(result);\n    });\n    if (view.cursorWrapper)\n        found.push(DecorationSet.create(view.state.doc, [view.cursorWrapper.deco]));\n    return DecorationGroup.from(found);\n}\n\nconst observeOptions = {\n    childList: true,\n    characterData: true,\n    characterDataOldValue: true,\n    attributes: true,\n    attributeOldValue: true,\n    subtree: true\n};\n// IE11 has very broken mutation observers, so we also listen to DOMCharacterDataModified\nconst useCharData = ie && ie_version <= 11;\nclass SelectionState {\n    constructor() {\n        this.anchorNode = null;\n        this.anchorOffset = 0;\n        this.focusNode = null;\n        this.focusOffset = 0;\n    }\n    set(sel) {\n        this.anchorNode = sel.anchorNode;\n        this.anchorOffset = sel.anchorOffset;\n        this.focusNode = sel.focusNode;\n        this.focusOffset = sel.focusOffset;\n    }\n    clear() {\n        this.anchorNode = this.focusNode = null;\n    }\n    eq(sel) {\n        return sel.anchorNode == this.anchorNode && sel.anchorOffset == this.anchorOffset &&\n            sel.focusNode == this.focusNode && sel.focusOffset == this.focusOffset;\n    }\n}\nclass DOMObserver {\n    constructor(view, handleDOMChange) {\n        this.view = view;\n        this.handleDOMChange = handleDOMChange;\n        this.queue = [];\n        this.flushingSoon = -1;\n        this.observer = null;\n        this.currentSelection = new SelectionState;\n        this.onCharData = null;\n        this.suppressingSelectionUpdates = false;\n        this.lastChangedTextNode = null;\n        this.observer = window.MutationObserver &&\n            new window.MutationObserver(mutations => {\n                for (let i = 0; i < mutations.length; i++)\n                    this.queue.push(mutations[i]);\n                // IE11 will sometimes (on backspacing out a single character\n                // text node after a BR node) call the observer callback\n                // before actually updating the DOM, which will cause\n                // ProseMirror to miss the change (see #930)\n                if (ie && ie_version <= 11 && mutations.some(m => m.type == \"childList\" && m.removedNodes.length ||\n                    m.type == \"characterData\" && m.oldValue.length > m.target.nodeValue.length))\n                    this.flushSoon();\n                else\n                    this.flush();\n            });\n        if (useCharData) {\n            this.onCharData = e => {\n                this.queue.push({ target: e.target, type: \"characterData\", oldValue: e.prevValue });\n                this.flushSoon();\n            };\n        }\n        this.onSelectionChange = this.onSelectionChange.bind(this);\n    }\n    flushSoon() {\n        if (this.flushingSoon < 0)\n            this.flushingSoon = window.setTimeout(() => { this.flushingSoon = -1; this.flush(); }, 20);\n    }\n    forceFlush() {\n        if (this.flushingSoon > -1) {\n            window.clearTimeout(this.flushingSoon);\n            this.flushingSoon = -1;\n            this.flush();\n        }\n    }\n    start() {\n        if (this.observer) {\n            this.observer.takeRecords();\n            this.observer.observe(this.view.dom, observeOptions);\n        }\n        if (this.onCharData)\n            this.view.dom.addEventListener(\"DOMCharacterDataModified\", this.onCharData);\n        this.connectSelection();\n    }\n    stop() {\n        if (this.observer) {\n            let take = this.observer.takeRecords();\n            if (take.length) {\n                for (let i = 0; i < take.length; i++)\n                    this.queue.push(take[i]);\n                window.setTimeout(() => this.flush(), 20);\n            }\n            this.observer.disconnect();\n        }\n        if (this.onCharData)\n            this.view.dom.removeEventListener(\"DOMCharacterDataModified\", this.onCharData);\n        this.disconnectSelection();\n    }\n    connectSelection() {\n        this.view.dom.ownerDocument.addEventListener(\"selectionchange\", this.onSelectionChange);\n    }\n    disconnectSelection() {\n        this.view.dom.ownerDocument.removeEventListener(\"selectionchange\", this.onSelectionChange);\n    }\n    suppressSelectionUpdates() {\n        this.suppressingSelectionUpdates = true;\n        setTimeout(() => this.suppressingSelectionUpdates = false, 50);\n    }\n    onSelectionChange() {\n        if (!hasFocusAndSelection(this.view))\n            return;\n        if (this.suppressingSelectionUpdates)\n            return selectionToDOM(this.view);\n        // Deletions on IE11 fire their events in the wrong order, giving\n        // us a selection change event before the DOM changes are\n        // reported.\n        if (ie && ie_version <= 11 && !this.view.state.selection.empty) {\n            let sel = this.view.domSelectionRange();\n            // Selection.isCollapsed isn't reliable on IE\n            if (sel.focusNode && isEquivalentPosition(sel.focusNode, sel.focusOffset, sel.anchorNode, sel.anchorOffset))\n                return this.flushSoon();\n        }\n        this.flush();\n    }\n    setCurSelection() {\n        this.currentSelection.set(this.view.domSelectionRange());\n    }\n    ignoreSelectionChange(sel) {\n        if (!sel.focusNode)\n            return true;\n        let ancestors = new Set, container;\n        for (let scan = sel.focusNode; scan; scan = parentNode(scan))\n            ancestors.add(scan);\n        for (let scan = sel.anchorNode; scan; scan = parentNode(scan))\n            if (ancestors.has(scan)) {\n                container = scan;\n                break;\n            }\n        let desc = container && this.view.docView.nearestDesc(container);\n        if (desc && desc.ignoreMutation({\n            type: \"selection\",\n            target: container.nodeType == 3 ? container.parentNode : container\n        })) {\n            this.setCurSelection();\n            return true;\n        }\n    }\n    pendingRecords() {\n        if (this.observer)\n            for (let mut of this.observer.takeRecords())\n                this.queue.push(mut);\n        return this.queue;\n    }\n    flush() {\n        let { view } = this;\n        if (!view.docView || this.flushingSoon > -1)\n            return;\n        let mutations = this.pendingRecords();\n        if (mutations.length)\n            this.queue = [];\n        let sel = view.domSelectionRange();\n        let newSel = !this.suppressingSelectionUpdates && !this.currentSelection.eq(sel) && hasFocusAndSelection(view) && !this.ignoreSelectionChange(sel);\n        let from = -1, to = -1, typeOver = false, added = [];\n        if (view.editable) {\n            for (let i = 0; i < mutations.length; i++) {\n                let result = this.registerMutation(mutations[i], added);\n                if (result) {\n                    from = from < 0 ? result.from : Math.min(result.from, from);\n                    to = to < 0 ? result.to : Math.max(result.to, to);\n                    if (result.typeOver)\n                        typeOver = true;\n                }\n            }\n        }\n        if (gecko && added.length) {\n            let brs = added.filter(n => n.nodeName == \"BR\");\n            if (brs.length == 2) {\n                let [a, b] = brs;\n                if (a.parentNode && a.parentNode.parentNode == b.parentNode)\n                    b.remove();\n                else\n                    a.remove();\n            }\n            else {\n                let { focusNode } = this.currentSelection;\n                for (let br of brs) {\n                    let parent = br.parentNode;\n                    if (parent && parent.nodeName == \"LI\" && (!focusNode || blockParent(view, focusNode) != parent))\n                        br.remove();\n                }\n            }\n        }\n        let readSel = null;\n        // If it looks like the browser has reset the selection to the\n        // start of the document after focus, restore the selection from\n        // the state\n        if (from < 0 && newSel && view.input.lastFocus > Date.now() - 200 &&\n            Math.max(view.input.lastTouch, view.input.lastClick.time) < Date.now() - 300 &&\n            selectionCollapsed(sel) && (readSel = selectionFromDOM(view)) &&\n            readSel.eq(Selection.near(view.state.doc.resolve(0), 1))) {\n            view.input.lastFocus = 0;\n            selectionToDOM(view);\n            this.currentSelection.set(sel);\n            view.scrollToSelection();\n        }\n        else if (from > -1 || newSel) {\n            if (from > -1) {\n                view.docView.markDirty(from, to);\n                checkCSS(view);\n            }\n            this.handleDOMChange(from, to, typeOver, added);\n            if (view.docView && view.docView.dirty)\n                view.updateState(view.state);\n            else if (!this.currentSelection.eq(sel))\n                selectionToDOM(view);\n            this.currentSelection.set(sel);\n        }\n    }\n    registerMutation(mut, added) {\n        // Ignore mutations inside nodes that were already noted as inserted\n        if (added.indexOf(mut.target) > -1)\n            return null;\n        let desc = this.view.docView.nearestDesc(mut.target);\n        if (mut.type == \"attributes\" &&\n            (desc == this.view.docView || mut.attributeName == \"contenteditable\" ||\n                // Firefox sometimes fires spurious events for null/empty styles\n                (mut.attributeName == \"style\" && !mut.oldValue && !mut.target.getAttribute(\"style\"))))\n            return null;\n        if (!desc || desc.ignoreMutation(mut))\n            return null;\n        if (mut.type == \"childList\") {\n            for (let i = 0; i < mut.addedNodes.length; i++) {\n                let node = mut.addedNodes[i];\n                added.push(node);\n                if (node.nodeType == 3)\n                    this.lastChangedTextNode = node;\n            }\n            if (desc.contentDOM && desc.contentDOM != desc.dom && !desc.contentDOM.contains(mut.target))\n                return { from: desc.posBefore, to: desc.posAfter };\n            let prev = mut.previousSibling, next = mut.nextSibling;\n            if (ie && ie_version <= 11 && mut.addedNodes.length) {\n                // IE11 gives us incorrect next/prev siblings for some\n                // insertions, so if there are added nodes, recompute those\n                for (let i = 0; i < mut.addedNodes.length; i++) {\n                    let { previousSibling, nextSibling } = mut.addedNodes[i];\n                    if (!previousSibling || Array.prototype.indexOf.call(mut.addedNodes, previousSibling) < 0)\n                        prev = previousSibling;\n                    if (!nextSibling || Array.prototype.indexOf.call(mut.addedNodes, nextSibling) < 0)\n                        next = nextSibling;\n                }\n            }\n            let fromOffset = prev && prev.parentNode == mut.target\n                ? domIndex(prev) + 1 : 0;\n            let from = desc.localPosFromDOM(mut.target, fromOffset, -1);\n            let toOffset = next && next.parentNode == mut.target\n                ? domIndex(next) : mut.target.childNodes.length;\n            let to = desc.localPosFromDOM(mut.target, toOffset, 1);\n            return { from, to };\n        }\n        else if (mut.type == \"attributes\") {\n            return { from: desc.posAtStart - desc.border, to: desc.posAtEnd + desc.border };\n        }\n        else { // \"characterData\"\n            this.lastChangedTextNode = mut.target;\n            return {\n                from: desc.posAtStart,\n                to: desc.posAtEnd,\n                // An event was generated for a text change that didn't change\n                // any text. Mark the dom change to fall back to assuming the\n                // selection was typed over with an identical value if it can't\n                // find another change.\n                typeOver: mut.target.nodeValue == mut.oldValue\n            };\n        }\n    }\n}\nlet cssChecked = new WeakMap();\nlet cssCheckWarned = false;\nfunction checkCSS(view) {\n    if (cssChecked.has(view))\n        return;\n    cssChecked.set(view, null);\n    if (['normal', 'nowrap', 'pre-line'].indexOf(getComputedStyle(view.dom).whiteSpace) !== -1) {\n        view.requiresGeckoHackNode = gecko;\n        if (cssCheckWarned)\n            return;\n        console[\"warn\"](\"ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package.\");\n        cssCheckWarned = true;\n    }\n}\nfunction rangeToSelectionRange(view, range) {\n    let anchorNode = range.startContainer, anchorOffset = range.startOffset;\n    let focusNode = range.endContainer, focusOffset = range.endOffset;\n    let currentAnchor = view.domAtPos(view.state.selection.anchor);\n    // Since such a range doesn't distinguish between anchor and head,\n    // use a heuristic that flips it around if its end matches the\n    // current anchor.\n    if (isEquivalentPosition(currentAnchor.node, currentAnchor.offset, focusNode, focusOffset))\n        [anchorNode, anchorOffset, focusNode, focusOffset] = [focusNode, focusOffset, anchorNode, anchorOffset];\n    return { anchorNode, anchorOffset, focusNode, focusOffset };\n}\n// Used to work around a Safari Selection/shadow DOM bug\n// Based on https://github.com/codemirror/dev/issues/414 fix\nfunction safariShadowSelectionRange(view, selection) {\n    if (selection.getComposedRanges) {\n        let range = selection.getComposedRanges(view.root)[0];\n        if (range)\n            return rangeToSelectionRange(view, range);\n    }\n    let found;\n    function read(event) {\n        event.preventDefault();\n        event.stopImmediatePropagation();\n        found = event.getTargetRanges()[0];\n    }\n    // Because Safari (at least in 2018-2022) doesn't provide regular\n    // access to the selection inside a shadowRoot, we have to perform a\n    // ridiculous hack to get at it—using `execCommand` to trigger a\n    // `beforeInput` event so that we can read the target range from the\n    // event.\n    view.dom.addEventListener(\"beforeinput\", read, true);\n    document.execCommand(\"indent\");\n    view.dom.removeEventListener(\"beforeinput\", read, true);\n    return found ? rangeToSelectionRange(view, found) : null;\n}\nfunction blockParent(view, node) {\n    for (let p = node.parentNode; p && p != view.dom; p = p.parentNode) {\n        let desc = view.docView.nearestDesc(p, true);\n        if (desc && desc.node.isBlock)\n            return p;\n    }\n    return null;\n}\n\n// Note that all referencing and parsing is done with the\n// start-of-operation selection and document, since that's the one\n// that the DOM represents. If any changes came in in the meantime,\n// the modification is mapped over those before it is applied, in\n// readDOMChange.\nfunction parseBetween(view, from_, to_) {\n    let { node: parent, fromOffset, toOffset, from, to } = view.docView.parseRange(from_, to_);\n    let domSel = view.domSelectionRange();\n    let find;\n    let anchor = domSel.anchorNode;\n    if (anchor && view.dom.contains(anchor.nodeType == 1 ? anchor : anchor.parentNode)) {\n        find = [{ node: anchor, offset: domSel.anchorOffset }];\n        if (!selectionCollapsed(domSel))\n            find.push({ node: domSel.focusNode, offset: domSel.focusOffset });\n    }\n    // Work around issue in Chrome where backspacing sometimes replaces\n    // the deleted content with a random BR node (issues #799, #831)\n    if (chrome && view.input.lastKeyCode === 8) {\n        for (let off = toOffset; off > fromOffset; off--) {\n            let node = parent.childNodes[off - 1], desc = node.pmViewDesc;\n            if (node.nodeName == \"BR\" && !desc) {\n                toOffset = off;\n                break;\n            }\n            if (!desc || desc.size)\n                break;\n        }\n    }\n    let startDoc = view.state.doc;\n    let parser = view.someProp(\"domParser\") || DOMParser.fromSchema(view.state.schema);\n    let $from = startDoc.resolve(from);\n    let sel = null, doc = parser.parse(parent, {\n        topNode: $from.parent,\n        topMatch: $from.parent.contentMatchAt($from.index()),\n        topOpen: true,\n        from: fromOffset,\n        to: toOffset,\n        preserveWhitespace: $from.parent.type.whitespace == \"pre\" ? \"full\" : true,\n        findPositions: find,\n        ruleFromNode,\n        context: $from\n    });\n    if (find && find[0].pos != null) {\n        let anchor = find[0].pos, head = find[1] && find[1].pos;\n        if (head == null)\n            head = anchor;\n        sel = { anchor: anchor + from, head: head + from };\n    }\n    return { doc, sel, from, to };\n}\nfunction ruleFromNode(dom) {\n    let desc = dom.pmViewDesc;\n    if (desc) {\n        return desc.parseRule();\n    }\n    else if (dom.nodeName == \"BR\" && dom.parentNode) {\n        // Safari replaces the list item or table cell with a BR\n        // directly in the list node (?!) if you delete the last\n        // character in a list item or table cell (#708, #862)\n        if (safari && /^(ul|ol)$/i.test(dom.parentNode.nodeName)) {\n            let skip = document.createElement(\"div\");\n            skip.appendChild(document.createElement(\"li\"));\n            return { skip };\n        }\n        else if (dom.parentNode.lastChild == dom || safari && /^(tr|table)$/i.test(dom.parentNode.nodeName)) {\n            return { ignore: true };\n        }\n    }\n    else if (dom.nodeName == \"IMG\" && dom.getAttribute(\"mark-placeholder\")) {\n        return { ignore: true };\n    }\n    return null;\n}\nconst isInline = /^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;\nfunction readDOMChange(view, from, to, typeOver, addedNodes) {\n    let compositionID = view.input.compositionPendingChanges || (view.composing ? view.input.compositionID : 0);\n    view.input.compositionPendingChanges = 0;\n    if (from < 0) {\n        let origin = view.input.lastSelectionTime > Date.now() - 50 ? view.input.lastSelectionOrigin : null;\n        let newSel = selectionFromDOM(view, origin);\n        if (newSel && !view.state.selection.eq(newSel)) {\n            if (chrome && android &&\n                view.input.lastKeyCode === 13 && Date.now() - 100 < view.input.lastKeyCodeTime &&\n                view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\"))))\n                return;\n            let tr = view.state.tr.setSelection(newSel);\n            if (origin == \"pointer\")\n                tr.setMeta(\"pointer\", true);\n            else if (origin == \"key\")\n                tr.scrollIntoView();\n            if (compositionID)\n                tr.setMeta(\"composition\", compositionID);\n            view.dispatch(tr);\n        }\n        return;\n    }\n    let $before = view.state.doc.resolve(from);\n    let shared = $before.sharedDepth(to);\n    from = $before.before(shared + 1);\n    to = view.state.doc.resolve(to).after(shared + 1);\n    let sel = view.state.selection;\n    let parse = parseBetween(view, from, to);\n    let doc = view.state.doc, compare = doc.slice(parse.from, parse.to);\n    let preferredPos, preferredSide;\n    // Prefer anchoring to end when Backspace is pressed\n    if (view.input.lastKeyCode === 8 && Date.now() - 100 < view.input.lastKeyCodeTime) {\n        preferredPos = view.state.selection.to;\n        preferredSide = \"end\";\n    }\n    else {\n        preferredPos = view.state.selection.from;\n        preferredSide = \"start\";\n    }\n    view.input.lastKeyCode = null;\n    let change = findDiff(compare.content, parse.doc.content, parse.from, preferredPos, preferredSide);\n    if (change)\n        view.input.domChangeCount++;\n    if ((ios && view.input.lastIOSEnter > Date.now() - 225 || android) &&\n        addedNodes.some(n => n.nodeType == 1 && !isInline.test(n.nodeName)) &&\n        (!change || change.endA >= change.endB) &&\n        view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\")))) {\n        view.input.lastIOSEnter = 0;\n        return;\n    }\n    if (!change) {\n        if (typeOver && sel instanceof TextSelection && !sel.empty && sel.$head.sameParent(sel.$anchor) &&\n            !view.composing && !(parse.sel && parse.sel.anchor != parse.sel.head)) {\n            change = { start: sel.from, endA: sel.to, endB: sel.to };\n        }\n        else {\n            if (parse.sel) {\n                let sel = resolveSelection(view, view.state.doc, parse.sel);\n                if (sel && !sel.eq(view.state.selection)) {\n                    let tr = view.state.tr.setSelection(sel);\n                    if (compositionID)\n                        tr.setMeta(\"composition\", compositionID);\n                    view.dispatch(tr);\n                }\n            }\n            return;\n        }\n    }\n    // Handle the case where overwriting a selection by typing matches\n    // the start or end of the selected content, creating a change\n    // that's smaller than what was actually overwritten.\n    if (view.state.selection.from < view.state.selection.to &&\n        change.start == change.endB &&\n        view.state.selection instanceof TextSelection) {\n        if (change.start > view.state.selection.from && change.start <= view.state.selection.from + 2 &&\n            view.state.selection.from >= parse.from) {\n            change.start = view.state.selection.from;\n        }\n        else if (change.endA < view.state.selection.to && change.endA >= view.state.selection.to - 2 &&\n            view.state.selection.to <= parse.to) {\n            change.endB += (view.state.selection.to - change.endA);\n            change.endA = view.state.selection.to;\n        }\n    }\n    // IE11 will insert a non-breaking space _ahead_ of the space after\n    // the cursor space when adding a space before another space. When\n    // that happened, adjust the change to cover the space instead.\n    if (ie && ie_version <= 11 && change.endB == change.start + 1 &&\n        change.endA == change.start && change.start > parse.from &&\n        parse.doc.textBetween(change.start - parse.from - 1, change.start - parse.from + 1) == \" \\u00a0\") {\n        change.start--;\n        change.endA--;\n        change.endB--;\n    }\n    let $from = parse.doc.resolveNoCache(change.start - parse.from);\n    let $to = parse.doc.resolveNoCache(change.endB - parse.from);\n    let $fromA = doc.resolve(change.start);\n    let inlineChange = $from.sameParent($to) && $from.parent.inlineContent && $fromA.end() >= change.endA;\n    let nextSel;\n    // If this looks like the effect of pressing Enter (or was recorded\n    // as being an iOS enter press), just dispatch an Enter key instead.\n    if (((ios && view.input.lastIOSEnter > Date.now() - 225 &&\n        (!inlineChange || addedNodes.some(n => n.nodeName == \"DIV\" || n.nodeName == \"P\"))) ||\n        (!inlineChange && $from.pos < parse.doc.content.size &&\n            (!$from.sameParent($to) || !$from.parent.inlineContent) &&\n            !/\\S/.test(parse.doc.textBetween($from.pos, $to.pos, \"\", \"\")) &&\n            (nextSel = Selection.findFrom(parse.doc.resolve($from.pos + 1), 1, true)) &&\n            nextSel.head > $from.pos)) &&\n        view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\")))) {\n        view.input.lastIOSEnter = 0;\n        return;\n    }\n    // Same for backspace\n    if (view.state.selection.anchor > change.start &&\n        looksLikeBackspace(doc, change.start, change.endA, $from, $to) &&\n        view.someProp(\"handleKeyDown\", f => f(view, keyEvent(8, \"Backspace\")))) {\n        if (android && chrome)\n            view.domObserver.suppressSelectionUpdates(); // #820\n        return;\n    }\n    // Chrome will occasionally, during composition, delete the\n    // entire composition and then immediately insert it again. This is\n    // used to detect that situation.\n    if (chrome && change.endB == change.start)\n        view.input.lastChromeDelete = Date.now();\n    // This tries to detect Android virtual keyboard\n    // enter-and-pick-suggestion action. That sometimes (see issue\n    // #1059) first fires a DOM mutation, before moving the selection to\n    // the newly created block. And then, because ProseMirror cleans up\n    // the DOM selection, it gives up moving the selection entirely,\n    // leaving the cursor in the wrong place. When that happens, we drop\n    // the new paragraph from the initial change, and fire a simulated\n    // enter key afterwards.\n    if (android && !inlineChange && $from.start() != $to.start() && $to.parentOffset == 0 && $from.depth == $to.depth &&\n        parse.sel && parse.sel.anchor == parse.sel.head && parse.sel.head == change.endA) {\n        change.endB -= 2;\n        $to = parse.doc.resolveNoCache(change.endB - parse.from);\n        setTimeout(() => {\n            view.someProp(\"handleKeyDown\", function (f) { return f(view, keyEvent(13, \"Enter\")); });\n        }, 20);\n    }\n    let chFrom = change.start, chTo = change.endA;\n    let mkTr = (base) => {\n        let tr = base || view.state.tr.replace(chFrom, chTo, parse.doc.slice(change.start - parse.from, change.endB - parse.from));\n        if (parse.sel) {\n            let sel = resolveSelection(view, tr.doc, parse.sel);\n            // Chrome will sometimes, during composition, report the\n            // selection in the wrong place. If it looks like that is\n            // happening, don't update the selection.\n            // Edge just doesn't move the cursor forward when you start typing\n            // in an empty block or between br nodes.\n            if (sel && !(chrome && view.composing && sel.empty &&\n                (change.start != change.endB || view.input.lastChromeDelete < Date.now() - 100) &&\n                (sel.head == chFrom || sel.head == tr.mapping.map(chTo) - 1) ||\n                ie && sel.empty && sel.head == chFrom))\n                tr.setSelection(sel);\n        }\n        if (compositionID)\n            tr.setMeta(\"composition\", compositionID);\n        return tr.scrollIntoView();\n    };\n    let markChange;\n    if (inlineChange) {\n        if ($from.pos == $to.pos) { // Deletion\n            // IE11 sometimes weirdly moves the DOM selection around after\n            // backspacing out the first element in a textblock\n            if (ie && ie_version <= 11 && $from.parentOffset == 0) {\n                view.domObserver.suppressSelectionUpdates();\n                setTimeout(() => selectionToDOM(view), 20);\n            }\n            let tr = mkTr(view.state.tr.delete(chFrom, chTo));\n            let marks = doc.resolve(change.start).marksAcross(doc.resolve(change.endA));\n            if (marks)\n                tr.ensureMarks(marks);\n            view.dispatch(tr);\n        }\n        else if ( // Adding or removing a mark\n        change.endA == change.endB &&\n            (markChange = isMarkChange($from.parent.content.cut($from.parentOffset, $to.parentOffset), $fromA.parent.content.cut($fromA.parentOffset, change.endA - $fromA.start())))) {\n            let tr = mkTr(view.state.tr);\n            if (markChange.type == \"add\")\n                tr.addMark(chFrom, chTo, markChange.mark);\n            else\n                tr.removeMark(chFrom, chTo, markChange.mark);\n            view.dispatch(tr);\n        }\n        else if ($from.parent.child($from.index()).isText && $from.index() == $to.index() - ($to.textOffset ? 0 : 1)) {\n            // Both positions in the same text node -- simply insert text\n            let text = $from.parent.textBetween($from.parentOffset, $to.parentOffset);\n            let deflt = () => mkTr(view.state.tr.insertText(text, chFrom, chTo));\n            if (!view.someProp(\"handleTextInput\", f => f(view, chFrom, chTo, text, deflt)))\n                view.dispatch(deflt());\n        }\n    }\n    else {\n        view.dispatch(mkTr());\n    }\n}\nfunction resolveSelection(view, doc, parsedSel) {\n    if (Math.max(parsedSel.anchor, parsedSel.head) > doc.content.size)\n        return null;\n    return selectionBetween(view, doc.resolve(parsedSel.anchor), doc.resolve(parsedSel.head));\n}\n// Given two same-length, non-empty fragments of inline content,\n// determine whether the first could be created from the second by\n// removing or adding a single mark type.\nfunction isMarkChange(cur, prev) {\n    let curMarks = cur.firstChild.marks, prevMarks = prev.firstChild.marks;\n    let added = curMarks, removed = prevMarks, type, mark, update;\n    for (let i = 0; i < prevMarks.length; i++)\n        added = prevMarks[i].removeFromSet(added);\n    for (let i = 0; i < curMarks.length; i++)\n        removed = curMarks[i].removeFromSet(removed);\n    if (added.length == 1 && removed.length == 0) {\n        mark = added[0];\n        type = \"add\";\n        update = (node) => node.mark(mark.addToSet(node.marks));\n    }\n    else if (added.length == 0 && removed.length == 1) {\n        mark = removed[0];\n        type = \"remove\";\n        update = (node) => node.mark(mark.removeFromSet(node.marks));\n    }\n    else {\n        return null;\n    }\n    let updated = [];\n    for (let i = 0; i < prev.childCount; i++)\n        updated.push(update(prev.child(i)));\n    if (Fragment.from(updated).eq(cur))\n        return { mark, type };\n}\nfunction looksLikeBackspace(old, start, end, $newStart, $newEnd) {\n    if ( // The content must have shrunk\n    end - start <= $newEnd.pos - $newStart.pos ||\n        // newEnd must point directly at or after the end of the block that newStart points into\n        skipClosingAndOpening($newStart, true, false) < $newEnd.pos)\n        return false;\n    let $start = old.resolve(start);\n    // Handle the case where, rather than joining blocks, the change just removed an entire block\n    if (!$newStart.parent.isTextblock) {\n        let after = $start.nodeAfter;\n        return after != null && end == start + after.nodeSize;\n    }\n    // Start must be at the end of a block\n    if ($start.parentOffset < $start.parent.content.size || !$start.parent.isTextblock)\n        return false;\n    let $next = old.resolve(skipClosingAndOpening($start, true, true));\n    // The next textblock must start before end and end near it\n    if (!$next.parent.isTextblock || $next.pos > end ||\n        skipClosingAndOpening($next, true, false) < end)\n        return false;\n    // The fragments after the join point must match\n    return $newStart.parent.content.cut($newStart.parentOffset).eq($next.parent.content);\n}\nfunction skipClosingAndOpening($pos, fromEnd, mayOpen) {\n    let depth = $pos.depth, end = fromEnd ? $pos.end() : $pos.pos;\n    while (depth > 0 && (fromEnd || $pos.indexAfter(depth) == $pos.node(depth).childCount)) {\n        depth--;\n        end++;\n        fromEnd = false;\n    }\n    if (mayOpen) {\n        let next = $pos.node(depth).maybeChild($pos.indexAfter(depth));\n        while (next && !next.isLeaf) {\n            next = next.firstChild;\n            end++;\n        }\n    }\n    return end;\n}\nfunction findDiff(a, b, pos, preferredPos, preferredSide) {\n    let start = a.findDiffStart(b, pos);\n    if (start == null)\n        return null;\n    let { a: endA, b: endB } = a.findDiffEnd(b, pos + a.size, pos + b.size);\n    if (preferredSide == \"end\") {\n        let adjust = Math.max(0, start - Math.min(endA, endB));\n        preferredPos -= endA + adjust - start;\n    }\n    if (endA < start && a.size < b.size) {\n        let move = preferredPos <= start && preferredPos >= endA ? start - preferredPos : 0;\n        start -= move;\n        if (start && start < b.size && isSurrogatePair(b.textBetween(start - 1, start + 1)))\n            start += move ? 1 : -1;\n        endB = start + (endB - endA);\n        endA = start;\n    }\n    else if (endB < start) {\n        let move = preferredPos <= start && preferredPos >= endB ? start - preferredPos : 0;\n        start -= move;\n        if (start && start < a.size && isSurrogatePair(a.textBetween(start - 1, start + 1)))\n            start += move ? 1 : -1;\n        endA = start + (endA - endB);\n        endB = start;\n    }\n    return { start, endA, endB };\n}\nfunction isSurrogatePair(str) {\n    if (str.length != 2)\n        return false;\n    let a = str.charCodeAt(0), b = str.charCodeAt(1);\n    return a >= 0xDC00 && a <= 0xDFFF && b >= 0xD800 && b <= 0xDBFF;\n}\n\n/**\n@internal\n*/\nconst __parseFromClipboard = parseFromClipboard;\n/**\n@internal\n*/\nconst __endComposition = endComposition;\n/**\nAn editor view manages the DOM structure that represents an\neditable document. Its state and behavior are determined by its\n[props](https://prosemirror.net/docs/ref/#view.DirectEditorProps).\n*/\nclass EditorView {\n    /**\n    Create a view. `place` may be a DOM node that the editor should\n    be appended to, a function that will place it into the document,\n    or an object whose `mount` property holds the node to use as the\n    document container. If it is `null`, the editor will not be\n    added to the document.\n    */\n    constructor(place, props) {\n        this._root = null;\n        /**\n        @internal\n        */\n        this.focused = false;\n        /**\n        Kludge used to work around a Chrome bug @internal\n        */\n        this.trackWrites = null;\n        this.mounted = false;\n        /**\n        @internal\n        */\n        this.markCursor = null;\n        /**\n        @internal\n        */\n        this.cursorWrapper = null;\n        /**\n        @internal\n        */\n        this.lastSelectedViewDesc = undefined;\n        /**\n        @internal\n        */\n        this.input = new InputState;\n        this.prevDirectPlugins = [];\n        this.pluginViews = [];\n        /**\n        Holds `true` when a hack node is needed in Firefox to prevent the\n        [space is eaten issue](https://github.com/ProseMirror/prosemirror/issues/651)\n        @internal\n        */\n        this.requiresGeckoHackNode = false;\n        /**\n        When editor content is being dragged, this object contains\n        information about the dragged slice and whether it is being\n        copied or moved. At any other time, it is null.\n        */\n        this.dragging = null;\n        this._props = props;\n        this.state = props.state;\n        this.directPlugins = props.plugins || [];\n        this.directPlugins.forEach(checkStateComponent);\n        this.dispatch = this.dispatch.bind(this);\n        this.dom = (place && place.mount) || document.createElement(\"div\");\n        if (place) {\n            if (place.appendChild)\n                place.appendChild(this.dom);\n            else if (typeof place == \"function\")\n                place(this.dom);\n            else if (place.mount)\n                this.mounted = true;\n        }\n        this.editable = getEditable(this);\n        updateCursorWrapper(this);\n        this.nodeViews = buildNodeViews(this);\n        this.docView = docViewDesc(this.state.doc, computeDocDeco(this), viewDecorations(this), this.dom, this);\n        this.domObserver = new DOMObserver(this, (from, to, typeOver, added) => readDOMChange(this, from, to, typeOver, added));\n        this.domObserver.start();\n        initInput(this);\n        this.updatePluginViews();\n    }\n    /**\n    Holds `true` when a\n    [composition](https://w3c.github.io/uievents/#events-compositionevents)\n    is active.\n    */\n    get composing() { return this.input.composing; }\n    /**\n    The view's current [props](https://prosemirror.net/docs/ref/#view.EditorProps).\n    */\n    get props() {\n        if (this._props.state != this.state) {\n            let prev = this._props;\n            this._props = {};\n            for (let name in prev)\n                this._props[name] = prev[name];\n            this._props.state = this.state;\n        }\n        return this._props;\n    }\n    /**\n    Update the view's props. Will immediately cause an update to\n    the DOM.\n    */\n    update(props) {\n        if (props.handleDOMEvents != this._props.handleDOMEvents)\n            ensureListeners(this);\n        let prevProps = this._props;\n        this._props = props;\n        if (props.plugins) {\n            props.plugins.forEach(checkStateComponent);\n            this.directPlugins = props.plugins;\n        }\n        this.updateStateInner(props.state, prevProps);\n    }\n    /**\n    Update the view by updating existing props object with the object\n    given as argument. Equivalent to `view.update(Object.assign({},\n    view.props, props))`.\n    */\n    setProps(props) {\n        let updated = {};\n        for (let name in this._props)\n            updated[name] = this._props[name];\n        updated.state = this.state;\n        for (let name in props)\n            updated[name] = props[name];\n        this.update(updated);\n    }\n    /**\n    Update the editor's `state` prop, without touching any of the\n    other props.\n    */\n    updateState(state) {\n        this.updateStateInner(state, this._props);\n    }\n    updateStateInner(state, prevProps) {\n        var _a;\n        let prev = this.state, redraw = false, updateSel = false;\n        // When stored marks are added, stop composition, so that they can\n        // be displayed.\n        if (state.storedMarks && this.composing) {\n            clearComposition(this);\n            updateSel = true;\n        }\n        this.state = state;\n        let pluginsChanged = prev.plugins != state.plugins || this._props.plugins != prevProps.plugins;\n        if (pluginsChanged || this._props.plugins != prevProps.plugins || this._props.nodeViews != prevProps.nodeViews) {\n            let nodeViews = buildNodeViews(this);\n            if (changedNodeViews(nodeViews, this.nodeViews)) {\n                this.nodeViews = nodeViews;\n                redraw = true;\n            }\n        }\n        if (pluginsChanged || prevProps.handleDOMEvents != this._props.handleDOMEvents) {\n            ensureListeners(this);\n        }\n        this.editable = getEditable(this);\n        updateCursorWrapper(this);\n        let innerDeco = viewDecorations(this), outerDeco = computeDocDeco(this);\n        let scroll = prev.plugins != state.plugins && !prev.doc.eq(state.doc) ? \"reset\"\n            : state.scrollToSelection > prev.scrollToSelection ? \"to selection\" : \"preserve\";\n        let updateDoc = redraw || !this.docView.matchesNode(state.doc, outerDeco, innerDeco);\n        if (updateDoc || !state.selection.eq(prev.selection))\n            updateSel = true;\n        let oldScrollPos = scroll == \"preserve\" && updateSel && this.dom.style.overflowAnchor == null && storeScrollPos(this);\n        if (updateSel) {\n            this.domObserver.stop();\n            // Work around an issue in Chrome, IE, and Edge where changing\n            // the DOM around an active selection puts it into a broken\n            // state where the thing the user sees differs from the\n            // selection reported by the Selection object (#710, #973,\n            // #1011, #1013, #1035).\n            let forceSelUpdate = updateDoc && (ie || chrome) && !this.composing &&\n                !prev.selection.empty && !state.selection.empty && selectionContextChanged(prev.selection, state.selection);\n            if (updateDoc) {\n                // If the node that the selection points into is written to,\n                // Chrome sometimes starts misreporting the selection, so this\n                // tracks that and forces a selection reset when our update\n                // did write to the node.\n                let chromeKludge = chrome ? (this.trackWrites = this.domSelectionRange().focusNode) : null;\n                if (this.composing)\n                    this.input.compositionNode = findCompositionNode(this);\n                if (redraw || !this.docView.update(state.doc, outerDeco, innerDeco, this)) {\n                    this.docView.updateOuterDeco(outerDeco);\n                    this.docView.destroy();\n                    this.docView = docViewDesc(state.doc, outerDeco, innerDeco, this.dom, this);\n                }\n                if (chromeKludge && !this.trackWrites)\n                    forceSelUpdate = true;\n            }\n            // Work around for an issue where an update arriving right between\n            // a DOM selection change and the \"selectionchange\" event for it\n            // can cause a spurious DOM selection update, disrupting mouse\n            // drag selection.\n            if (forceSelUpdate ||\n                !(this.input.mouseDown && this.domObserver.currentSelection.eq(this.domSelectionRange()) &&\n                    anchorInRightPlace(this))) {\n                selectionToDOM(this, forceSelUpdate);\n            }\n            else {\n                syncNodeSelection(this, state.selection);\n                this.domObserver.setCurSelection();\n            }\n            this.domObserver.start();\n        }\n        this.updatePluginViews(prev);\n        if (((_a = this.dragging) === null || _a === void 0 ? void 0 : _a.node) && !prev.doc.eq(state.doc))\n            this.updateDraggedNode(this.dragging, prev);\n        if (scroll == \"reset\") {\n            this.dom.scrollTop = 0;\n        }\n        else if (scroll == \"to selection\") {\n            this.scrollToSelection();\n        }\n        else if (oldScrollPos) {\n            resetScrollPos(oldScrollPos);\n        }\n    }\n    /**\n    @internal\n    */\n    scrollToSelection() {\n        let startDOM = this.domSelectionRange().focusNode;\n        if (!startDOM || !this.dom.contains(startDOM.nodeType == 1 ? startDOM : startDOM.parentNode)) ;\n        else if (this.someProp(\"handleScrollToSelection\", f => f(this))) ;\n        else if (this.state.selection instanceof NodeSelection) {\n            let target = this.docView.domAfterPos(this.state.selection.from);\n            if (target.nodeType == 1)\n                scrollRectIntoView(this, target.getBoundingClientRect(), startDOM);\n        }\n        else {\n            scrollRectIntoView(this, this.coordsAtPos(this.state.selection.head, 1), startDOM);\n        }\n    }\n    destroyPluginViews() {\n        let view;\n        while (view = this.pluginViews.pop())\n            if (view.destroy)\n                view.destroy();\n    }\n    updatePluginViews(prevState) {\n        if (!prevState || prevState.plugins != this.state.plugins || this.directPlugins != this.prevDirectPlugins) {\n            this.prevDirectPlugins = this.directPlugins;\n            this.destroyPluginViews();\n            for (let i = 0; i < this.directPlugins.length; i++) {\n                let plugin = this.directPlugins[i];\n                if (plugin.spec.view)\n                    this.pluginViews.push(plugin.spec.view(this));\n            }\n            for (let i = 0; i < this.state.plugins.length; i++) {\n                let plugin = this.state.plugins[i];\n                if (plugin.spec.view)\n                    this.pluginViews.push(plugin.spec.view(this));\n            }\n        }\n        else {\n            for (let i = 0; i < this.pluginViews.length; i++) {\n                let pluginView = this.pluginViews[i];\n                if (pluginView.update)\n                    pluginView.update(this, prevState);\n            }\n        }\n    }\n    updateDraggedNode(dragging, prev) {\n        let sel = dragging.node, found = -1;\n        if (this.state.doc.nodeAt(sel.from) == sel.node) {\n            found = sel.from;\n        }\n        else {\n            let movedPos = sel.from + (this.state.doc.content.size - prev.doc.content.size);\n            let moved = movedPos > 0 && this.state.doc.nodeAt(movedPos);\n            if (moved == sel.node)\n                found = movedPos;\n        }\n        this.dragging = new Dragging(dragging.slice, dragging.move, found < 0 ? undefined : NodeSelection.create(this.state.doc, found));\n    }\n    someProp(propName, f) {\n        let prop = this._props && this._props[propName], value;\n        if (prop != null && (value = f ? f(prop) : prop))\n            return value;\n        for (let i = 0; i < this.directPlugins.length; i++) {\n            let prop = this.directPlugins[i].props[propName];\n            if (prop != null && (value = f ? f(prop) : prop))\n                return value;\n        }\n        let plugins = this.state.plugins;\n        if (plugins)\n            for (let i = 0; i < plugins.length; i++) {\n                let prop = plugins[i].props[propName];\n                if (prop != null && (value = f ? f(prop) : prop))\n                    return value;\n            }\n    }\n    /**\n    Query whether the view has focus.\n    */\n    hasFocus() {\n        // Work around IE not handling focus correctly if resize handles are shown.\n        // If the cursor is inside an element with resize handles, activeElement\n        // will be that element instead of this.dom.\n        if (ie) {\n            // If activeElement is within this.dom, and there are no other elements\n            // setting `contenteditable` to false in between, treat it as focused.\n            let node = this.root.activeElement;\n            if (node == this.dom)\n                return true;\n            if (!node || !this.dom.contains(node))\n                return false;\n            while (node && this.dom != node && this.dom.contains(node)) {\n                if (node.contentEditable == 'false')\n                    return false;\n                node = node.parentElement;\n            }\n            return true;\n        }\n        return this.root.activeElement == this.dom;\n    }\n    /**\n    Focus the editor.\n    */\n    focus() {\n        this.domObserver.stop();\n        if (this.editable)\n            focusPreventScroll(this.dom);\n        selectionToDOM(this);\n        this.domObserver.start();\n    }\n    /**\n    Get the document root in which the editor exists. This will\n    usually be the top-level `document`, but might be a [shadow\n    DOM](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Shadow_DOM)\n    root if the editor is inside one.\n    */\n    get root() {\n        let cached = this._root;\n        if (cached == null)\n            for (let search = this.dom.parentNode; search; search = search.parentNode) {\n                if (search.nodeType == 9 || (search.nodeType == 11 && search.host)) {\n                    if (!search.getSelection)\n                        Object.getPrototypeOf(search).getSelection = () => search.ownerDocument.getSelection();\n                    return this._root = search;\n                }\n            }\n        return cached || document;\n    }\n    /**\n    When an existing editor view is moved to a new document or\n    shadow tree, call this to make it recompute its root.\n    */\n    updateRoot() {\n        this._root = null;\n    }\n    /**\n    Given a pair of viewport coordinates, return the document\n    position that corresponds to them. May return null if the given\n    coordinates aren't inside of the editor. When an object is\n    returned, its `pos` property is the position nearest to the\n    coordinates, and its `inside` property holds the position of the\n    inner node that the position falls inside of, or -1 if it is at\n    the top level, not in any node.\n    */\n    posAtCoords(coords) {\n        return posAtCoords(this, coords);\n    }\n    /**\n    Returns the viewport rectangle at a given document position.\n    `left` and `right` will be the same number, as this returns a\n    flat cursor-ish rectangle. If the position is between two things\n    that aren't directly adjacent, `side` determines which element\n    is used. When < 0, the element before the position is used,\n    otherwise the element after.\n    */\n    coordsAtPos(pos, side = 1) {\n        return coordsAtPos(this, pos, side);\n    }\n    /**\n    Find the DOM position that corresponds to the given document\n    position. When `side` is negative, find the position as close as\n    possible to the content before the position. When positive,\n    prefer positions close to the content after the position. When\n    zero, prefer as shallow a position as possible.\n    \n    Note that you should **not** mutate the editor's internal DOM,\n    only inspect it (and even that is usually not necessary).\n    */\n    domAtPos(pos, side = 0) {\n        return this.docView.domFromPos(pos, side);\n    }\n    /**\n    Find the DOM node that represents the document node after the\n    given position. May return `null` when the position doesn't point\n    in front of a node or if the node is inside an opaque node view.\n    \n    This is intended to be able to call things like\n    `getBoundingClientRect` on that DOM node. Do **not** mutate the\n    editor DOM directly, or add styling this way, since that will be\n    immediately overriden by the editor as it redraws the node.\n    */\n    nodeDOM(pos) {\n        let desc = this.docView.descAt(pos);\n        return desc ? desc.nodeDOM : null;\n    }\n    /**\n    Find the document position that corresponds to a given DOM\n    position. (Whenever possible, it is preferable to inspect the\n    document structure directly, rather than poking around in the\n    DOM, but sometimes—for example when interpreting an event\n    target—you don't have a choice.)\n    \n    The `bias` parameter can be used to influence which side of a DOM\n    node to use when the position is inside a leaf node.\n    */\n    posAtDOM(node, offset, bias = -1) {\n        let pos = this.docView.posFromDOM(node, offset, bias);\n        if (pos == null)\n            throw new RangeError(\"DOM position not inside the editor\");\n        return pos;\n    }\n    /**\n    Find out whether the selection is at the end of a textblock when\n    moving in a given direction. When, for example, given `\"left\"`,\n    it will return true if moving left from the current cursor\n    position would leave that position's parent textblock. Will apply\n    to the view's current state by default, but it is possible to\n    pass a different state.\n    */\n    endOfTextblock(dir, state) {\n        return endOfTextblock(this, state || this.state, dir);\n    }\n    /**\n    Run the editor's paste logic with the given HTML string. The\n    `event`, if given, will be passed to the\n    [`handlePaste`](https://prosemirror.net/docs/ref/#view.EditorProps.handlePaste) hook.\n    */\n    pasteHTML(html, event) {\n        return doPaste(this, \"\", html, false, event || new ClipboardEvent(\"paste\"));\n    }\n    /**\n    Run the editor's paste logic with the given plain-text input.\n    */\n    pasteText(text, event) {\n        return doPaste(this, text, null, true, event || new ClipboardEvent(\"paste\"));\n    }\n    /**\n    Serialize the given slice as it would be if it was copied from\n    this editor. Returns a DOM element that contains a\n    representation of the slice as its children, a textual\n    representation, and the transformed slice (which can be\n    different from the given input due to hooks like\n    [`transformCopied`](https://prosemirror.net/docs/ref/#view.EditorProps.transformCopied)).\n    */\n    serializeForClipboard(slice) {\n        return serializeForClipboard(this, slice);\n    }\n    /**\n    Removes the editor from the DOM and destroys all [node\n    views](https://prosemirror.net/docs/ref/#view.NodeView).\n    */\n    destroy() {\n        if (!this.docView)\n            return;\n        destroyInput(this);\n        this.destroyPluginViews();\n        if (this.mounted) {\n            this.docView.update(this.state.doc, [], viewDecorations(this), this);\n            this.dom.textContent = \"\";\n        }\n        else if (this.dom.parentNode) {\n            this.dom.parentNode.removeChild(this.dom);\n        }\n        this.docView.destroy();\n        this.docView = null;\n        clearReusedRange();\n    }\n    /**\n    This is true when the view has been\n    [destroyed](https://prosemirror.net/docs/ref/#view.EditorView.destroy) (and thus should not be\n    used anymore).\n    */\n    get isDestroyed() {\n        return this.docView == null;\n    }\n    /**\n    Used for testing.\n    */\n    dispatchEvent(event) {\n        return dispatchEvent(this, event);\n    }\n    /**\n    @internal\n    */\n    domSelectionRange() {\n        let sel = this.domSelection();\n        if (!sel)\n            return { focusNode: null, focusOffset: 0, anchorNode: null, anchorOffset: 0 };\n        return safari && this.root.nodeType === 11 &&\n            deepActiveElement(this.dom.ownerDocument) == this.dom && safariShadowSelectionRange(this, sel) || sel;\n    }\n    /**\n    @internal\n    */\n    domSelection() {\n        return this.root.getSelection();\n    }\n}\nEditorView.prototype.dispatch = function (tr) {\n    let dispatchTransaction = this._props.dispatchTransaction;\n    if (dispatchTransaction)\n        dispatchTransaction.call(this, tr);\n    else\n        this.updateState(this.state.apply(tr));\n};\nfunction computeDocDeco(view) {\n    let attrs = Object.create(null);\n    attrs.class = \"ProseMirror\";\n    attrs.contenteditable = String(view.editable);\n    view.someProp(\"attributes\", value => {\n        if (typeof value == \"function\")\n            value = value(view.state);\n        if (value)\n            for (let attr in value) {\n                if (attr == \"class\")\n                    attrs.class += \" \" + value[attr];\n                else if (attr == \"style\")\n                    attrs.style = (attrs.style ? attrs.style + \";\" : \"\") + value[attr];\n                else if (!attrs[attr] && attr != \"contenteditable\" && attr != \"nodeName\")\n                    attrs[attr] = String(value[attr]);\n            }\n    });\n    if (!attrs.translate)\n        attrs.translate = \"no\";\n    return [Decoration.node(0, view.state.doc.content.size, attrs)];\n}\nfunction updateCursorWrapper(view) {\n    if (view.markCursor) {\n        let dom = document.createElement(\"img\");\n        dom.className = \"ProseMirror-separator\";\n        dom.setAttribute(\"mark-placeholder\", \"true\");\n        dom.setAttribute(\"alt\", \"\");\n        view.cursorWrapper = { dom, deco: Decoration.widget(view.state.selection.from, dom, { raw: true, marks: view.markCursor }) };\n    }\n    else {\n        view.cursorWrapper = null;\n    }\n}\nfunction getEditable(view) {\n    return !view.someProp(\"editable\", value => value(view.state) === false);\n}\nfunction selectionContextChanged(sel1, sel2) {\n    let depth = Math.min(sel1.$anchor.sharedDepth(sel1.head), sel2.$anchor.sharedDepth(sel2.head));\n    return sel1.$anchor.start(depth) != sel2.$anchor.start(depth);\n}\nfunction buildNodeViews(view) {\n    let result = Object.create(null);\n    function add(obj) {\n        for (let prop in obj)\n            if (!Object.prototype.hasOwnProperty.call(result, prop))\n                result[prop] = obj[prop];\n    }\n    view.someProp(\"nodeViews\", add);\n    view.someProp(\"markViews\", add);\n    return result;\n}\nfunction changedNodeViews(a, b) {\n    let nA = 0, nB = 0;\n    for (let prop in a) {\n        if (a[prop] != b[prop])\n            return true;\n        nA++;\n    }\n    for (let _ in b)\n        nB++;\n    return nA != nB;\n}\nfunction checkStateComponent(plugin) {\n    if (plugin.spec.state || plugin.spec.filterTransaction || plugin.spec.appendTransaction)\n        throw new RangeError(\"Plugins passed directly to the view must not have a state component\");\n}\n\nexport { Decoration, DecorationSet, EditorView, __endComposition, __parseFromClipboard };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,WAAW,SAAU,IAAI;IAC3B,IAAK,IAAI,QAAQ,IAAI,QAAS;QAC1B,OAAO,KAAK,eAAe;QAC3B,IAAI,CAAC,MACD,OAAO;IACf;AACJ;AACA,MAAM,aAAa,SAAU,IAAI;IAC7B,IAAI,SAAS,KAAK,YAAY,IAAI,KAAK,UAAU;IACjD,OAAO,UAAU,OAAO,QAAQ,IAAI,KAAK,OAAO,IAAI,GAAG;AAC3D;AACA,IAAI,cAAc;AAClB,sEAAsE;AACtE,oEAAoE;AACpE,4BAA4B;AAC5B,MAAM,YAAY,SAAU,IAAI,EAAE,IAAI,EAAE,EAAE;IACtC,IAAI,QAAQ,eAAe,CAAC,cAAc,SAAS,WAAW,EAAE;IAChE,MAAM,MAAM,CAAC,MAAM,MAAM,OAAO,KAAK,SAAS,CAAC,MAAM,GAAG;IACxD,MAAM,QAAQ,CAAC,MAAM,QAAQ;IAC7B,OAAO;AACX;AACA,MAAM,mBAAmB;IACrB,cAAc;AAClB;AACA,qEAAqE;AACrE,kEAAkE;AAClE,6CAA6C;AAC7C,MAAM,uBAAuB,SAAU,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS;IACnE,OAAO,cAAc,CAAC,QAAQ,MAAM,KAAK,YAAY,WAAW,CAAC,MAC7D,QAAQ,MAAM,KAAK,YAAY,WAAW,EAAE;AACpD;AACA,MAAM,eAAe;AACrB,SAAS,QAAQ,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG;IAClD,IAAI;IACJ,OAAS;QACL,IAAI,QAAQ,cAAc,OAAO,WAC7B,OAAO;QACX,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,SAAS,KAAK,GAAG;YACvC,IAAI,SAAS,KAAK,UAAU;YAC5B,IAAI,CAAC,UAAU,OAAO,QAAQ,IAAI,KAAK,aAAa,SAAS,aAAa,IAAI,CAAC,KAAK,QAAQ,KACxF,KAAK,eAAe,IAAI,SACxB,OAAO;YACX,MAAM,SAAS,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC;YACvC,OAAO;QACX,OACK,IAAI,KAAK,QAAQ,IAAI,GAAG;YACzB,IAAI,QAAQ,KAAK,UAAU,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;YACrD,IAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,eAAe,IAAI,SAAS;gBACzD,IAAI,CAAC,KAAK,MAAM,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,kBAAkB,EAClF,OAAO;qBAEP,OAAO;YACf,OACK;gBACD,OAAO;gBACP,MAAM,MAAM,IAAI,SAAS,QAAQ;YACrC;QACJ,OACK;YACD,OAAO;QACX;IACJ;AACJ;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,KAAK,QAAQ,IAAI,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK,UAAU,CAAC,MAAM;AAC9E;AACA,SAAS,iBAAiB,IAAI,EAAE,MAAM;IAClC,OAAS;QACL,IAAI,KAAK,QAAQ,IAAI,KAAK,QACtB,OAAO;QACX,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,GAAG;YAClC,IAAI,KAAK,eAAe,IAAI,SACxB,OAAO;YACX,OAAO,KAAK,UAAU,CAAC,SAAS,EAAE;YAClC,SAAS,SAAS;QACtB,OACK,IAAI,KAAK,UAAU,IAAI,CAAC,aAAa,OAAO;YAC7C,SAAS,SAAS;YAClB,OAAO,KAAK,UAAU;QAC1B,OACK;YACD,OAAO;QACX;IACJ;AACJ;AACA,SAAS,gBAAgB,IAAI,EAAE,MAAM;IACjC,OAAS;QACL,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,KAAK,SAAS,CAAC,MAAM,EACpD,OAAO;QACX,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,KAAK,UAAU,CAAC,MAAM,EAAE;YACvD,IAAI,KAAK,eAAe,IAAI,SACxB,OAAO;YACX,OAAO,KAAK,UAAU,CAAC,OAAO;YAC9B,SAAS;QACb,OACK,IAAI,KAAK,UAAU,IAAI,CAAC,aAAa,OAAO;YAC7C,SAAS,SAAS,QAAQ;YAC1B,OAAO,KAAK,UAAU;QAC1B,OACK;YACD,OAAO;QACX;IACJ;AACJ;AACA,SAAS,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM;IAClC,IAAK,IAAI,UAAU,UAAU,GAAG,QAAQ,UAAU,SAAS,OAAO,WAAW,OAAQ;QACjF,IAAI,QAAQ,QACR,OAAO;QACX,IAAI,QAAQ,SAAS;QACrB,OAAO,KAAK,UAAU;QACtB,IAAI,CAAC,MACD,OAAO;QACX,UAAU,WAAW,SAAS;QAC9B,QAAQ,SAAS,SAAS,SAAS;IACvC;AACJ;AACA,SAAS,aAAa,GAAG;IACrB,IAAI;IACJ,IAAK,IAAI,MAAM,KAAK,KAAK,MAAM,IAAI,UAAU,CACzC,IAAI,OAAO,IAAI,UAAU,EACrB;IACR,OAAO,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,OAAO,KAAK,UAAU,IAAI,GAAG;AAC/F;AACA,wFAAwF;AACxF,2DAA2D;AAC3D,MAAM,qBAAqB,SAAU,MAAM;IACvC,OAAO,OAAO,SAAS,IAAI,qBAAqB,OAAO,SAAS,EAAE,OAAO,WAAW,EAAE,OAAO,UAAU,EAAE,OAAO,YAAY;AAChI;AACA,SAAS,SAAS,OAAO,EAAE,GAAG;IAC1B,IAAI,QAAQ,SAAS,WAAW,CAAC;IACjC,MAAM,SAAS,CAAC,WAAW,MAAM;IACjC,MAAM,OAAO,GAAG;IAChB,MAAM,GAAG,GAAG,MAAM,IAAI,GAAG;IACzB,OAAO;AACX;AACA,SAAS,kBAAkB,GAAG;IAC1B,IAAI,MAAM,IAAI,aAAa;IAC3B,MAAO,OAAO,IAAI,UAAU,CACxB,MAAM,IAAI,UAAU,CAAC,aAAa;IACtC,OAAO;AACX;AACA,SAAS,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC;IAC7B,IAAI,IAAI,sBAAsB,EAAE;QAC5B,IAAI;YACA,IAAI,MAAM,IAAI,sBAAsB,CAAC,GAAG;YACxC,4DAA4D;YAC5D,8DAA8D;YAC9D,SAAS;YACT,IAAI,KACA,OAAO;gBAAE,MAAM,IAAI,UAAU;gBAAE,QAAQ,KAAK,GAAG,CAAC,SAAS,IAAI,UAAU,GAAG,IAAI,MAAM;YAAE;QAC9F,EACA,OAAO,GAAG,CAAE;IAChB;IACA,IAAI,IAAI,mBAAmB,EAAE;QACzB,IAAI,QAAQ,IAAI,mBAAmB,CAAC,GAAG;QACvC,IAAI,OACA,OAAO;YAAE,MAAM,MAAM,cAAc;YAAE,QAAQ,KAAK,GAAG,CAAC,SAAS,MAAM,cAAc,GAAG,MAAM,WAAW;QAAE;IACjH;AACJ;AAEA,MAAM,MAAM,OAAO,aAAa,cAAc,YAAY;AAC1D,MAAM,MAAM,OAAO,YAAY,cAAc,WAAW;AACxD,MAAM,QAAQ,AAAC,OAAO,IAAI,SAAS,IAAK;AACxC,MAAM,UAAU,cAAc,IAAI,CAAC;AACnC,MAAM,YAAY,UAAU,IAAI,CAAC;AACjC,MAAM,UAAU,wCAAwC,IAAI,CAAC;AAC7D,MAAM,KAAK,CAAC,CAAC,CAAC,aAAa,WAAW,OAAO;AAC7C,MAAM,aAAa,YAAY,SAAS,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,GAAG;AACvG,MAAM,QAAQ,CAAC,MAAM,gBAAgB,IAAI,CAAC;AAC1C,SAAS,CAAC,CAAC,iBAAiB,IAAI,CAAC,UAAU;IAAC;IAAG;CAAE,CAAC,CAAC,EAAE;AACrD,MAAM,UAAU,CAAC,MAAM,gBAAgB,IAAI,CAAC;AAC5C,MAAM,SAAS,CAAC,CAAC;AACjB,MAAM,iBAAiB,UAAU,CAAC,OAAO,CAAC,EAAE,GAAG;AAC/C,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,iBAAiB,IAAI,CAAC,IAAI,MAAM;AAC/D,kDAAkD;AAClD,MAAM,MAAM,UAAU,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,IAAI,cAAc,GAAG,CAAC;AACnF,MAAM,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,QAAQ,IAAI,KAAK;AAC1D,MAAM,UAAU,MAAM,MAAM,IAAI,CAAC,IAAI,QAAQ,IAAI;AACjD,MAAM,UAAU,aAAa,IAAI,CAAC;AAClC,MAAM,SAAS,CAAC,CAAC,OAAO,yBAAyB,IAAI,eAAe,CAAC,KAAK;AAC1E,MAAM,iBAAiB,SAAS,CAAC,CAAC,uBAAuB,IAAI,CAAC,UAAU,SAAS,KAAK;IAAC;IAAG;CAAE,CAAC,CAAC,EAAE,GAAG;AAEnG,SAAS,WAAW,GAAG;IACnB,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,WAAW,CAAC,cAAc;IAC1D,IAAI,IACA,OAAO;QACH,MAAM;QAAG,OAAO,GAAG,KAAK;QACxB,KAAK;QAAG,QAAQ,GAAG,MAAM;IAC7B;IACJ,OAAO;QAAE,MAAM;QAAG,OAAO,IAAI,eAAe,CAAC,WAAW;QACpD,KAAK;QAAG,QAAQ,IAAI,eAAe,CAAC,YAAY;IAAC;AACzD;AACA,SAAS,QAAQ,KAAK,EAAE,IAAI;IACxB,OAAO,OAAO,SAAS,WAAW,QAAQ,KAAK,CAAC,KAAK;AACzD;AACA,SAAS,WAAW,IAAI;IACpB,IAAI,OAAO,KAAK,qBAAqB;IACrC,sDAAsD;IACtD,IAAI,SAAS,AAAC,KAAK,KAAK,GAAG,KAAK,WAAW,IAAK;IAChD,IAAI,SAAS,AAAC,KAAK,MAAM,GAAG,KAAK,YAAY,IAAK;IAClD,4DAA4D;IAC5D,OAAO;QAAE,MAAM,KAAK,IAAI;QAAE,OAAO,KAAK,IAAI,GAAG,KAAK,WAAW,GAAG;QAC5D,KAAK,KAAK,GAAG;QAAE,QAAQ,KAAK,GAAG,GAAG,KAAK,YAAY,GAAG;IAAO;AACrE;AACA,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC5C,IAAI,kBAAkB,KAAK,QAAQ,CAAC,sBAAsB,GAAG,eAAe,KAAK,QAAQ,CAAC,mBAAmB;IAC7G,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa;IAChC,IAAK,IAAI,SAAS,YAAY,KAAK,GAAG,GAAI;QACtC,IAAI,CAAC,QACD;QACJ,IAAI,OAAO,QAAQ,IAAI,GAAG;YACtB,SAAS,WAAW;YACpB;QACJ;QACA,IAAI,MAAM;QACV,IAAI,QAAQ,OAAO,IAAI,IAAI;QAC3B,IAAI,WAAW,QAAQ,WAAW,OAAO,WAAW;QACpD,IAAI,QAAQ,GAAG,QAAQ;QACvB,IAAI,KAAK,GAAG,GAAG,SAAS,GAAG,GAAG,QAAQ,iBAAiB,QACnD,QAAQ,CAAC,CAAC,SAAS,GAAG,GAAG,KAAK,GAAG,GAAG,QAAQ,cAAc,MAAM;aAC/D,IAAI,KAAK,MAAM,GAAG,SAAS,MAAM,GAAG,QAAQ,iBAAiB,WAC9D,QAAQ,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,SAAS,MAAM,GAAG,SAAS,GAAG,GACzD,KAAK,GAAG,GAAG,QAAQ,cAAc,SAAS,SAAS,GAAG,GACtD,KAAK,MAAM,GAAG,SAAS,MAAM,GAAG,QAAQ,cAAc;QAChE,IAAI,KAAK,IAAI,GAAG,SAAS,IAAI,GAAG,QAAQ,iBAAiB,SACrD,QAAQ,CAAC,CAAC,SAAS,IAAI,GAAG,KAAK,IAAI,GAAG,QAAQ,cAAc,OAAO;aAClE,IAAI,KAAK,KAAK,GAAG,SAAS,KAAK,GAAG,QAAQ,iBAAiB,UAC5D,QAAQ,KAAK,KAAK,GAAG,SAAS,KAAK,GAAG,QAAQ,cAAc;QAChE,IAAI,SAAS,OAAO;YAChB,IAAI,OAAO;gBACP,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO;YACpC,OACK;gBACD,IAAI,SAAS,IAAI,UAAU,EAAE,SAAS,IAAI,SAAS;gBACnD,IAAI,OACA,IAAI,SAAS,IAAI;gBACrB,IAAI,OACA,IAAI,UAAU,IAAI;gBACtB,IAAI,KAAK,IAAI,UAAU,GAAG,QAAQ,KAAK,IAAI,SAAS,GAAG;gBACvD,OAAO;oBAAE,MAAM,KAAK,IAAI,GAAG;oBAAI,KAAK,KAAK,GAAG,GAAG;oBAAI,OAAO,KAAK,KAAK,GAAG;oBAAI,QAAQ,KAAK,MAAM,GAAG;gBAAG;YACxG;QACJ;QACA,IAAI,MAAM,QAAQ,UAAU,iBAAiB,QAAQ,QAAQ;QAC7D,IAAI,mBAAmB,IAAI,CAAC,MACxB;QACJ,SAAS,OAAO,aAAa,OAAO,YAAY,GAAG,WAAW;IAClE;AACJ;AACA,qEAAqE;AACrE,mEAAmE;AACnE,qEAAqE;AACrE,8CAA8C;AAC9C,SAAS,eAAe,IAAI;IACxB,IAAI,OAAO,KAAK,GAAG,CAAC,qBAAqB,IAAI,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG;IAC1E,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,SAAS,GAAG,IAAI,KAAK,GAAG,CAAC,aAAa,KAAK,MAAM,GAAG,KAAK,EAAG;QACvG,IAAI,MAAM,KAAK,IAAI,CAAC,gBAAgB,CAAC,GAAG;QACxC,IAAI,CAAC,OAAO,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,MAC9C;QACJ,IAAI,YAAY,IAAI,qBAAqB;QACzC,IAAI,UAAU,GAAG,IAAI,SAAS,IAAI;YAC9B,SAAS;YACT,SAAS,UAAU,GAAG;YACtB;QACJ;IACJ;IACA,OAAO;QAAE,QAAQ;QAAQ,QAAQ;QAAQ,OAAO,YAAY,KAAK,GAAG;IAAE;AAC1E;AACA,SAAS,YAAY,GAAG;IACpB,IAAI,QAAQ,EAAE,EAAE,MAAM,IAAI,aAAa;IACvC,IAAK,IAAI,MAAM,KAAK,KAAK,MAAM,WAAW,KAAM;QAC5C,MAAM,IAAI,CAAC;YAAE,KAAK;YAAK,KAAK,IAAI,SAAS;YAAE,MAAM,IAAI,UAAU;QAAC;QAChE,IAAI,OAAO,KACP;IACR;IACA,OAAO;AACX;AACA,sEAAsE;AACtE,iDAAiD;AACjD,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;IAC7C,IAAI,YAAY,SAAS,OAAO,qBAAqB,GAAG,GAAG,GAAG;IAC9D,mBAAmB,OAAO,aAAa,IAAI,IAAI,YAAY;AAC/D;AACA,SAAS,mBAAmB,KAAK,EAAE,IAAI;IACnC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,EAAE;QACjC,IAAI,IAAI,SAAS,IAAI,MAAM,MACvB,IAAI,SAAS,GAAG,MAAM;QAC1B,IAAI,IAAI,UAAU,IAAI,MAClB,IAAI,UAAU,GAAG;IACzB;AACJ;AACA,IAAI,yBAAyB;AAC7B,sEAAsE;AACtE,wCAAwC;AACxC,SAAS,mBAAmB,GAAG;IAC3B,IAAI,IAAI,SAAS,EACb,OAAO,IAAI,SAAS,IAAI,QAAQ;IACpC,IAAI,wBACA,OAAO,IAAI,KAAK,CAAC;IACrB,IAAI,SAAS,YAAY;IACzB,IAAI,KAAK,CAAC,0BAA0B,OAAO;QACvC,IAAI,iBAAgB;YAChB,yBAAyB;gBAAE,eAAe;YAAK;YAC/C,OAAO;QACX;IACJ,IAAI;IACJ,IAAI,CAAC,wBAAwB;QACzB,yBAAyB;QACzB,mBAAmB,QAAQ;IAC/B;AACJ;AACA,SAAS,iBAAiB,IAAI,EAAE,MAAM;IAClC,IAAI,SAAS,YAAY,KAAK,eAAe,SAAS;IACtD,IAAI,SAAS,OAAO,GAAG,EAAE,SAAS,OAAO,GAAG;IAC5C,IAAI,YAAY;IAChB,IAAK,IAAI,QAAQ,KAAK,UAAU,EAAE,aAAa,GAAG,OAAO,QAAQ,MAAM,WAAW,EAAE,aAAc;QAC9F,IAAI;QACJ,IAAI,MAAM,QAAQ,IAAI,GAClB,QAAQ,MAAM,cAAc;aAC3B,IAAI,MAAM,QAAQ,IAAI,GACvB,QAAQ,UAAU,OAAO,cAAc;aAEvC;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,OAAO,KAAK,CAAC,EAAE;YACnB,IAAI,KAAK,GAAG,IAAI,UAAU,KAAK,MAAM,IAAI,QAAQ;gBAC7C,SAAS,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;gBAC/B,SAAS,KAAK,GAAG,CAAC,KAAK,GAAG,EAAE;gBAC5B,IAAI,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,GAAG,KAAK,IAAI,GAAG,OAAO,IAAI,GACpD,KAAK,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,IAAI,GAAG,KAAK,KAAK,GAAG;gBAC5D,IAAI,KAAK,WAAW;oBAChB,UAAU;oBACV,YAAY;oBACZ,gBAAgB,MAAM,QAAQ,QAAQ,IAAI,IAAI;wBAC1C,MAAM,KAAK,KAAK,GAAG,OAAO,IAAI,GAAG,KAAK,KAAK,GAAG,KAAK,IAAI;wBACvD,KAAK,OAAO,GAAG;oBACnB,IAAI;oBACJ,IAAI,MAAM,QAAQ,IAAI,KAAK,IACvB,SAAS,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;oBAC9E;gBACJ;YACJ,OACK,IAAI,KAAK,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI,EAAE;gBACpG,aAAa;gBACb,cAAc;oBAAE,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,OAAO,IAAI;oBAAI,KAAK,KAAK,GAAG;gBAAC;YAChG;YACA,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,IAAI,KAAK,KAAK,IAAI,OAAO,GAAG,IAAI,KAAK,GAAG,IAChE,OAAO,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,GAAG,IAAI,KAAK,MAAM,GACrD,SAAS,aAAa;QAC9B;IACJ;IACA,IAAI,CAAC,WAAW,YAAY;QACxB,UAAU;QACV,gBAAgB;QAChB,YAAY;IAChB;IACA,IAAI,WAAW,QAAQ,QAAQ,IAAI,GAC/B,OAAO,iBAAiB,SAAS;IACrC,IAAI,CAAC,WAAY,aAAa,QAAQ,QAAQ,IAAI,GAC9C,OAAO;QAAE;QAAM;IAAO;IAC1B,OAAO,iBAAiB,SAAS;AACrC;AACA,SAAS,iBAAiB,IAAI,EAAE,MAAM;IAClC,IAAI,MAAM,KAAK,SAAS,CAAC,MAAM;IAC/B,IAAI,QAAQ,SAAS,WAAW;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC1B,MAAM,MAAM,CAAC,MAAM,IAAI;QACvB,MAAM,QAAQ,CAAC,MAAM;QACrB,IAAI,OAAO,WAAW,OAAO;QAC7B,IAAI,KAAK,GAAG,IAAI,KAAK,MAAM,EACvB;QACJ,IAAI,OAAO,QAAQ,OACf,OAAO;YAAE;YAAM,QAAQ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;QAAE;IACzF;IACA,OAAO;QAAE;QAAM,QAAQ;IAAE;AAC7B;AACA,SAAS,OAAO,MAAM,EAAE,IAAI;IACxB,OAAO,OAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,IAAI,IAAI,KAAK,KAAK,GAAG,KAC/D,OAAO,GAAG,IAAI,KAAK,GAAG,GAAG,KAAK,OAAO,GAAG,IAAI,KAAK,MAAM,GAAG;AAClE;AACA,SAAS,aAAa,GAAG,EAAE,MAAM;IAC7B,IAAI,SAAS,IAAI,UAAU;IAC3B,IAAI,UAAU,QAAQ,IAAI,CAAC,OAAO,QAAQ,KAAK,OAAO,IAAI,GAAG,IAAI,qBAAqB,GAAG,IAAI,EACzF,OAAO;IACX,OAAO;AACX;AACA,SAAS,eAAe,IAAI,EAAE,GAAG,EAAE,MAAM;IACrC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,iBAAiB,KAAK,SAAS,OAAO,CAAC;IAC9D,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,UAAU,EAAE;QACxC,IAAI,OAAO,KAAK,qBAAqB;QACrC,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;IACxF;IACA,OAAO,KAAK,OAAO,CAAC,UAAU,CAAC,MAAM,QAAQ;AACjD;AACA,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM;IAC5C,6DAA6D;IAC7D,oEAAoE;IACpE,oEAAoE;IACpE,gEAAgE;IAChE,iEAAiE;IACjE,8DAA8D;IAC9D,IAAI,eAAe,CAAC;IACpB,IAAK,IAAI,MAAM,MAAM,WAAW,QAAS;QACrC,IAAI,OAAO,KAAK,GAAG,EACf;QACJ,IAAI,OAAO,KAAK,OAAO,CAAC,WAAW,CAAC,KAAK,OAAO;QAChD,IAAI,CAAC,MACD,OAAO;QACX,IAAI,KAAK,GAAG,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,UAAU,KAC/E,qDAAqD;QACrD,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,qBAAqB,EAAE,EAAE,KAAK,IAAI,KAAK,MAAM,GAAG;YAClE,IAAI,KAAK,IAAI,CAAC,OAAO,IAAI,KAAK,MAAM,EAAE;gBAClC,kFAAkF;gBAClF,IAAI,CAAC,YAAY,KAAK,IAAI,GAAG,OAAO,IAAI,IAAI,KAAK,GAAG,GAAG,OAAO,GAAG,EAC7D,eAAe,KAAK,SAAS;qBAC5B,IAAI,CAAC,YAAY,KAAK,KAAK,GAAG,OAAO,IAAI,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,EACtE,eAAe,KAAK,QAAQ;gBAChC,WAAW;YACf;YACA,IAAI,CAAC,KAAK,UAAU,IAAI,eAAe,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;gBAC3D,4EAA4E;gBAC5E,IAAI,SAAS,KAAK,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,KAAK,MAAM,IAAI,IACnE,OAAO,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI;gBAC/C,OAAO,SAAS,KAAK,SAAS,GAAG,KAAK,QAAQ;YAClD;QACJ;QACA,MAAM,KAAK,GAAG,CAAC,UAAU;IAC7B;IACA,OAAO,eAAe,CAAC,IAAI,eAAe,KAAK,OAAO,CAAC,UAAU,CAAC,MAAM,QAAQ,CAAC;AACrF;AACA,SAAS,iBAAiB,OAAO,EAAE,MAAM,EAAE,GAAG;IAC1C,IAAI,MAAM,QAAQ,UAAU,CAAC,MAAM;IACnC,IAAI,OAAO,IAAI,GAAG,GAAG,IAAI,MAAM,EAAE;QAC7B,IAAK,IAAI,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,KAAK,IAAI,SAAU;YACnI,IAAI,QAAQ,QAAQ,UAAU,CAAC,EAAE;YACjC,IAAI,MAAM,QAAQ,IAAI,GAAG;gBACrB,IAAI,QAAQ,MAAM,cAAc;gBAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACnC,IAAI,OAAO,KAAK,CAAC,EAAE;oBACnB,IAAI,OAAO,QAAQ,OACf,OAAO,iBAAiB,OAAO,QAAQ;gBAC/C;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,QACvB;QACR;IACJ;IACA,OAAO;AACX;AACA,yEAAyE;AACzE,SAAS,YAAY,IAAI,EAAE,MAAM;IAC7B,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,EAAE,MAAM,SAAS;IACjD,IAAI,QAAQ,eAAe,KAAK,OAAO,IAAI,EAAE,OAAO,GAAG;IACvD,IAAI,OACA,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK;IAC7B,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,gBAAgB,GAAG,KAAK,IAAI,GAAG,GAAG,EAClD,gBAAgB,CAAC,OAAO,IAAI,EAAE,OAAO,GAAG;IAC7C,IAAI;IACJ,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,UAAU,GAAG,MAAM;QACtE,IAAI,MAAM,KAAK,GAAG,CAAC,qBAAqB;QACxC,IAAI,CAAC,OAAO,QAAQ,MAChB,OAAO;QACX,MAAM,iBAAiB,KAAK,GAAG,EAAE,QAAQ;QACzC,IAAI,CAAC,KACD,OAAO;IACf;IACA,4EAA4E;IAC5E,IAAI,QAAQ;QACR,IAAK,IAAI,IAAI,KAAK,QAAQ,GAAG,IAAI,WAAW,GACxC,IAAI,EAAE,SAAS,EACX,OAAO;IACnB;IACA,MAAM,aAAa,KAAK;IACxB,IAAI,MAAM;QACN,IAAI,SAAS,KAAK,QAAQ,IAAI,GAAG;YAC7B,kEAAkE;YAClE,8DAA8D;YAC9D,SAAS,KAAK,GAAG,CAAC,QAAQ,KAAK,UAAU,CAAC,MAAM;YAChD,4DAA4D;YAC5D,+BAA+B;YAC/B,IAAI,SAAS,KAAK,UAAU,CAAC,MAAM,EAAE;gBACjC,IAAI,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE;gBACpC,IAAI,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,qBAAqB,EAAE,EAAE,KAAK,IAAI,OAAO,IAAI,IACnF,IAAI,MAAM,GAAG,OAAO,GAAG,EACvB;YACR;QACJ;QACA,IAAI;QACJ,kHAAkH;QAClH,IAAI,UAAU,UAAU,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,UAAU,CAAC,SAAS,EAAE,EAAE,QAAQ,IAAI,KAC3F,KAAK,eAAe,IAAI,WAAW,KAAK,qBAAqB,GAAG,GAAG,IAAI,OAAO,GAAG,EACjF;QACJ,8DAA8D;QAC9D,wDAAwD;QACxD,IAAI,QAAQ,KAAK,GAAG,IAAI,UAAU,KAAK,UAAU,CAAC,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC,QAAQ,IAAI,KACvF,OAAO,GAAG,GAAG,KAAK,SAAS,CAAC,qBAAqB,GAAG,MAAM,EAC1D,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI;aAIhC,IAAI,UAAU,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,UAAU,CAAC,SAAS,EAAE,CAAC,QAAQ,IAAI,MAClF,MAAM,aAAa,MAAM,MAAM,QAAQ;IAC/C;IACA,IAAI,OAAO,MACP,MAAM,eAAe,MAAM,KAAK;IACpC,IAAI,OAAO,KAAK,OAAO,CAAC,WAAW,CAAC,KAAK;IACzC,OAAO;QAAE;QAAK,QAAQ,OAAO,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,CAAC;IAAE;AACpE;AACA,SAAS,QAAQ,IAAI;IACjB,OAAO,KAAK,GAAG,GAAG,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,KAAK,KAAK;AAC3D;AACA,SAAS,WAAW,MAAM,EAAE,IAAI;IAC5B,IAAI,QAAQ,OAAO,cAAc;IACjC,IAAI,MAAM,MAAM,EAAE;QACd,IAAI,QAAQ,KAAK,CAAC,OAAO,IAAI,IAAI,MAAM,MAAM,GAAG,EAAE;QAClD,IAAI,QAAQ,QACR,OAAO;IACf;IACA,OAAO,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,YAAY,OAAO,qBAAqB;AACpF;AACA,MAAM,OAAO;AACb,oEAAoE;AACpE,sDAAsD;AACtD,SAAS,YAAY,IAAI,EAAE,GAAG,EAAE,IAAI;IAChC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,OAAO,CAAC,UAAU,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI;IAC1E,IAAI,oBAAoB,UAAU;IAClC,IAAI,KAAK,QAAQ,IAAI,GAAG;QACpB,oEAAoE;QACpE,6CAA6C;QAC7C,IAAI,qBAAqB,CAAC,KAAK,IAAI,CAAC,KAAK,SAAS,KAAK,CAAC,OAAO,IAAI,CAAC,SAAS,UAAU,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG;YAC5G,IAAI,OAAO,WAAW,UAAU,MAAM,QAAQ,SAAS;YACvD,8DAA8D;YAC9D,sDAAsD;YACtD,6DAA6D;YAC7D,IAAI,SAAS,UAAU,KAAK,IAAI,CAAC,KAAK,SAAS,CAAC,SAAS,EAAE,KAAK,SAAS,KAAK,SAAS,CAAC,MAAM,EAAE;gBAC5F,IAAI,aAAa,WAAW,UAAU,MAAM,SAAS,GAAG,SAAS,IAAI,CAAC;gBACtE,IAAI,WAAW,GAAG,IAAI,KAAK,GAAG,EAAE;oBAC5B,IAAI,YAAY,WAAW,UAAU,MAAM,QAAQ,SAAS,IAAI,CAAC;oBACjE,IAAI,UAAU,GAAG,IAAI,KAAK,GAAG,EACzB,OAAO,SAAS,WAAW,UAAU,IAAI,GAAG,WAAW,IAAI;gBACnE;YACJ;YACA,OAAO;QACX,OACK;YACD,IAAI,OAAO,QAAQ,KAAK,QAAQ,WAAW,OAAO,IAAI,IAAI,CAAC;YAC3D,IAAI,OAAO,KAAK,CAAC,QAAQ;gBACrB;gBACA,WAAW,CAAC;YAChB,OACK,IAAI,QAAQ,KAAK,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE;gBACnD;gBACA,WAAW;YACf,OACK,IAAI,OAAO,GAAG;gBACf;YACJ,OACK;gBACD;YACJ;YACA,OAAO,SAAS,WAAW,UAAU,MAAM,MAAM,KAAK,WAAW,WAAW;QAChF;IACJ;IACA,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC;IAClD,4CAA4C;IAC5C,IAAI,CAAC,KAAK,MAAM,CAAC,aAAa,EAAE;QAC5B,IAAI,QAAQ,QAAQ,UAAU,CAAC,OAAO,KAAK,UAAU,SAAS,KAAK,GAAG;YAClE,IAAI,SAAS,KAAK,UAAU,CAAC,SAAS,EAAE;YACxC,IAAI,OAAO,QAAQ,IAAI,GACnB,OAAO,SAAS,OAAO,qBAAqB,IAAI;QACxD;QACA,IAAI,QAAQ,QAAQ,SAAS,SAAS,OAAO;YACzC,IAAI,QAAQ,KAAK,UAAU,CAAC,OAAO;YACnC,IAAI,MAAM,QAAQ,IAAI,GAClB,OAAO,SAAS,MAAM,qBAAqB,IAAI;QACvD;QACA,OAAO,SAAS,KAAK,qBAAqB,IAAI,QAAQ;IAC1D;IACA,mDAAmD;IACnD,IAAI,QAAQ,QAAQ,UAAU,CAAC,OAAO,KAAK,UAAU,SAAS,KAAK,GAAG;QAClE,IAAI,SAAS,KAAK,UAAU,CAAC,SAAS,EAAE;QACxC,IAAI,SAAS,OAAO,QAAQ,IAAI,IAAI,UAAU,QAAQ,SAAS,UAAU,CAAC,oBAAoB,IAAI,CAAC,KAG7F,OAAO,QAAQ,IAAI,KAAK,CAAC,OAAO,QAAQ,IAAI,QAAQ,CAAC,OAAO,WAAW,IAAI,SAAS;QAC1F,IAAI,QACA,OAAO,SAAS,WAAW,QAAQ,IAAI;IAC/C;IACA,IAAI,QAAQ,QAAQ,SAAS,SAAS,OAAO;QACzC,IAAI,QAAQ,KAAK,UAAU,CAAC,OAAO;QACnC,MAAO,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,eAAe,CACvD,QAAQ,MAAM,WAAW;QAC7B,IAAI,SAAS,CAAC,QAAQ,OAAO,MAAM,QAAQ,IAAI,IAAI,UAAU,OAAO,GAAI,oBAAoB,IAAI,KAC1F,MAAM,QAAQ,IAAI,IAAI,QAAQ;QACpC,IAAI,QACA,OAAO,SAAS,WAAW,QAAQ,CAAC,IAAI;IAChD;IACA,mEAAmE;IACnE,OAAO,SAAS,WAAW,KAAK,QAAQ,IAAI,IAAI,UAAU,QAAQ,MAAM,CAAC,OAAO,QAAQ;AAC5F;AACA,SAAS,SAAS,IAAI,EAAE,IAAI;IACxB,IAAI,KAAK,KAAK,IAAI,GACd,OAAO;IACX,IAAI,IAAI,OAAO,KAAK,IAAI,GAAG,KAAK,KAAK;IACrC,OAAO;QAAE,KAAK,KAAK,GAAG;QAAE,QAAQ,KAAK,MAAM;QAAE,MAAM;QAAG,OAAO;IAAE;AACnE;AACA,SAAS,SAAS,IAAI,EAAE,GAAG;IACvB,IAAI,KAAK,MAAM,IAAI,GACf,OAAO;IACX,IAAI,IAAI,MAAM,KAAK,GAAG,GAAG,KAAK,MAAM;IACpC,OAAO;QAAE,KAAK;QAAG,QAAQ;QAAG,MAAM,KAAK,IAAI;QAAE,OAAO,KAAK,KAAK;IAAC;AACnE;AACA,SAAS,iBAAiB,IAAI,EAAE,KAAK,EAAE,CAAC;IACpC,IAAI,YAAY,KAAK,KAAK,EAAE,SAAS,KAAK,IAAI,CAAC,aAAa;IAC5D,IAAI,aAAa,OACb,KAAK,WAAW,CAAC;IACrB,IAAI,UAAU,KAAK,GAAG,EAClB,KAAK,KAAK;IACd,IAAI;QACA,OAAO;IACX,SACQ;QACJ,IAAI,aAAa,OACb,KAAK,WAAW,CAAC;QACrB,IAAI,UAAU,KAAK,GAAG,IAAI,QACtB,OAAO,KAAK;IACpB;AACJ;AACA,wDAAwD;AACxD,4CAA4C;AAC5C,SAAS,uBAAuB,IAAI,EAAE,KAAK,EAAE,GAAG;IAC5C,IAAI,MAAM,MAAM,SAAS;IACzB,IAAI,OAAO,OAAO,OAAO,IAAI,KAAK,GAAG,IAAI,GAAG;IAC5C,OAAO,iBAAiB,MAAM,OAAO;QACjC,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,KAAK,OAAO,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE,OAAO,OAAO,CAAC,IAAI;QACzE,OAAS;YACL,IAAI,UAAU,KAAK,OAAO,CAAC,WAAW,CAAC,KAAK;YAC5C,IAAI,CAAC,SACD;YACJ,IAAI,QAAQ,IAAI,CAAC,OAAO,EAAE;gBACtB,MAAM,QAAQ,UAAU,IAAI,QAAQ,GAAG;gBACvC;YACJ;YACA,MAAM,QAAQ,GAAG,CAAC,UAAU;QAChC;QACA,IAAI,SAAS,YAAY,MAAM,KAAK,GAAG,EAAE;QACzC,IAAK,IAAI,QAAQ,IAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW,CAAE;YAC/D,IAAI;YACJ,IAAI,MAAM,QAAQ,IAAI,GAClB,QAAQ,MAAM,cAAc;iBAC3B,IAAI,MAAM,QAAQ,IAAI,GACvB,QAAQ,UAAU,OAAO,GAAG,MAAM,SAAS,CAAC,MAAM,EAAE,cAAc;iBAElE;YACJ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,IAAI,MAAM,KAAK,CAAC,EAAE;gBAClB,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,KACvB,CAAC,OAAO,OAAO,OAAO,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,OAAO,GAAG,IAAI,IAC5D,IAAI,MAAM,GAAG,OAAO,MAAM,GAAG,CAAC,OAAO,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,GAChE,OAAO;YACf;QACJ;QACA,OAAO;IACX;AACJ;AACA,MAAM,WAAW;AACjB,SAAS,yBAAyB,IAAI,EAAE,KAAK,EAAE,GAAG;IAC9C,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS;IAC/B,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW,EACzB,OAAO;IACX,IAAI,SAAS,MAAM,YAAY,EAAE,UAAU,CAAC,QAAQ,QAAQ,UAAU,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI;IAC/F,IAAI,MAAM,KAAK,YAAY;IAC3B,IAAI,CAAC,KACD,OAAO,MAAM,GAAG,IAAI,MAAM,KAAK,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG;IAC/D,8DAA8D;IAC9D,6DAA6D;IAC7D,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,EACvD,OAAO,OAAO,UAAU,OAAO,aAAa,UAAU;IAC1D,OAAO,iBAAiB,MAAM,OAAO;QACjC,yDAAyD;QACzD,gEAAgE;QAChE,6DAA6D;QAC7D,iEAAiE;QACjE,iBAAiB;QACjB,IAAI,EAAE,WAAW,OAAO,EAAE,aAAa,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,KAAK,iBAAiB;QAClG,IAAI,eAAe,IAAI,cAAc,CAAC,mBAAmB;;QAEzD,IAAI,MAAM,CAAC,QAAQ,KAAK;QACxB,IAAI,YAAY,MAAM,KAAK,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,MAAM,MAAM,MAAM,KAAK,GAAG;QACjF,IAAI,EAAE,WAAW,OAAO,EAAE,aAAa,MAAM,EAAE,GAAG,KAAK,iBAAiB;QACxE,IAAI,SAAS,WAAW,CAAC,UAAU,QAAQ,CAAC,QAAQ,QAAQ,IAAI,IAAI,UAAU,QAAQ,UAAU,KAC3F,WAAW,WAAW,UAAU;QACrC,iCAAiC;QACjC,IAAI;YACA,IAAI,QAAQ,CAAC,YAAY;YACzB,IAAI,WAAW,CAAC,WAAW,cAAc,UAAU,YAAY,KAAK,IAAI,MAAM,EAC1E,IAAI,MAAM,CAAC,SAAS;QAC5B,EACA,OAAO,GAAG,CAAE;QACZ,IAAI,gBAAgB,MAChB,IAAI,cAAc,GAAG;QACzB,OAAO;IACX;AACJ;AACA,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,SAAS,eAAe,IAAI,EAAE,KAAK,EAAE,GAAG;IACpC,IAAI,eAAe,SAAS,aAAa,KACrC,OAAO;IACX,cAAc;IACd,YAAY;IACZ,OAAO,eAAe,OAAO,QAAQ,OAAO,SACtC,uBAAuB,MAAM,OAAO,OACpC,yBAAyB,MAAM,OAAO;AAChD;AAEA,sEAAsE;AACtE,6DAA6D;AAC7D,EAAE;AACF,oDAAoD;AACpD,EAAE;AACF,gEAAgE;AAChE,mBAAmB;AACnB,EAAE;AACF,oEAAoE;AACpE,eAAe;AACf,EAAE;AACF,sEAAsE;AACtE,MAAM,YAAY,GAAG,cAAc,GAAG,gBAAgB,GAAG,aAAa;AACtE,kEAAkE;AAClE,sCAAsC;AACtC,MAAM;IACF,YAAY,MAAM,EAAE,QAAQ,EAAE,GAAG,EACjC,kEAAkE;IAClE,kCAAkC;IAClC,UAAU,CAAE;QACR,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;QACb,kEAAkE;QAClE,eAAe;QACf,IAAI,UAAU,GAAG,IAAI;IACzB;IACA,6DAA6D;IAC7D,oBAAoB;IACpB,cAAc,MAAM,EAAE;QAAE,OAAO;IAAO;IACtC,YAAY,IAAI,EAAE;QAAE,OAAO;IAAO;IAClC,YAAY,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE;QAAE,OAAO;IAAO;IACxD,YAAY,QAAQ,EAAE;QAAE,OAAO;IAAO;IACtC,6DAA6D;IAC7D,mEAAmE;IACnE,cAAc;IACd,YAAY;QAAE,OAAO;IAAM;IAC3B,gEAAgE;IAChE,sBAAsB;IACtB,UAAU,KAAK,EAAE;QAAE,OAAO;IAAO;IACjC,oDAAoD;IACpD,IAAI,OAAO;QACP,IAAI,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IACtC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI;QACjC,OAAO;IACX;IACA,+DAA+D;IAC/D,oBAAoB;IACpB,IAAI,SAAS;QAAE,OAAO;IAAG;IACzB,UAAU;QACN,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,EAC3B,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO;IAChC;IACA,eAAe,KAAK,EAAE;QAClB,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,GAAG,IAAK;YACzC,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC1B,IAAI,OAAO,OACP,OAAO;YACX,OAAO,IAAI,IAAI;QACnB;IACJ;IACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI;IAC1C;IACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG;IAC1E;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI;IACrC;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM;IACxD;IACA,gBAAgB,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE;QAC/B,kEAAkE;QAClE,+BAA+B;QAC/B,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,QAAQ,IAAI,IAAI,MAAM,IAAI,UAAU,GAAG;YACvF,IAAI,OAAO,GAAG;gBACV,IAAI,WAAW;gBACf,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;oBACxB,YAAY,IAAI,UAAU,CAAC,SAAS,EAAE;gBAC1C,OACK;oBACD,MAAO,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CACpC,MAAM,IAAI,UAAU;oBACxB,YAAY,IAAI,eAAe;gBACnC;gBACA,MAAO,aAAa,CAAC,CAAC,CAAC,OAAO,UAAU,UAAU,KAAK,KAAK,MAAM,IAAI,IAAI,EACtE,YAAY,UAAU,eAAe;gBACzC,OAAO,YAAY,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC,UAAU;YAC9E,OACK;gBACD,IAAI,UAAU;gBACd,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;oBACxB,WAAW,IAAI,UAAU,CAAC,OAAO;gBACrC,OACK;oBACD,MAAO,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CACpC,MAAM,IAAI,UAAU;oBACxB,WAAW,IAAI,WAAW;gBAC9B;gBACA,MAAO,YAAY,CAAC,CAAC,CAAC,OAAO,SAAS,UAAU,KAAK,KAAK,MAAM,IAAI,IAAI,EACpE,WAAW,SAAS,WAAW;gBACnC,OAAO,WAAW,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,CAAC,QAAQ;YAC/D;QACJ;QACA,8DAA8D;QAC9D,gEAAgE;QAChE,yCAAyC;QACzC,IAAI;QACJ,IAAI,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,QAAQ,SAAS,SAAS,IAAI,CAAC,UAAU;QAC7C,OACK,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,GAAG;YAC3F,QAAQ,IAAI,uBAAuB,CAAC,IAAI,CAAC,UAAU,IAAI;QAC3D,OACK,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;YAC1B,IAAI,UAAU,GACV,IAAK,IAAI,SAAS,MAAM,SAAS,OAAO,UAAU,CAAE;gBAChD,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;oBACpB,QAAQ;oBACR;gBACJ;gBACA,IAAI,OAAO,eAAe,EACtB;YACR;YACJ,IAAI,SAAS,QAAQ,UAAU,IAAI,UAAU,CAAC,MAAM,EAChD,IAAK,IAAI,SAAS,MAAM,SAAS,OAAO,UAAU,CAAE;gBAChD,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;oBACpB,QAAQ;oBACR;gBACJ;gBACA,IAAI,OAAO,WAAW,EAClB;YACR;QACR;QACA,OAAO,CAAC,SAAS,OAAO,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU;IAC/E;IACA,YAAY,GAAG,EAAE,YAAY,KAAK,EAAE;QAChC,IAAK,IAAI,QAAQ,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI,UAAU,CAAE;YACzD,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;YAC9B,IAAI,QAAQ,CAAC,CAAC,aAAa,KAAK,IAAI,GAAG;gBACnC,4DAA4D;gBAC5D,IAAI,SAAS,CAAC,UAAU,KAAK,OAAO,KAChC,CAAC,CAAC,QAAQ,QAAQ,IAAI,IAAI,QAAQ,QAAQ,CAAC,IAAI,QAAQ,IAAI,IAAI,MAAM,IAAI,UAAU,IAAI,WAAW,GAAG,GACrG,QAAQ;qBAER,OAAO;YACf;QACJ;IACJ;IACA,QAAQ,GAAG,EAAE;QACT,IAAI,OAAO,IAAI,UAAU;QACzB,IAAK,IAAI,MAAM,MAAM,KAAK,MAAM,IAAI,MAAM,CACtC,IAAI,OAAO,IAAI,EACX,OAAO;IACnB;IACA,WAAW,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE;QAC1B,IAAK,IAAI,OAAO,KAAK,MAAM,OAAO,KAAK,UAAU,CAAE;YAC/C,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;YACxB,IAAI,MACA,OAAO,KAAK,eAAe,CAAC,KAAK,QAAQ;QACjD;QACA,OAAO,CAAC;IACZ;IACA,kEAAkE;IAClE,2DAA2D;IAC3D,OAAO,GAAG,EAAE;QACR,IAAK,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YACvD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,MAAM,IAAI;YACvD,IAAI,UAAU,OAAO,OAAO,QAAQ;gBAChC,MAAO,CAAC,MAAM,MAAM,IAAI,MAAM,QAAQ,CAAC,MAAM,CAAE;oBAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,CAAC,MAAM,EAAE,IAAK;wBAC5C,IAAI,QAAQ,MAAM,QAAQ,CAAC,EAAE;wBAC7B,IAAI,MAAM,IAAI,EAAE;4BACZ,QAAQ;4BACR;wBACJ;oBACJ;gBACJ;gBACA,OAAO;YACX;YACA,IAAI,MAAM,KACN,OAAO,MAAM,MAAM,CAAC,MAAM,SAAS,MAAM,MAAM;YACnD,SAAS;QACb;IACJ;IACA,WAAW,GAAG,EAAE,IAAI,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,UAAU,EAChB,OAAO;YAAE,MAAM,IAAI,CAAC,GAAG;YAAE,QAAQ;YAAG,MAAM,MAAM;QAAE;QACtD,6CAA6C;QAC7C,IAAI,IAAI,GAAG,SAAS;QACpB,IAAK,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YAChD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,MAAM,IAAI;YACvD,IAAI,MAAM,OAAO,iBAAiB,sBAAsB;gBACpD,SAAS,MAAM;gBACf;YACJ;YACA,SAAS;QACb;QACA,0DAA0D;QAC1D,IAAI,QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE;QACzE,iFAAiF;QACjF,IAAK,IAAI,MAAM,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI,gBAAgB,kBAAkB,KAAK,IAAI,IAAI,GAAG,IAAK,CAAE;QACpH,sCAAsC;QACtC,IAAI,QAAQ,GAAG;YACX,IAAI,MAAM,QAAQ;YAClB,OAAQ,KAAK,QAAQ,MAAO;gBACxB,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG;gBAClC,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAC/C;YACR;YACA,IAAI,QAAQ,QAAQ,SAAS,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,OAAO,EACtD,OAAO,KAAK,UAAU,CAAC,KAAK,IAAI,EAAE;YACtC,OAAO;gBAAE,MAAM,IAAI,CAAC,UAAU;gBAAE,QAAQ,OAAO,SAAS,KAAK,GAAG,IAAI,IAAI;YAAE;QAC9E,OACK;YACD,IAAI,MAAM,QAAQ;YAClB,OAAQ,KAAK,QAAQ,MAAO;gBACxB,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;gBACrD,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAC/C;YACR;YACA,IAAI,QAAQ,SAAS,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,OAAO,EAC9C,OAAO,KAAK,UAAU,CAAC,GAAG;YAC9B,OAAO;gBAAE,MAAM,IAAI,CAAC,UAAU;gBAAE,QAAQ,OAAO,SAAS,KAAK,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM;YAAC;QAC1G;IACJ;IACA,kEAAkE;IAClE,SAAS;IACT,WAAW,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE;QAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,GACxB,OAAO;YAAE,MAAM,IAAI,CAAC,UAAU;YAAE;YAAM;YAAI,YAAY;YAAG,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM;QAAC;QACzG,IAAI,aAAa,CAAC,GAAG,WAAW,CAAC;QACjC,IAAK,IAAI,SAAS,MAAM,IAAI,IAAI,IAAK;YACjC,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,MAAM,IAAI;YACvD,IAAI,cAAc,CAAC,KAAK,QAAQ,KAAK;gBACjC,IAAI,YAAY,SAAS,MAAM,MAAM;gBACrC,4DAA4D;gBAC5D,IAAI,QAAQ,aAAa,MAAM,MAAM,MAAM,MAAM,IAAI,MAAM,IAAI,IAC3D,MAAM,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,UAAU,GAC7D,OAAO,MAAM,UAAU,CAAC,MAAM,IAAI;gBACtC,OAAO;gBACP,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBACxB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC/B,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,YAAY,CAAC,IAAI;wBAC9E,aAAa,SAAS,KAAK,GAAG,IAAI;wBAClC;oBACJ;oBACA,QAAQ,KAAK,IAAI;gBACrB;gBACA,IAAI,cAAc,CAAC,GACf,aAAa;YACrB;YACA,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG;gBAChE,KAAK;gBACL,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;oBAC/C,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAC3B,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,YAAY,CAAC,CAAC,IAAI;wBAC/E,WAAW,SAAS,KAAK,GAAG;wBAC5B;oBACJ;oBACA,MAAM,KAAK,IAAI;gBACnB;gBACA,IAAI,YAAY,CAAC,GACb,WAAW,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM;gBAChD;YACJ;YACA,SAAS;QACb;QACA,OAAO;YAAE,MAAM,IAAI,CAAC,UAAU;YAAE;YAAM;YAAI;YAAY;QAAS;IACnE;IACA,aAAa,IAAI,EAAE;QACf,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EACxD,OAAO;QACX,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE;QAClE,OAAO,MAAM,IAAI,IAAI,KAAK,MAAM,YAAY,CAAC;IACjD;IACA,YAAY,GAAG,EAAE;QACb,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;QAC5C,IAAI,KAAK,QAAQ,IAAI,KAAK,UAAU,KAAK,UAAU,CAAC,MAAM,EACtD,MAAM,IAAI,WAAW,uBAAuB;QAChD,OAAO,KAAK,UAAU,CAAC,OAAO;IAClC;IACA,kEAAkE;IAClE,iEAAiE;IACjE,oEAAoE;IACpE,kEAAkE;IAClE,kEAAkE;IAClE,aAAa,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,KAAK,EAAE;QAC5C,oEAAoE;QACpE,IAAI,OAAO,KAAK,GAAG,CAAC,QAAQ,OAAO,KAAK,KAAK,GAAG,CAAC,QAAQ;QACzD,IAAK,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YACvD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,MAAM,IAAI;YACvD,IAAI,OAAO,UAAU,KAAK,KACtB,OAAO,MAAM,YAAY,CAAC,SAAS,SAAS,MAAM,MAAM,EAAE,OAAO,SAAS,MAAM,MAAM,EAAE,MAAM;YAClG,SAAS;QACb;QACA,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,QAAQ,SAAS,CAAC,IAAI;QACtD,IAAI,UAAU,QAAQ,SAAS,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,IAAI;QAC7E,IAAI,SAAS,KAAK,IAAI,CAAC,YAAY;QACnC,IAAI,WAAW,KAAK,iBAAiB;QACrC,IAAI,WAAW;QACf,iEAAiE;QACjE,kEAAkE;QAClE,6DAA6D;QAC7D,gDAAgD;QAChD,IAAI,CAAC,SAAS,MAAM,KAAK,UAAU,MAAM;YACrC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;YACvB,IAAI,KAAK,QAAQ,IAAI,GAAG;gBACpB,WAAW,CAAC,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC,SAAS,EAAE,IAAI,IAAI;gBAC1D,cAAc;gBACd,IAAI,YAAY,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE;oBAC7C,IAAK,IAAI,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,UAAU,CAAE;wBACvD,IAAI,QAAQ,KAAK,WAAW,EAAE;4BAC1B,IAAI,MAAM,QAAQ,IAAI,MAClB,YAAY,UAAU;gCAAE,MAAM,MAAM,UAAU;gCAAE,QAAQ,SAAS,SAAS;4BAAE;4BAChF;wBACJ;wBACA,IAAI,OAAO,KAAK,UAAU;wBAC1B,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,OAAO,EACtC;oBACR;gBACJ;YACJ,OACK;gBACD,IAAI,OAAO,KAAK,UAAU,CAAC,SAAS,EAAE;gBACtC,WAAW,QAAQ,CAAC,KAAK,QAAQ,IAAI,QAAQ,KAAK,eAAe,IAAI,OAAO;YAChF;QACJ;QACA,iEAAiE;QACjE,sFAAsF;QACtF,IAAI,SAAS,SAAS,SAAS,IAAI,SAAS,SAAS,IAAI,QAAQ,IAAI,IAAI,SAAS,SAAS,CAAC,QAAQ,IAAI,GAAG;YACvG,IAAI,QAAQ,SAAS,SAAS,CAAC,UAAU,CAAC,SAAS,WAAW,CAAC;YAC/D,IAAI,SAAS,MAAM,eAAe,IAAI,SAClC,QAAQ;QAChB;QACA,IAAI,CAAC,CAAC,SAAS,YAAY,MAAM,KAC7B,qBAAqB,UAAU,IAAI,EAAE,UAAU,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS,YAAY,KACjG,qBAAqB,QAAQ,IAAI,EAAE,QAAQ,MAAM,EAAE,SAAS,SAAS,EAAE,SAAS,WAAW,GAC3F;QACJ,iEAAiE;QACjE,0DAA0D;QAC1D,2BAA2B;QAC3B,IAAI,iBAAiB;QACrB,IAAI,CAAC,OAAO,MAAM,IAAI,UAAU,IAAI,KAAK,CAAC,UAAU;YAChD,OAAO,QAAQ,CAAC,UAAU,IAAI,EAAE,UAAU,MAAM;YAChD,IAAI;gBACA,IAAI,UAAU,MACV,OAAO,MAAM,CAAC,QAAQ,IAAI,EAAE,QAAQ,MAAM;gBAC9C,iBAAiB;YACrB,EACA,OAAO,GAAG;YACN,iEAAiE;YACjE,wEAAwE;YACxE,uEAAuE;YACvE,wBAAwB;YACxB,qEAAqE;YACrE,0BAA0B;YAC9B;QACJ;QACA,IAAI,CAAC,gBAAgB;YACjB,IAAI,SAAS,MAAM;gBACf,IAAI,MAAM;gBACV,YAAY;gBACZ,UAAU;YACd;YACA,IAAI,QAAQ,SAAS,WAAW;YAChC,MAAM,MAAM,CAAC,QAAQ,IAAI,EAAE,QAAQ,MAAM;YACzC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,UAAU,MAAM;YAC/C,OAAO,eAAe;YACtB,OAAO,QAAQ,CAAC;QACpB;IACJ;IACA,eAAe,QAAQ,EAAE;QACrB,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,SAAS,IAAI,IAAI;IAChD;IACA,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU;IAC/F;IACA,6DAA6D;IAC7D,2DAA2D;IAC3D,UAAU,IAAI,EAAE,EAAE,EAAE;QAChB,IAAK,IAAI,SAAS,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YACvD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,MAAM,IAAI;YACvD,IAAI,UAAU,MAAM,QAAQ,OAAO,MAAM,SAAS,OAAO,OAAO,KAAK,QAAQ;gBACzE,IAAI,cAAc,SAAS,MAAM,MAAM,EAAE,YAAY,MAAM,MAAM,MAAM;gBACvE,IAAI,QAAQ,eAAe,MAAM,WAAW;oBACxC,IAAI,CAAC,KAAK,GAAG,QAAQ,UAAU,MAAM,MAAM,gBAAgB;oBAC3D,IAAI,QAAQ,eAAe,MAAM,aAC7B,CAAC,MAAM,WAAW,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,GAC7D,MAAM,KAAK,GAAG;yBAEd,MAAM,SAAS,CAAC,OAAO,aAAa,KAAK;oBAC7C;gBACJ,OACK;oBACD,MAAM,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,UAAU,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,GAC1G,gBAAgB;gBAC1B;YACJ;YACA,SAAS;QACb;QACA,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,mBAAmB;QACf,IAAI,QAAQ;QACZ,IAAK,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,OAAO,KAAK,MAAM,EAAE,QAAS;YAC5D,IAAI,QAAQ,SAAS,IAAI,gBAAgB;YACzC,IAAI,KAAK,KAAK,GAAG,OACb,KAAK,KAAK,GAAG;QACrB;IACJ;IACA,IAAI,UAAU;QAAE,OAAO;IAAO;IAC9B,IAAI,kBAAkB;QAAE,OAAO;IAAO;IACtC,IAAI,qBAAqB;QAAE,OAAO;IAAO;IACzC,OAAO,IAAI,EAAE;QAAE,OAAO;IAAO;AACjC;AACA,oEAAoE;AACpE,oCAAoC;AACpC,MAAM,uBAAuB;IACzB,YAAY,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAE;QACnC,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC,KAAK;QACjC,IAAI,OAAO,OAAO,YACd,MAAM,IAAI,MAAM;YACZ,IAAI,CAAC,MACD,OAAO;YACX,IAAI,KAAK,MAAM,EACX,OAAO,KAAK,MAAM,CAAC,cAAc,CAAC;QAC1C;QACJ,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACvB,IAAI,IAAI,QAAQ,IAAI,GAAG;gBACnB,IAAI,OAAO,SAAS,aAAa,CAAC;gBAClC,KAAK,WAAW,CAAC;gBACjB,MAAM;YACV;YACA,IAAI,eAAe,GAAG;YACtB,IAAI,SAAS,CAAC,GAAG,CAAC;QACtB;QACA,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK;QACvB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,IAAI;IACf;IACA,cAAc,MAAM,EAAE;QAClB,OAAO,IAAI,CAAC,KAAK,IAAI,aAAa,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;IACrE;IACA,YAAY;QAAE,OAAO;YAAE,QAAQ;QAAK;IAAG;IACvC,UAAU,KAAK,EAAE;QACb,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS;QACrC,OAAO,OAAO,KAAK,SAAS;IAChC;IACA,eAAe,QAAQ,EAAE;QACrB,OAAO,SAAS,IAAI,IAAI,eAAe,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe;IAC3E;IACA,UAAU;QACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;QACjC,KAAK,CAAC;IACV;IACA,IAAI,UAAU;QAAE,OAAO;IAAM;IAC7B,IAAI,qBAAqB;QAAE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;IAAE;IACvE,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;IAAE;AAC/C;AACA,MAAM,4BAA4B;IAC9B,YAAY,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,CAAE;QACpC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK;QACvB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;IAAE;IACtC,gBAAgB,GAAG,EAAE,MAAM,EAAE;QACzB,IAAI,OAAO,IAAI,CAAC,OAAO,EACnB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG,CAAC;QACpD,OAAO,IAAI,CAAC,UAAU,GAAG;IAC7B;IACA,WAAW,GAAG,EAAE;QACZ,OAAO;YAAE,MAAM,IAAI,CAAC,OAAO;YAAE,QAAQ;QAAI;IAC7C;IACA,eAAe,GAAG,EAAE;QAChB,OAAO,IAAI,IAAI,KAAK,mBAAmB,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI,QAAQ;IAC/E;AACJ;AACA,6DAA6D;AAC7D,sEAAsE;AACtE,kEAAkE;AAClE,6DAA6D;AAC7D,aAAa;AACb,MAAM,qBAAqB;IACvB,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,CAAE;QAC7C,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK;QACvB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,OAAO,OAAO,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QACtC,IAAI,SAAS,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC;QAC3C,IAAI,OAAO,UAAU,OAAO,MAAM,MAAM;QACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,EAClB,OAAO,qJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,SAAS,MAAM,KAAK,KAAK;QAClG,OAAO,IAAI,aAAa,QAAQ,MAAM,KAAK,GAAG,EAAE,KAAK,UAAU,IAAI,KAAK,GAAG,EAAE;IACjF;IACA,YAAY;QACR,IAAI,AAAC,IAAI,CAAC,KAAK,GAAG,cAAe,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAC9D,OAAO;QACX,OAAO;YAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,gBAAgB,IAAI,CAAC,UAAU;QAAC;IAChG;IACA,YAAY,IAAI,EAAE;QAAE,OAAO,IAAI,CAAC,KAAK,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAAO;IAC3E,UAAU,IAAI,EAAE,EAAE,EAAE;QAChB,KAAK,CAAC,UAAU,MAAM;QACtB,uCAAuC;QACvC,IAAI,IAAI,CAAC,KAAK,IAAI,WAAW;YACzB,IAAI,SAAS,IAAI,CAAC,MAAM;YACxB,MAAO,CAAC,OAAO,IAAI,CACf,SAAS,OAAO,MAAM;YAC1B,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,EACzB,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK;YAC7B,IAAI,CAAC,KAAK,GAAG;QACjB;IACJ;IACA,MAAM,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;QAClB,IAAI,OAAO,aAAa,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM;QAC7D,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,IAAI;QAC3C,IAAI,KAAK,MACL,QAAQ,aAAa,OAAO,IAAI,MAAM;QAC1C,IAAI,OAAO,GACP,QAAQ,aAAa,OAAO,GAAG,MAAM;QACzC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAC9B,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG;QACtB,KAAK,QAAQ,GAAG;QAChB,OAAO;IACX;IACA,eAAe,QAAQ,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,KAAK,CAAC,eAAe;IAChG;IACA,UAAU;QACN,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,OAAO;QACrB,KAAK,CAAC;IACV;AACJ;AACA,mEAAmE;AACnE,mEAAmE;AACnE,8CAA8C;AAC9C,MAAM,qBAAqB;IACvB,YAAY,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAE;QACjF,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK;QACvB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,mEAAmE;IACnE,kEAAkE;IAClE,oEAAoE;IACpE,0BAA0B;IAC1B,EAAE;IACF,iEAAiE;IACjE,sDAAsD;IACtD,kEAAkE;IAClE,eAAe;IACf,OAAO,OAAO,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE;QACzD,IAAI,SAAS,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;QAC7C,IAAI,OAAO,UAAU,OAAO,MAAM,MAAM;YACpC,8DAA8D;YAC9D,gBAAgB;YAChB,IAAI,CAAC,SACD,OAAO;YACX,IAAI,QAAQ,MAAM,EACd,OAAO,QAAQ,MAAM,CAAC,cAAc,CAAC;QAC7C,GAAG,WAAW;QACd,IAAI,MAAM,QAAQ,KAAK,GAAG,EAAE,aAAa,QAAQ,KAAK,UAAU;QAChE,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,KACD,MAAM,SAAS,cAAc,CAAC,KAAK,IAAI;iBACtC,IAAI,IAAI,QAAQ,IAAI,GACrB,MAAM,IAAI,WAAW;QAC7B,OACK,IAAI,CAAC,KAAK;YACX,IAAI,OAAO,qJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,MAAM,KAAK,KAAK;YAC1F,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,IAAI;QAC/B;QACA,IAAI,CAAC,cAAc,CAAC,KAAK,MAAM,IAAI,IAAI,QAAQ,IAAI,MAAM;YACrD,IAAI,CAAC,IAAI,YAAY,CAAC,oBAClB,IAAI,eAAe,GAAG;YAC1B,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,EACxB,IAAI,SAAS,GAAG;QACxB;QACA,IAAI,UAAU;QACd,MAAM,eAAe,KAAK,WAAW;QACrC,IAAI,MACA,OAAO,UAAU,IAAI,mBAAmB,QAAQ,MAAM,WAAW,WAAW,KAAK,cAAc,MAAM,SAAS,MAAM,MAAM,MAAM;aAC/H,IAAI,KAAK,MAAM,EAChB,OAAO,IAAI,aAAa,QAAQ,MAAM,WAAW,WAAW,KAAK,SAAS;aAE1E,OAAO,IAAI,aAAa,QAAQ,MAAM,WAAW,WAAW,KAAK,cAAc,MAAM,SAAS,MAAM,MAAM;IAClH;IACA,YAAY;QACR,0DAA0D;QAC1D,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EACjC,OAAO;QACX,+DAA+D;QAC/D,6DAA6D;QAC7D,+DAA+D;QAC/D,4BAA4B;QAC5B,IAAI,OAAO;YAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;QAAC;QAC/D,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,OAC7B,KAAK,kBAAkB,GAAG;QAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,KAAK,UAAU,GAAG,IAAM,IAAI,CAAC,IAAI,CAAC,OAAO;QAC7C,OACK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACxB,KAAK,cAAc,GAAG,IAAI,CAAC,UAAU;QACzC,OACK;YACD,sDAAsD;YACtD,gEAAgE;YAChE,cAAc;YACd,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;gBAChD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,UAAU,GAAG;oBACzC,KAAK,cAAc,GAAG,MAAM,GAAG,CAAC,UAAU;oBAC1C;gBACJ;YACJ;YACA,IAAI,CAAC,KAAK,cAAc,EACpB,KAAK,UAAU,GAAG,IAAM,qJAAA,CAAA,WAAQ,CAAC,KAAK;QAC9C;QACA,OAAO;IACX;IACA,YAAY,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE;QACpC,OAAO,IAAI,CAAC,KAAK,IAAI,aAAa,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,KAC/C,cAAc,WAAW,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS;IAC/E;IACA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;IAAE;IACxC,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI;IAAG;IAChD,mEAAmE;IACnE,kEAAkE;IAClE,2DAA2D;IAC3D,mBAAmB;IACnB,eAAe,IAAI,EAAE,GAAG,EAAE;QACtB,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM;QAC5C,IAAI,cAAc,KAAK,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,OAAO;QAC1E,IAAI,mBAAmB,eAAe,YAAY,GAAG,GAAG,CAAC,IAAI,cAAc;QAC3E,IAAI,qBAAqB,eAAe,YAAY,GAAG,GAAG;QAC1D,IAAI,UAAU,IAAI,gBAAgB,IAAI,EAAE,oBAAoB,iBAAiB,IAAI,EAAE;QACnF,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,GAAG;YAC5C,IAAI,OAAO,IAAI,CAAC,KAAK,EACjB,QAAQ,WAAW,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE,QAAQ;iBAC9C,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,YAC/B,QAAQ,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,qJAAA,CAAA,OAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE,QAAQ;YAClG,6DAA6D;YAC7D,kDAAkD;YAClD,QAAQ,WAAW,CAAC,QAAQ,MAAM;QACtC,GAAG,CAAC,OAAO,WAAW,WAAW;YAC7B,4DAA4D;YAC5D,QAAQ,WAAW,CAAC,MAAM,KAAK,EAAE,QAAQ;YACzC,+CAA+C;YAC/C,IAAI;YACJ,IAAI,QAAQ,aAAa,CAAC,OAAO,WAAW,WAAW;iBAClD,IAAI,sBAAsB,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,OACvD,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,MAAM,MAAM,QAAQ,IAC9C,CAAC,YAAY,QAAQ,kBAAkB,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,KAC9D,QAAQ,YAAY,CAAC,OAAO,WAAW,WAAW,WAAW;iBAC5D,IAAI,QAAQ,cAAc,CAAC,OAAO,WAAW,WAAW,MAAM,GAAG;iBACjE;gBACD,uBAAuB;gBACvB,QAAQ,OAAO,CAAC,OAAO,WAAW,WAAW,MAAM;YACvD;YACA,OAAO,MAAM,QAAQ;QACzB;QACA,uDAAuD;QACvD,QAAQ,WAAW,CAAC,EAAE,EAAE,QAAQ;QAChC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EACrB,QAAQ,iBAAiB;QAC7B,QAAQ,WAAW;QACnB,mCAAmC;QACnC,IAAI,QAAQ,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,eAAe;YAChD,gFAAgF;YAChF,IAAI,kBACA,IAAI,CAAC,uBAAuB,CAAC,MAAM;YACvC,YAAY,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE;YAC5C,IAAI,KACA,SAAS,IAAI,CAAC,GAAG;QACzB;IACJ;IACA,qBAAqB,IAAI,EAAE,GAAG,EAAE;QAC5B,kEAAkE;QAClE,0BAA0B;QAC1B,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS;QACvC,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,SAAS,YAAY,qJAAA,CAAA,gBAAa,KAAK,OAAO,OAAO,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EACnG,OAAO;QACX,IAAI,WAAW,KAAK,KAAK,CAAC,eAAe;QACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,UAAU,GACnD,OAAO;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACzB,kEAAkE;YAClE,8DAA8D;YAC9D,8BAA8B;YAC9B,IAAI,OAAO,SAAS,SAAS;YAC7B,IAAI,UAAU,mBAAmB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,OAAO,KAAK,KAAK;YAC3E,OAAO,UAAU,IAAI,OAAO;gBAAE,MAAM;gBAAU,KAAK;gBAAS;YAAK;QACrE,OACK;YACD,OAAO;gBAAE,MAAM;gBAAU,KAAK,CAAC;gBAAG,MAAM;YAAG;QAC/C;IACJ;IACA,wBAAwB,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QAC/C,gEAAgE;QAChE,IAAI,IAAI,CAAC,OAAO,CAAC,OACb;QACJ,mDAAmD;QACnD,IAAI,UAAU;QACd,OAAQ,UAAU,QAAQ,UAAU,CAAE;YAClC,IAAI,QAAQ,UAAU,IAAI,IAAI,CAAC,UAAU,EACrC;YACJ,MAAO,QAAQ,eAAe,CAC1B,QAAQ,UAAU,CAAC,WAAW,CAAC,QAAQ,eAAe;YAC1D,MAAO,QAAQ,WAAW,CACtB,QAAQ,UAAU,CAAC,WAAW,CAAC,QAAQ,WAAW;YACtD,IAAI,QAAQ,UAAU,EAClB,QAAQ,UAAU,GAAG;QAC7B;QACA,IAAI,OAAO,IAAI,oBAAoB,IAAI,EAAE,SAAS,MAAM;QACxD,KAAK,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACjC,yDAAyD;QACzD,IAAI,CAAC,QAAQ,GAAG,aAAa,IAAI,CAAC,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM;IAC9E;IACA,mEAAmE;IACnE,yBAAyB;IACzB,OAAO,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;QACrC,IAAI,IAAI,CAAC,KAAK,IAAI,cACd,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,IAAI,GAC1B,OAAO;QACX,IAAI,CAAC,WAAW,CAAC,MAAM,WAAW,WAAW;QAC7C,OAAO;IACX;IACA,YAAY,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;QAC1C,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,UAAU;QAC7C,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,gBAAgB,SAAS,EAAE;QACvB,IAAI,cAAc,WAAW,IAAI,CAAC,SAAS,GACvC;QACJ,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI;QACzC,IAAI,SAAS,IAAI,CAAC,GAAG;QACrB,IAAI,CAAC,GAAG,GAAG,eAAe,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,iBAAiB,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,YAAY,iBAAiB,WAAW,IAAI,CAAC,IAAI,EAAE;QACjJ,IAAI,IAAI,CAAC,GAAG,IAAI,QAAQ;YACpB,OAAO,UAAU,GAAG;YACpB,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI;QAC9B;QACA,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,6CAA6C;IAC7C,aAAa;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,GACzB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;QAC/B,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EACjD,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG;IAC7B;IACA,+CAA+C;IAC/C,eAAe;QACX,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG;YAC5B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;YAC9B,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EACjD,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;QACjC;IACJ;IACA,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;IAAE;AAC7C;AACA,qEAAqE;AACrE,8BAA8B;AAC9B,SAAS,YAAY,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI;IACrD,eAAe,KAAK,WAAW;IAC/B,IAAI,UAAU,IAAI,aAAa,WAAW,KAAK,WAAW,WAAW,KAAK,KAAK,KAAK,MAAM;IAC1F,IAAI,QAAQ,UAAU,EAClB,QAAQ,cAAc,CAAC,MAAM;IACjC,OAAO;AACX;AACA,MAAM,qBAAqB;IACvB,YAAY,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,CAAE;QAChE,KAAK,CAAC,QAAQ,MAAM,WAAW,WAAW,KAAK,MAAM,SAAS,MAAM;IACxE;IACA,YAAY;QACR,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;QAClC,MAAO,QAAQ,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,QAAQ,CAC7C,OAAO,KAAK,UAAU;QAC1B,OAAO;YAAE,MAAO,QAAQ;QAAM;IAClC;IACA,OAAO,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;QACrC,IAAI,IAAI,CAAC,KAAK,IAAI,cAAe,IAAI,CAAC,KAAK,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,MACtE,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,IAAI,GAC1B,OAAO;QACX,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACjG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,IAAI;YAClC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,OAAO,EAChC,KAAK,WAAW,GAAG;QAC3B;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,OAAO;IACX;IACA,WAAW;QACP,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU;QACtC,IAAK,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE,UAAU,CAC1C,IAAI,KAAK,WACL,OAAO;QACf,OAAO;IACX;IACA,WAAW,GAAG,EAAE;QACZ,OAAO;YAAE,MAAM,IAAI,CAAC,OAAO;YAAE,QAAQ;QAAI;IAC7C;IACA,gBAAgB,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE;QAC/B,IAAI,OAAO,IAAI,CAAC,OAAO,EACnB,OAAO,IAAI,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;QACnE,OAAO,KAAK,CAAC,gBAAgB,KAAK,QAAQ;IAC9C;IACA,eAAe,QAAQ,EAAE;QACrB,OAAO,SAAS,IAAI,IAAI,mBAAmB,SAAS,IAAI,IAAI;IAChE;IACA,MAAM,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;QAClB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,SAAS,cAAc,CAAC,KAAK,IAAI;QAC3E,OAAO,IAAI,aAAa,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,KAAK;IACzF;IACA,UAAU,IAAI,EAAE,EAAE,EAAE;QAChB,KAAK,CAAC,UAAU,MAAM;QACtB,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,GAC7E,IAAI,CAAC,KAAK,GAAG;IACrB;IACA,IAAI,UAAU;QAAE,OAAO;IAAO;IAC9B,OAAO,IAAI,EAAE;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IAAM;AAClD;AACA,oEAAoE;AACpE,uCAAuC;AACvC,MAAM,6BAA6B;IAC/B,YAAY;QAAE,OAAO;YAAE,QAAQ;QAAK;IAAG;IACvC,YAAY,QAAQ,EAAE;QAAE,OAAO,IAAI,CAAC,KAAK,IAAI,aAAa,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI;IAAU;IACzF,IAAI,UAAU;QAAE,OAAO;IAAM;IAC7B,IAAI,kBAAkB;QAAE,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI;IAAO;AAC/D;AACA,qEAAqE;AACrE,gEAAgE;AAChE,cAAc;AACd,MAAM,2BAA2B;IAC7B,YAAY,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAE;QACvF,KAAK,CAAC,QAAQ,MAAM,WAAW,WAAW,KAAK,YAAY,SAAS,MAAM;QAC1E,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,kEAAkE;IAClE,kEAAkE;IAClE,wBAAwB;IACxB,OAAO,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;QACrC,IAAI,IAAI,CAAC,KAAK,IAAI,YACd,OAAO;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG;YAC1E,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,WAAW;YAC/C,IAAI,QACA,IAAI,CAAC,WAAW,CAAC,MAAM,WAAW,WAAW;YACjD,OAAO;QACX,OACK,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,MAAM,EAAE;YACvC,OAAO;QACX,OACK;YACD,OAAO,KAAK,CAAC,OAAO,MAAM,WAAW,WAAW;QACpD;IACJ;IACA,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC;IAC1D;IACA,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC;IAC9D;IACA,aAAa,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;QACpC,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,MAAM,KAAK,IAAI,IACjE,KAAK,CAAC,aAAa,QAAQ,MAAM,MAAM;IACjD;IACA,UAAU;QACN,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,OAAO;QACrB,KAAK,CAAC;IACV;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS;IAC9D;IACA,eAAe,QAAQ,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,KAAK,CAAC,eAAe;IAChG;AACJ;AACA,mEAAmE;AACnE,gEAAgE;AAChE,mEAAmE;AACnE,SAAS,YAAY,SAAS,EAAE,KAAK,EAAE,IAAI;IACvC,IAAI,MAAM,UAAU,UAAU,EAAE,UAAU;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE,WAAW,KAAK,GAAG;QACxC,IAAI,SAAS,UAAU,IAAI,WAAW;YAClC,MAAO,YAAY,IAAK;gBACpB,MAAM,GAAG;gBACT,UAAU;YACd;YACA,MAAM,IAAI,WAAW;QACzB,OACK;YACD,UAAU;YACV,UAAU,YAAY,CAAC,UAAU;QACrC;QACA,IAAI,gBAAgB,cAAc;YAC9B,IAAI,MAAM,MAAM,IAAI,eAAe,GAAG,UAAU,SAAS;YACzD,YAAY,KAAK,UAAU,EAAE,KAAK,QAAQ,EAAE;YAC5C,MAAM,MAAM,IAAI,WAAW,GAAG,UAAU,UAAU;QACtD;IACJ;IACA,MAAO,IAAK;QACR,MAAM,GAAG;QACT,UAAU;IACd;IACA,IAAI,WAAW,KAAK,WAAW,IAAI,WAC/B,KAAK,WAAW,GAAG;AAC3B;AACA,MAAM,iBAAiB,SAAU,QAAQ;IACrC,IAAI,UACA,IAAI,CAAC,QAAQ,GAAG;AACxB;AACA,eAAe,SAAS,GAAG,OAAO,MAAM,CAAC;AACzC,MAAM,SAAS;IAAC,IAAI;CAAe;AACnC,SAAS,iBAAiB,SAAS,EAAE,IAAI,EAAE,SAAS;IAChD,IAAI,UAAU,MAAM,IAAI,GACpB,OAAO;IACX,IAAI,MAAM,YAAY,MAAM,CAAC,EAAE,GAAG,IAAI,gBAAgB,SAAS;QAAC;KAAI;IACpE,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,IAAI,QAAQ,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK;QACnC,IAAI,CAAC,OACD;QACJ,IAAI,MAAM,QAAQ,EACd,OAAO,IAAI,CAAC,MAAM,IAAI,eAAe,MAAM,QAAQ;QACvD,IAAK,IAAI,QAAQ,MAAO;YACpB,IAAI,MAAM,KAAK,CAAC,KAAK;YACrB,IAAI,OAAO,MACP;YACJ,IAAI,aAAa,OAAO,MAAM,IAAI,GAC9B,OAAO,IAAI,CAAC,MAAM,IAAI,eAAe,KAAK,QAAQ,GAAG,SAAS;YAClE,IAAI,QAAQ,SACR,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,MAAM,EAAE,IAAI;iBAChD,IAAI,QAAQ,SACb,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,MAAM,EAAE,IAAI;iBAChD,IAAI,QAAQ,YACb,GAAG,CAAC,KAAK,GAAG;QACpB;IACJ;IACA,OAAO;AACX;AACA,SAAS,eAAe,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW;IAChE,4BAA4B;IAC5B,IAAI,gBAAgB,UAAU,eAAe,QACzC,OAAO;IACX,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QACzC,IAAI,OAAO,WAAW,CAAC,EAAE,EAAE,OAAO,YAAY,CAAC,EAAE;QACjD,IAAI,GAAG;YACH,IAAI;YACJ,IAAI,QAAQ,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI,UAAU,YACpD,CAAC,SAAS,OAAO,UAAU,KAAK,OAAO,QAAQ,CAAC,WAAW,MAAM,KAAK,QAAQ,EAAE;gBAChF,SAAS;YACb,OACK;gBACD,SAAS,SAAS,aAAa,CAAC,KAAK,QAAQ;gBAC7C,OAAO,QAAQ,GAAG;gBAClB,OAAO,WAAW,CAAC;gBACnB,OAAO,MAAM,CAAC,EAAE;gBAChB,SAAS;YACb;QACJ;QACA,gBAAgB,QAAQ,QAAQ,MAAM,CAAC,EAAE,EAAE;IAC/C;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,GAAG,EAAE,IAAI,EAAE,GAAG;IACnC,IAAK,IAAI,QAAQ,KACb,IAAI,QAAQ,WAAW,QAAQ,WAAW,QAAQ,cAAc,CAAC,CAAC,QAAQ,GAAG,GACzE,IAAI,eAAe,CAAC;IAC5B,IAAK,IAAI,QAAQ,IACb,IAAI,QAAQ,WAAW,QAAQ,WAAW,QAAQ,cAAc,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EACnF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,KAAK;IACxC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,EAAE;QACzB,IAAI,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,WAAW,EAAE;QACtE,IAAI,UAAU,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,WAAW,EAAE;QACnE,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IACjC,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,GACjC,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;QACxC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAChC,IAAI,SAAS,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,GACjC,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QACpC,IAAI,IAAI,SAAS,CAAC,MAAM,IAAI,GACxB,IAAI,eAAe,CAAC;IAC5B;IACA,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,EAAE;QACzB,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,OAAO,iFAAiF;YAC5F,MAAO,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,EAC3B,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;QACrC;QACA,IAAI,IAAI,KAAK,EACT,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,KAAK;IACtC;AACJ;AACA,SAAS,eAAe,GAAG,EAAE,IAAI,EAAE,IAAI;IACnC,OAAO,eAAe,KAAK,KAAK,QAAQ,iBAAiB,MAAM,MAAM,IAAI,QAAQ,IAAI;AACzF;AACA,SAAS,cAAc,CAAC,EAAE,CAAC;IACvB,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,EACpB,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAC1B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,GACvB,OAAO;IACf,OAAO;AACX;AACA,iDAAiD;AACjD,SAAS,GAAG,GAAG;IACX,IAAI,OAAO,IAAI,WAAW;IAC1B,IAAI,UAAU,CAAC,WAAW,CAAC;IAC3B,OAAO;AACX;AACA,mEAAmE;AACnE,4CAA4C;AAC5C,MAAM;IACF,YAAY,GAAG,EAAE,IAAI,EAAE,IAAI,CAAE;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,8DAA8D;QAC9D,mBAAmB;QACnB,IAAI,CAAC,KAAK,GAAG;QACb,6DAA6D;QAC7D,aAAa;QACb,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,sCAAsC;QACtC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE;IAC/C;IACA,+DAA+D;IAC/D,cAAc;IACd,eAAe,KAAK,EAAE,GAAG,EAAE;QACvB,IAAI,SAAS,KACT;QACJ,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,IACzB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO;QAChC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,MAAM;QACtC,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,gDAAgD;IAChD,cAAc;QACV,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM;IAC5D;IACA,+DAA+D;IAC/D,oDAAoD;IACpD,YAAY,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;QAC7B,IAAI,OAAO,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI;QAC3C,IAAI,UAAU,KAAK,GAAG,CAAC,OAAO,MAAM,MAAM;QAC1C,MAAO,OAAO,WACV,CAAC,QAAQ,QAAQ,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,AAAC,OAAO,KAAM,EAAE,EACtD,WAAW,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,MACpE;QACJ,MAAO,OAAO,MAAO;YACjB,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;YACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG;YAC3B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG;YACzB;QACJ;QACA,MAAO,QAAQ,MAAM,MAAM,CAAE;YACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG;YACvC,IAAI,QAAQ,CAAC;YACb,IAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAK;gBAClF,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC/B,IAAI,KAAK,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,GAAG;oBAC5D,QAAQ;oBACR;gBACJ;YACJ;YACA,IAAI,QAAQ,CAAC,GAAG;gBACZ,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE;oBACpB,IAAI,CAAC,OAAO,GAAG;oBACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE;gBACpC;gBACA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;YAC5C,OACK;gBACD,IAAI,WAAW,aAAa,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ;gBACnE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG;gBACxC,IAAI,CAAC,GAAG,GAAG;gBACX,IAAI,CAAC,OAAO,GAAG;YACnB;YACA,IAAI,CAAC,KAAK,GAAG;YACb;QACJ;IACJ;IACA,oEAAoE;IACpE,+BAA+B;IAC/B,cAAc,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE;QAC7C,IAAI,QAAQ,CAAC,GAAG;QAChB,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,KAAK,IAC5B,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,GAAG,IACpF,WAAW,WAAW,CAAC,MAAM,WAAW,YAAY;YACpD,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,KAAK;QAC5D,OACK;YACD,IAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,IAAI,IAAI,GAAG,IAAK;gBAChF,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAChC,IAAI,MAAM,WAAW,CAAC,MAAM,WAAW,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ;oBACpF,QAAQ;oBACR;gBACJ;YACJ;QACJ;QACA,IAAI,QAAQ,GACR,OAAO;QACX,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE;QAChC,IAAI,CAAC,KAAK;QACV,OAAO;IACX;IACA,aAAa,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE;QAClD,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM;QACpC,IAAI,MAAM,KAAK,IAAI,cAAc,MAAM,GAAG,IAAI,MAAM,UAAU,EAC1D,MAAM,KAAK,GAAG;QAClB,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,WAAW,WAAW,OAC1C,OAAO;QACX,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE;QAChC,IAAI,CAAC,KAAK;QACV,OAAO;IACX;IACA,mBAAmB,OAAO,EAAE;QACxB,OAAS;YACL,IAAI,SAAS,QAAQ,UAAU;YAC/B,IAAI,CAAC,QACD,OAAO,CAAC;YACZ,IAAI,UAAU,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC/B,IAAI,OAAO,QAAQ,UAAU;gBAC7B,IAAI,MACA,IAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;oBACxD,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,IAAI,MACxB,OAAO;gBACf;gBACJ,OAAO,CAAC;YACZ;YACA,UAAU;QACd;IACJ;IACA,iEAAiE;IACjE,mEAAmE;IACnE,eAAe,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;QACzD,IAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YACxD,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,gBAAgB,cAAc;gBAC9B,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;gBACzC,IAAI,YAAY,QAAQ,YAAY,OAChC,OAAO;gBACX,IAAI,UAAU,KAAK,GAAG,EAAE;gBACxB,8DAA8D;gBAC9D,8DAA8D;gBAC9D,4CAA4C;gBAC5C,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,YACvB,CAAC,CAAC,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI,KAAK,OAAO,CAAC,SAAS,IAAI,KAAK,IAAI,IACjF,KAAK,KAAK,IAAI,cAAc,cAAc,WAAW,KAAK,SAAS,CAAC;gBAC5E,IAAI,CAAC,UAAU,KAAK,MAAM,CAAC,MAAM,WAAW,WAAW,OAAO;oBAC1D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE;oBAChC,IAAI,KAAK,GAAG,IAAI,SACZ,IAAI,CAAC,OAAO,GAAG;oBACnB,IAAI,CAAC,KAAK;oBACV,OAAO;gBACX,OACK,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,eAAe,CAAC,MAAM,MAAM,WAAW,WAAW,MAAM,IAAI,GAAG;oBAC/F,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE;oBAChC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;oBAChC,IAAI,QAAQ,UAAU,EAAE;wBACpB,QAAQ,KAAK,GAAG;wBAChB,QAAQ,cAAc,CAAC,MAAM,MAAM;wBACnC,QAAQ,KAAK,GAAG;oBACpB;oBACA,IAAI,CAAC,OAAO,GAAG;oBACf,IAAI,CAAC,KAAK;oBACV,OAAO;gBACX;gBACA;YACJ;QACJ;QACA,OAAO;IACX;IACA,gEAAgE;IAChE,6CAA6C;IAC7C,gBAAgB,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE;QACzD,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,IAClD,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,KAClC,CAAC,cAAc,WAAW,KAAK,SAAS,KAAK,CAAC,UAAU,EAAE,CAAC,KAAK,SAAS,GACzE,OAAO;QACX,IAAI,UAAU,aAAa,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,WAAW,WAAW,MAAM;QAC9E,IAAI,QAAQ,UAAU,EAAE;YACpB,QAAQ,QAAQ,GAAG,KAAK,QAAQ;YAChC,KAAK,QAAQ,GAAG,EAAE;YAClB,KAAK,IAAI,MAAM,QAAQ,QAAQ,CAC3B,GAAG,MAAM,GAAG;QACpB;QACA,KAAK,OAAO;QACZ,OAAO;IACX;IACA,gDAAgD;IAChD,QAAQ,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE;QAC3C,IAAI,OAAO,aAAa,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,WAAW,WAAW,MAAM;QAC3E,IAAI,KAAK,UAAU,EACf,KAAK,cAAc,CAAC,MAAM,MAAM;QACpC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG;QAC1C,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE;QAC3B,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;QACnF,IAAI,QAAQ,KAAK,aAAa,CAAC,WAC3B,CAAC,UAAU,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;YAC/D,IAAI,CAAC,KAAK;QACd,OACK;YACD,IAAI,OAAO,IAAI,eAAe,IAAI,CAAC,GAAG,EAAE,QAAQ,MAAM;YACtD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG;YAC1C,IAAI,CAAC,OAAO,GAAG;QACnB;IACJ;IACA,uDAAuD;IACvD,mBAAmB;IACnB,oBAAoB;QAChB,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG;QACpE,MAAO,qBAAqB,aAAc;YACtC,SAAS;YACT,YAAY,OAAO,QAAQ,CAAC,OAAO,QAAQ,CAAC,MAAM,GAAG,EAAE;QAC3D;QACA,IAAI,CAAC,aAAa,kBAAkB;QAChC,CAAC,CAAC,qBAAqB,YAAY,KACnC,MAAM,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,KAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,MAAM,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,GAAI;YACtE,qFAAqF;YACrF,IAAI,CAAC,UAAU,MAAM,KAAK,aAAa,UAAU,GAAG,CAAC,eAAe,IAAI,SACpE,IAAI,CAAC,WAAW,CAAC,OAAO;YAC5B,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,GAAG;QACnC;IACJ;IACA,YAAY,QAAQ,EAAE,MAAM,EAAE;QAC1B,IAAI,UAAU,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,QAAQ,CAAC,MAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,WAAW;YAChH,IAAI,CAAC,KAAK;QACd,OACK;YACD,IAAI,MAAM,SAAS,aAAa,CAAC;YACjC,IAAI,YAAY,OAAO;gBACnB,IAAI,SAAS,GAAG;gBAChB,IAAI,GAAG,GAAG;YACd;YACA,IAAI,YAAY,MACZ,IAAI,SAAS,GAAG;YACpB,IAAI,OAAO,IAAI,qBAAqB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK;YACvD,IAAI,UAAU,IAAI,CAAC,GAAG,EAClB,OAAO,QAAQ,CAAC,IAAI,CAAC;iBAErB,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG;YAC5C,IAAI,CAAC,OAAO,GAAG;QACnB;IACJ;IACA,SAAS,IAAI,EAAE;QACX,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;IACvG;AACJ;AACA,kEAAkE;AAClE,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC7D,YAAY;AACZ,SAAS,SAAS,IAAI,EAAE,UAAU;IAC9B,IAAI,UAAU,YAAY,QAAQ,QAAQ,QAAQ,CAAC,MAAM;IACzD,IAAI,KAAK,KAAK,UAAU,EAAE,UAAU,IAAI,KAAK,UAAU,EAAE;IACzD,OAAO,MAAO,KAAK,EAAG;QAClB,IAAI;QACJ,OAAS;YACL,IAAI,OAAO;gBACP,IAAI,OAAO,QAAQ,QAAQ,CAAC,QAAQ,EAAE;gBACtC,IAAI,gBAAgB,cAAc;oBAC9B,UAAU;oBACV,QAAQ,KAAK,QAAQ,CAAC,MAAM;gBAChC,OACK;oBACD,OAAO;oBACP;oBACA;gBACJ;YACJ,OACK,IAAI,WAAW,YAAY;gBAC5B,MAAM;YACV,OACK;gBACD,QAAQ;gBACR,QAAQ,QAAQ,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACxC,UAAU,QAAQ,MAAM;YAC5B;QACJ;QACA,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,CAAC,MACD;QACJ,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,IACxB;QACJ,EAAE;QACF,QAAQ,GAAG,CAAC,MAAM;QAClB,QAAQ,IAAI,CAAC;IACjB;IACA,OAAO;QAAE,OAAO;QAAI;QAAS,SAAS,QAAQ,OAAO;IAAG;AAC5D;AACA,SAAS,YAAY,CAAC,EAAE,CAAC;IACrB,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI;AACpC;AACA,sEAAsE;AACtE,qEAAqE;AACrE,qEAAqE;AACrE,8DAA8D;AAC9D,SAAS,SAAS,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM;IAC5C,IAAI,SAAS,KAAK,MAAM,CAAC,SAAS,SAAS;IAC3C,gEAAgE;IAChE,IAAI,OAAO,MAAM,IAAI,GAAG;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,UAAU,EAAE,IAAK;YACxC,IAAI,QAAQ,OAAO,KAAK,CAAC;YACzB,OAAO,OAAO,QAAQ,KAAK,QAAQ,CAAC,QAAQ,QAAQ;YACpD,UAAU,MAAM,QAAQ;QAC5B;QACA;IACJ;IACA,IAAI,YAAY,GAAG,SAAS,EAAE,EAAE,WAAW;IAC3C,IAAK,IAAI,cAAc,IAAK;QACxB,IAAI,QAAQ;QACZ,MAAO,YAAY,OAAO,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,IAAI,OAAQ;YAChE,IAAI,OAAO,MAAM,CAAC,YAAY;YAC9B,IAAI,KAAK,MAAM,EAAE;gBACb,IAAI,CAAC,QACD,SAAS;qBAET,CAAC,WAAW,CAAC,UAAU;oBAAC;iBAAO,CAAC,EAAE,IAAI,CAAC;YAC/C;QACJ;QACA,IAAI,QAAQ;YACR,IAAI,SAAS;gBACT,QAAQ,IAAI,CAAC;gBACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAChC,SAAS,OAAO,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;YAC5C,OACK;gBACD,SAAS,QAAQ,aAAa,CAAC,CAAC;YACpC;QACJ;QACA,IAAI,OAAO;QACX,IAAI,UAAU;YACV,QAAQ,CAAC;YACT,QAAQ;YACR,WAAW;QACf,OACK,IAAI,cAAc,OAAO,UAAU,EAAE;YACtC,QAAQ;YACR,QAAQ,OAAO,KAAK,CAAC;QACzB,OACK;YACD;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAC/B,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,QAChB,OAAO,MAAM,CAAC,KAAK;QAC3B,MAAO,YAAY,OAAO,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,UAAU,MAAM,CAAC,UAAU,CAAC,EAAE,GAAG,OAC3F,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY;QACnC,IAAI,MAAM,SAAS,MAAM,QAAQ;QACjC,IAAI,MAAM,MAAM,EAAE;YACd,IAAI,QAAQ;YACZ,IAAI,YAAY,OAAO,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,GAAG,OACtD,QAAQ,MAAM,CAAC,UAAU,CAAC,IAAI;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAC/B,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,OACf,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE;YAC5B,IAAI,QAAQ,KAAK;gBACb,WAAW,MAAM,GAAG,CAAC,QAAQ;gBAC7B,QAAQ,MAAM,GAAG,CAAC,GAAG,QAAQ;gBAC7B,MAAM;gBACN,QAAQ,CAAC;YACb;QACJ,OACK;YACD,MAAO,YAAY,OAAO,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,GAAG,IACvD;QACR;QACA,IAAI,YAAY,MAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,GAAG,OAAO,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,IAAI,OAAO,KAAK;QAC9F,OAAO,OAAO,WAAW,KAAK,QAAQ,CAAC,QAAQ,QAAQ;QACvD,SAAS;IACb;AACJ;AACA,4DAA4D;AAC5D,qCAAqC;AACrC,SAAS,SAAS,GAAG;IACjB,IAAI,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM;QAC9C,IAAI,SAAS,IAAI,KAAK,CAAC,OAAO;QAC9B,IAAI,KAAK,CAAC,OAAO,GAAG,SAAS;QAC7B,OAAO,gBAAgB,CAAC,KAAK,SAAS;QACtC,IAAI,KAAK,CAAC,OAAO,GAAG;IACxB;AACJ;AACA,kEAAkE;AAClE,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;IAC5C,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,UAAU,IAAI,OAAO,IAAK;QACxD,IAAI,QAAQ,KAAK,KAAK,CAAC,MAAM,aAAa;QAC1C,OAAO,MAAM,QAAQ;QACrB,IAAI,CAAC,MAAM,MAAM,EACb;QACJ,IAAI,MAAM,MAAM,IAAI;QACpB,MAAO,IAAI,KAAK,UAAU,CAAE;YACxB,IAAI,OAAO,KAAK,KAAK,CAAC;YACtB,OAAO,KAAK,QAAQ;YACpB,IAAI,CAAC,KAAK,MAAM,EACZ;YACJ,OAAO,KAAK,IAAI;QACpB;QACA,IAAI,OAAO,MAAM;YACb,IAAI,OAAO,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,GAAG,YAAY,KAAK,eAAe,MAC1E,OAAO,KAAK,KAAK,MAAM;YAC3B,IAAI,QAAQ,aAAa,KAAK,IAAI,WAAW,CAAC,MAAM,KAAK,aAAa,KAAK,CAAC;YAC5E,IAAI,SAAS,KAAK,QAAQ,KAAK,MAAM,GAAG,cAAc,MAClD,OAAO,aAAa;YACxB,IAAI,QAAQ,MAAM,IAAI,MAAM,IAAI,AAAC,KAAK,KAAK,MAAM,GAAI,cACjD,IAAI,KAAK,CAAC,KAAK,YAAY,KAAK,aAAa,KAAK,MAAM,KAAK,MAC7D,OAAO;QACf;IACJ;IACA,OAAO,CAAC;AACZ;AACA,mEAAmE;AACnE,sEAAsE;AACtE,iEAAiE;AACjE,6DAA6D;AAC7D,sDAAsD;AACtD,SAAS,aAAa,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW;IACpD,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAC5C,IAAI,QAAQ,KAAK,CAAC,EAAE,EAAE,QAAQ,KAAK,MAAM,OAAO,MAAM,IAAI;QAC1D,IAAI,SAAS,MAAM,OAAO,MAAM;YAC5B,OAAO,IAAI,CAAC;QAChB,OACK;YACD,IAAI,QAAQ,MACR,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,OAAO;YAC7C,IAAI,aAAa;gBACb,OAAO,IAAI,CAAC;gBACZ,cAAc;YAClB;YACA,IAAI,MAAM,IACN,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,OAAO,MAAM,IAAI,EAAE;QACxD;IACJ;IACA,OAAO;AACX;AAEA,SAAS,iBAAiB,IAAI,EAAE,SAAS,IAAI;IACzC,IAAI,SAAS,KAAK,iBAAiB,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG;IAC3D,IAAI,CAAC,OAAO,SAAS,EACjB,OAAO;IACX,IAAI,cAAc,KAAK,OAAO,CAAC,WAAW,CAAC,OAAO,SAAS,GAAG,WAAW,eAAe,YAAY,IAAI,IAAI;IAC5G,IAAI,OAAO,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,SAAS,EAAE,OAAO,WAAW,EAAE;IACzE,IAAI,OAAO,GACP,OAAO;IACX,IAAI,QAAQ,IAAI,OAAO,CAAC,OAAO,QAAQ;IACvC,IAAI,mBAAmB,SAAS;QAC5B,SAAS;QACT,MAAO,eAAe,CAAC,YAAY,IAAI,CACnC,cAAc,YAAY,MAAM;QACpC,IAAI,kBAAkB,YAAY,IAAI;QACtC,IAAI,eAAe,gBAAgB,MAAM,IAAI,qJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,oBAAoB,YAAY,MAAM,IACvG,CAAC,CAAC,gBAAgB,QAAQ,IAAI,SAAS,OAAO,SAAS,EAAE,OAAO,WAAW,EAAE,YAAY,GAAG,CAAC,GAAG;YACnG,IAAI,MAAM,YAAY,SAAS;YAC/B,YAAY,IAAI,qJAAA,CAAA,gBAAa,CAAC,QAAQ,MAAM,QAAQ,IAAI,OAAO,CAAC;QACpE;IACJ,OACK;QACD,IAAI,kBAAkB,KAAK,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,IAAI,OAAO,UAAU,GAAG,GAAG;YACzF,IAAI,MAAM,MAAM,MAAM;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,UAAU,EAAE,IAAK;gBACxC,IAAI,QAAQ,OAAO,UAAU,CAAC;gBAC9B,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,UAAU,CAAC,MAAM,cAAc,EAAE,MAAM,WAAW,EAAE;gBACrF,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,UAAU,CAAC,MAAM,YAAY,EAAE,MAAM,SAAS,EAAE,CAAC;YACtF;YACA,IAAI,MAAM,GACN,OAAO;YACX,CAAC,QAAQ,KAAK,GAAG,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG;gBAAC;gBAAK;aAAI,GAAG;gBAAC;gBAAK;aAAI;YAC7E,QAAQ,IAAI,OAAO,CAAC;QACxB,OACK;YACD,SAAS,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,UAAU,EAAE,OAAO,YAAY,EAAE;QAC7E;QACA,IAAI,SAAS,GACT,OAAO;IACf;IACA,IAAI,UAAU,IAAI,OAAO,CAAC;IAC1B,IAAI,CAAC,WAAW;QACZ,IAAI,OAAO,UAAU,aAAc,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,WAAY,IAAI,CAAC;QAC9F,YAAY,iBAAiB,MAAM,SAAS,OAAO;IACvD;IACA,OAAO;AACX;AACA,SAAS,oBAAoB,IAAI;IAC7B,OAAO,KAAK,QAAQ,GAAG,KAAK,QAAQ,KAChC,aAAa,SAAS,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,QAAQ,CAAC,KAAK,GAAG;AAChG;AACA,SAAS,eAAe,IAAI,EAAE,QAAQ,KAAK;IACvC,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS;IAC9B,kBAAkB,MAAM;IACxB,IAAI,CAAC,oBAAoB,OACrB;IACJ,gEAAgE;IAChE,iEAAiE;IACjE,gCAAgC;IAChC,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,YAAY,IAAI,QAAQ;QAC/E,IAAI,SAAS,KAAK,iBAAiB,IAAI,SAAS,KAAK,WAAW,CAAC,gBAAgB;QACjF,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,IACtC,qBAAqB,OAAO,UAAU,EAAE,OAAO,YAAY,EAAE,OAAO,UAAU,EAAE,OAAO,YAAY,GAAG;YACtG,KAAK,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG;YAC5C,KAAK,WAAW,CAAC,eAAe;YAChC;QACJ;IACJ;IACA,KAAK,WAAW,CAAC,mBAAmB;IACpC,IAAI,KAAK,aAAa,EAAE;QACpB,oBAAoB;IACxB,OACK;QACD,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,mBAAmB;QAC/C,IAAI,iCAAiC,CAAC,CAAC,eAAe,qJAAA,CAAA,gBAAa,GAAG;YAClE,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,EAC/B,oBAAoB,wBAAwB,MAAM,IAAI,IAAI;YAC9D,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,EAC7C,kBAAkB,wBAAwB,MAAM,IAAI,EAAE;QAC9D;QACA,KAAK,OAAO,CAAC,YAAY,CAAC,QAAQ,MAAM,MAAM;QAC9C,IAAI,+BAA+B;YAC/B,IAAI,mBACA,cAAc;YAClB,IAAI,iBACA,cAAc;QACtB;QACA,IAAI,IAAI,OAAO,EAAE;YACb,KAAK,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC;QAC9B,OACK;YACD,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC;YACvB,IAAI,uBAAuB,UACvB,6BAA6B;QACrC;IACJ;IACA,KAAK,WAAW,CAAC,eAAe;IAChC,KAAK,WAAW,CAAC,gBAAgB;AACrC;AACA,qEAAqE;AACrE,8DAA8D;AAC9D,6DAA6D;AAC7D,MAAM,gCAAgC,UAAU,UAAU,iBAAiB;AAC3E,SAAS,wBAAwB,IAAI,EAAE,GAAG;IACtC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,OAAO,CAAC,UAAU,CAAC,KAAK;IACpD,IAAI,QAAQ,SAAS,KAAK,UAAU,CAAC,MAAM,GAAG,KAAK,UAAU,CAAC,OAAO,GAAG;IACxE,IAAI,SAAS,SAAS,KAAK,UAAU,CAAC,SAAS,EAAE,GAAG;IACpD,IAAI,UAAU,SAAS,MAAM,eAAe,IAAI,SAC5C,OAAO,YAAY;IACvB,IAAI,CAAC,CAAC,SAAS,MAAM,eAAe,IAAI,OAAO,KAC3C,CAAC,CAAC,UAAU,OAAO,eAAe,IAAI,OAAO,GAAG;QAChD,IAAI,OACA,OAAO,YAAY;aAClB,IAAI,QACL,OAAO,YAAY;IAC3B;AACJ;AACA,SAAS,YAAY,OAAO;IACxB,QAAQ,eAAe,GAAG;IAC1B,IAAI,UAAU,QAAQ,SAAS,EAAE;QAC7B,QAAQ,SAAS,GAAG;QACpB,QAAQ,YAAY,GAAG;IAC3B;IACA,OAAO;AACX;AACA,SAAS,cAAc,OAAO;IAC1B,QAAQ,eAAe,GAAG;IAC1B,IAAI,QAAQ,YAAY,EAAE;QACtB,QAAQ,SAAS,GAAG;QACpB,QAAQ,YAAY,GAAG;IAC3B;AACJ;AACA,SAAS,6BAA6B,IAAI;IACtC,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa;IAChC,IAAI,mBAAmB,CAAC,mBAAmB,KAAK,KAAK,CAAC,kBAAkB;IACxE,IAAI,SAAS,KAAK,iBAAiB;IACnC,IAAI,OAAO,OAAO,UAAU,EAAE,SAAS,OAAO,YAAY;IAC1D,IAAI,gBAAgB,CAAC,mBAAmB,KAAK,KAAK,CAAC,kBAAkB,GAAG;QACpE,IAAI,OAAO,UAAU,IAAI,QAAQ,OAAO,YAAY,IAAI,QAAQ;YAC5D,IAAI,mBAAmB,CAAC,mBAAmB,KAAK,KAAK,CAAC,kBAAkB;YACxE,WAAW;gBACP,IAAI,CAAC,oBAAoB,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,EAC1D,KAAK,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC;YAClC,GAAG;QACP;IACJ;AACJ;AACA,SAAS,oBAAoB,IAAI;IAC7B,IAAI,SAAS,KAAK,YAAY,IAAI,QAAQ,SAAS,WAAW;IAC9D,IAAI,CAAC,QACD;IACJ,IAAI,OAAO,KAAK,aAAa,CAAC,GAAG,EAAE,MAAM,KAAK,QAAQ,IAAI;IAC1D,IAAI,KACA,MAAM,QAAQ,CAAC,KAAK,UAAU,EAAE,SAAS,QAAQ;SAEjD,MAAM,QAAQ,CAAC,MAAM;IACzB,MAAM,QAAQ,CAAC;IACf,OAAO,eAAe;IACtB,OAAO,QAAQ,CAAC;IAChB,+DAA+D;IAC/D,mEAAmE;IACnE,+DAA+D;IAC/D,8DAA8D;IAC9D,mBAAmB;IACnB,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,MAAM,cAAc,IAAI;QACjE,KAAK,QAAQ,GAAG;QAChB,KAAK,QAAQ,GAAG;IACpB;AACJ;AACA,SAAS,kBAAkB,IAAI,EAAE,GAAG;IAChC,IAAI,eAAe,qJAAA,CAAA,gBAAa,EAAE;QAC9B,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI;QACvC,IAAI,QAAQ,KAAK,oBAAoB,EAAE;YACnC,mBAAmB;YACnB,IAAI,MACA,KAAK,UAAU;YACnB,KAAK,oBAAoB,GAAG;QAChC;IACJ,OACK;QACD,mBAAmB;IACvB;AACJ;AACA,yDAAyD;AACzD,SAAS,mBAAmB,IAAI;IAC5B,IAAI,KAAK,oBAAoB,EAAE;QAC3B,IAAI,KAAK,oBAAoB,CAAC,MAAM,EAChC,KAAK,oBAAoB,CAAC,YAAY;QAC1C,KAAK,oBAAoB,GAAG;IAChC;AACJ;AACA,SAAS,iBAAiB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI;IAChD,OAAO,KAAK,QAAQ,CAAC,0BAA0B,CAAA,IAAK,EAAE,MAAM,SAAS,WAC9D,qJAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,SAAS,OAAO;AACjD;AACA,SAAS,qBAAqB,IAAI;IAC9B,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,IAC/B,OAAO;IACX,OAAO,aAAa;AACxB;AACA,SAAS,aAAa,IAAI;IACtB,IAAI,MAAM,KAAK,iBAAiB;IAChC,IAAI,CAAC,IAAI,UAAU,EACf,OAAO;IACX,IAAI;QACA,+DAA+D;QAC/D,8DAA8D;QAC9D,WAAW;QACX,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,IAAI,IAAI,UAAU,CAAC,UAAU,GAAG,IAAI,UAAU,KAC9F,CAAC,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,QAAQ,IAAI,IAAI,IAAI,SAAS,CAAC,UAAU,GAAG,IAAI,SAAS,CAAC;IACnH,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AACA,SAAS,mBAAmB,IAAI;IAC5B,IAAI,YAAY,KAAK,OAAO,CAAC,UAAU,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE;IACrE,IAAI,SAAS,KAAK,iBAAiB;IACnC,OAAO,qBAAqB,UAAU,IAAI,EAAE,UAAU,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,YAAY;AACxG;AAEA,SAAS,mBAAmB,KAAK,EAAE,GAAG;IAClC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS;IACxC,IAAI,QAAQ,MAAM,IAAI,QAAQ,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC;IACvD,IAAI,SAAS,CAAC,MAAM,MAAM,CAAC,aAAa,GAAG,QAAQ,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM;IAC/H,OAAO,UAAU,qJAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;AAChD;AACA,SAAS,MAAM,IAAI,EAAE,GAAG;IACpB,KAAK,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,cAAc;IAC5D,OAAO;AACX;AACA,SAAS,mBAAmB,IAAI,EAAE,GAAG,EAAE,IAAI;IACvC,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS;IAC9B,IAAI,eAAe,qJAAA,CAAA,gBAAa,EAAE;QAC9B,IAAI,KAAK,OAAO,CAAC,OAAO,CAAC,GAAG;YACxB,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,OAAO,MAAM,UAAU,GAAG,OAAO,MAAM,IAAI,MAAM,UAAU,GAAG,MAAM,SAAS;YAClG,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,EACpC,OAAO;YACX,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,KAAK,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC;YACnF,OAAO,MAAM,MAAM,IAAI,qJAAA,CAAA,gBAAa,CAAC,IAAI,OAAO,EAAE;QACtD,OACK,IAAI,CAAC,IAAI,KAAK,EAAE;YACjB,OAAO;QACX,OACK,IAAI,KAAK,cAAc,CAAC,MAAM,IAAI,YAAY,aAAa;YAC5D,IAAI,OAAO,mBAAmB,KAAK,KAAK,EAAE;YAC1C,IAAI,QAAS,gBAAgB,qJAAA,CAAA,gBAAa,EACtC,OAAO,MAAM,MAAM;YACvB,OAAO;QACX,OACK,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG;YACvC,IAAI,QAAQ,IAAI,KAAK,EAAE,OAAO,MAAM,UAAU,GAAG,OAAO,MAAM,IAAI,MAAM,UAAU,GAAG,MAAM,SAAS,EAAE;YACtG,IAAI,CAAC,QAAQ,KAAK,MAAM,EACpB,OAAO;YACX,IAAI,UAAU,MAAM,IAAI,MAAM,GAAG,GAAG,KAAK,QAAQ,GAAG,MAAM,GAAG;YAC7D,IAAI,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,QAAQ,KAAK,CAAC,KAAK,UAAU,GAC1E,OAAO;YACX,IAAI,qJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,OAAO;gBAClC,OAAO,MAAM,MAAM,IAAI,qJAAA,CAAA,gBAAa,CAAC,MAAM,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,KAAK,QAAQ,IAAI;YACvG,OACK,IAAI,QAAQ;gBACb,0DAA0D;gBAC1D,0DAA0D;gBAC1D,iDAAiD;gBACjD,OAAO,MAAM,MAAM,IAAI,qJAAA,CAAA,gBAAa,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,UAAU,UAAU,KAAK,QAAQ;YAC3G,OACK;gBACD,OAAO;YACX;QACJ;IACJ,OACK,IAAI,eAAe,qJAAA,CAAA,gBAAa,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;QACxD,OAAO,MAAM,MAAM,IAAI,qJAAA,CAAA,gBAAa,CAAC,MAAM,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK;IACtE,OACK;QACD,IAAI,OAAO,mBAAmB,KAAK,KAAK,EAAE;QAC1C,IAAI,MACA,OAAO,MAAM,MAAM;QACvB,OAAO;IACX;AACJ;AACA,SAAS,QAAQ,IAAI;IACjB,OAAO,KAAK,QAAQ,IAAI,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK,UAAU,CAAC,MAAM;AAC9E;AACA,SAAS,YAAY,GAAG,EAAE,GAAG;IACzB,IAAI,OAAO,IAAI,UAAU;IACzB,OAAO,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI;AACxF;AACA,SAAS,iBAAiB,IAAI,EAAE,GAAG;IAC/B,OAAO,MAAM,IAAI,uBAAuB,QAAQ,sBAAsB;AAC1E;AACA,gEAAgE;AAChE,+DAA+D;AAC/D,SAAS,uBAAuB,IAAI;IAChC,IAAI,MAAM,KAAK,iBAAiB;IAChC,IAAI,OAAO,IAAI,SAAS,EAAE,SAAS,IAAI,WAAW;IAClD,IAAI,CAAC,MACD;IACJ,IAAI,UAAU,YAAY,QAAQ;IAClC,mEAAmE;IACnE,iEAAiE;IACjE,uDAAuD;IACvD,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,SAAS,QAAQ,SAAS,YAAY,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC,IAC/F,QAAQ;IACZ,OAAS;QACL,IAAI,SAAS,GAAG;YACZ,IAAI,KAAK,QAAQ,IAAI,GAAG;gBACpB;YACJ,OACK;gBACD,IAAI,SAAS,KAAK,UAAU,CAAC,SAAS,EAAE;gBACxC,IAAI,YAAY,QAAQ,CAAC,IAAI;oBACzB,WAAW;oBACX,aAAa,EAAE;gBACnB,OACK,IAAI,OAAO,QAAQ,IAAI,GAAG;oBAC3B,OAAO;oBACP,SAAS,KAAK,SAAS,CAAC,MAAM;gBAClC,OAEI;YACR;QACJ,OACK,IAAI,YAAY,OAAO;YACxB;QACJ,OACK;YACD,IAAI,OAAO,KAAK,eAAe;YAC/B,MAAO,QAAQ,YAAY,MAAM,CAAC,GAAI;gBAClC,WAAW,KAAK,UAAU;gBAC1B,aAAa,SAAS;gBACtB,OAAO,KAAK,eAAe;YAC/B;YACA,IAAI,CAAC,MAAM;gBACP,OAAO,KAAK,UAAU;gBACtB,IAAI,QAAQ,KAAK,GAAG,EAChB;gBACJ,SAAS;YACb,OACK;gBACD,OAAO;gBACP,SAAS,QAAQ;YACrB;QACJ;IACJ;IACA,IAAI,OACA,YAAY,MAAM,MAAM;SACvB,IAAI,UACL,YAAY,MAAM,UAAU;AACpC;AACA,iEAAiE;AACjE,SAAS;AACT,SAAS,sBAAsB,IAAI;IAC/B,IAAI,MAAM,KAAK,iBAAiB;IAChC,IAAI,OAAO,IAAI,SAAS,EAAE,SAAS,IAAI,WAAW;IAClD,IAAI,CAAC,MACD;IACJ,IAAI,MAAM,QAAQ;IAClB,IAAI,UAAU;IACd,OAAS;QACL,IAAI,SAAS,KAAK;YACd,IAAI,KAAK,QAAQ,IAAI,GACjB;YACJ,IAAI,QAAQ,KAAK,UAAU,CAAC,OAAO;YACnC,IAAI,YAAY,OAAO,IAAI;gBACvB,WAAW;gBACX,aAAa,EAAE;YACnB,OAEI;QACR,OACK,IAAI,YAAY,OAAO;YACxB;QACJ,OACK;YACD,IAAI,OAAO,KAAK,WAAW;YAC3B,MAAO,QAAQ,YAAY,MAAM,GAAI;gBACjC,WAAW,KAAK,UAAU;gBAC1B,aAAa,SAAS,QAAQ;gBAC9B,OAAO,KAAK,WAAW;YAC3B;YACA,IAAI,CAAC,MAAM;gBACP,OAAO,KAAK,UAAU;gBACtB,IAAI,QAAQ,KAAK,GAAG,EAChB;gBACJ,SAAS,MAAM;YACnB,OACK;gBACD,OAAO;gBACP,SAAS;gBACT,MAAM,QAAQ;YAClB;QACJ;IACJ;IACA,IAAI,UACA,YAAY,MAAM,UAAU;AACpC;AACA,SAAS,YAAY,GAAG;IACpB,IAAI,OAAO,IAAI,UAAU;IACzB,OAAO,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,OAAO;AACjD;AACA,SAAS,cAAc,IAAI,EAAE,MAAM;IAC/B,MAAO,QAAQ,UAAU,KAAK,UAAU,CAAC,MAAM,IAAI,CAAC,aAAa,MAAO;QACpE,SAAS,SAAS,QAAQ;QAC1B,OAAO,KAAK,UAAU;IAC1B;IACA,MAAO,QAAQ,SAAS,KAAK,UAAU,CAAC,MAAM,CAAE;QAC5C,IAAI,OAAO,KAAK,UAAU,CAAC,OAAO;QAClC,IAAI,KAAK,QAAQ,IAAI,GACjB,OAAO;QACX,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,eAAe,IAAI,SAC9C;QACJ,OAAO;QACP,SAAS;IACb;AACJ;AACA,SAAS,eAAe,IAAI,EAAE,MAAM;IAChC,MAAO,QAAQ,CAAC,UAAU,CAAC,aAAa,MAAO;QAC3C,SAAS,SAAS;QAClB,OAAO,KAAK,UAAU;IAC1B;IACA,MAAO,QAAQ,OAAQ;QACnB,IAAI,OAAO,KAAK,UAAU,CAAC,SAAS,EAAE;QACtC,IAAI,KAAK,QAAQ,IAAI,GACjB,OAAO;QACX,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,eAAe,IAAI,SAC9C;QACJ,OAAO;QACP,SAAS,KAAK,UAAU,CAAC,MAAM;IACnC;AACJ;AACA,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,MAAM;IACnC,IAAI,KAAK,QAAQ,IAAI,GAAG;QACpB,IAAI,QAAQ;QACZ,IAAI,QAAQ,cAAc,MAAM,SAAS;YACrC,OAAO;YACP,SAAS;QACb,OACK,IAAI,SAAS,eAAe,MAAM,SAAS;YAC5C,OAAO;YACP,SAAS,OAAO,SAAS,CAAC,MAAM;QACpC;IACJ;IACA,IAAI,MAAM,KAAK,YAAY;IAC3B,IAAI,CAAC,KACD;IACJ,IAAI,mBAAmB,MAAM;QACzB,IAAI,QAAQ,SAAS,WAAW;QAChC,MAAM,MAAM,CAAC,MAAM;QACnB,MAAM,QAAQ,CAAC,MAAM;QACrB,IAAI,eAAe;QACnB,IAAI,QAAQ,CAAC;IACjB,OACK,IAAI,IAAI,MAAM,EAAE;QACjB,IAAI,MAAM,CAAC,MAAM;IACrB;IACA,KAAK,WAAW,CAAC,eAAe;IAChC,IAAI,EAAE,KAAK,EAAE,GAAG;IAChB,6DAA6D;IAC7D,WAAW;QACP,IAAI,KAAK,KAAK,IAAI,OACd,eAAe;IACvB,GAAG;AACP;AACA,SAAS,cAAc,IAAI,EAAE,GAAG;IAC5B,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;IAClC,IAAI,CAAC,CAAC,UAAU,OAAO,KAAK,KAAK,MAAM,CAAC,aAAa,EAAE;QACnD,IAAI,SAAS,KAAK,WAAW,CAAC;QAC9B,IAAI,MAAM,KAAK,KAAK,IAAI;YACpB,IAAI,SAAS,KAAK,WAAW,CAAC,MAAM;YACpC,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,OAAO,MAAM,IAAI;YACzC,IAAI,MAAM,OAAO,GAAG,IAAI,MAAM,OAAO,MAAM,IAAI,KAAK,GAAG,CAAC,OAAO,IAAI,GAAG,OAAO,IAAI,IAAI,GACjF,OAAO,OAAO,IAAI,GAAG,OAAO,IAAI,GAAG,QAAQ;QACnD;QACA,IAAI,MAAM,KAAK,GAAG,IAAI;YAClB,IAAI,QAAQ,KAAK,WAAW,CAAC,MAAM;YACnC,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,MAAM,IAAI;YACvC,IAAI,MAAM,OAAO,GAAG,IAAI,MAAM,OAAO,MAAM,IAAI,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,OAAO,IAAI,IAAI,GAChF,OAAO,MAAM,IAAI,GAAG,OAAO,IAAI,GAAG,QAAQ;QAClD;IACJ;IACA,IAAI,WAAW,iBAAiB,KAAK,GAAG,EAAE,SAAS;IACnD,OAAO,YAAY,QAAQ,QAAQ;AACvC;AACA,6DAA6D;AAC7D,iEAAiE;AACjE,WAAW;AACX,SAAS,iBAAiB,IAAI,EAAE,GAAG,EAAE,IAAI;IACrC,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS;IAC9B,IAAI,eAAe,qJAAA,CAAA,gBAAa,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,OAAO,CAAC,GACnE,OAAO;IACX,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,GAC5B,OAAO;IACX,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IACrB,IAAI,CAAC,MAAM,MAAM,CAAC,aAAa,IAAI,KAAK,cAAc,CAAC,MAAM,IAAI,OAAO,SAAS;QAC7E,IAAI,OAAO,mBAAmB,KAAK,KAAK,EAAE;QAC1C,IAAI,QAAS,gBAAgB,qJAAA,CAAA,gBAAa,EACtC,OAAO,MAAM,MAAM;IAC3B;IACA,IAAI,CAAC,MAAM,MAAM,CAAC,aAAa,EAAE;QAC7B,IAAI,OAAO,MAAM,IAAI,QAAQ;QAC7B,IAAI,SAAS,eAAe,qJAAA,CAAA,eAAY,GAAG,qJAAA,CAAA,YAAS,CAAC,IAAI,CAAC,MAAM,OAAO,qJAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,MAAM;QAChG,OAAO,SAAS,MAAM,MAAM,UAAU;IAC1C;IACA,OAAO;AACX;AACA,SAAS,2BAA2B,IAAI,EAAE,GAAG;IACzC,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,SAAS,YAAY,qJAAA,CAAA,gBAAa,GAC/C,OAAO;IACX,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS;IACpD,IAAI,CAAC,MAAM,UAAU,CAAC,UAClB,OAAO;IACX,IAAI,CAAC,OACD,OAAO;IACX,IAAI,KAAK,cAAc,CAAC,MAAM,IAAI,YAAY,aAC1C,OAAO;IACX,IAAI,WAAW,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,IAAI,MAAM,UAAU,GAAG,MAAM,SAAS;IACjF,IAAI,YAAY,CAAC,SAAS,MAAM,EAAE;QAC9B,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;QACtB,IAAI,MAAM,GACN,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,SAAS,QAAQ,EAAE,MAAM,GAAG;aAElD,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,GAAG,GAAG,SAAS,QAAQ;QACtD,KAAK,QAAQ,CAAC;QACd,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,eAAe,IAAI,EAAE,IAAI,EAAE,KAAK;IACrC,KAAK,WAAW,CAAC,IAAI;IACrB,KAAK,eAAe,GAAG;IACvB,KAAK,WAAW,CAAC,KAAK;AAC1B;AACA,oFAAoF;AACpF,sEAAsE;AACtE,iEAAiE;AACjE,kEAAkE;AAClE,WAAW;AACX,SAAS,mBAAmB,IAAI;IAC5B,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,GACrD,OAAO;IACX,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK,iBAAiB;IACvD,IAAI,aAAa,UAAU,QAAQ,IAAI,KAAK,eAAe,KACvD,UAAU,UAAU,IAAI,UAAU,UAAU,CAAC,eAAe,IAAI,SAAS;QACzE,IAAI,QAAQ,UAAU,UAAU;QAChC,eAAe,MAAM,OAAO;QAC5B,WAAW,IAAM,eAAe,MAAM,OAAO,UAAU;IAC3D;IACA,OAAO;AACX;AACA,mEAAmE;AACnE,sEAAsE;AACtE,kEAAkE;AAClE,mEAAmE;AACnE,kEAAkE;AAClE,oBAAoB;AACpB,SAAS,QAAQ,KAAK;IAClB,IAAI,SAAS;IACb,IAAI,MAAM,OAAO,EACb,UAAU;IACd,IAAI,MAAM,OAAO,EACb,UAAU;IACd,IAAI,MAAM,MAAM,EACZ,UAAU;IACd,IAAI,MAAM,QAAQ,EACd,UAAU;IACd,OAAO;AACX;AACA,SAAS,eAAe,IAAI,EAAE,KAAK;IAC/B,IAAI,OAAO,MAAM,OAAO,EAAE,OAAO,QAAQ;IACzC,IAAI,QAAQ,KAAM,OAAO,QAAQ,MAAM,QAAQ,KAAM;QACjD,OAAO,2BAA2B,MAAM,CAAC,MAAM,iBAAiB,MAAM,CAAC;IAC3E,OACK,IAAI,AAAC,QAAQ,MAAM,CAAC,MAAM,QAAQ,IAAM,OAAO,QAAQ,MAAM,QAAQ,KAAM;QAC5E,OAAO,2BAA2B,MAAM,MAAM,iBAAiB,MAAM;IACzE,OACK,IAAI,QAAQ,MAAM,QAAQ,IAAI;QAC/B,OAAO;IACX,OACK,IAAI,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;QACvD,IAAI,MAAM,QAAQ,KAAM,cAAc,MAAM,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,IAAK,CAAC;QAC7F,OAAO,mBAAmB,MAAM,KAAK,SAAS,iBAAiB,MAAM;IACzE,OACK,IAAI,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;QACvD,IAAI,MAAM,QAAQ,KAAM,cAAc,MAAM,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAK;QAC5F,OAAO,mBAAmB,MAAM,KAAK,SAAS,iBAAiB,MAAM;IACzE,OACK,IAAI,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;QACvD,OAAO,iBAAiB,MAAM,CAAC,GAAG,SAAS,iBAAiB,MAAM,CAAC;IACvE,OACK,IAAI,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;QACvD,OAAO,mBAAmB,SAAS,iBAAiB,MAAM,GAAG,SAAS,iBAAiB,MAAM;IACjG,OACK,IAAI,QAAQ,CAAC,MAAM,MAAM,GAAG,KAC7B,CAAC,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,EAAE,GAAG;QACxD,OAAO;IACX;IACA,OAAO;AACX;AAEA,SAAS,sBAAsB,IAAI,EAAE,KAAK;IACtC,KAAK,QAAQ,CAAC,mBAAmB,CAAA;QAAO,QAAQ,EAAE,OAAO;IAAO;IAChE,IAAI,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;IACpD,MAAO,YAAY,KAAK,UAAU,KAAK,QAAQ,UAAU,IAAI,KAAK,QAAQ,UAAU,CAAC,UAAU,IAAI,EAAG;QAClG;QACA;QACA,IAAI,OAAO,QAAQ,UAAU;QAC7B,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,YAAY,GAAG,KAAK,KAAK,GAAG;QACjF,UAAU,KAAK,OAAO;IAC1B;IACA,IAAI,aAAa,KAAK,QAAQ,CAAC,0BAA0B,qJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;IACnG,IAAI,MAAM,eAAe,OAAO,IAAI,aAAa,CAAC;IAClD,KAAK,WAAW,CAAC,WAAW,iBAAiB,CAAC,SAAS;QAAE,UAAU;IAAI;IACvE,IAAI,aAAa,KAAK,UAAU,EAAE,WAAW,WAAW;IACxD,MAAO,cAAc,WAAW,QAAQ,IAAI,KAAK,CAAC,YAAY,OAAO,CAAC,WAAW,QAAQ,CAAC,WAAW,GAAG,EAAG;QACvG,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC5C,IAAI,UAAU,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;YAC5C,MAAO,KAAK,UAAU,CAClB,QAAQ,WAAW,CAAC,KAAK,UAAU;YACvC,KAAK,WAAW,CAAC;YACjB;QACJ;QACA,aAAa,KAAK,UAAU;IAChC;IACA,IAAI,cAAc,WAAW,QAAQ,IAAI,GACrC,WAAW,YAAY,CAAC,iBAAiB,GAAG,UAAU,CAAC,EAAE,UAAU,WAAW,CAAC,EAAE,EAAE,UAAU,GAAG,GAAG,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IACnI,IAAI,OAAO,KAAK,QAAQ,CAAC,2BAA2B,CAAA,IAAK,EAAE,OAAO,UAC9D,MAAM,OAAO,CAAC,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;IACrD,OAAO;QAAE,KAAK;QAAM;QAAM;IAAM;AACpC;AACA,6DAA6D;AAC7D,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ;IAC7D,IAAI,SAAS,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IAC3C,IAAI,KAAK;IACT,IAAI,CAAC,QAAQ,CAAC,MACV,OAAO;IACX,IAAI,SAAS,QAAQ,CAAC,aAAa,UAAU,CAAC,IAAI;IAClD,IAAI,QAAQ;QACR,KAAK,QAAQ,CAAC,uBAAuB,CAAA;YAAO,OAAO,EAAE,MAAM,UAAU,WAAW;QAAO;QACvF,IAAI,QACA,OAAO,OAAO,IAAI,qJAAA,CAAA,QAAK,CAAC,qJAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC,UAAU,SAAS,GAAG,KAAK,qJAAA,CAAA,QAAK,CAAC,KAAK;QACpH,IAAI,SAAS,KAAK,QAAQ,CAAC,uBAAuB,CAAA,IAAK,EAAE,MAAM,UAAU,WAAW;QACpF,IAAI,QAAQ;YACR,QAAQ;QACZ,OACK;YACD,IAAI,QAAQ,SAAS,KAAK;YAC1B,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,KAAK,EAAE,aAAa,qJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC;YACnE,MAAM,SAAS,aAAa,CAAC;YAC7B,KAAK,KAAK,CAAC,iBAAiB,OAAO,CAAC,CAAA;gBAChC,IAAI,IAAI,IAAI,WAAW,CAAC,SAAS,aAAa,CAAC;gBAC/C,IAAI,OACA,EAAE,WAAW,CAAC,WAAW,aAAa,CAAC,OAAO,IAAI,CAAC,OAAO;YAClE;QACJ;IACJ,OACK;QACD,KAAK,QAAQ,CAAC,uBAAuB,CAAA;YAAO,OAAO,EAAE,MAAM;QAAO;QAClE,MAAM,SAAS;QACf,IAAI,QACA,sBAAsB;IAC9B;IACA,IAAI,cAAc,OAAO,IAAI,aAAa,CAAC;IAC3C,IAAI,YAAY,eAAe,gCAAgC,IAAI,CAAC,YAAY,YAAY,CAAC,oBAAoB;IACjH,IAAI,aAAa,SAAS,CAAC,EAAE,EACzB,IAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG,IAAK;QACpC,IAAI,QAAQ,IAAI,UAAU;QAC1B,MAAO,SAAS,MAAM,QAAQ,IAAI,EAC9B,QAAQ,MAAM,WAAW;QAC7B,IAAI,CAAC,OACD;QACJ,MAAM;IACV;IACJ,IAAI,CAAC,OAAO;QACR,IAAI,SAAS,KAAK,QAAQ,CAAC,sBAAsB,KAAK,QAAQ,CAAC,gBAAgB,qJAAA,CAAA,YAAS,CAAC,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;QACrH,QAAQ,OAAO,UAAU,CAAC,KAAK;YAC3B,oBAAoB,CAAC,CAAC,CAAC,UAAU,SAAS;YAC1C,SAAS;YACT,cAAa,GAAG;gBACZ,IAAI,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,WAAW,IACxC,IAAI,UAAU,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,GAC7D,OAAO;oBAAE,QAAQ;gBAAK;gBAC1B,OAAO;YACX;QACJ;IACJ;IACA,IAAI,WAAW;QACX,QAAQ,WAAW,WAAW,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;IACpF,OACK;QACD,QAAQ,qJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB,MAAM,OAAO,EAAE,WAAW;QAClE,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,EAAE;YAClC,IAAI,YAAY,GAAG,UAAU;YAC7B,IAAK,IAAI,OAAO,MAAM,OAAO,CAAC,UAAU,EAAE,YAAY,MAAM,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,OAAO,KAAK,UAAU,CAAE,CAAE;YAC3I,IAAK,IAAI,OAAO,MAAM,OAAO,CAAC,SAAS,EAAE,UAAU,MAAM,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,OAAO,KAAK,SAAS,CAAE,CAAE;YACnI,QAAQ,WAAW,OAAO,WAAW;QACzC;IACJ;IACA,KAAK,QAAQ,CAAC,mBAAmB,CAAA;QAAO,QAAQ,EAAE,OAAO;IAAO;IAChE,OAAO;AACX;AACA,MAAM,gBAAgB;AACtB,sEAAsE;AACtE,kEAAkE;AAClE,sEAAsE;AACtE,0EAA0E;AAC1E,EAAE;AACF,8DAA8D;AAC9D,sEAAsE;AACtE,8BAA8B;AAC9B,SAAS,kBAAkB,QAAQ,EAAE,QAAQ;IACzC,IAAI,SAAS,UAAU,GAAG,GACtB,OAAO;IACX,IAAK,IAAI,IAAI,SAAS,KAAK,EAAE,KAAK,GAAG,IAAK;QACtC,IAAI,SAAS,SAAS,IAAI,CAAC;QAC3B,IAAI,QAAQ,OAAO,cAAc,CAAC,SAAS,KAAK,CAAC;QACjD,IAAI,UAAU,SAAS,EAAE;QACzB,SAAS,OAAO,CAAC,CAAA;YACb,IAAI,CAAC,QACD;YACJ,IAAI,OAAO,MAAM,YAAY,CAAC,KAAK,IAAI,GAAG;YAC1C,IAAI,CAAC,MACD,OAAO,SAAS;YACpB,IAAI,SAAS,OAAO,MAAM,IAAI,SAAS,MAAM,IAAI,aAAa,MAAM,UAAU,MAAM,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,EAAE,IAAI;gBAC/G,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,GAAG;YAChC,OACK;gBACD,IAAI,OAAO,MAAM,EACb,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,GAAG,WAAW,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,EAAE,SAAS,MAAM;gBACrF,IAAI,UAAU,aAAa,MAAM;gBACjC,OAAO,IAAI,CAAC;gBACZ,QAAQ,MAAM,SAAS,CAAC,QAAQ,IAAI;gBACpC,WAAW;YACf;QACJ;QACA,IAAI,QACA,OAAO,qJAAA,CAAA,WAAQ,CAAC,IAAI,CAAC;IAC7B;IACA,OAAO;AACX;AACA,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;IACtC,IAAK,IAAI,IAAI,KAAK,MAAM,GAAG,GAAG,KAAK,MAAM,IACrC,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,qJAAA,CAAA,WAAQ,CAAC,IAAI,CAAC;IAC9C,OAAO;AACX;AACA,6DAA6D;AAC7D,8CAA8C;AAC9C,SAAS,aAAa,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK;IACtD,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,SAAS,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;QAClF,IAAI,QAAQ,aAAa,MAAM,UAAU,MAAM,QAAQ,SAAS,EAAE,QAAQ;QAC1E,IAAI,OACA,OAAO,QAAQ,IAAI,CAAC,QAAQ,OAAO,CAAC,YAAY,CAAC,QAAQ,UAAU,GAAG,GAAG;QAC7E,IAAI,QAAQ,QAAQ,cAAc,CAAC,QAAQ,UAAU;QACrD,IAAI,MAAM,SAAS,CAAC,SAAS,KAAK,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,GACtE,OAAO,QAAQ,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,CAAC,qJAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,aAAa,MAAM,MAAM,QAAQ;IAClG;AACJ;AACA,SAAS,WAAW,IAAI,EAAE,KAAK;IAC3B,IAAI,SAAS,GACT,OAAO;IACX,IAAI,WAAW,KAAK,OAAO,CAAC,YAAY,CAAC,KAAK,UAAU,GAAG,GAAG,WAAW,KAAK,SAAS,EAAE,QAAQ;IACjG,IAAI,OAAO,KAAK,cAAc,CAAC,KAAK,UAAU,EAAE,UAAU,CAAC,qJAAA,CAAA,WAAQ,CAAC,KAAK,EAAE;IAC3E,OAAO,KAAK,IAAI,CAAC,SAAS,MAAM,CAAC;AACrC;AACA,SAAS,WAAW,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO;IACxD,IAAI,OAAO,OAAO,IAAI,SAAS,UAAU,GAAG,SAAS,SAAS,EAAE,QAAQ,KAAK,OAAO;IACpF,IAAI,SAAS,UAAU,GAAG,GACtB,UAAU;IACd,IAAI,QAAQ,KAAK,GACb,QAAQ,WAAW,OAAO,MAAM,MAAM,IAAI,QAAQ,GAAG;IACzD,IAAI,SAAS,MACT,QAAQ,OAAO,IAAI,KAAK,cAAc,CAAC,GAAG,UAAU,CAAC,OAAO,WAAW,OAAO,MAAM,CAAC,SAC/E,MAAM,MAAM,CAAC,KAAK,cAAc,CAAC,KAAK,UAAU,EAAE,UAAU,CAAC,qJAAA,CAAA,WAAQ,CAAC,KAAK,EAAE;IACvF,OAAO,SAAS,YAAY,CAAC,OAAO,IAAI,IAAI,SAAS,UAAU,GAAG,GAAG,KAAK,IAAI,CAAC;AACnF;AACA,SAAS,WAAW,KAAK,EAAE,SAAS,EAAE,OAAO;IACzC,IAAI,YAAY,MAAM,SAAS,EAC3B,QAAQ,IAAI,qJAAA,CAAA,QAAK,CAAC,WAAW,MAAM,OAAO,EAAE,CAAC,GAAG,WAAW,MAAM,SAAS,EAAE,GAAG,MAAM,OAAO,GAAG,WAAW,MAAM,OAAO;IAC3H,IAAI,UAAU,MAAM,OAAO,EACvB,QAAQ,IAAI,qJAAA,CAAA,QAAK,CAAC,WAAW,MAAM,OAAO,EAAE,GAAG,SAAS,MAAM,OAAO,EAAE,GAAG,IAAI,MAAM,SAAS,EAAE;IACnG,OAAO;AACX;AACA,8DAA8D;AAC9D,kEAAkE;AAClE,8CAA8C;AAC9C,MAAM,UAAU;IACZ,OAAO;QAAC;KAAQ;IAChB,OAAO;QAAC;KAAQ;IAChB,OAAO;QAAC;KAAQ;IAChB,SAAS;QAAC;KAAQ;IAClB,UAAU;QAAC;KAAQ;IACnB,KAAK;QAAC;QAAS;KAAW;IAC1B,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;QAAS;KAAK;IAC5B,IAAI;QAAC;QAAS;QAAS;KAAK;AAChC;AACA,IAAI,eAAe;AACnB,SAAS;IACL,OAAO,gBAAgB,CAAC,eAAe,SAAS,cAAc,CAAC,kBAAkB,CAAC,QAAQ;AAC9F;AACA,IAAI,UAAU;AACd,SAAS,iBAAiB,IAAI;IAC1B,IAAI,eAAe,OAAO,YAAY;IACtC,IAAI,CAAC,cACD,OAAO;IACX,4DAA4D;IAC5D,mEAAmE;IACnE,iEAAiE;IACjE,IAAI,CAAC,SACD,UAAU,aAAa,aAAa,IAAI,aAAa,YAAY,CAAC,wBAAwB;QAAE,YAAY,CAAC,IAAM;IAAE;IACrH,OAAO,QAAQ,UAAU,CAAC;AAC9B;AACA,SAAS,SAAS,IAAI;IAClB,IAAI,QAAQ,sBAAsB,IAAI,CAAC;IACvC,IAAI,OACA,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM;IACrC,IAAI,MAAM,cAAc,aAAa,CAAC;IACtC,IAAI,WAAW,mBAAmB,IAAI,CAAC,OAAO;IAC9C,IAAI,OAAO,YAAY,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,EACrD,OAAO,KAAK,GAAG,CAAC,CAAA,IAAK,MAAM,IAAI,KAAK,IAAI,CAAC,MAAM,OAAO,KAAK,GAAG,CAAC,CAAA,IAAK,OAAO,IAAI,KAAK,OAAO,GAAG,IAAI,CAAC;IACvG,IAAI,SAAS,GAAG,iBAAiB;IACjC,IAAI,MACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAC7B,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK;IAC5C,OAAO;AACX;AACA,iEAAiE;AACjE,8DAA8D;AAC9D,mEAAmE;AACnE,+DAA+D;AAC/D,2DAA2D;AAC3D,SAAS,sBAAsB,GAAG;IAC9B,IAAI,QAAQ,IAAI,gBAAgB,CAAC,SAAS,mCAAmC;IAC7E,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,IAAI,OAAO,KAAK,CAAC,EAAE;QACnB,IAAI,KAAK,UAAU,CAAC,MAAM,IAAI,KAAK,KAAK,WAAW,IAAI,YAAY,KAAK,UAAU,EAC9E,KAAK,UAAU,CAAC,YAAY,CAAC,IAAI,aAAa,CAAC,cAAc,CAAC,MAAM;IAC5E;AACJ;AACA,SAAS,WAAW,KAAK,EAAE,OAAO;IAC9B,IAAI,CAAC,MAAM,IAAI,EACX,OAAO;IACX,IAAI,SAAS,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE;IACnD,IAAI;QACA,QAAQ,KAAK,KAAK,CAAC;IACvB,EACA,OAAO,GAAG;QACN,OAAO;IACX;IACA,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;IACtC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,EAAG;QAC3C,IAAI,OAAO,OAAO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,IAAI,CAAC,QAAQ,KAAK,gBAAgB,IAC9B;QACJ,UAAU,qJAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;QAClD;QACA;IACJ;IACA,OAAO,IAAI,qJAAA,CAAA,QAAK,CAAC,SAAS,WAAW;AACzC;AAEA,kFAAkF;AAClF,kCAAkC;AAClC,MAAM,WAAW,CAAC;AAClB,MAAM,eAAe,CAAC;AACtB,MAAM,kBAAkB;IAAE,YAAY;IAAM,WAAW;AAAK;AAC5D,MAAM;IACF,aAAc;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,SAAS,GAAG;YAAE,MAAM;YAAG,GAAG;YAAG,GAAG;YAAG,MAAM;YAAI,QAAQ;QAAE;QAC5D,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,2BAA2B,GAAG,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,gBAAgB,GAAG,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAAC,kBAAkB,GAAG,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG;QACrB,2EAA2E;QAC3E,IAAI,CAAC,yBAAyB,GAAG;QACjC,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,OAAO,MAAM,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG;IAC9B;AACJ;AACA,SAAS,UAAU,IAAI;IACnB,IAAK,IAAI,SAAS,SAAU;QACxB,IAAI,UAAU,QAAQ,CAAC,MAAM;QAC7B,KAAK,GAAG,CAAC,gBAAgB,CAAC,OAAO,KAAK,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;YAChE,IAAI,mBAAmB,MAAM,UAAU,CAAC,iBAAiB,MAAM,UAC3D,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,YAAY,CAAC,GAC/C,QAAQ,MAAM;QACtB,GAAG,eAAe,CAAC,MAAM,GAAG;YAAE,SAAS;QAAK,IAAI;IACpD;IACA,kEAAkE;IAClE,mEAAmE;IACnE,2BAA2B;IAC3B,IAAI,QACA,KAAK,GAAG,CAAC,gBAAgB,CAAC,SAAS,IAAM;IAC7C,gBAAgB;AACpB;AACA,SAAS,mBAAmB,IAAI,EAAE,MAAM;IACpC,KAAK,KAAK,CAAC,mBAAmB,GAAG;IACjC,KAAK,KAAK,CAAC,iBAAiB,GAAG,KAAK,GAAG;AAC3C;AACA,SAAS,aAAa,IAAI;IACtB,KAAK,WAAW,CAAC,IAAI;IACrB,IAAK,IAAI,QAAQ,KAAK,KAAK,CAAC,aAAa,CACrC,KAAK,GAAG,CAAC,mBAAmB,CAAC,MAAM,KAAK,KAAK,CAAC,aAAa,CAAC,KAAK;IACrE,aAAa,KAAK,KAAK,CAAC,gBAAgB;IACxC,aAAa,KAAK,KAAK,CAAC,2BAA2B;AACvD;AACA,SAAS,gBAAgB,IAAI;IACzB,KAAK,QAAQ,CAAC,mBAAmB,CAAA;QAC7B,IAAK,IAAI,QAAQ,gBACb,IAAI,CAAC,KAAK,KAAK,CAAC,aAAa,CAAC,KAAK,EAC/B,KAAK,GAAG,CAAC,gBAAgB,CAAC,MAAM,KAAK,KAAK,CAAC,aAAa,CAAC,KAAK,GAAG,CAAA,QAAS,iBAAiB,MAAM;IAC7G;AACJ;AACA,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACjC,OAAO,KAAK,QAAQ,CAAC,mBAAmB,CAAA;QACpC,IAAI,UAAU,QAAQ,CAAC,MAAM,IAAI,CAAC;QAClC,OAAO,UAAU,QAAQ,MAAM,UAAU,MAAM,gBAAgB,GAAG;IACtE;AACJ;AACA,SAAS,mBAAmB,IAAI,EAAE,KAAK;IACnC,IAAI,CAAC,MAAM,OAAO,EACd,OAAO;IACX,IAAI,MAAM,gBAAgB,EACtB,OAAO;IACX,IAAK,IAAI,OAAO,MAAM,MAAM,EAAE,QAAQ,KAAK,GAAG,EAAE,OAAO,KAAK,UAAU,CAClE,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,MACzB,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,SAAS,CAAC,QAC9C,OAAO;IACf,OAAO;AACX;AACA,SAAS,cAAc,IAAI,EAAE,KAAK;IAC9B,IAAI,CAAC,iBAAiB,MAAM,UAAU,QAAQ,CAAC,MAAM,IAAI,CAAC,IACtD,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,YAAY,CAAC,GAC/C,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM;AACnC;AACA,aAAa,OAAO,GAAG,CAAC,MAAM;IAC1B,IAAI,QAAQ;IACZ,KAAK,KAAK,CAAC,QAAQ,GAAG,MAAM,OAAO,IAAI,MAAM,MAAM,QAAQ;IAC3D,IAAI,oBAAoB,MAAM,QAC1B;IACJ,KAAK,KAAK,CAAC,WAAW,GAAG,MAAM,OAAO;IACtC,KAAK,KAAK,CAAC,eAAe,GAAG,KAAK,GAAG;IACrC,kEAAkE;IAClE,iEAAiE;IACjE,wDAAwD;IACxD,IAAI,WAAW,UAAU,MAAM,OAAO,IAAI,IACtC;IACJ,IAAI,MAAM,OAAO,IAAI,KACjB,KAAK,WAAW,CAAC,UAAU;IAC/B,8DAA8D;IAC9D,iEAAiE;IACjE,oEAAoE;IACpE,qDAAqD;IACrD,IAAI,OAAO,MAAM,OAAO,IAAI,MAAM,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,OAAO,EAAE;QACjF,IAAI,MAAM,KAAK,GAAG;QAClB,KAAK,KAAK,CAAC,YAAY,GAAG;QAC1B,KAAK,KAAK,CAAC,2BAA2B,GAAG,WAAW;YAChD,IAAI,KAAK,KAAK,CAAC,YAAY,IAAI,KAAK;gBAChC,KAAK,QAAQ,CAAC,iBAAiB,CAAA,IAAK,EAAE,MAAM,SAAS,IAAI;gBACzD,KAAK,KAAK,CAAC,YAAY,GAAG;YAC9B;QACJ,GAAG;IACP,OACK,IAAI,KAAK,QAAQ,CAAC,iBAAiB,CAAA,IAAK,EAAE,MAAM,WAAW,eAAe,MAAM,QAAQ;QACzF,MAAM,cAAc;IACxB,OACK;QACD,mBAAmB,MAAM;IAC7B;AACJ;AACA,aAAa,KAAK,GAAG,CAAC,MAAM;IACxB,IAAI,MAAM,OAAO,IAAI,IACjB,KAAK,KAAK,CAAC,QAAQ,GAAG;AAC9B;AACA,aAAa,QAAQ,GAAG,CAAC,MAAM;IAC3B,IAAI,QAAQ;IACZ,IAAI,oBAAoB,MAAM,UAAU,CAAC,MAAM,QAAQ,IACnD,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,OAAO,MAAM,OAAO,EACtD;IACJ,IAAI,KAAK,QAAQ,CAAC,kBAAkB,CAAA,IAAK,EAAE,MAAM,SAAS;QACtD,MAAM,cAAc;QACpB;IACJ;IACA,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS;IAC9B,IAAI,CAAC,CAAC,eAAe,qJAAA,CAAA,gBAAa,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG,GAAG;QACnE,IAAI,OAAO,OAAO,YAAY,CAAC,MAAM,QAAQ;QAC7C,IAAI,QAAQ,IAAM,KAAK,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,cAAc;QAC/D,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ,CAAC,mBAAmB,CAAA,IAAK,EAAE,MAAM,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,SACzG,KAAK,QAAQ,CAAC;QAClB,MAAM,cAAc;IACxB;AACJ;AACA,SAAS,YAAY,KAAK;IAAI,OAAO;QAAE,MAAM,MAAM,OAAO;QAAE,KAAK,MAAM,OAAO;IAAC;AAAG;AAClF,SAAS,OAAO,KAAK,EAAE,KAAK;IACxB,IAAI,KAAK,MAAM,CAAC,GAAG,MAAM,OAAO,EAAE,KAAK,MAAM,CAAC,GAAG,MAAM,OAAO;IAC9D,OAAO,KAAK,KAAK,KAAK,KAAK;AAC/B;AACA,SAAS,oBAAoB,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;IAC3D,IAAI,UAAU,CAAC,GACX,OAAO;IACX,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;IAClC,IAAK,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,IAAI,GAAG,IAAK;QACrC,IAAI,KAAK,QAAQ,CAAC,UAAU,CAAA,IAAK,IAAI,KAAK,KAAK,GAAG,EAAE,MAAM,KAAK,KAAK,SAAS,EAAE,KAAK,MAAM,CAAC,IAAI,OAAO,QAChG,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,SACpD,OAAO;IACf;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,IAAI,EAAE,SAAS,EAAE,MAAM;IAC5C,IAAI,CAAC,KAAK,OAAO,EACb,KAAK,KAAK;IACd,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,YACxB;IACJ,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC;IACpC,IAAI,UAAU,WACV,GAAG,OAAO,CAAC,WAAW;IAC1B,KAAK,QAAQ,CAAC;AAClB;AACA,SAAS,kBAAkB,IAAI,EAAE,MAAM;IACnC,IAAI,UAAU,CAAC,GACX,OAAO;IACX,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,OAAO,KAAK,SAAS;IAChE,IAAI,QAAQ,KAAK,MAAM,IAAI,qJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,OAAO;QACzD,gBAAgB,MAAM,IAAI,qJAAA,CAAA,gBAAa,CAAC,OAAO;QAC/C,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,IAAI,EAAE,MAAM;IACnC,IAAI,UAAU,CAAC,GACX,OAAO;IACX,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS,EAAE,cAAc;IAC9C,IAAI,eAAe,qJAAA,CAAA,gBAAa,EAC5B,eAAe,IAAI,IAAI;IAC3B,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;IAClC,IAAK,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,IAAI,GAAG,IAAK;QACrC,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;QACvD,IAAI,qJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,OAAO;YAClC,IAAI,gBAAgB,IAAI,KAAK,CAAC,KAAK,GAAG,KAClC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM,IAAI,KAAK,CAAC,GAAG,EACzE,WAAW,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK;iBAEtC,WAAW,KAAK,MAAM,CAAC;YAC3B;QACJ;IACJ;IACA,IAAI,YAAY,MAAM;QAClB,gBAAgB,MAAM,qJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE,WAAW;QACtE,OAAO;IACX,OACK;QACD,OAAO;IACX;AACJ;AACA,SAAS,kBAAkB,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU;IAC3D,OAAO,oBAAoB,MAAM,iBAAiB,KAAK,QAAQ,UAC3D,KAAK,QAAQ,CAAC,eAAe,CAAA,IAAK,EAAE,MAAM,KAAK,WAC/C,CAAC,aAAa,kBAAkB,MAAM,UAAU,kBAAkB,MAAM,OAAO;AACvF;AACA,SAAS,kBAAkB,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;IAC/C,OAAO,oBAAoB,MAAM,uBAAuB,KAAK,QAAQ,UACjE,KAAK,QAAQ,CAAC,qBAAqB,CAAA,IAAK,EAAE,MAAM,KAAK;AAC7D;AACA,SAAS,kBAAkB,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;IAC/C,OAAO,oBAAoB,MAAM,uBAAuB,KAAK,QAAQ,UACjE,KAAK,QAAQ,CAAC,qBAAqB,CAAA,IAAK,EAAE,MAAM,KAAK,WACrD,mBAAmB,MAAM,QAAQ;AACzC;AACA,SAAS,mBAAmB,IAAI,EAAE,MAAM,EAAE,KAAK;IAC3C,IAAI,MAAM,MAAM,IAAI,GAChB,OAAO;IACX,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG;IACxB,IAAI,UAAU,CAAC,GAAG;QACd,IAAI,IAAI,aAAa,EAAE;YACnB,gBAAgB,MAAM,qJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG;YACtE,OAAO;QACX;QACA,OAAO;IACX;IACA,IAAI,OAAO,IAAI,OAAO,CAAC;IACvB,IAAK,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,IAAI,GAAG,IAAK;QACrC,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;QACvD,IAAI,UAAU,KAAK,MAAM,CAAC;QAC1B,IAAI,KAAK,aAAa,EAClB,gBAAgB,MAAM,qJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK,UAAU,GAAG,UAAU,IAAI,KAAK,OAAO,CAAC,IAAI,GAAG;aAC9F,IAAI,qJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,OAChC,gBAAgB,MAAM,qJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK,UAAU;aAE1D;QACJ,OAAO;IACX;AACJ;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,eAAe;AAC1B;AACA,MAAM,qBAAqB,MAAM,YAAY;AAC7C,SAAS,SAAS,GAAG,CAAC,MAAM;IACxB,IAAI,QAAQ;IACZ,KAAK,KAAK,CAAC,QAAQ,GAAG,MAAM,QAAQ;IACpC,IAAI,UAAU,cAAc;IAC5B,IAAI,MAAM,KAAK,GAAG,IAAI,OAAO;IAC7B,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,OAAO,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,mBAAmB,IAC1G,KAAK,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,MAAM,MAAM,EAAE;QAC7C,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,eAC7B,OAAO;aACN,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,eAClC,OAAO;IACf;IACA,KAAK,KAAK,CAAC,SAAS,GAAG;QAAE,MAAM;QAAK,GAAG,MAAM,OAAO;QAAE,GAAG,MAAM,OAAO;QAAE;QAAM,QAAQ,MAAM,MAAM;IAAC;IACnG,IAAI,MAAM,KAAK,WAAW,CAAC,YAAY;IACvC,IAAI,CAAC,KACD;IACJ,IAAI,QAAQ,eAAe;QACvB,IAAI,KAAK,KAAK,CAAC,SAAS,EACpB,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI;QAC7B,KAAK,KAAK,CAAC,SAAS,GAAG,IAAI,UAAU,MAAM,KAAK,OAAO,CAAC,CAAC;IAC7D,OACK,IAAI,CAAC,QAAQ,gBAAgB,oBAAoB,iBAAiB,EAAE,MAAM,IAAI,GAAG,EAAE,IAAI,MAAM,EAAE,QAAQ;QACxG,MAAM,cAAc;IACxB,OACK;QACD,mBAAmB,MAAM;IAC7B;AACJ;AACA,MAAM;IACF,YAAY,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,CAAE;QACnC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC,GAAG;QAC9B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,mBAAmB;QAC7C,IAAI,CAAC,YAAY,GAAG,MAAM,QAAQ;QAClC,IAAI,YAAY;QAChB,IAAI,IAAI,MAAM,GAAG,CAAC,GAAG;YACjB,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,MAAM;YAC7C,YAAY,IAAI,MAAM;QAC1B,OACK;YACD,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;YACzC,aAAa,KAAK,MAAM;YACxB,YAAY,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK;QAC7C;QACA,MAAM,SAAS,UAAU,OAAO,MAAM,MAAM;QAC5C,MAAM,aAAa,SAAS,KAAK,OAAO,CAAC,WAAW,CAAC,QAAQ,QAAQ;QACrE,IAAI,CAAC,MAAM,GAAG,cAAc,WAAW,GAAG,CAAC,QAAQ,IAAI,IAAI,WAAW,GAAG,GAAG;QAC5E,IAAI,EAAE,SAAS,EAAE,GAAG,KAAK,KAAK;QAC9B,IAAI,MAAM,MAAM,IAAI,KAChB,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,SACtE,qBAAqB,qJAAA,CAAA,gBAAa,IAAI,UAAU,IAAI,IAAI,aAAa,UAAU,EAAE,GAAG,WACpF,IAAI,CAAC,SAAS,GAAG;YACb,MAAM;YACN,KAAK;YACL,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS;YACjD,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB;QAC1F;QACJ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG;YAC3F,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;YAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EACtB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;YAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAC5B,WAAW;gBACP,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,EACjC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,mBAAmB;YACpD,GAAG;YACP,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK;QAC/B;QACA,KAAK,IAAI,CAAC,gBAAgB,CAAC,WAAW,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI;QACjE,KAAK,IAAI,CAAC,gBAAgB,CAAC,aAAa,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;QACvE,mBAAmB,MAAM;IAC7B;IACA,OAAO;QACH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,IAAI,CAAC,EAAE;QACrD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa,IAAI,CAAC,IAAI;QACzD,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;YAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EACtB,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAC5B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK;QAC/B;QACA,IAAI,IAAI,CAAC,oBAAoB,EACzB,WAAW,IAAM,eAAe,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;IAChC;IACA,GAAG,KAAK,EAAE;QACN,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,MAAM,GACpC;QACJ,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,EACpC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY;QAC5C,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,KAAK;YAC3B,mBAAmB,IAAI,CAAC,IAAI,EAAE;QAClC,OACK,IAAI,kBAAkB,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,MAAM,EAAE,OAAO,IAAI,CAAC,UAAU,GAAG;YAChF,MAAM,cAAc;QACxB,OACK,IAAI,MAAM,MAAM,IAAI,KACrB,CAAC,IAAI,CAAC,OAAO,IAER,UAAU,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,IAQvD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,IACzC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM,CAAE,GAAG;YAC/H,gBAAgB,IAAI,CAAC,IAAI,EAAE,qJAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI;YACjF,MAAM,cAAc;QACxB,OACK;YACD,mBAAmB,IAAI,CAAC,IAAI,EAAE;QAClC;IACJ;IACA,KAAK,KAAK,EAAE;QACR,IAAI,CAAC,kBAAkB,CAAC;QACxB,mBAAmB,IAAI,CAAC,IAAI,EAAE;QAC9B,IAAI,MAAM,OAAO,IAAI,GACjB,IAAI,CAAC,IAAI;IACjB;IACA,mBAAmB,KAAK,EAAE;QACtB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,OAAO,IAAI,KAChE,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,GAC1C,IAAI,CAAC,YAAY,GAAG;IAC5B;AACJ;AACA,SAAS,UAAU,GAAG,CAAA;IAClB,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,GAAG;IAC/B,cAAc;IACd,mBAAmB,MAAM;AAC7B;AACA,SAAS,SAAS,GAAG,CAAA;IACjB,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,GAAG;IAC/B,mBAAmB,MAAM;AAC7B;AACA,SAAS,WAAW,GAAG,CAAA,OAAQ,cAAc;AAC7C,SAAS,oBAAoB,IAAI,EAAE,KAAK;IACpC,IAAI,KAAK,SAAS,EACd,OAAO;IACX,yEAAyE;IACzE,sFAAsF;IACtF,qFAAqF;IACrF,8EAA8E;IAC9E,mEAAmE;IACnE,qFAAqF;IACrF,8FAA8F;IAC9F,sFAAsF;IACtF,qFAAqF;IACrF,yEAAyE;IACzE,IAAI,UAAU,KAAK,GAAG,CAAC,MAAM,SAAS,GAAG,KAAK,KAAK,CAAC,kBAAkB,IAAI,KAAK;QAC3E,KAAK,KAAK,CAAC,kBAAkB,GAAG,CAAC;QACjC,OAAO;IACX;IACA,OAAO;AACX;AACA,mEAAmE;AACnE,MAAM,qBAAqB,UAAU,OAAO,CAAC;AAC7C,aAAa,gBAAgB,GAAG,aAAa,iBAAiB,GAAG,CAAA;IAC7D,IAAI,CAAC,KAAK,SAAS,EAAE;QACjB,KAAK,WAAW,CAAC,KAAK;QACtB,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,SAAS,CAAC,GAAG;QAChD,IAAI,MAAM,SAAS,YAAY,qJAAA,CAAA,gBAAa,IACxC,CAAC,MAAM,WAAW,IACb,CAAC,KAAK,UAAU,IAAI,KAAK,YAAY,IAAI,KAAK,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,MAAO,GAAG;YAClH,mFAAmF;YACnF,KAAK,UAAU,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,KAAK;YACtD,eAAe,MAAM;YACrB,KAAK,UAAU,GAAG;QACtB,OACK;YACD,eAAe,MAAM,CAAC,MAAM,SAAS,CAAC,KAAK;YAC3C,gEAAgE;YAChE,8DAA8D;YAC9D,uBAAuB;YACvB,IAAI,SAAS,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,YAAY,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE;gBACzG,IAAI,MAAM,KAAK,iBAAiB;gBAChC,IAAK,IAAI,OAAO,IAAI,SAAS,EAAE,SAAS,IAAI,WAAW,EAAE,QAAQ,KAAK,QAAQ,IAAI,KAAK,UAAU,GAAI;oBACjG,IAAI,SAAS,SAAS,IAAI,KAAK,SAAS,GAAG,KAAK,UAAU,CAAC,SAAS,EAAE;oBACtE,IAAI,CAAC,QACD;oBACJ,IAAI,OAAO,QAAQ,IAAI,GAAG;wBACtB,IAAI,MAAM,KAAK,YAAY;wBAC3B,IAAI,KACA,IAAI,QAAQ,CAAC,QAAQ,OAAO,SAAS,CAAC,MAAM;wBAChD;oBACJ,OACK;wBACD,OAAO;wBACP,SAAS,CAAC;oBACd;gBACJ;YACJ;QACJ;QACA,KAAK,KAAK,CAAC,SAAS,GAAG;IAC3B;IACA,mBAAmB,MAAM;AAC7B;AACA,aAAa,cAAc,GAAG,CAAC,MAAM;IACjC,IAAI,KAAK,SAAS,EAAE;QAChB,KAAK,KAAK,CAAC,SAAS,GAAG;QACvB,KAAK,KAAK,CAAC,kBAAkB,GAAG,MAAM,SAAS;QAC/C,KAAK,KAAK,CAAC,yBAAyB,GAAG,KAAK,WAAW,CAAC,cAAc,GAAG,MAAM,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG;QAC7G,KAAK,KAAK,CAAC,eAAe,GAAG;QAC7B,IAAI,KAAK,KAAK,CAAC,yBAAyB,EACpC,QAAQ,OAAO,GAAG,IAAI,CAAC,IAAM,KAAK,WAAW,CAAC,KAAK;QACvD,KAAK,KAAK,CAAC,aAAa;QACxB,mBAAmB,MAAM;IAC7B;AACJ;AACA,SAAS,mBAAmB,IAAI,EAAE,KAAK;IACnC,aAAa,KAAK,KAAK,CAAC,gBAAgB;IACxC,IAAI,QAAQ,CAAC,GACT,KAAK,KAAK,CAAC,gBAAgB,GAAG,WAAW,IAAM,eAAe,OAAO;AAC7E;AACA,SAAS,iBAAiB,IAAI;IAC1B,IAAI,KAAK,SAAS,EAAE;QAChB,KAAK,KAAK,CAAC,SAAS,GAAG;QACvB,KAAK,KAAK,CAAC,kBAAkB,GAAG;IACpC;IACA,MAAO,KAAK,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,EACxC,KAAK,KAAK,CAAC,gBAAgB,CAAC,GAAG,GAAG,gBAAgB;AAC1D;AACA,SAAS,oBAAoB,IAAI;IAC7B,IAAI,MAAM,KAAK,iBAAiB;IAChC,IAAI,CAAC,IAAI,SAAS,EACd,OAAO;IACX,IAAI,aAAa,iBAAiB,IAAI,SAAS,EAAE,IAAI,WAAW;IAChE,IAAI,YAAY,gBAAgB,IAAI,SAAS,EAAE,IAAI,WAAW;IAC9D,IAAI,cAAc,aAAa,cAAc,WAAW;QACpD,IAAI,YAAY,UAAU,UAAU,EAAE,cAAc,KAAK,WAAW,CAAC,mBAAmB;QACxF,IAAI,cAAc,eAAe,aAAa,aAC1C,OAAO;QACX,IAAI,CAAC,aAAa,CAAC,UAAU,MAAM,CAAC,UAAU,SAAS,GAAG;YACtD,OAAO;QACX,OACK,IAAI,KAAK,KAAK,CAAC,eAAe,IAAI,WAAW;YAC9C,IAAI,aAAa,WAAW,UAAU;YACtC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,MAAM,CAAC,WAAW,SAAS,CAAC,GACzD,OAAO;QACf;IACJ;IACA,OAAO,cAAc;AACzB;AACA,SAAS;IACL,IAAI,QAAQ,SAAS,WAAW,CAAC;IACjC,MAAM,SAAS,CAAC,SAAS,MAAM;IAC/B,OAAO,MAAM,SAAS;AAC1B;AACA;;AAEA,GACA,SAAS,eAAe,IAAI,EAAE,aAAa,KAAK;IAC5C,IAAI,WAAW,KAAK,WAAW,CAAC,YAAY,IAAI,GAC5C;IACJ,KAAK,WAAW,CAAC,UAAU;IAC3B,iBAAiB;IACjB,IAAI,cAAc,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,KAAK,EAAE;QAClD,IAAI,MAAM,iBAAiB,OAAO,MAAM,KAAK,KAAK,CAAC,SAAS;QAC5D,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MACf,KAAK,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC;aACxC,IAAI,CAAC,KAAK,UAAU,IAAI,UAAU,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,aAAa,EACpG,KAAK,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,eAAe;aAE3C,KAAK,WAAW,CAAC,KAAK,KAAK;QAC/B,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,YAAY,IAAI,EAAE,GAAG;IAC1B,mEAAmE;IACnE,+DAA+D;IAC/D,IAAI,CAAC,KAAK,GAAG,CAAC,UAAU,EACpB;IACJ,IAAI,OAAO,KAAK,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,aAAa,CAAC;IAClE,KAAK,WAAW,CAAC;IACjB,KAAK,KAAK,CAAC,OAAO,GAAG;IACrB,IAAI,MAAM,gBAAgB,QAAQ,SAAS,WAAW;IACtD,MAAM,kBAAkB,CAAC;IACzB,mEAAmE;IACnE,mEAAmE;IACnE,+DAA+D;IAC/D,KAAK,GAAG,CAAC,IAAI;IACb,IAAI,eAAe;IACnB,IAAI,QAAQ,CAAC;IACb,WAAW;QACP,IAAI,KAAK,UAAU,EACf,KAAK,UAAU,CAAC,WAAW,CAAC;QAChC,KAAK,KAAK;IACd,GAAG;AACP;AACA,sEAAsE;AACtE,iEAAiE;AACjE,0DAA0D;AAC1D,MAAM,qBAAqB,AAAC,MAAM,aAAa,MAC1C,OAAO,iBAAiB;AAC7B,SAAS,IAAI,GAAG,aAAa,GAAG,GAAG,CAAC,MAAM;IACtC,IAAI,QAAQ;IACZ,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS,EAAE,MAAM,MAAM,IAAI,IAAI;IACpD,IAAI,IAAI,KAAK,EACT;IACJ,yDAAyD;IACzD,IAAI,OAAO,qBAAqB,OAAO,MAAM,aAAa;IAC1D,IAAI,QAAQ,IAAI,OAAO,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,sBAAsB,MAAM;IACvE,IAAI,MAAM;QACN,MAAM,cAAc;QACpB,KAAK,SAAS;QACd,KAAK,OAAO,CAAC,aAAa,IAAI,SAAS;QACvC,KAAK,OAAO,CAAC,cAAc;IAC/B,OACK;QACD,YAAY,MAAM;IACtB;IACA,IAAI,KACA,KAAK,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,eAAe,GAAG,cAAc,GAAG,OAAO,CAAC,WAAW;AAC1F;AACA,SAAS,gBAAgB,KAAK;IAC1B,OAAO,MAAM,SAAS,IAAI,KAAK,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO,CAAC,UAAU,IAAI,IAAI,MAAM,OAAO,CAAC,UAAU,GAAG;AACpH;AACA,SAAS,aAAa,IAAI,EAAE,KAAK;IAC7B,IAAI,CAAC,KAAK,GAAG,CAAC,UAAU,EACpB;IACJ,IAAI,YAAY,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IACvF,IAAI,SAAS,KAAK,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,aAAa,CAAC,YAAY,aAAa;IAC7F,IAAI,CAAC,WACD,OAAO,eAAe,GAAG;IAC7B,OAAO,KAAK,CAAC,OAAO,GAAG;IACvB,OAAO,KAAK;IACZ,IAAI,QAAQ,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK,KAAK,CAAC,WAAW,IAAI;IAC7D,WAAW;QACP,KAAK,KAAK;QACV,IAAI,OAAO,UAAU,EACjB,OAAO,UAAU,CAAC,WAAW,CAAC;QAClC,IAAI,WACA,QAAQ,MAAM,OAAO,KAAK,EAAE,MAAM,OAAO;aAEzC,QAAQ,MAAM,OAAO,WAAW,EAAE,OAAO,SAAS,EAAE,OAAO;IACnE,GAAG;AACP;AACA,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK;IACjD,IAAI,QAAQ,mBAAmB,MAAM,MAAM,MAAM,aAAa,KAAK,KAAK,CAAC,SAAS,CAAC,KAAK;IACxF,IAAI,KAAK,QAAQ,CAAC,eAAe,CAAA,IAAK,EAAE,MAAM,OAAO,SAAS,qJAAA,CAAA,QAAK,CAAC,KAAK,IACrE,OAAO;IACX,IAAI,CAAC,OACD,OAAO;IACX,IAAI,aAAa,gBAAgB;IACjC,IAAI,KAAK,aACH,KAAK,KAAK,CAAC,EAAE,CAAC,oBAAoB,CAAC,YAAY,eAC/C,KAAK,KAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC;IACrC,KAAK,QAAQ,CAAC,GAAG,cAAc,GAAG,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,WAAW;IAC5E,OAAO;AACX;AACA,SAAS,QAAQ,aAAa;IAC1B,IAAI,OAAO,cAAc,OAAO,CAAC,iBAAiB,cAAc,OAAO,CAAC;IACxE,IAAI,MACA,OAAO;IACX,IAAI,OAAO,cAAc,OAAO,CAAC;IACjC,OAAO,OAAO,KAAK,OAAO,CAAC,UAAU,OAAO;AAChD;AACA,aAAa,KAAK,GAAG,CAAC,MAAM;IACxB,IAAI,QAAQ;IACZ,mEAAmE;IACnE,oEAAoE;IACpE,gEAAgE;IAChE,+CAA+C;IAC/C,IAAI,KAAK,SAAS,IAAI,CAAC,SACnB;IACJ,IAAI,OAAO,qBAAqB,OAAO,MAAM,aAAa;IAC1D,IAAI,QAAQ,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK,KAAK,CAAC,WAAW,IAAI;IAC7D,IAAI,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,OAAO,CAAC,cAAc,OAAO,QACvE,MAAM,cAAc;SAEpB,aAAa,MAAM;AAC3B;AACA,MAAM;IACF,YAAY,KAAK,EAAE,IAAI,EAAE,IAAI,CAAE;QAC3B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,MAAM,mBAAmB,MAAM,WAAW;AAC1C,SAAS,UAAU,IAAI,EAAE,KAAK;IAC1B,IAAI,QAAQ,KAAK,QAAQ,CAAC,cAAc,CAAA,OAAQ,CAAC,KAAK;IACtD,OAAO,SAAS,OAAO,QAAQ,CAAC,KAAK,CAAC,iBAAiB;AAC3D;AACA,SAAS,SAAS,GAAG,CAAC,MAAM;IACxB,IAAI,QAAQ;IACZ,IAAI,YAAY,KAAK,KAAK,CAAC,SAAS;IACpC,IAAI,WACA,UAAU,IAAI;IAClB,IAAI,CAAC,MAAM,YAAY,EACnB;IACJ,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS;IAC9B,IAAI,MAAM,IAAI,KAAK,GAAG,OAAO,KAAK,WAAW,CAAC,YAAY;IAC1D,IAAI;IACJ,IAAI,OAAO,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,qJAAA,CAAA,gBAAa,GAAG,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE;SAC3F,IAAI,aAAa,UAAU,SAAS,EAAE;QACvC,OAAO,qJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE,UAAU,SAAS,CAAC,GAAG;IACvE,OACK,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,IAAI,GAAG;QACjD,IAAI,OAAO,KAAK,OAAO,CAAC,WAAW,CAAC,MAAM,MAAM,EAAE;QAClD,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,OAAO,EAC7D,OAAO,qJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE,KAAK,SAAS;IAClE;IACA,IAAI,eAAe,CAAC,QAAQ,KAAK,KAAK,CAAC,SAAS,EAAE,OAAO;IACzD,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,sBAAsB,MAAM;IACvD,uEAAuE;IACvE,IAAI,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,iBAAiB,KAChE,MAAM,YAAY,CAAC,SAAS;IAChC,MAAM,YAAY,CAAC,OAAO,CAAC,qBAAqB,SAAS,aAAa,IAAI,SAAS;IACnF,6DAA6D;IAC7D,MAAM,YAAY,CAAC,aAAa,GAAG;IACnC,IAAI,CAAC,oBACD,MAAM,YAAY,CAAC,OAAO,CAAC,cAAc;IAC7C,KAAK,QAAQ,GAAG,IAAI,SAAS,OAAO,UAAU,MAAM,QAAQ;AAChE;AACA,SAAS,OAAO,GAAG,CAAA;IACf,IAAI,WAAW,KAAK,QAAQ;IAC5B,OAAO,UAAU,CAAC;QACd,IAAI,KAAK,QAAQ,IAAI,UACjB,KAAK,QAAQ,GAAG;IACxB,GAAG;AACP;AACA,aAAa,QAAQ,GAAG,aAAa,SAAS,GAAG,CAAC,GAAG,IAAM,EAAE,cAAc;AAC3E,aAAa,IAAI,GAAG,CAAC,MAAM;IACvB,IAAI,QAAQ;IACZ,IAAI,WAAW,KAAK,QAAQ;IAC5B,KAAK,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,YAAY,EACnB;IACJ,IAAI,WAAW,KAAK,WAAW,CAAC,YAAY;IAC5C,IAAI,CAAC,UACD;IACJ,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG;IAChD,IAAI,QAAQ,YAAY,SAAS,KAAK;IACtC,IAAI,OAAO;QACP,KAAK,QAAQ,CAAC,mBAAmB,CAAA;YAAO,QAAQ,EAAE,OAAO;QAAO;IACpE,OACK;QACD,QAAQ,mBAAmB,MAAM,QAAQ,MAAM,YAAY,GAAG,qBAAqB,OAAO,MAAM,YAAY,CAAC,OAAO,CAAC,cAAc,OAAO;IAC9I;IACA,IAAI,OAAO,CAAC,CAAC,CAAC,YAAY,UAAU,MAAM,MAAM;IAChD,IAAI,KAAK,QAAQ,CAAC,cAAc,CAAA,IAAK,EAAE,MAAM,OAAO,SAAS,qJAAA,CAAA,QAAK,CAAC,KAAK,EAAE,QAAQ;QAC9E,MAAM,cAAc;QACpB;IACJ;IACA,IAAI,CAAC,OACD;IACJ,MAAM,cAAc;IACpB,IAAI,YAAY,QAAQ,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,OAAO,GAAG,EAAE,SAAS,OAAO,GAAG;IACjF,IAAI,aAAa,MACb,YAAY,OAAO,GAAG;IAC1B,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;IACtB,IAAI,MAAM;QACN,IAAI,EAAE,IAAI,EAAE,GAAG;QACf,IAAI,MACA,KAAK,OAAO,CAAC;aAEb,GAAG,eAAe;IAC1B;IACA,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IACzB,IAAI,SAAS,MAAM,SAAS,IAAI,KAAK,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO,CAAC,UAAU,IAAI;IACvF,IAAI,eAAe,GAAG,GAAG;IACzB,IAAI,QACA,GAAG,gBAAgB,CAAC,KAAK,KAAK,MAAM,OAAO,CAAC,UAAU;SAEtD,GAAG,YAAY,CAAC,KAAK,KAAK;IAC9B,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,eACV;IACJ,IAAI,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAC1B,IAAI,UAAU,qJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,MAAM,OAAO,CAAC,UAAU,KAC7D,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,UAAU,GAAG;QACvE,GAAG,YAAY,CAAC,IAAI,qJAAA,CAAA,gBAAa,CAAC;IACtC,OACK;QACD,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;QACzB,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,QAAU,MAAM;QAC3F,GAAG,YAAY,CAAC,iBAAiB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;IAChE;IACA,KAAK,KAAK;IACV,KAAK,QAAQ,CAAC,GAAG,OAAO,CAAC,WAAW;AACxC;AACA,SAAS,KAAK,GAAG,CAAA;IACb,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,GAAG;IAC/B,IAAI,CAAC,KAAK,OAAO,EAAE;QACf,KAAK,WAAW,CAAC,IAAI;QACrB,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC;QACvB,KAAK,WAAW,CAAC,KAAK;QACtB,KAAK,OAAO,GAAG;QACf,WAAW;YACP,IAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,MAAM,CAAC,KAAK,WAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC,KAAK,iBAAiB,KAC/F,eAAe;QACvB,GAAG;IACP;AACJ;AACA,SAAS,IAAI,GAAG,CAAC,MAAM;IACnB,IAAI,QAAQ;IACZ,IAAI,KAAK,OAAO,EAAE;QACd,KAAK,WAAW,CAAC,IAAI;QACrB,KAAK,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC;QAC1B,KAAK,WAAW,CAAC,KAAK;QACtB,IAAI,MAAM,aAAa,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,aAAa,GAC5D,KAAK,WAAW,CAAC,gBAAgB,CAAC,KAAK;QAC3C,KAAK,OAAO,GAAG;IACnB;AACJ;AACA,SAAS,WAAW,GAAG,CAAC,MAAM;IAC1B,IAAI,QAAQ;IACZ,kEAAkE;IAClE,mEAAmE;IACnE,iEAAiE;IACjE,gDAAgD;IAChD,IAAI,UAAU,WAAW,MAAM,SAAS,IAAI,yBAAyB;QACjE,KAAK,WAAW,CAAC,SAAS;QAC1B,IAAI,EAAE,cAAc,EAAE,GAAG,KAAK,KAAK;QACnC,WAAW;YACP,IAAI,KAAK,KAAK,CAAC,cAAc,IAAI,gBAC7B,QAAQ,gCAAgC;YAC5C,8DAA8D;YAC9D,KAAK,GAAG,CAAC,IAAI;YACb,KAAK,KAAK;YACV,IAAI,KAAK,QAAQ,CAAC,iBAAiB,CAAA,IAAK,EAAE,MAAM,SAAS,GAAG,gBACxD;YACJ,IAAI,EAAE,OAAO,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS;YACtC,uEAAuE;YACvE,IAAI,WAAW,QAAQ,GAAG,GAAG,GACzB,KAAK,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,EAAE,cAAc;QACvF,GAAG;IACP;AACJ;AACA,wCAAwC;AACxC,IAAK,IAAI,QAAQ,aACb,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK;AAEvC,SAAS,YAAY,CAAC,EAAE,CAAC;IACrB,IAAI,KAAK,GACL,OAAO;IACX,IAAK,IAAI,KAAK,EACV,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EACb,OAAO;IACf,IAAK,IAAI,KAAK,EACV,IAAI,CAAC,CAAC,KAAK,CAAC,GACR,OAAO;IACf,OAAO;AACX;AACA,MAAM;IACF,YAAY,KAAK,EAAE,IAAI,CAAE;QACrB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG,QAAQ;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IAClC;IACA,IAAI,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE;QAClC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,QAAQ,SAAS,CAAC,KAAK,IAAI,GAAG,WAAW,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QACrF,OAAO,UAAU,OAAO,IAAI,WAAW,MAAM,QAAQ,MAAM,QAAQ,IAAI;IAC3E;IACA,QAAQ;QAAE,OAAO;IAAM;IACvB,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,IAAI,SACV,iBAAiB,cACd,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,IAAI,CAAC,GAAG,IAC7C,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK,IAAI,YAAY,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;IAC/E;IACA,QAAQ,IAAI,EAAE;QACV,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;IAC1B;AACJ;AACA,MAAM;IACF,YAAY,KAAK,EAAE,IAAI,CAAE;QACrB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG,QAAQ;IACxB;IACA,IAAI,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE;QAClC,IAAI,OAAO,QAAQ,GAAG,CAAC,KAAK,IAAI,GAAG,WAAW,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,KAAK;QACnF,IAAI,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG,WAAW,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK;QAC7E,OAAO,QAAQ,KAAK,OAAO,IAAI,WAAW,MAAM,IAAI,IAAI;IAC5D;IACA,MAAM,CAAC,EAAE,IAAI,EAAE;QAAE,OAAO,KAAK,IAAI,GAAG,KAAK,EAAE;IAAE;IAC7C,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,IAAI,SACV,iBAAiB,cAAc,YAAY,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,KAC/D,YAAY,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI;IAC7C;IACA,OAAO,GAAG,IAAI,EAAE;QAAE,OAAO,KAAK,IAAI,YAAY;IAAY;IAC1D,UAAU,CAAE;AAChB;AACA,MAAM;IACF,YAAY,KAAK,EAAE,IAAI,CAAE;QACrB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG,QAAQ;IACxB;IACA,IAAI,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE;QAClC,IAAI,OAAO,QAAQ,SAAS,CAAC,KAAK,IAAI,GAAG,WAAW;QACpD,IAAI,KAAK,OAAO,EACZ,OAAO;QACX,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK,EAAE,GAAG,WAAW,CAAC;QACjD,IAAI,GAAG,OAAO,IAAI,GAAG,GAAG,IAAI,KAAK,GAAG,EAChC,OAAO;QACX,OAAO,IAAI,WAAW,KAAK,GAAG,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,IAAI;IAClE;IACA,MAAM,IAAI,EAAE,IAAI,EAAE;QACd,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,OAAO,CAAC,SAAS,CAAC,KAAK,IAAI,GAAG;QAC3D,OAAO,UAAU,KAAK,IAAI,IAAI,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,MAAM,EAAE,MAAM,IAAI,SAAS,MAAM,QAAQ,IAAI,KAAK,EAAE;IAC3G;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,IAAI,SACV,iBAAiB,YAAY,YAAY,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,KAC7D,YAAY,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI;IAC7C;IACA,UAAU,CAAE;AAChB;AACA;;;;AAIA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,IAAI,EACJ;;;IAGA,GACA,EAAE,EACF;;IAEA,GACA,IAAI,CAAE;QACF,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;IAEA,GACA,KAAK,IAAI,EAAE,EAAE,EAAE;QACX,OAAO,IAAI,WAAW,MAAM,IAAI,IAAI,CAAC,IAAI;IAC7C;IACA;;IAEA,GACA,GAAG,KAAK,EAAE,SAAS,CAAC,EAAE;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,UAAU,MAAM,IAAI,IAAI,IAAI,CAAC,EAAE,GAAG,UAAU,MAAM,EAAE;IACvG;IACA;;IAEA,GACA,IAAI,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,QAAQ;IAChD;IACA;;;;;;;IAOA,GACA,OAAO,OAAO,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;QAC5B,OAAO,IAAI,WAAW,KAAK,KAAK,IAAI,WAAW,OAAO;IAC1D;IACA;;;IAGA,GACA,OAAO,OAAO,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;QACjC,OAAO,IAAI,WAAW,MAAM,IAAI,IAAI,WAAW,OAAO;IAC1D;IACA;;;;IAIA,GACA,OAAO,KAAK,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;QAC/B,OAAO,IAAI,WAAW,MAAM,IAAI,IAAI,SAAS,OAAO;IACxD;IACA;;;IAGA,GACA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IAAE;IACpC;;IAEA,GACA,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,IAAI,YAAY;IAAY;IACvD;;IAEA,GACA,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,IAAI,YAAY;IAAY;AAC3D;AACA,MAAM,OAAO,EAAE,EAAE,SAAS,CAAC;AAC3B;;;;;AAKA,GACA,MAAM;IACF;;IAEA,GACA,YAAY,KAAK,EAAE,QAAQ,CAAE;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM,GAAG,QAAQ;QACpC,IAAI,CAAC,QAAQ,GAAG,SAAS,MAAM,GAAG,WAAW;IACjD;IACA;;;;IAIA,GACA,OAAO,OAAO,GAAG,EAAE,WAAW,EAAE;QAC5B,OAAO,YAAY,MAAM,GAAG,UAAU,aAAa,KAAK,GAAG,UAAU;IACzE;IACA;;;;;;;IAOA,GACA,KAAK,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE;QACxB,IAAI,SAAS,EAAE;QACf,IAAI,CAAC,SAAS,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,OAAO,MAAM,KAAK,QAAQ,GAAG;QAC9E,OAAO;IACX;IACA,UAAU,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;YACxB,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,EAAE,IAAI,SAAS,CAAC,CAAC,aAAa,UAAU,KAAK,IAAI,CAAC,GAC3E,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,QAAQ,KAAK,EAAE,GAAG;QAC5D;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;YAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,OAAO;gBACxD,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;gBAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,QAAQ,UAAU,MAAM,UAAU,QAAQ,SAAS,UAAU;YAChG;QACJ;IACJ;IACA;;;IAGA,GACA,IAAI,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE;QACvB,IAAI,IAAI,IAAI,SAAS,QAAQ,IAAI,CAAC,MAAM,IAAI,GACxC,OAAO,IAAI;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,GAAG,GAAG,WAAW;IACxD;IACA;;IAEA,GACA,SAAS,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE;QAChD,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,QAAQ;YAChD,IAAI,UAAU,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,SAClC,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC;iBAClC,IAAI,QAAQ,QAAQ,EACrB,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QAC3C;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,OAAO,YAAY,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAE,EAAE,SAAS,MAAM,QAAQ,WAAW;aAEpF,OAAO,WAAW,IAAI,cAAc,SAAS,IAAI,CAAC,QAAQ,QAAQ;IAC1E;IACA;;;;;IAKA,GACA,IAAI,GAAG,EAAE,WAAW,EAAE;QAClB,IAAI,CAAC,YAAY,MAAM,EACnB,OAAO,IAAI;QACf,IAAI,IAAI,IAAI,OACR,OAAO,cAAc,MAAM,CAAC,KAAK;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa;IAC3C;IACA,SAAS,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE;QAC/B,IAAI,UAAU,aAAa;QAC3B,IAAI,OAAO,CAAC,CAAC,WAAW;YACpB,IAAI,aAAa,cAAc,QAAQ;YACvC,IAAI,CAAC,CAAC,QAAQ,iBAAiB,aAAa,WAAW,WAAW,GAC9D;YACJ,IAAI,CAAC,UACD,WAAW,IAAI,CAAC,QAAQ,CAAC,KAAK;YAClC,MAAO,aAAa,SAAS,MAAM,IAAI,QAAQ,CAAC,WAAW,GAAG,YAC1D,cAAc;YAClB,IAAI,QAAQ,CAAC,WAAW,IAAI,aACxB,QAAQ,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,WAAW,OAAO,aAAa;iBAE5F,SAAS,MAAM,CAAC,YAAY,GAAG,aAAa,cAAc,UAAU,QAAQ,EAAE,UAAU,OAAO,WAAW,aAAa,GAAG;YAC9H,cAAc;QAClB;QACA,IAAI,QAAQ,UAAU,aAAa,aAAa,eAAe,aAAa,CAAC;QAC7E,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAC9B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,GAClC,MAAM,MAAM,CAAC,KAAK;QAC1B,OAAO,IAAI,cAAc,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC,QAAQ;IACxH;IACA;;;IAGA,GACA,OAAO,WAAW,EAAE;QAChB,IAAI,YAAY,MAAM,IAAI,KAAK,IAAI,IAAI,OACnC,OAAO,IAAI;QACf,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;IACzC;IACA,YAAY,WAAW,EAAE,MAAM,EAAE;QAC7B,IAAI,WAAW,IAAI,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC,KAAK;QAChD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;YACzC,IAAI;YACJ,IAAI,OAAO,QAAQ,CAAC,EAAE,GAAG,QAAQ,KAAK,QAAQ,CAAC,IAAI,EAAE,GAAG;YACxD,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,YAAY,MAAM,EAAE,IAC1C,IAAI,OAAO,WAAW,CAAC,EAAE,EAAE;gBACvB,IAAI,KAAK,IAAI,GAAG,QAAQ,KAAK,EAAE,GAAG,IAAI;oBAClC,WAAW,CAAC,EAAE,GAAG;oBACjB,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;gBACjC;YACJ;YACJ,IAAI,CAAC,OACD;YACJ,IAAI,YAAY,IAAI,CAAC,QAAQ,EACzB,WAAW,IAAI,CAAC,QAAQ,CAAC,KAAK;YAClC,IAAI,UAAU,QAAQ,CAAC,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,OAAO;YACxD,IAAI,WAAW,OAAO;gBAClB,QAAQ,CAAC,IAAI,EAAE,GAAG;YACtB,OACK;gBACD,SAAS,MAAM,CAAC,GAAG;gBACnB,KAAK;YACT;QACJ;QACA,IAAI,MAAM,MAAM,EACZ;YAAA,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,YAAY,MAAM,EAAE,IAC1C,IAAI,OAAO,WAAW,CAAC,EAAE,EAAE;gBACvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAC9B,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,SAAS;oBAC3B,IAAI,SAAS,IAAI,CAAC,KAAK,EACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK;oBAC5B,MAAM,MAAM,CAAC,KAAK;gBACtB;YACR;QAAA;QACR,IAAI,YAAY,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,KAAK,EAChD,OAAO,IAAI;QACf,OAAO,MAAM,MAAM,IAAI,SAAS,MAAM,GAAG,IAAI,cAAc,OAAO,YAAY;IAClF;IACA,SAAS,MAAM,EAAE,IAAI,EAAE;QACnB,IAAI,IAAI,IAAI,OACR,OAAO,IAAI;QACf,IAAI,KAAK,MAAM,EACX,OAAO,cAAc,KAAK;QAC9B,IAAI,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ;YAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,QACpB,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YAChC;QACJ;QACJ,IAAI,QAAQ,SAAS,GAAG,MAAM,QAAQ,KAAK,OAAO,CAAC,IAAI;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,IAAI,IAAI,GAAG,OAAO,IAAI,EAAE,GAAG,SAAU,IAAI,IAAI,YAAY,YAAa;gBACtE,IAAI,OAAO,KAAK,GAAG,CAAC,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,IAAI,EAAE,IAAI;gBAC3E,IAAI,OAAO,IACP,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM;YACpD;QACJ;QACA,IAAI,OAAO;YACP,IAAI,WAAW,IAAI,cAAc,MAAM,IAAI,CAAC,QAAQ;YACpD,OAAO,QAAQ,IAAI,gBAAgB;gBAAC;gBAAU;aAAM,IAAI;QAC5D;QACA,OAAO,SAAS;IACpB;IACA;;IAEA,GACA,GAAG,KAAK,EAAE;QACN,IAAI,IAAI,IAAI,OACR,OAAO;QACX,IAAI,CAAC,CAAC,iBAAiB,aAAa,KAChC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,KAAK,CAAC,MAAM,IACvC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,MAAM,QAAQ,CAAC,MAAM,EAC7C,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IACnC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,GAChC,OAAO;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,MAAM,QAAQ,CAAC,EAAE,IACrC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,IAC7C,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,GAC9C,OAAO;QACf,OAAO;IACX;IACA;;IAEA,GACA,OAAO,IAAI,EAAE;QACT,OAAO,cAAc,IAAI,CAAC,WAAW,CAAC;IAC1C;IACA;;IAEA,GACA,YAAY,IAAI,EAAE;QACd,IAAI,IAAI,IAAI,OACR,OAAO;QACX,IAAI,KAAK,aAAa,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,GACpD,OAAO,IAAI,CAAC,KAAK;QACrB,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,YAAY,UAAU,GAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACjC;QACA,OAAO;IACX;IACA,WAAW,CAAC,EAAE;QAAE,EAAE,IAAI;IAAG;AAC7B;AACA;;AAEA,GACA,cAAc,KAAK,GAAG,IAAI,cAAc,EAAE,EAAE,EAAE;AAC9C;;AAEA,GACA,cAAc,aAAa,GAAG;AAC9B,MAAM,QAAQ,cAAc,KAAK;AACjC,kEAAkE;AAClE,qEAAqE;AACrE,yCAAyC;AACzC,MAAM;IACF,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,IAAI,OAAO,EAAE,GAAG,EAAE;QACd,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,OAAO,GAAG,CAAC,SAAS,KAAK;QACxE,OAAO,gBAAgB,IAAI,CAAC;IAChC;IACA,SAAS,MAAM,EAAE,KAAK,EAAE;QACpB,IAAI,MAAM,MAAM,EACZ,OAAO,cAAc,KAAK;QAC9B,IAAI,QAAQ,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ;YAC9C,IAAI,UAAU,OACV;YACJ,IAAI,kBAAkB,iBAClB,QAAQ,MAAM,MAAM,CAAC,OAAO,OAAO;iBAEnC,MAAM,IAAI,CAAC;QACnB;QACA,OAAO,gBAAgB,IAAI,CAAC;IAChC;IACA,GAAG,KAAK,EAAE;QACN,IAAI,CAAC,CAAC,iBAAiB,eAAe,KAClC,MAAM,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAC3C,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IACrC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,OAAO,CAAC,EAAE,GACpC,OAAO;QACf,OAAO;IACX;IACA,OAAO,IAAI,EAAE;QACT,IAAI,QAAQ,SAAS;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;YACzC,IAAI,CAAC,OAAO,MAAM,EACd;YACJ,IAAI,CAAC,QAAQ;gBACT,SAAS;YACb,OACK;gBACD,IAAI,QAAQ;oBACR,SAAS,OAAO,KAAK;oBACrB,SAAS;gBACb;gBACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7B;QACJ;QACA,OAAO,SAAS,cAAc,SAAS,SAAS,OAAO,IAAI,CAAC,UAAU;IAC1E;IACA,mEAAmE;IACnE,8BAA8B;IAC9B,OAAO,KAAK,OAAO,EAAE;QACjB,OAAQ,QAAQ,MAAM;YAClB,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO,OAAO,CAAC,EAAE;YACzB;gBAAS,OAAO,IAAI,gBAAgB,QAAQ,KAAK,CAAC,CAAA,IAAK,aAAa,iBAAiB,UACjF,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,CAAC,aAAa,gBAAgB,IAAI,EAAE,OAAO,GAAG,EAAE;QACzF;IACJ;IACA,WAAW,CAAC,EAAE;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IACrC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC;IACnC;AACJ;AACA,SAAS,YAAY,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IACjF,IAAI,WAAW,YAAY,KAAK;IAChC,8DAA8D;IAC9D,yCAAyC;IACzC,IAAK,IAAI,IAAI,GAAG,aAAa,WAAW,IAAI,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAK;QAClE,IAAI,QAAQ;QACZ,QAAQ,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,UAAU,QAAQ,UAAU;YACjD,IAAI,QAAQ,AAAC,SAAS,WAAY,CAAC,SAAS,QAAQ;YACpD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;gBACzC,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;gBACzB,IAAI,MAAM,KAAK,WAAW,MAAM,aAAa,OACzC;gBACJ,IAAI,QAAQ,QAAQ,CAAC,EAAE,GAAG,aAAa;gBACvC,IAAI,UAAU,OAAO;oBACjB,QAAQ,CAAC,IAAI,EAAE,GAAG,YAAY,QAAQ,CAAC,IAAI,CAAC;gBAChD,OACK,IAAI,YAAY,cAAc,OAAO;oBACtC,QAAQ,CAAC,EAAE,IAAI;oBACf,QAAQ,CAAC,IAAI,EAAE,IAAI;gBACvB;YACJ;YACA,SAAS;QACb;QACA,aAAa,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC;IAClD;IACA,+DAA+D;IAC/D,gEAAgE;IAChE,IAAI,cAAc;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EACtC,IAAI,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG;QACrB,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;YACvB,cAAc;YACd,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC;YACnB;QACJ;QACA,IAAI,OAAO,QAAQ,GAAG,CAAC,WAAW,CAAC,EAAE,GAAG,YAAY,YAAY,OAAO;QACvE,IAAI,YAAY,KAAK,aAAa,KAAK,OAAO,CAAC,IAAI,EAAE;YACjD,cAAc;YACd;QACJ;QACA,4DAA4D;QAC5D,IAAI,KAAK,QAAQ,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,IAAI,UAAU,KAAK;QACzE,IAAI,EAAE,KAAK,EAAE,QAAQ,WAAW,EAAE,GAAG,KAAK,OAAO,CAAC,SAAS,CAAC;QAC5D,IAAI,YAAY,KAAK,UAAU,CAAC;QAChC,IAAI,aAAa,eAAe,aAAa,cAAc,UAAU,QAAQ,IAAI,SAAS;YACtF,IAAI,SAAS,QAAQ,CAAC,IAAI,EAAE,CACvB,QAAQ,CAAC,SAAS,WAAW,OAAO,GAAG,WAAW,CAAC,EAAE,GAAG,YAAY,GAAG;YAC5E,IAAI,UAAU,OAAO;gBACjB,QAAQ,CAAC,EAAE,GAAG;gBACd,QAAQ,CAAC,IAAI,EAAE,GAAG;gBAClB,QAAQ,CAAC,IAAI,EAAE,GAAG;YACtB,OACK;gBACD,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC;gBACnB,cAAc;YAClB;QACJ,OACK;YACD,cAAc;QAClB;IACJ;IACJ,kFAAkF;IAClF,IAAI,aAAa;QACb,IAAI,cAAc,iCAAiC,UAAU,aAAa,UAAU,SAAS,QAAQ,WAAW;QAChH,IAAI,QAAQ,UAAU,aAAa,MAAM,GAAG;QAC5C,WAAW,MAAM,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EACtC,IAAI,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG;YACrB,SAAS,MAAM,CAAC,GAAG;YACnB,KAAK;QACT;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;YACtD,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE;YAC5B,MAAO,IAAI,SAAS,MAAM,IAAI,QAAQ,CAAC,EAAE,GAAG,KACxC,KAAK;YACT,SAAS,MAAM,CAAC,GAAG,GAAG,MAAM,QAAQ,CAAC,EAAE,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE;QACzF;IACJ;IACA,OAAO,IAAI,cAAc,SAAS,IAAI,CAAC,QAAQ;AACnD;AACA,SAAS,UAAU,KAAK,EAAE,MAAM;IAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,EACxB,OAAO;IACX,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,IAAI,OAAO,KAAK,CAAC,EAAE;QACnB,OAAO,IAAI,CAAC,IAAI,WAAW,KAAK,IAAI,GAAG,QAAQ,KAAK,EAAE,GAAG,QAAQ,KAAK,IAAI;IAC9E;IACA,OAAO;AACX;AACA,SAAS,iCAAiC,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IAC7G,4DAA4D;IAC5D,SAAS,OAAO,GAAG,EAAE,SAAS;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,IAAK;YACvC,IAAI,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,QAAQ;YAC/C,IAAI,QACA,YAAY,IAAI,CAAC;iBAChB,IAAI,QAAQ,QAAQ,EACrB,QAAQ,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI;QAC1C;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE,KAAK,EAC1C,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,QAAQ,CAAC,EAAE,GAAG,YAAY;IAClE;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EACtC,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GACpB,OAAO,QAAQ,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,EAAE,GAAG,YAAY;IAC7D,OAAO;AACX;AACA,SAAS,iBAAiB,KAAK,EAAE,IAAI,EAAE,MAAM;IACzC,IAAI,KAAK,MAAM,EACX,OAAO;IACX,IAAI,MAAM,SAAS,KAAK,QAAQ,EAAE,QAAQ;IAC1C,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,MAAM,EAAE,IAAK;QACzC,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,GAAG,UAAU,KAAK,EAAE,GAAG,KAAK;YAC1D,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;YAC7B,KAAK,CAAC,EAAE,GAAG;QACf;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,KAAK;IACvB,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAC9B,IAAI,KAAK,CAAC,EAAE,IAAI,MACZ,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAO;AACX;AACA,qEAAqE;AACrE,sEAAsE;AACtE,sEAAsE;AACtE,wBAAwB;AACxB,SAAS,UAAU,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO;IAC3C,IAAI,WAAW,EAAE,EAAE,WAAW;IAC9B,KAAK,OAAO,CAAC,CAAC,WAAW;QACrB,IAAI,QAAQ,iBAAiB,OAAO,WAAW,aAAa;QAC5D,IAAI,OAAO;YACP,WAAW;YACX,IAAI,UAAU,UAAU,OAAO,WAAW,SAAS,aAAa,GAAG;YACnE,IAAI,WAAW,OACX,SAAS,IAAI,CAAC,YAAY,aAAa,UAAU,QAAQ,EAAE;QACnE;IACJ;IACA,IAAI,SAAS,UAAU,WAAW,aAAa,SAAS,OAAO,CAAC,QAAQ,IAAI,CAAC;IAC7E,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAC/B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,EAAE,GAAG;QACxC,IAAI,QAAQ,QAAQ,EAChB,QAAQ,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;QACnC,OAAO,MAAM,CAAC,KAAK;IACvB;IACJ,OAAO,OAAO,MAAM,IAAI,SAAS,MAAM,GAAG,IAAI,cAAc,QAAQ,YAAY;AACpF;AACA,kEAAkE;AAClE,mEAAmE;AACnE,2CAA2C;AAC3C,SAAS,MAAM,CAAC,EAAE,CAAC;IACf,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE;AACzC;AACA,sEAAsE;AACtE,oEAAoE;AACpE,oEAAoE;AACpE,8DAA8D;AAC9D,SAAS,cAAc,KAAK;IACxB,IAAI,UAAU;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAK;QACzC,IAAI,OAAO,OAAO,CAAC,EAAE;QACrB,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,EACpB,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACzC,IAAI,OAAO,OAAO,CAAC,EAAE;YACrB,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE;gBACxB,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,EAAE;oBACpB,IAAI,WAAW,OACX,UAAU,MAAM,KAAK;oBACzB,8DAA8D;oBAC9D,QAAQ;oBACR,OAAO,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE;oBACzC,YAAY,SAAS,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE;gBAC1D;gBACA;YACJ,OACK;gBACD,IAAI,KAAK,IAAI,GAAG,KAAK,EAAE,EAAE;oBACrB,IAAI,WAAW,OACX,UAAU,MAAM,KAAK;oBACzB,6DAA6D;oBAC7D,YAAY;oBACZ,OAAO,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI;oBAC3C,YAAY,SAAS,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE;gBACxD;gBACA;YACJ;QACJ;IACR;IACA,OAAO;AACX;AACA,SAAS,YAAY,KAAK,EAAE,CAAC,EAAE,IAAI;IAC/B,MAAO,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,CAAC,EAAE,IAAI,EAC/C;IACJ,MAAM,MAAM,CAAC,GAAG,GAAG;AACvB;AACA,mEAAmE;AACnE,SAAS,gBAAgB,IAAI;IACzB,IAAI,QAAQ,EAAE;IACd,KAAK,QAAQ,CAAC,eAAe,CAAA;QACzB,IAAI,SAAS,EAAE,KAAK,KAAK;QACzB,IAAI,UAAU,UAAU,OACpB,MAAM,IAAI,CAAC;IACnB;IACA,IAAI,KAAK,aAAa,EAClB,MAAM,IAAI,CAAC,cAAc,MAAM,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE;QAAC,KAAK,aAAa,CAAC,IAAI;KAAC;IAC7E,OAAO,gBAAgB,IAAI,CAAC;AAChC;AAEA,MAAM,iBAAiB;IACnB,WAAW;IACX,eAAe;IACf,uBAAuB;IACvB,YAAY;IACZ,mBAAmB;IACnB,SAAS;AACb;AACA,yFAAyF;AACzF,MAAM,cAAc,MAAM,cAAc;AACxC,MAAM;IACF,aAAc;QACV,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,IAAI,GAAG,EAAE;QACL,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY;QACpC,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW;IACtC;IACA,QAAQ;QACJ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG;IACvC;IACA,GAAG,GAAG,EAAE;QACJ,OAAO,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC,YAAY,IAC7E,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,WAAW,IAAI,IAAI,CAAC,WAAW;IAC9E;AACJ;AACA,MAAM;IACF,YAAY,IAAI,EAAE,eAAe,CAAE;QAC/B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,2BAA2B,GAAG;QACnC,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,gBAAgB,IACnC,IAAI,OAAO,gBAAgB,CAAC,CAAA;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAClC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YAChC,6DAA6D;YAC7D,wDAAwD;YACxD,qDAAqD;YACrD,4CAA4C;YAC5C,IAAI,MAAM,cAAc,MAAM,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,eAAe,EAAE,YAAY,CAAC,MAAM,IAC5F,EAAE,IAAI,IAAI,mBAAmB,EAAE,QAAQ,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,GAC1E,IAAI,CAAC,SAAS;iBAEd,IAAI,CAAC,KAAK;QAClB;QACJ,IAAI,aAAa;YACb,IAAI,CAAC,UAAU,GAAG,CAAA;gBACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBAAE,QAAQ,EAAE,MAAM;oBAAE,MAAM;oBAAiB,UAAU,EAAE,SAAS;gBAAC;gBACjF,IAAI,CAAC,SAAS;YAClB;QACJ;QACA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI;IAC7D;IACA,YAAY;QACR,IAAI,IAAI,CAAC,YAAY,GAAG,GACpB,IAAI,CAAC,YAAY,GAAG,OAAO,UAAU,CAAC;YAAQ,IAAI,CAAC,YAAY,GAAG,CAAC;YAAG,IAAI,CAAC,KAAK;QAAI,GAAG;IAC/F;IACA,aAAa;QACT,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG;YACxB,OAAO,YAAY,CAAC,IAAI,CAAC,YAAY;YACrC,IAAI,CAAC,YAAY,GAAG,CAAC;YACrB,IAAI,CAAC,KAAK;QACd;IACJ;IACA,QAAQ;QACJ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,WAAW;YACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACzC;QACA,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,4BAA4B,IAAI,CAAC,UAAU;QAC9E,IAAI,CAAC,gBAAgB;IACzB;IACA,OAAO;QACH,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW;YACpC,IAAI,KAAK,MAAM,EAAE;gBACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC3B,OAAO,UAAU,CAAC,IAAM,IAAI,CAAC,KAAK,IAAI;YAC1C;YACA,IAAI,CAAC,QAAQ,CAAC,UAAU;QAC5B;QACA,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,4BAA4B,IAAI,CAAC,UAAU;QACjF,IAAI,CAAC,mBAAmB;IAC5B;IACA,mBAAmB;QACf,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,gBAAgB,CAAC,mBAAmB,IAAI,CAAC,iBAAiB;IAC1F;IACA,sBAAsB;QAClB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,mBAAmB,CAAC,mBAAmB,IAAI,CAAC,iBAAiB;IAC7F;IACA,2BAA2B;QACvB,IAAI,CAAC,2BAA2B,GAAG;QACnC,WAAW,IAAM,IAAI,CAAC,2BAA2B,GAAG,OAAO;IAC/D;IACA,oBAAoB;QAChB,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,GAC/B;QACJ,IAAI,IAAI,CAAC,2BAA2B,EAChC,OAAO,eAAe,IAAI,CAAC,IAAI;QACnC,iEAAiE;QACjE,yDAAyD;QACzD,YAAY;QACZ,IAAI,MAAM,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE;YAC5D,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB;YACrC,6CAA6C;YAC7C,IAAI,IAAI,SAAS,IAAI,qBAAqB,IAAI,SAAS,EAAE,IAAI,WAAW,EAAE,IAAI,UAAU,EAAE,IAAI,YAAY,GACtG,OAAO,IAAI,CAAC,SAAS;QAC7B;QACA,IAAI,CAAC,KAAK;IACd;IACA,kBAAkB;QACd,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB;IACzD;IACA,sBAAsB,GAAG,EAAE;QACvB,IAAI,CAAC,IAAI,SAAS,EACd,OAAO;QACX,IAAI,YAAY,IAAI,KAAK;QACzB,IAAK,IAAI,OAAO,IAAI,SAAS,EAAE,MAAM,OAAO,WAAW,MACnD,UAAU,GAAG,CAAC;QAClB,IAAK,IAAI,OAAO,IAAI,UAAU,EAAE,MAAM,OAAO,WAAW,MACpD,IAAI,UAAU,GAAG,CAAC,OAAO;YACrB,YAAY;YACZ;QACJ;QACJ,IAAI,OAAO,aAAa,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QACtD,IAAI,QAAQ,KAAK,cAAc,CAAC;YAC5B,MAAM;YACN,QAAQ,UAAU,QAAQ,IAAI,IAAI,UAAU,UAAU,GAAG;QAC7D,IAAI;YACA,IAAI,CAAC,eAAe;YACpB,OAAO;QACX;IACJ;IACA,iBAAiB;QACb,IAAI,IAAI,CAAC,QAAQ,EACb,KAAK,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,GACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACxB,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,QAAQ;QACJ,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI;QACnB,IAAI,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,GACtC;QACJ,IAAI,YAAY,IAAI,CAAC,cAAc;QACnC,IAAI,UAAU,MAAM,EAChB,IAAI,CAAC,KAAK,GAAG,EAAE;QACnB,IAAI,MAAM,KAAK,iBAAiB;QAChC,IAAI,SAAS,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,QAAQ,qBAAqB,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC;QAC9I,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,OAAO,QAAQ,EAAE;QACpD,IAAI,KAAK,QAAQ,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,EAAE;gBACjD,IAAI,QAAQ;oBACR,OAAO,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE;oBACtD,KAAK,KAAK,IAAI,OAAO,EAAE,GAAG,KAAK,GAAG,CAAC,OAAO,EAAE,EAAE;oBAC9C,IAAI,OAAO,QAAQ,EACf,WAAW;gBACnB;YACJ;QACJ;QACA,IAAI,SAAS,MAAM,MAAM,EAAE;YACvB,IAAI,MAAM,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI;YAC1C,IAAI,IAAI,MAAM,IAAI,GAAG;gBACjB,IAAI,CAAC,GAAG,EAAE,GAAG;gBACb,IAAI,EAAE,UAAU,IAAI,EAAE,UAAU,CAAC,UAAU,IAAI,EAAE,UAAU,EACvD,EAAE,MAAM;qBAER,EAAE,MAAM;YAChB,OACK;gBACD,IAAI,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,gBAAgB;gBACzC,KAAK,IAAI,MAAM,IAAK;oBAChB,IAAI,SAAS,GAAG,UAAU;oBAC1B,IAAI,UAAU,OAAO,QAAQ,IAAI,QAAQ,CAAC,CAAC,aAAa,YAAY,MAAM,cAAc,MAAM,GAC1F,GAAG,MAAM;gBACjB;YACJ;QACJ;QACA,IAAI,UAAU;QACd,8DAA8D;QAC9D,gEAAgE;QAChE,YAAY;QACZ,IAAI,OAAO,KAAK,UAAU,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,GAAG,KAAK,OAC1D,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,GAAG,KAAK,OACzE,mBAAmB,QAAQ,CAAC,UAAU,iBAAiB,KAAK,KAC5D,QAAQ,EAAE,CAAC,qJAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK;YAC1D,KAAK,KAAK,CAAC,SAAS,GAAG;YACvB,eAAe;YACf,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;YAC1B,KAAK,iBAAiB;QAC1B,OACK,IAAI,OAAO,CAAC,KAAK,QAAQ;YAC1B,IAAI,OAAO,CAAC,GAAG;gBACX,KAAK,OAAO,CAAC,SAAS,CAAC,MAAM;gBAC7B,SAAS;YACb;YACA,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,UAAU;YACzC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,KAAK,EAClC,KAAK,WAAW,CAAC,KAAK,KAAK;iBAC1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,MAC/B,eAAe;YACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QAC9B;IACJ;IACA,iBAAiB,GAAG,EAAE,KAAK,EAAE;QACzB,oEAAoE;QACpE,IAAI,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI,CAAC,GAC7B,OAAO;QACX,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,MAAM;QACnD,IAAI,IAAI,IAAI,IAAI,gBACZ,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,aAAa,IAAI,qBAE9C,IAAI,aAAa,IAAI,WAAW,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,QAAS,GACxF,OAAO;QACX,IAAI,CAAC,QAAQ,KAAK,cAAc,CAAC,MAC7B,OAAO;QACX,IAAI,IAAI,IAAI,IAAI,aAAa;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC5C,IAAI,OAAO,IAAI,UAAU,CAAC,EAAE;gBAC5B,MAAM,IAAI,CAAC;gBACX,IAAI,KAAK,QAAQ,IAAI,GACjB,IAAI,CAAC,mBAAmB,GAAG;YACnC;YACA,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,UAAU,CAAC,QAAQ,CAAC,IAAI,MAAM,GACtF,OAAO;gBAAE,MAAM,KAAK,SAAS;gBAAE,IAAI,KAAK,QAAQ;YAAC;YACrD,IAAI,OAAO,IAAI,eAAe,EAAE,OAAO,IAAI,WAAW;YACtD,IAAI,MAAM,cAAc,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE;gBACjD,sDAAsD;gBACtD,2DAA2D;gBAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE,IAAK;oBAC5C,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE;oBACxD,IAAI,CAAC,mBAAmB,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,UAAU,EAAE,mBAAmB,GACpF,OAAO;oBACX,IAAI,CAAC,eAAe,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,UAAU,EAAE,eAAe,GAC5E,OAAO;gBACf;YACJ;YACA,IAAI,aAAa,QAAQ,KAAK,UAAU,IAAI,IAAI,MAAM,GAChD,SAAS,QAAQ,IAAI;YAC3B,IAAI,OAAO,KAAK,eAAe,CAAC,IAAI,MAAM,EAAE,YAAY,CAAC;YACzD,IAAI,WAAW,QAAQ,KAAK,UAAU,IAAI,IAAI,MAAM,GAC9C,SAAS,QAAQ,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM;YACnD,IAAI,KAAK,KAAK,eAAe,CAAC,IAAI,MAAM,EAAE,UAAU;YACpD,OAAO;gBAAE;gBAAM;YAAG;QACtB,OACK,IAAI,IAAI,IAAI,IAAI,cAAc;YAC/B,OAAO;gBAAE,MAAM,KAAK,UAAU,GAAG,KAAK,MAAM;gBAAE,IAAI,KAAK,QAAQ,GAAG,KAAK,MAAM;YAAC;QAClF,OACK;YACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,MAAM;YACrC,OAAO;gBACH,MAAM,KAAK,UAAU;gBACrB,IAAI,KAAK,QAAQ;gBACjB,8DAA8D;gBAC9D,6DAA6D;gBAC7D,+DAA+D;gBAC/D,uBAAuB;gBACvB,UAAU,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI,QAAQ;YAClD;QACJ;IACJ;AACJ;AACA,IAAI,aAAa,IAAI;AACrB,IAAI,iBAAiB;AACrB,SAAS,SAAS,IAAI;IAClB,IAAI,WAAW,GAAG,CAAC,OACf;IACJ,WAAW,GAAG,CAAC,MAAM;IACrB,IAAI;QAAC;QAAU;QAAU;KAAW,CAAC,OAAO,CAAC,iBAAiB,KAAK,GAAG,EAAE,UAAU,MAAM,CAAC,GAAG;QACxF,KAAK,qBAAqB,GAAG;QAC7B,IAAI,gBACA;QACJ,OAAO,CAAC,OAAO,CAAC;QAChB,iBAAiB;IACrB;AACJ;AACA,SAAS,sBAAsB,IAAI,EAAE,KAAK;IACtC,IAAI,aAAa,MAAM,cAAc,EAAE,eAAe,MAAM,WAAW;IACvE,IAAI,YAAY,MAAM,YAAY,EAAE,cAAc,MAAM,SAAS;IACjE,IAAI,gBAAgB,KAAK,QAAQ,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,MAAM;IAC7D,kEAAkE;IAClE,8DAA8D;IAC9D,kBAAkB;IAClB,IAAI,qBAAqB,cAAc,IAAI,EAAE,cAAc,MAAM,EAAE,WAAW,cAC1E,CAAC,YAAY,cAAc,WAAW,YAAY,GAAG;QAAC;QAAW;QAAa;QAAY;KAAa;IAC3G,OAAO;QAAE;QAAY;QAAc;QAAW;IAAY;AAC9D;AACA,wDAAwD;AACxD,4DAA4D;AAC5D,SAAS,2BAA2B,IAAI,EAAE,SAAS;IAC/C,IAAI,UAAU,iBAAiB,EAAE;QAC7B,IAAI,QAAQ,UAAU,iBAAiB,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;QACrD,IAAI,OACA,OAAO,sBAAsB,MAAM;IAC3C;IACA,IAAI;IACJ,SAAS,KAAK,KAAK;QACf,MAAM,cAAc;QACpB,MAAM,wBAAwB;QAC9B,QAAQ,MAAM,eAAe,EAAE,CAAC,EAAE;IACtC;IACA,iEAAiE;IACjE,oEAAoE;IACpE,gEAAgE;IAChE,oEAAoE;IACpE,SAAS;IACT,KAAK,GAAG,CAAC,gBAAgB,CAAC,eAAe,MAAM;IAC/C,SAAS,WAAW,CAAC;IACrB,KAAK,GAAG,CAAC,mBAAmB,CAAC,eAAe,MAAM;IAClD,OAAO,QAAQ,sBAAsB,MAAM,SAAS;AACxD;AACA,SAAS,YAAY,IAAI,EAAE,IAAI;IAC3B,IAAK,IAAI,IAAI,KAAK,UAAU,EAAE,KAAK,KAAK,KAAK,GAAG,EAAE,IAAI,EAAE,UAAU,CAAE;QAChE,IAAI,OAAO,KAAK,OAAO,CAAC,WAAW,CAAC,GAAG;QACvC,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,EACzB,OAAO;IACf;IACA,OAAO;AACX;AAEA,yDAAyD;AACzD,kEAAkE;AAClE,mEAAmE;AACnE,iEAAiE;AACjE,iBAAiB;AACjB,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,GAAG;IAClC,IAAI,EAAE,MAAM,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO;IACtF,IAAI,SAAS,KAAK,iBAAiB;IACnC,IAAI;IACJ,IAAI,SAAS,OAAO,UAAU;IAC9B,IAAI,UAAU,KAAK,GAAG,CAAC,QAAQ,CAAC,OAAO,QAAQ,IAAI,IAAI,SAAS,OAAO,UAAU,GAAG;QAChF,OAAO;YAAC;gBAAE,MAAM;gBAAQ,QAAQ,OAAO,YAAY;YAAC;SAAE;QACtD,IAAI,CAAC,mBAAmB,SACpB,KAAK,IAAI,CAAC;YAAE,MAAM,OAAO,SAAS;YAAE,QAAQ,OAAO,WAAW;QAAC;IACvE;IACA,mEAAmE;IACnE,gEAAgE;IAChE,IAAI,UAAU,KAAK,KAAK,CAAC,WAAW,KAAK,GAAG;QACxC,IAAK,IAAI,MAAM,UAAU,MAAM,YAAY,MAAO;YAC9C,IAAI,OAAO,OAAO,UAAU,CAAC,MAAM,EAAE,EAAE,OAAO,KAAK,UAAU;YAC7D,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,MAAM;gBAChC,WAAW;gBACX;YACJ;YACA,IAAI,CAAC,QAAQ,KAAK,IAAI,EAClB;QACR;IACJ;IACA,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG;IAC7B,IAAI,SAAS,KAAK,QAAQ,CAAC,gBAAgB,qJAAA,CAAA,YAAS,CAAC,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;IACjF,IAAI,QAAQ,SAAS,OAAO,CAAC;IAC7B,IAAI,MAAM,MAAM,MAAM,OAAO,KAAK,CAAC,QAAQ;QACvC,SAAS,MAAM,MAAM;QACrB,UAAU,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,KAAK;QACjD,SAAS;QACT,MAAM;QACN,IAAI;QACJ,oBAAoB,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,QAAQ,SAAS;QACrE,eAAe;QACf;QACA,SAAS;IACb;IACA,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,MAAM;QAC7B,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG;QACvD,IAAI,QAAQ,MACR,OAAO;QACX,MAAM;YAAE,QAAQ,SAAS;YAAM,MAAM,OAAO;QAAK;IACrD;IACA,OAAO;QAAE;QAAK;QAAK;QAAM;IAAG;AAChC;AACA,SAAS,aAAa,GAAG;IACrB,IAAI,OAAO,IAAI,UAAU;IACzB,IAAI,MAAM;QACN,OAAO,KAAK,SAAS;IACzB,OACK,IAAI,IAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,EAAE;QAC7C,wDAAwD;QACxD,wDAAwD;QACxD,sDAAsD;QACtD,IAAI,UAAU,aAAa,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,GAAG;YACtD,IAAI,OAAO,SAAS,aAAa,CAAC;YAClC,KAAK,WAAW,CAAC,SAAS,aAAa,CAAC;YACxC,OAAO;gBAAE;YAAK;QAClB,OACK,IAAI,IAAI,UAAU,CAAC,SAAS,IAAI,OAAO,UAAU,gBAAgB,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,GAAG;YACjG,OAAO;gBAAE,QAAQ;YAAK;QAC1B;IACJ,OACK,IAAI,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,CAAC,qBAAqB;QACpE,OAAO;YAAE,QAAQ;QAAK;IAC1B;IACA,OAAO;AACX;AACA,MAAM,WAAW;AACjB,SAAS,cAAc,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,UAAU;IACvD,IAAI,gBAAgB,KAAK,KAAK,CAAC,yBAAyB,IAAI,CAAC,KAAK,SAAS,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,CAAC;IAC1G,KAAK,KAAK,CAAC,yBAAyB,GAAG;IACvC,IAAI,OAAO,GAAG;QACV,IAAI,SAAS,KAAK,KAAK,CAAC,iBAAiB,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,CAAC,mBAAmB,GAAG;QAC/F,IAAI,SAAS,iBAAiB,MAAM;QACpC,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS;YAC5C,IAAI,UAAU,WACV,KAAK,KAAK,CAAC,WAAW,KAAK,MAAM,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,CAAC,eAAe,IAC9E,KAAK,QAAQ,CAAC,iBAAiB,CAAA,IAAK,EAAE,MAAM,SAAS,IAAI,YACzD;YACJ,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC;YACpC,IAAI,UAAU,WACV,GAAG,OAAO,CAAC,WAAW;iBACrB,IAAI,UAAU,OACf,GAAG,cAAc;YACrB,IAAI,eACA,GAAG,OAAO,CAAC,eAAe;YAC9B,KAAK,QAAQ,CAAC;QAClB;QACA;IACJ;IACA,IAAI,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;IACrC,IAAI,SAAS,QAAQ,WAAW,CAAC;IACjC,OAAO,QAAQ,MAAM,CAAC,SAAS;IAC/B,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,SAAS;IAC/C,IAAI,MAAM,KAAK,KAAK,CAAC,SAAS;IAC9B,IAAI,QAAQ,aAAa,MAAM,MAAM;IACrC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,EAAE,UAAU,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE;IAClE,IAAI,cAAc;IAClB,oDAAoD;IACpD,IAAI,KAAK,KAAK,CAAC,WAAW,KAAK,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,CAAC,eAAe,EAAE;QAC/E,eAAe,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QACtC,gBAAgB;IACpB,OACK;QACD,eAAe,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI;QACxC,gBAAgB;IACpB;IACA,KAAK,KAAK,CAAC,WAAW,GAAG;IACzB,IAAI,SAAS,SAAS,QAAQ,OAAO,EAAE,MAAM,GAAG,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE,cAAc;IACpF,IAAI,QACA,KAAK,KAAK,CAAC,cAAc;IAC7B,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,GAAG,KAAK,GAAG,KAAK,OAAO,OAAO,KAC7D,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,EAAE,QAAQ,MACjE,CAAC,CAAC,UAAU,OAAO,IAAI,IAAI,OAAO,IAAI,KACtC,KAAK,QAAQ,CAAC,iBAAiB,CAAA,IAAK,EAAE,MAAM,SAAS,IAAI,YAAY;QACrE,KAAK,KAAK,CAAC,YAAY,GAAG;QAC1B;IACJ;IACA,IAAI,CAAC,QAAQ;QACT,IAAI,YAAY,eAAe,qJAAA,CAAA,gBAAa,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,OAAO,KAC1F,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,GAAG;YACvE,SAAS;gBAAE,OAAO,IAAI,IAAI;gBAAE,MAAM,IAAI,EAAE;gBAAE,MAAM,IAAI,EAAE;YAAC;QAC3D,OACK;YACD,IAAI,MAAM,GAAG,EAAE;gBACX,IAAI,MAAM,iBAAiB,MAAM,KAAK,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG;gBAC1D,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,KAAK,CAAC,SAAS,GAAG;oBACtC,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC;oBACpC,IAAI,eACA,GAAG,OAAO,CAAC,eAAe;oBAC9B,KAAK,QAAQ,CAAC;gBAClB;YACJ;YACA;QACJ;IACJ;IACA,kEAAkE;IAClE,8DAA8D;IAC9D,qDAAqD;IACrD,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,IACnD,OAAO,KAAK,IAAI,OAAO,IAAI,IAC3B,KAAK,KAAK,CAAC,SAAS,YAAY,qJAAA,CAAA,gBAAa,EAAE;QAC/C,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,KACxF,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,MAAM,IAAI,EAAE;YACzC,OAAO,KAAK,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI;QAC5C,OACK,IAAI,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,KACvF,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,MAAM,EAAE,EAAE;YACrC,OAAO,IAAI,IAAK,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,OAAO,IAAI;YACrD,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QACzC;IACJ;IACA,mEAAmE;IACnE,kEAAkE;IAClE,+DAA+D;IAC/D,IAAI,MAAM,cAAc,MAAM,OAAO,IAAI,IAAI,OAAO,KAAK,GAAG,KACxD,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,GAAG,MAAM,IAAI,IACxD,MAAM,GAAG,CAAC,WAAW,CAAC,OAAO,KAAK,GAAG,MAAM,IAAI,GAAG,GAAG,OAAO,KAAK,GAAG,MAAM,IAAI,GAAG,MAAM,WAAW;QAClG,OAAO,KAAK;QACZ,OAAO,IAAI;QACX,OAAO,IAAI;IACf;IACA,IAAI,QAAQ,MAAM,GAAG,CAAC,cAAc,CAAC,OAAO,KAAK,GAAG,MAAM,IAAI;IAC9D,IAAI,MAAM,MAAM,GAAG,CAAC,cAAc,CAAC,OAAO,IAAI,GAAG,MAAM,IAAI;IAC3D,IAAI,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK;IACrC,IAAI,eAAe,MAAM,UAAU,CAAC,QAAQ,MAAM,MAAM,CAAC,aAAa,IAAI,OAAO,GAAG,MAAM,OAAO,IAAI;IACrG,IAAI;IACJ,mEAAmE;IACnE,oEAAoE;IACpE,IAAI,CAAC,AAAC,OAAO,KAAK,KAAK,CAAC,YAAY,GAAG,KAAK,GAAG,KAAK,OAChD,CAAC,CAAC,gBAAgB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI,SAAS,EAAE,QAAQ,IAAI,IAAI,KAC/E,CAAC,gBAAgB,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,IAChD,CAAC,CAAC,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,MAAM,CAAC,aAAa,KACtD,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,IAAI,GAAG,EAAE,IAAI,QACzD,CAAC,UAAU,qJAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,IAAI,GAAG,KAAK,KACxE,QAAQ,IAAI,GAAG,MAAM,GAAG,AAAC,KAC7B,KAAK,QAAQ,CAAC,iBAAiB,CAAA,IAAK,EAAE,MAAM,SAAS,IAAI,YAAY;QACrE,KAAK,KAAK,CAAC,YAAY,GAAG;QAC1B;IACJ;IACA,qBAAqB;IACrB,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,KAAK,IAC1C,mBAAmB,KAAK,OAAO,KAAK,EAAE,OAAO,IAAI,EAAE,OAAO,QAC1D,KAAK,QAAQ,CAAC,iBAAiB,CAAA,IAAK,EAAE,MAAM,SAAS,GAAG,gBAAgB;QACxE,IAAI,WAAW,QACX,KAAK,WAAW,CAAC,wBAAwB,IAAI,OAAO;QACxD;IACJ;IACA,2DAA2D;IAC3D,mEAAmE;IACnE,iCAAiC;IACjC,IAAI,UAAU,OAAO,IAAI,IAAI,OAAO,KAAK,EACrC,KAAK,KAAK,CAAC,gBAAgB,GAAG,KAAK,GAAG;IAC1C,gDAAgD;IAChD,8DAA8D;IAC9D,oEAAoE;IACpE,mEAAmE;IACnE,gEAAgE;IAChE,oEAAoE;IACpE,kEAAkE;IAClE,wBAAwB;IACxB,IAAI,WAAW,CAAC,gBAAgB,MAAM,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,YAAY,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK,IAC7G,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,OAAO,IAAI,EAAE;QAClF,OAAO,IAAI,IAAI;QACf,MAAM,MAAM,GAAG,CAAC,cAAc,CAAC,OAAO,IAAI,GAAG,MAAM,IAAI;QACvD,WAAW;YACP,KAAK,QAAQ,CAAC,iBAAiB,SAAU,CAAC;gBAAI,OAAO,EAAE,MAAM,SAAS,IAAI;YAAW;QACzF,GAAG;IACP;IACA,IAAI,SAAS,OAAO,KAAK,EAAE,OAAO,OAAO,IAAI;IAC7C,IAAI,OAAO,CAAC;QACR,IAAI,KAAK,QAAQ,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,KAAK,GAAG,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;QACxH,IAAI,MAAM,GAAG,EAAE;YACX,IAAI,MAAM,iBAAiB,MAAM,GAAG,GAAG,EAAE,MAAM,GAAG;YAClD,wDAAwD;YACxD,yDAAyD;YACzD,yCAAyC;YACzC,kEAAkE;YAClE,yCAAyC;YACzC,IAAI,OAAO,CAAC,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,KAAK,IAC9C,CAAC,OAAO,KAAK,IAAI,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC,gBAAgB,GAAG,KAAK,GAAG,KAAK,GAAG,KAC9E,CAAC,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAC3D,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,MAAM,GACrC,GAAG,YAAY,CAAC;QACxB;QACA,IAAI,eACA,GAAG,OAAO,CAAC,eAAe;QAC9B,OAAO,GAAG,cAAc;IAC5B;IACA,IAAI;IACJ,IAAI,cAAc;QACd,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE;YACtB,8DAA8D;YAC9D,mDAAmD;YACnD,IAAI,MAAM,cAAc,MAAM,MAAM,YAAY,IAAI,GAAG;gBACnD,KAAK,WAAW,CAAC,wBAAwB;gBACzC,WAAW,IAAM,eAAe,OAAO;YAC3C;YACA,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ;YAC3C,IAAI,QAAQ,IAAI,OAAO,CAAC,OAAO,KAAK,EAAE,WAAW,CAAC,IAAI,OAAO,CAAC,OAAO,IAAI;YACzE,IAAI,OACA,GAAG,WAAW,CAAC;YACnB,KAAK,QAAQ,CAAC;QAClB,OACK,IACL,OAAO,IAAI,IAAI,OAAO,IAAI,IACtB,CAAC,aAAa,aAAa,MAAM,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,YAAY,EAAE,IAAI,YAAY,GAAG,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,YAAY,EAAE,OAAO,IAAI,GAAG,OAAO,KAAK,IAAI,GAAG;YAC3K,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE;YAC3B,IAAI,WAAW,IAAI,IAAI,OACnB,GAAG,OAAO,CAAC,QAAQ,MAAM,WAAW,IAAI;iBAExC,GAAG,UAAU,CAAC,QAAQ,MAAM,WAAW,IAAI;YAC/C,KAAK,QAAQ,CAAC;QAClB,OACK,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG;YAC1G,6DAA6D;YAC7D,IAAI,OAAO,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,YAAY,EAAE,IAAI,YAAY;YACxE,IAAI,QAAQ,IAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,QAAQ;YAC9D,IAAI,CAAC,KAAK,QAAQ,CAAC,mBAAmB,CAAA,IAAK,EAAE,MAAM,QAAQ,MAAM,MAAM,SACnE,KAAK,QAAQ,CAAC;QACtB;IACJ,OACK;QACD,KAAK,QAAQ,CAAC;IAClB;AACJ;AACA,SAAS,iBAAiB,IAAI,EAAE,GAAG,EAAE,SAAS;IAC1C,IAAI,KAAK,GAAG,CAAC,UAAU,MAAM,EAAE,UAAU,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,EAC7D,OAAO;IACX,OAAO,iBAAiB,MAAM,IAAI,OAAO,CAAC,UAAU,MAAM,GAAG,IAAI,OAAO,CAAC,UAAU,IAAI;AAC3F;AACA,gEAAgE;AAChE,kEAAkE;AAClE,yCAAyC;AACzC,SAAS,aAAa,GAAG,EAAE,IAAI;IAC3B,IAAI,WAAW,IAAI,UAAU,CAAC,KAAK,EAAE,YAAY,KAAK,UAAU,CAAC,KAAK;IACtE,IAAI,QAAQ,UAAU,UAAU,WAAW,MAAM,MAAM;IACvD,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAClC,QAAQ,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IACjC,UAAU,QAAQ,CAAC,EAAE,CAAC,aAAa,CAAC;IACxC,IAAI,MAAM,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,GAAG;QAC1C,OAAO,KAAK,CAAC,EAAE;QACf,OAAO;QACP,SAAS,CAAC,OAAS,KAAK,IAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK;IACzD,OACK,IAAI,MAAM,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,GAAG;QAC/C,OAAO,OAAO,CAAC,EAAE;QACjB,OAAO;QACP,SAAS,CAAC,OAAS,KAAK,IAAI,CAAC,KAAK,aAAa,CAAC,KAAK,KAAK;IAC9D,OACK;QACD,OAAO;IACX;IACA,IAAI,UAAU,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,UAAU,EAAE,IACjC,QAAQ,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC;IACnC,IAAI,qJAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,MAC1B,OAAO;QAAE;QAAM;IAAK;AAC5B;AACA,SAAS,mBAAmB,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO;IAC3D,IACA,MAAM,SAAS,QAAQ,GAAG,GAAG,UAAU,GAAG,IACtC,wFAAwF;IACxF,sBAAsB,WAAW,MAAM,SAAS,QAAQ,GAAG,EAC3D,OAAO;IACX,IAAI,SAAS,IAAI,OAAO,CAAC;IACzB,6FAA6F;IAC7F,IAAI,CAAC,UAAU,MAAM,CAAC,WAAW,EAAE;QAC/B,IAAI,QAAQ,OAAO,SAAS;QAC5B,OAAO,SAAS,QAAQ,OAAO,QAAQ,MAAM,QAAQ;IACzD;IACA,sCAAsC;IACtC,IAAI,OAAO,YAAY,GAAG,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC,WAAW,EAC9E,OAAO;IACX,IAAI,QAAQ,IAAI,OAAO,CAAC,sBAAsB,QAAQ,MAAM;IAC5D,2DAA2D;IAC3D,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW,IAAI,MAAM,GAAG,GAAG,OACzC,sBAAsB,OAAO,MAAM,SAAS,KAC5C,OAAO;IACX,gDAAgD;IAChD,OAAO,UAAU,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,YAAY,EAAE,EAAE,CAAC,MAAM,MAAM,CAAC,OAAO;AACvF;AACA,SAAS,sBAAsB,IAAI,EAAE,OAAO,EAAE,OAAO;IACjD,IAAI,QAAQ,KAAK,KAAK,EAAE,MAAM,UAAU,KAAK,GAAG,KAAK,KAAK,GAAG;IAC7D,MAAO,QAAQ,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,OAAO,UAAU,EAAG;QACpF;QACA;QACA,UAAU;IACd;IACA,IAAI,SAAS;QACT,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,UAAU,CAAC,KAAK,UAAU,CAAC;QACvD,MAAO,QAAQ,CAAC,KAAK,MAAM,CAAE;YACzB,OAAO,KAAK,UAAU;YACtB;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa;IACpD,IAAI,QAAQ,EAAE,aAAa,CAAC,GAAG;IAC/B,IAAI,SAAS,MACT,OAAO;IACX,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,WAAW,CAAC,GAAG,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI;IACtE,IAAI,iBAAiB,OAAO;QACxB,IAAI,SAAS,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG,CAAC,MAAM;QAChD,gBAAgB,OAAO,SAAS;IACpC;IACA,IAAI,OAAO,SAAS,EAAE,IAAI,GAAG,EAAE,IAAI,EAAE;QACjC,IAAI,OAAO,gBAAgB,SAAS,gBAAgB,OAAO,QAAQ,eAAe;QAClF,SAAS;QACT,IAAI,SAAS,QAAQ,EAAE,IAAI,IAAI,gBAAgB,EAAE,WAAW,CAAC,QAAQ,GAAG,QAAQ,KAC5E,SAAS,OAAO,IAAI,CAAC;QACzB,OAAO,QAAQ,CAAC,OAAO,IAAI;QAC3B,OAAO;IACX,OACK,IAAI,OAAO,OAAO;QACnB,IAAI,OAAO,gBAAgB,SAAS,gBAAgB,OAAO,QAAQ,eAAe;QAClF,SAAS;QACT,IAAI,SAAS,QAAQ,EAAE,IAAI,IAAI,gBAAgB,EAAE,WAAW,CAAC,QAAQ,GAAG,QAAQ,KAC5E,SAAS,OAAO,IAAI,CAAC;QACzB,OAAO,QAAQ,CAAC,OAAO,IAAI;QAC3B,OAAO;IACX;IACA,OAAO;QAAE;QAAO;QAAM;IAAK;AAC/B;AACA,SAAS,gBAAgB,GAAG;IACxB,IAAI,IAAI,MAAM,IAAI,GACd,OAAO;IACX,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC;IAC9C,OAAO,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK;AAC7D;AAEA;;AAEA,GACA,MAAM,uBAAuB;AAC7B;;AAEA,GACA,MAAM,mBAAmB;AACzB;;;;AAIA,GACA,MAAM;IACF;;;;;;IAMA,GACA,YAAY,KAAK,EAAE,KAAK,CAAE;QACtB,IAAI,CAAC,KAAK,GAAG;QACb;;QAEA,GACA,IAAI,CAAC,OAAO,GAAG;QACf;;QAEA,GACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf;;QAEA,GACA,IAAI,CAAC,UAAU,GAAG;QAClB;;QAEA,GACA,IAAI,CAAC,aAAa,GAAG;QACrB;;QAEA,GACA,IAAI,CAAC,oBAAoB,GAAG;QAC5B;;QAEA,GACA,IAAI,CAAC,KAAK,GAAG,IAAI;QACjB,IAAI,CAAC,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB;;;;QAIA,GACA,IAAI,CAAC,qBAAqB,GAAG;QAC7B;;;;QAIA,GACA,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;QACxB,IAAI,CAAC,aAAa,GAAG,MAAM,OAAO,IAAI,EAAE;QACxC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACvC,IAAI,CAAC,GAAG,GAAG,AAAC,SAAS,MAAM,KAAK,IAAK,SAAS,aAAa,CAAC;QAC5D,IAAI,OAAO;YACP,IAAI,MAAM,WAAW,EACjB,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG;iBACzB,IAAI,OAAO,SAAS,YACrB,MAAM,IAAI,CAAC,GAAG;iBACb,IAAI,MAAM,KAAK,EAChB,IAAI,CAAC,OAAO,GAAG;QACvB;QACA,IAAI,CAAC,QAAQ,GAAG,YAAY,IAAI;QAChC,oBAAoB,IAAI;QACxB,IAAI,CAAC,SAAS,GAAG,eAAe,IAAI;QACpC,IAAI,CAAC,OAAO,GAAG,YAAY,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,eAAe,IAAI,GAAG,gBAAgB,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI;QACtG,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,IAAI,EAAE,CAAC,MAAM,IAAI,UAAU,QAAU,cAAc,IAAI,EAAE,MAAM,IAAI,UAAU;QAChH,IAAI,CAAC,WAAW,CAAC,KAAK;QACtB,UAAU,IAAI;QACd,IAAI,CAAC,iBAAiB;IAC1B;IACA;;;;IAIA,GACA,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;IAAE;IAC/C;;IAEA,GACA,IAAI,QAAQ;QACR,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;YACjC,IAAI,OAAO,IAAI,CAAC,MAAM;YACtB,IAAI,CAAC,MAAM,GAAG,CAAC;YACf,IAAK,IAAI,QAAQ,KACb,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;QAClC;QACA,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;;IAGA,GACA,OAAO,KAAK,EAAE;QACV,IAAI,MAAM,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EACpD,gBAAgB,IAAI;QACxB,IAAI,YAAY,IAAI,CAAC,MAAM;QAC3B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,MAAM,OAAO,EAAE;YACf,MAAM,OAAO,CAAC,OAAO,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,MAAM,OAAO;QACtC;QACA,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,EAAE;IACvC;IACA;;;;IAIA,GACA,SAAS,KAAK,EAAE;QACZ,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,QAAQ,IAAI,CAAC,MAAM,CACxB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;QACrC,QAAQ,KAAK,GAAG,IAAI,CAAC,KAAK;QAC1B,IAAK,IAAI,QAAQ,MACb,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;QAC/B,IAAI,CAAC,MAAM,CAAC;IAChB;IACA;;;IAGA,GACA,YAAY,KAAK,EAAE;QACf,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,CAAC,MAAM;IAC5C;IACA,iBAAiB,KAAK,EAAE,SAAS,EAAE;QAC/B,IAAI;QACJ,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,OAAO,YAAY;QACnD,kEAAkE;QAClE,gBAAgB;QAChB,IAAI,MAAM,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE;YACrC,iBAAiB,IAAI;YACrB,YAAY;QAChB;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,iBAAiB,KAAK,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,UAAU,OAAO;QAC9F,IAAI,kBAAkB,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,UAAU,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,UAAU,SAAS,EAAE;YAC5G,IAAI,YAAY,eAAe,IAAI;YACnC,IAAI,iBAAiB,WAAW,IAAI,CAAC,SAAS,GAAG;gBAC7C,IAAI,CAAC,SAAS,GAAG;gBACjB,SAAS;YACb;QACJ;QACA,IAAI,kBAAkB,UAAU,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YAC5E,gBAAgB,IAAI;QACxB;QACA,IAAI,CAAC,QAAQ,GAAG,YAAY,IAAI;QAChC,oBAAoB,IAAI;QACxB,IAAI,YAAY,gBAAgB,IAAI,GAAG,YAAY,eAAe,IAAI;QACtE,IAAI,SAAS,KAAK,OAAO,IAAI,MAAM,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,UAClE,MAAM,iBAAiB,GAAG,KAAK,iBAAiB,GAAG,iBAAiB;QAC1E,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,WAAW;QAC1E,IAAI,aAAa,CAAC,MAAM,SAAS,CAAC,EAAE,CAAC,KAAK,SAAS,GAC/C,YAAY;QAChB,IAAI,eAAe,UAAU,cAAc,aAAa,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,QAAQ,eAAe,IAAI;QACpH,IAAI,WAAW;YACX,IAAI,CAAC,WAAW,CAAC,IAAI;YACrB,8DAA8D;YAC9D,2DAA2D;YAC3D,uDAAuD;YACvD,0DAA0D;YAC1D,wBAAwB;YACxB,IAAI,iBAAiB,aAAa,CAAC,MAAM,MAAM,KAAK,CAAC,IAAI,CAAC,SAAS,IAC/D,CAAC,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,MAAM,SAAS,CAAC,KAAK,IAAI,wBAAwB,KAAK,SAAS,EAAE,MAAM,SAAS;YAC9G,IAAI,WAAW;gBACX,4DAA4D;gBAC5D,8DAA8D;gBAC9D,2DAA2D;gBAC3D,yBAAyB;gBACzB,IAAI,eAAe,SAAU,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,GAAG,SAAS,GAAI;gBACtF,IAAI,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,oBAAoB,IAAI;gBACzD,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,WAAW,WAAW,IAAI,GAAG;oBACvE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;oBAC7B,IAAI,CAAC,OAAO,CAAC,OAAO;oBACpB,IAAI,CAAC,OAAO,GAAG,YAAY,MAAM,GAAG,EAAE,WAAW,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI;gBAC9E;gBACA,IAAI,gBAAgB,CAAC,IAAI,CAAC,WAAW,EACjC,iBAAiB;YACzB;YACA,kEAAkE;YAClE,gEAAgE;YAChE,8DAA8D;YAC9D,kBAAkB;YAClB,IAAI,kBACA,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,OACjF,mBAAmB,IAAI,CAAC,GAAG;gBAC/B,eAAe,IAAI,EAAE;YACzB,OACK;gBACD,kBAAkB,IAAI,EAAE,MAAM,SAAS;gBACvC,IAAI,CAAC,WAAW,CAAC,eAAe;YACpC;YACA,IAAI,CAAC,WAAW,CAAC,KAAK;QAC1B;QACA,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM,GAAG,GAC7F,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC1C,IAAI,UAAU,SAAS;YACnB,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG;QACzB,OACK,IAAI,UAAU,gBAAgB;YAC/B,IAAI,CAAC,iBAAiB;QAC1B,OACK,IAAI,cAAc;YACnB,eAAe;QACnB;IACJ;IACA;;IAEA,GACA,oBAAoB;QAChB,IAAI,WAAW,IAAI,CAAC,iBAAiB,GAAG,SAAS;QACjD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,QAAQ,IAAI,IAAI,WAAW,SAAS,UAAU;aACtF,IAAI,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAA,IAAK,EAAE,IAAI;aACxD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,YAAY,qJAAA,CAAA,gBAAa,EAAE;YACpD,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI;YAC/D,IAAI,OAAO,QAAQ,IAAI,GACnB,mBAAmB,IAAI,EAAE,OAAO,qBAAqB,IAAI;QACjE,OACK;YACD,mBAAmB,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI;QAC7E;IACJ;IACA,qBAAqB;QACjB,IAAI;QACJ,MAAO,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,GAC9B,IAAI,KAAK,OAAO,EACZ,KAAK,OAAO;IACxB;IACA,kBAAkB,SAAS,EAAE;QACzB,IAAI,CAAC,aAAa,UAAU,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACvG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa;YAC3C,IAAI,CAAC,kBAAkB;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAK;gBAChD,IAAI,SAAS,IAAI,CAAC,aAAa,CAAC,EAAE;gBAClC,IAAI,OAAO,IAAI,CAAC,IAAI,EAChB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;YACnD;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;gBAChD,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBAClC,IAAI,OAAO,IAAI,CAAC,IAAI,EAChB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;YACnD;QACJ,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;gBAC9C,IAAI,aAAa,IAAI,CAAC,WAAW,CAAC,EAAE;gBACpC,IAAI,WAAW,MAAM,EACjB,WAAW,MAAM,CAAC,IAAI,EAAE;YAChC;QACJ;IACJ;IACA,kBAAkB,QAAQ,EAAE,IAAI,EAAE;QAC9B,IAAI,MAAM,SAAS,IAAI,EAAE,QAAQ,CAAC;QAClC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;YAC7C,QAAQ,IAAI,IAAI;QACpB,OACK;YACD,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI;YAC9E,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;YAClD,IAAI,SAAS,IAAI,IAAI,EACjB,QAAQ;QAChB;QACA,IAAI,CAAC,QAAQ,GAAG,IAAI,SAAS,SAAS,KAAK,EAAE,SAAS,IAAI,EAAE,QAAQ,IAAI,YAAY,qJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;IAC7H;IACA,SAAS,QAAQ,EAAE,CAAC,EAAE;QAClB,IAAI,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;QACjD,IAAI,QAAQ,QAAQ,CAAC,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAC3C,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAK;YAChD,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS;YAChD,IAAI,QAAQ,QAAQ,CAAC,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAC3C,OAAO;QACf;QACA,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,OAAO;QAChC,IAAI,SACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS;YACrC,IAAI,QAAQ,QAAQ,CAAC,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAC3C,OAAO;QACf;IACR;IACA;;IAEA,GACA,WAAW;QACP,2EAA2E;QAC3E,wEAAwE;QACxE,4CAA4C;QAC5C,IAAI,IAAI;YACJ,uEAAuE;YACvE,sEAAsE;YACtE,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;YAClC,IAAI,QAAQ,IAAI,CAAC,GAAG,EAChB,OAAO;YACX,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAC5B,OAAO;YACX,MAAO,QAAQ,IAAI,CAAC,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAO;gBACxD,IAAI,KAAK,eAAe,IAAI,SACxB,OAAO;gBACX,OAAO,KAAK,aAAa;YAC7B;YACA,OAAO;QACX;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG;IAC9C;IACA;;IAEA,GACA,QAAQ;QACJ,IAAI,CAAC,WAAW,CAAC,IAAI;QACrB,IAAI,IAAI,CAAC,QAAQ,EACb,mBAAmB,IAAI,CAAC,GAAG;QAC/B,eAAe,IAAI;QACnB,IAAI,CAAC,WAAW,CAAC,KAAK;IAC1B;IACA;;;;;IAKA,GACA,IAAI,OAAO;QACP,IAAI,SAAS,IAAI,CAAC,KAAK;QACvB,IAAI,UAAU,MACV,IAAK,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,SAAS,OAAO,UAAU,CAAE;YACvE,IAAI,OAAO,QAAQ,IAAI,KAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,IAAI,EAAG;gBAChE,IAAI,CAAC,OAAO,YAAY,EACpB,OAAO,cAAc,CAAC,QAAQ,YAAY,GAAG,IAAM,OAAO,aAAa,CAAC,YAAY;gBACxF,OAAO,IAAI,CAAC,KAAK,GAAG;YACxB;QACJ;QACJ,OAAO,UAAU;IACrB;IACA;;;IAGA,GACA,aAAa;QACT,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;;;;;;;IAQA,GACA,YAAY,MAAM,EAAE;QAChB,OAAO,YAAY,IAAI,EAAE;IAC7B;IACA;;;;;;;IAOA,GACA,YAAY,GAAG,EAAE,OAAO,CAAC,EAAE;QACvB,OAAO,YAAY,IAAI,EAAE,KAAK;IAClC;IACA;;;;;;;;;IASA,GACA,SAAS,GAAG,EAAE,OAAO,CAAC,EAAE;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK;IACxC;IACA;;;;;;;;;IASA,GACA,QAAQ,GAAG,EAAE;QACT,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC/B,OAAO,OAAO,KAAK,OAAO,GAAG;IACjC;IACA;;;;;;;;;IASA,GACA,SAAS,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE;QAC9B,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,QAAQ;QAChD,IAAI,OAAO,MACP,MAAM,IAAI,WAAW;QACzB,OAAO;IACX;IACA;;;;;;;IAOA,GACA,eAAe,GAAG,EAAE,KAAK,EAAE;QACvB,OAAO,eAAe,IAAI,EAAE,SAAS,IAAI,CAAC,KAAK,EAAE;IACrD;IACA;;;;IAIA,GACA,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,OAAO,QAAQ,IAAI,EAAE,IAAI,MAAM,OAAO,SAAS,IAAI,eAAe;IACtE;IACA;;IAEA,GACA,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,OAAO,QAAQ,IAAI,EAAE,MAAM,MAAM,MAAM,SAAS,IAAI,eAAe;IACvE;IACA;;;;;;;IAOA,GACA,sBAAsB,KAAK,EAAE;QACzB,OAAO,sBAAsB,IAAI,EAAE;IACvC;IACA;;;IAGA,GACA,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,EACb;QACJ,aAAa,IAAI;QACjB,IAAI,CAAC,kBAAkB;QACvB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,gBAAgB,IAAI,GAAG,IAAI;YACnE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG;QAC3B,OACK,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;YAC1B,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG;QAC5C;QACA,IAAI,CAAC,OAAO,CAAC,OAAO;QACpB,IAAI,CAAC,OAAO,GAAG;QACf;IACJ;IACA;;;;IAIA,GACA,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,OAAO,IAAI;IAC3B;IACA;;IAEA,GACA,cAAc,KAAK,EAAE;QACjB,OAAO,cAAc,IAAI,EAAE;IAC/B;IACA;;IAEA,GACA,oBAAoB;QAChB,IAAI,MAAM,IAAI,CAAC,YAAY;QAC3B,IAAI,CAAC,KACD,OAAO;YAAE,WAAW;YAAM,aAAa;YAAG,YAAY;YAAM,cAAc;QAAE;QAChF,OAAO,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,MACpC,kBAAkB,IAAI,CAAC,GAAG,CAAC,aAAa,KAAK,IAAI,CAAC,GAAG,IAAI,2BAA2B,IAAI,EAAE,QAAQ;IAC1G;IACA;;IAEA,GACA,eAAe;QACX,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;IACjC;AACJ;AACA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAU,EAAE;IACxC,IAAI,sBAAsB,IAAI,CAAC,MAAM,CAAC,mBAAmB;IACzD,IAAI,qBACA,oBAAoB,IAAI,CAAC,IAAI,EAAE;SAE/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AAC1C;AACA,SAAS,eAAe,IAAI;IACxB,IAAI,QAAQ,OAAO,MAAM,CAAC;IAC1B,MAAM,KAAK,GAAG;IACd,MAAM,eAAe,GAAG,OAAO,KAAK,QAAQ;IAC5C,KAAK,QAAQ,CAAC,cAAc,CAAA;QACxB,IAAI,OAAO,SAAS,YAChB,QAAQ,MAAM,KAAK,KAAK;QAC5B,IAAI,OACA,IAAK,IAAI,QAAQ,MAAO;YACpB,IAAI,QAAQ,SACR,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,KAAK;iBAC/B,IAAI,QAAQ,SACb,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,EAAE,IAAI,KAAK,CAAC,KAAK;iBACjE,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,QAAQ,qBAAqB,QAAQ,YAC1D,KAAK,CAAC,KAAK,GAAG,OAAO,KAAK,CAAC,KAAK;QACxC;IACR;IACA,IAAI,CAAC,MAAM,SAAS,EAChB,MAAM,SAAS,GAAG;IACtB,OAAO;QAAC,WAAW,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;KAAO;AACnE;AACA,SAAS,oBAAoB,IAAI;IAC7B,IAAI,KAAK,UAAU,EAAE;QACjB,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,SAAS,GAAG;QAChB,IAAI,YAAY,CAAC,oBAAoB;QACrC,IAAI,YAAY,CAAC,OAAO;QACxB,KAAK,aAAa,GAAG;YAAE;YAAK,MAAM,WAAW,MAAM,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK;gBAAE,KAAK;gBAAM,OAAO,KAAK,UAAU;YAAC;QAAG;IAC/H,OACK;QACD,KAAK,aAAa,GAAG;IACzB;AACJ;AACA,SAAS,YAAY,IAAI;IACrB,OAAO,CAAC,KAAK,QAAQ,CAAC,YAAY,CAAA,QAAS,MAAM,KAAK,KAAK,MAAM;AACrE;AACA,SAAS,wBAAwB,IAAI,EAAE,IAAI;IACvC,IAAI,QAAQ,KAAK,GAAG,CAAC,KAAK,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI;IAC5F,OAAO,KAAK,OAAO,CAAC,KAAK,CAAC,UAAU,KAAK,OAAO,CAAC,KAAK,CAAC;AAC3D;AACA,SAAS,eAAe,IAAI;IACxB,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,SAAS,IAAI,GAAG;QACZ,IAAK,IAAI,QAAQ,IACb,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,OAC9C,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK;IACpC;IACA,KAAK,QAAQ,CAAC,aAAa;IAC3B,KAAK,QAAQ,CAAC,aAAa;IAC3B,OAAO;AACX;AACA,SAAS,iBAAiB,CAAC,EAAE,CAAC;IAC1B,IAAI,KAAK,GAAG,KAAK;IACjB,IAAK,IAAI,QAAQ,EAAG;QAChB,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,EAClB,OAAO;QACX;IACJ;IACA,IAAK,IAAI,KAAK,EACV;IACJ,OAAO,MAAM;AACjB;AACA,SAAS,oBAAoB,MAAM;IAC/B,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI,OAAO,IAAI,CAAC,iBAAiB,IAAI,OAAO,IAAI,CAAC,iBAAiB,EACnF,MAAM,IAAI,WAAW;AAC7B", "ignoreList": [0], "debugId": null}}]}