@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* TipTap Editor Styles */
.ProseMirror {
  outline: none;
  padding: 1rem;
  line-height: 1.6;
}

.ProseMirror p {
  margin: 0.5rem 0;
}

.ProseMirror mark {
  padding: 0.1rem 0.2rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ProseMirror mark:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ProseMirror mark[data-color="#fbbf24"] {
  background-color: #fbbf24;
  color: #1f2937;
}

.ProseMirror mark[data-color="#60a5fa"] {
  background-color: #60a5fa;
  color: #1f2937;
}

.ProseMirror mark[data-color="#34d399"] {
  background-color: #34d399;
  color: #1f2937;
}
