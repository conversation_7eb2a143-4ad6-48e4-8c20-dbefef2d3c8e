const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const { Server } = require('socket.io');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

// Create Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  // Create HTTP server
  const httpServer = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // Create Socket.IO server
  const io = new Server(httpServer, {
    cors: {
      origin: process.env.NEXT_PUBLIC_APP_URL || `http://localhost:${port}`,
      methods: ["GET", "POST"]
    },
    transports: ['websocket', 'polling']
  });

  // Store active sessions and users
  const sessions = new Map();
  const userSessions = new Map();

  io.on('connection', (socket) => {
    console.log('User connected:', socket.id);

    // Join a session
    socket.on('join-session', (sessionId) => {
      const previousSession = userSessions.get(socket.id);
      
      // Leave previous session if exists
      if (previousSession) {
        socket.leave(previousSession);
        const sessionUsers = sessions.get(previousSession);
        if (sessionUsers) {
          sessionUsers.delete(socket.id);
          if (sessionUsers.size === 0) {
            sessions.delete(previousSession);
          } else {
            socket.to(previousSession).emit('user-left', {
              userId: socket.id,
              sessionId: previousSession
            });
          }
        }
      }

      // Join new session
      socket.join(sessionId);
      userSessions.set(socket.id, sessionId);
      
      if (!sessions.has(sessionId)) {
        sessions.set(sessionId, new Set());
      }
      sessions.get(sessionId).add(socket.id);

      // Notify user and others in session
      socket.emit('session-joined', { sessionId, userId: socket.id });
      socket.to(sessionId).emit('user-joined', { userId: socket.id, sessionId });

      console.log(`User ${socket.id} joined session ${sessionId}`);
    });

    // Leave a session
    socket.on('leave-session', (sessionId) => {
      socket.leave(sessionId);
      userSessions.delete(socket.id);
      
      const sessionUsers = sessions.get(sessionId);
      if (sessionUsers) {
        sessionUsers.delete(socket.id);
        if (sessionUsers.size === 0) {
          sessions.delete(sessionId);
        } else {
          socket.to(sessionId).emit('user-left', {
            userId: socket.id,
            sessionId
          });
        }
      }

      console.log(`User ${socket.id} left session ${sessionId}`);
    });

    // Handle text selection
    socket.on('text-selection', (data) => {
      const { sessionId, selectedText, timestamp } = data;
      socket.to(sessionId).emit('text-selected', {
        userId: socket.id,
        selectedText,
        timestamp
      });
    });

    // Handle analysis request
    socket.on('analysis-request', (data) => {
      const { sessionId, text, type } = data;
      socket.to(sessionId).emit('analysis-started', {
        userId: socket.id,
        text,
        type
      });
    });

    // Handle generation request
    socket.on('generation-request', (data) => {
      const { sessionId, prompt, type } = data;
      socket.to(sessionId).emit('generation-started', {
        userId: socket.id,
        prompt,
        type
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      const sessionId = userSessions.get(socket.id);
      if (sessionId) {
        const sessionUsers = sessions.get(sessionId);
        if (sessionUsers) {
          sessionUsers.delete(socket.id);
          if (sessionUsers.size === 0) {
            sessions.delete(sessionId);
          } else {
            socket.to(sessionId).emit('user-left', {
              userId: socket.id,
              sessionId
            });
          }
        }
        userSessions.delete(socket.id);
      }
      
      console.log('User disconnected:', socket.id);
    });
  });

  // Start server
  httpServer
    .once('error', (err) => {
      console.error(err);
      process.exit(1);
    })
    .listen(port, () => {
      console.log(`> Ready on http://${hostname}:${port}`);
      console.log(`> WebSocket server running`);
    });
});
