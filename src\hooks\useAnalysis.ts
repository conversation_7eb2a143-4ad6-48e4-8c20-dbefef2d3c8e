'use client';

import { useState, useCallback } from 'react';
import axios from 'axios';
import { useWebSocket } from './useWebSocket';

export interface AnalysisData {
  concept: string;
  definition: string;
  visualizationPrompt: {
    originalText: string;
    enhancedPrompt: string;
    visualizationType: 'image' | 'video';
    scientificContext: string;
    educationalLevel: 'basic' | 'intermediate' | 'advanced';
  };
  relatedConcepts: string[];
  difficulty: 'basic' | 'intermediate' | 'advanced';
  educationalSuggestions: string[];
  timestamp: string;
}

export interface AnalysisState {
  data: AnalysisData | null;
  loading: boolean;
  error: string | null;
}

export function useAnalysis() {
  const [state, setState] = useState<AnalysisState>({
    data: null,
    loading: false,
    error: null,
  });

  const analyzeText = useCallback(async (
    selectedText: string,
    context?: string,
    visualizationType: 'image' | 'video' = 'image'
  ) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await axios.post('/api/analyze', {
        selectedText,
        context,
        visualizationType,
      });

      if (response.data.success) {
        setState({
          data: response.data.data,
          loading: false,
          error: null,
        });
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Analysis failed');
      }
    } catch (error) {
      const errorMessage = axios.isAxiosError(error)
        ? error.response?.data?.error || error.message
        : 'Failed to analyze text';

      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });

      throw new Error(errorMessage);
    }
  }, []);

  const clearAnalysis = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  const retry = useCallback(async () => {
    if (state.data?.visualizationPrompt.originalText) {
      await analyzeText(
        state.data.visualizationPrompt.originalText,
        state.data.visualizationPrompt.scientificContext,
        state.data.visualizationPrompt.visualizationType
      );
    }
  }, [state.data, analyzeText]);

  return {
    ...state,
    analyzeText,
    clearAnalysis,
    retry,
  };
}
