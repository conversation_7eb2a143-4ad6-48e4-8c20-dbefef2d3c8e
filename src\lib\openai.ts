import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface VisualizationPrompt {
  originalText: string;
  enhancedPrompt: string;
  visualizationType: 'image' | 'video';
  scientificContext: string;
  educationalLevel: 'basic' | 'intermediate' | 'advanced';
}

export interface AnalysisResult {
  concept: string;
  definition: string;
  visualizationPrompt: VisualizationPrompt;
  relatedConcepts: string[];
  difficulty: 'basic' | 'intermediate' | 'advanced';
}

/**
 * Analyzes scientific text and generates visualization prompts
 */
export async function analyzeScientificText(
  selectedText: string,
  context: string = '',
  visualizationType: 'image' | 'video' = 'image'
): Promise<AnalysisResult> {
  try {
    const systemPrompt = `
Você é um especialista em educação científica e visualização de conceitos complexos. 
Sua tarefa é analisar termos científicos e gerar prompts detalhados para criar visualizações educativas.

Instruções:
1. Identifique o conceito científico principal
2. Forneça uma definição clara e educativa
3. Crie um prompt detalhado para gerar uma visualização ${visualizationType === 'video' ? 'em vídeo' : 'em imagem'}
4. Sugira conceitos relacionados
5. Determine o nível de dificuldade

O prompt de visualização deve ser:
- Cientificamente preciso
- Visualmente descritivo
- Educativo e envolvente
- Adequado para ${visualizationType === 'video' ? 'animação' : 'ilustração estática'}

Responda APENAS em formato JSON válido.
`;

    const userPrompt = `
Texto selecionado: "${selectedText}"
Contexto adicional: "${context}"
Tipo de visualização: ${visualizationType}

Analise este conceito científico e gere uma resposta no seguinte formato JSON:
{
  "concept": "nome do conceito",
  "definition": "definição clara e educativa",
  "visualizationPrompt": {
    "originalText": "texto original",
    "enhancedPrompt": "prompt detalhado para visualização",
    "visualizationType": "${visualizationType}",
    "scientificContext": "contexto científico",
    "educationalLevel": "basic|intermediate|advanced"
  },
  "relatedConcepts": ["conceito1", "conceito2", "conceito3"],
  "difficulty": "basic|intermediate|advanced"
}
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 1000,
    });

    const response = completion.choices[0]?.message?.content;
    
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    // Parse JSON response
    const analysisResult: AnalysisResult = JSON.parse(response);
    
    return analysisResult;

  } catch (error) {
    console.error('Error analyzing scientific text:', error);
    
    // Fallback response
    return {
      concept: selectedText,
      definition: `${selectedText} é um conceito científico importante que merece visualização detalhada.`,
      visualizationPrompt: {
        originalText: selectedText,
        enhancedPrompt: `Crie uma ${visualizationType === 'video' ? 'animação educativa' : 'ilustração científica detalhada'} mostrando ${selectedText} de forma clara e educativa, com cores vibrantes e elementos visuais que facilitem o entendimento.`,
        visualizationType,
        scientificContext: 'Educação científica geral',
        educationalLevel: 'intermediate'
      },
      relatedConcepts: [],
      difficulty: 'intermediate'
    };
  }
}

/**
 * Generates educational content suggestions based on analysis
 */
export async function generateEducationalSuggestions(
  concept: string,
  difficulty: string
): Promise<string[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em educação científica. Gere sugestões de atividades educativas baseadas no conceito fornecido."
        },
        {
          role: "user",
          content: `Conceito: ${concept}\nNível: ${difficulty}\n\nGere 5 sugestões de atividades educativas em formato de lista JSON.`
        }
      ],
      temperature: 0.8,
      max_tokens: 500,
    });

    const response = completion.choices[0]?.message?.content;
    
    if (response) {
      return JSON.parse(response);
    }
    
    return [];
  } catch (error) {
    console.error('Error generating educational suggestions:', error);
    return [
      'Criar um mapa mental do conceito',
      'Fazer um experimento prático relacionado',
      'Pesquisar aplicações no mundo real',
      'Criar uma apresentação explicativa',
      'Discutir com colegas e professores'
    ];
  }
}

export { openai };
