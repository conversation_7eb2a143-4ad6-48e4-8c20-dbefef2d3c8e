'use client';

import { useState } from 'react';
import { BookO<PERSON>, Spark<PERSON>, Play, Download } from 'lucide-react';
import TextEditor from '@/components/editor/text-editor';
import VisualizationPanel from '@/components/visualization/visualization-panel';
import RealtimeStatus from '@/components/realtime/realtime-status';

export default function Home() {
  const [selectedText, setSelectedText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);

  const handleTextSelect = (text: string) => {
    setSelectedText(text);
  };

  const handleAnalysisComplete = (analysis: any) => {
    setAnalysisData(analysis);
    console.log('Analysis completed:', analysis);
  };

  const handleGenerate = (text: string, type: 'image' | 'video') => {
    setIsGenerating(true);
    // Here we'll integrate with media generation APIs
    console.log('Generating', type, 'for:', text);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-indigo-600" />
              <h1 className="text-2xl font-bold text-gray-900">StudyVision AI ✨</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900">
                Login
              </button>
              <button className="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700">
                Começar Grátis
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Transforme Texto em Visualizações Imersivas
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Destaque qualquer conceito científico e veja-o ganhar vida através de
            vídeos e imagens geradas por IA em tempo real.
          </p>
        </div>

        {/* Demo Section */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {/* Text Editor */}
          <div className="lg:col-span-2">
            <TextEditor
              onTextSelect={handleTextSelect}
              onContentChange={(content) => console.log('Content changed:', content)}
              onAnalysisComplete={handleAnalysisComplete}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Visualization Panel */}
            <VisualizationPanel
              selectedText={selectedText}
              analysisData={analysisData}
              onGenerate={handleGenerate}
            />

            {/* Realtime Status */}
            <RealtimeStatus
              onTextSelected={(data) => console.log('Text selected by user:', data)}
              onAnalysisUpdate={(data) => console.log('Analysis update:', data)}
              onGenerationUpdate={(data) => console.log('Generation update:', data)}
            />
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="text-center">
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Sparkles className="h-6 w-6 text-indigo-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">IA Avançada</h3>
            <p className="text-gray-600">
              Powered by GPT-4 e VEO3 para gerar visualizações precisas e educativas
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Play className="h-6 w-6 text-indigo-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Tempo Real</h3>
            <p className="text-gray-600">
              Visualizações geradas instantaneamente conforme você estuda
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Download className="h-6 w-6 text-indigo-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Exportação</h3>
            <p className="text-gray-600">
              Baixe vídeos e imagens para usar em apresentações e estudos
            </p>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <button className="px-8 py-4 bg-indigo-600 text-white text-lg font-semibold rounded-lg hover:bg-indigo-700 transition-colors">
            Começar Agora - Grátis
          </button>
          <p className="text-sm text-gray-600 mt-2">
            10 visualizações gratuitas • Sem cartão de crédito
          </p>
        </div>
      </main>
    </div>
  );
}
