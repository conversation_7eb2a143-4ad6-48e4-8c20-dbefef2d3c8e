# Production Environment Variables
# Copy this file to .env.local and fill in your actual values

# OpenAI Configuration
OPENAI_API_KEY=

# Application URL (replace with your actual domain)
NEXT_PUBLIC_APP_URL=https://studyvision.vercel.app

# VEO3 Configuration (when available)
VEO3_API_KEY=

# Imagen Configuration (when available)
IMAGEN_API_KEY=

# Analytics (optional)
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=
NEXT_PUBLIC_GA_MEASUREMENT_ID=

# Error Tracking (optional)
SENTRY_DSN=
SENTRY_ORG=
SENTRY_PROJECT=

# Database (if using)
DATABASE_URL=
REDIS_URL=

# Security
NEXTAUTH_SECRET=
NEXTAUTH_URL=

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_TRACKING=true
NEXT_PUBLIC_ENABLE_WEBSOCKETS=true
