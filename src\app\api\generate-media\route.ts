import { NextRequest, NextResponse } from 'next/server';
import { generateMedia, validateMediaRequest, enhancePromptForMedia } from '@/lib/media-generation';
import type { MediaGenerationRequest } from '@/lib/media-generation';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { prompt, type, style, duration, dimensions } = body;

    // Create media generation request
    const mediaRequest: MediaGenerationRequest = {
      prompt,
      type,
      style: style || 'educational',
      duration: duration || 5,
      dimensions: dimensions || { width: 800, height: 600 }
    };

    // Validate request
    const validation = validateMediaRequest(mediaRequest);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // Enhance prompt for better results
    const enhancedPrompt = enhancePromptForMedia(
      mediaRequest.prompt,
      mediaRequest.type,
      mediaRequest.style
    );

    // Update request with enhanced prompt
    mediaRequest.prompt = enhancedPrompt;

    // Generate media
    const result = await generateMedia(mediaRequest);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          url: result.url,
          metadata: result.metadata
        }
      });
    } else {
      return NextResponse.json(
        { 
          error: result.error || 'Media generation failed',
          metadata: result.metadata
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in generate-media API:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate media',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
