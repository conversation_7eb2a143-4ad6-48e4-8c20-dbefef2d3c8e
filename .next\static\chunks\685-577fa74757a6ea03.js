(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[685],{156:(t,e,r)=>{"use strict";function n(t){this.content=t}r.d(e,{S4:()=>J,ZF:()=>G,FK:()=>i,CU:()=>l,sX:()=>j,bP:()=>x,u$:()=>k,vI:()=>h,Sj:()=>P,Ji:()=>c}),n.prototype={constructor:n,find:function(t){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===t)return e;return -1},get:function(t){var e=this.find(t);return -1==e?void 0:this.content[e+1]},update:function(t,e,r){var i=r&&r!=t?this.remove(r):this,o=i.find(t),s=i.content.slice();return -1==o?s.push(r||t,e):(s[o+1]=e,r&&(s[o]=r)),new n(s)},remove:function(t){var e=this.find(t);if(-1==e)return this;var r=this.content.slice();return r.splice(e,2),new n(r)},addToStart:function(t,e){return new n([t,e].concat(this.remove(t).content))},addToEnd:function(t,e){var r=this.remove(t).content.slice();return r.push(t,e),new n(r)},addBefore:function(t,e,r){var i=this.remove(e),o=i.content.slice(),s=i.find(t);return o.splice(-1==s?o.length:s,0,e,r),new n(o)},forEach:function(t){for(var e=0;e<this.content.length;e+=2)t(this.content[e],this.content[e+1])},prepend:function(t){return(t=n.from(t)).size?new n(t.content.concat(this.subtract(t).content)):this},append:function(t){return(t=n.from(t)).size?new n(this.subtract(t).content.concat(t.content)):this},subtract:function(t){var e=this;t=n.from(t);for(var r=0;r<t.content.length;r+=2)e=e.remove(t.content[r]);return e},toObject:function(){var t={};return this.forEach(function(e,r){t[e]=r}),t},get size(){return this.content.length>>1}},n.from=function(t){if(t instanceof n)return t;var e=[];if(t)for(var r in t)e.push(r,t[r]);return new n(e)};class i{constructor(t,e){if(this.content=t,this.size=e||0,null==e)for(let e=0;e<t.length;e++)this.size+=t[e].nodeSize}nodesBetween(t,e,r,n=0,i){for(let o=0,s=0;s<e;o++){let a=this.content[o],l=s+a.nodeSize;if(l>t&&!1!==r(a,n+s,i||null,o)&&a.content.size){let i=s+1;a.nodesBetween(Math.max(0,t-i),Math.min(a.content.size,e-i),r,n+i)}s=l}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,r,n){let i="",o=!0;return this.nodesBetween(t,e,(s,a)=>{let l=s.isText?s.text.slice(Math.max(t,a)-a,e-a):s.isLeaf?n?"function"==typeof n?n(s):n:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&l||s.isTextblock)&&r&&(o?o=!1:i+=r),i+=l},0),i}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,r=t.firstChild,n=this.content.slice(),o=0;for(e.isText&&e.sameMarkup(r)&&(n[n.length-1]=e.withText(e.text+r.text),o=1);o<t.content.length;o++)n.push(t.content[o]);return new i(n,this.size+t.size)}cut(t,e=this.size){if(0==t&&e==this.size)return this;let r=[],n=0;if(e>t)for(let i=0,o=0;o<e;i++){let s=this.content[i],a=o+s.nodeSize;a>t&&((o<t||a>e)&&(s=s.isText?s.cut(Math.max(0,t-o),Math.min(s.text.length,e-o)):s.cut(Math.max(0,t-o-1),Math.min(s.content.size,e-o-1))),r.push(s),n+=s.nodeSize),o=a}return new i(r,n)}cutByIndex(t,e){return t==e?i.empty:0==t&&e==this.content.length?this:new i(this.content.slice(t,e))}replaceChild(t,e){let r=this.content[t];if(r==e)return this;let n=this.content.slice(),o=this.size+e.nodeSize-r.nodeSize;return n[t]=e,new i(n,o)}addToStart(t){return new i([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new i(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let e=0;e<this.content.length;e++)if(!this.content[e].eq(t.content[e]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let e=0,r=0;e<this.content.length;e++){let n=this.content[e];t(n,r,e),r+=n.nodeSize}}findDiffStart(t,e=0){return function t(e,r,n){for(let i=0;;i++){if(i==e.childCount||i==r.childCount)return e.childCount==r.childCount?null:n;let o=e.child(i),s=r.child(i);if(o==s){n+=o.nodeSize;continue}if(!o.sameMarkup(s))return n;if(o.isText&&o.text!=s.text){for(let t=0;o.text[t]==s.text[t];t++)n++;return n}if(o.content.size||s.content.size){let e=t(o.content,s.content,n+1);if(null!=e)return e}n+=o.nodeSize}}(this,t,e)}findDiffEnd(t,e=this.size,r=t.size){return function t(e,r,n,i){for(let o=e.childCount,s=r.childCount;;){if(0==o||0==s)return o==s?null:{a:n,b:i};let a=e.child(--o),l=r.child(--s),h=a.nodeSize;if(a==l){n-=h,i-=h;continue}if(!a.sameMarkup(l))return{a:n,b:i};if(a.isText&&a.text!=l.text){let t=0,e=Math.min(a.text.length,l.text.length);for(;t<e&&a.text[a.text.length-t-1]==l.text[l.text.length-t-1];)t++,n--,i--;return{a:n,b:i}}if(a.content.size||l.content.size){let e=t(a.content,l.content,n-1,i-1);if(e)return e}n-=h,i-=h}}(this,t,e,r)}findIndex(t,e=-1){if(0==t)return s(0,t);if(t==this.size)return s(this.content.length,t);if(t>this.size||t<0)throw RangeError(`Position ${t} outside of fragment (${this})`);for(let r=0,n=0;;r++){let i=n+this.child(r).nodeSize;if(i>=t){if(i==t||e>0)return s(r+1,i);return s(r,n)}n=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(t=>t.toJSON()):null}static fromJSON(t,e){if(!e)return i.empty;if(!Array.isArray(e))throw RangeError("Invalid input for Fragment.fromJSON");return new i(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return i.empty;let e,r=0;for(let n=0;n<t.length;n++){let i=t[n];r+=i.nodeSize,n&&i.isText&&t[n-1].sameMarkup(i)?(e||(e=t.slice(0,n)),e[e.length-1]=i.withText(e[e.length-1].text+i.text)):e&&e.push(i)}return new i(e||t,r)}static from(t){if(!t)return i.empty;if(t instanceof i)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new i([t],t.nodeSize);throw RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}i.empty=new i([],0);let o={index:0,offset:0};function s(t,e){return o.index=t,o.offset=e,o}function a(t,e){if(t===e)return!0;if(!(t&&"object"==typeof t)||!(e&&"object"==typeof e))return!1;let r=Array.isArray(t);if(Array.isArray(e)!=r)return!1;if(r){if(t.length!=e.length)return!1;for(let r=0;r<t.length;r++)if(!a(t[r],e[r]))return!1}else{for(let r in t)if(!(r in e)||!a(t[r],e[r]))return!1;for(let r in e)if(!(r in t))return!1}return!0}class l{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,r=!1;for(let n=0;n<t.length;n++){let i=t[n];if(this.eq(i))return t;if(this.type.excludes(i.type))e||(e=t.slice(0,n));else{if(i.type.excludes(this.type))return t;!r&&i.type.rank>this.type.rank&&(e||(e=t.slice(0,n)),e.push(this),r=!0),e&&e.push(i)}}return e||(e=t.slice()),r||e.push(this),e}removeFromSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return t.slice(0,e).concat(t.slice(e+1));return t}isInSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return!0;return!1}eq(t){return this==t||this.type==t.type&&a(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw RangeError("Invalid input for Mark.fromJSON");let r=t.marks[e.type];if(!r)throw RangeError(`There is no mark type ${e.type} in this schema`);let n=r.create(e.attrs);return r.checkAttrs(n.attrs),n}static sameSet(t,e){if(t==e)return!0;if(t.length!=e.length)return!1;for(let r=0;r<t.length;r++)if(!t[r].eq(e[r]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return l.none;if(t instanceof l)return[t];let e=t.slice();return e.sort((t,e)=>t.type.rank-e.type.rank),e}}l.none=[];class h extends Error{}class c{constructor(t,e,r){this.content=t,this.openStart=e,this.openEnd=r}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let r=function t(e,r,n,i){let{index:o,offset:s}=e.findIndex(r),a=e.maybeChild(o);if(s==r||a.isText)return i&&!i.canReplace(o,o,n)?null:e.cut(0,r).append(n).append(e.cut(r));let l=t(a.content,r-s-1,n);return l&&e.replaceChild(o,a.copy(l))}(this.content,t+this.openStart,e);return r&&new c(r,this.openStart,this.openEnd)}removeBetween(t,e){return new c(function t(e,r,n){let{index:i,offset:o}=e.findIndex(r),s=e.maybeChild(i),{index:a,offset:l}=e.findIndex(n);if(o==r||s.isText){if(l!=n&&!e.child(a).isText)throw RangeError("Removing non-flat range");return e.cut(0,r).append(e.cut(n))}if(i!=a)throw RangeError("Removing non-flat range");return e.replaceChild(i,s.copy(t(s.content,r-o-1,n-o-1)))}(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return c.empty;let r=e.openStart||0,n=e.openEnd||0;if("number"!=typeof r||"number"!=typeof n)throw RangeError("Invalid input for Slice.fromJSON");return new c(i.fromJSON(t,e.content),r,n)}static maxOpen(t,e=!0){let r=0,n=0;for(let n=t.firstChild;n&&!n.isLeaf&&(e||!n.type.spec.isolating);n=n.firstChild)r++;for(let r=t.lastChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.lastChild)n++;return new c(t,r,n)}}function u(t,e){if(!e.type.compatibleContent(t.type))throw new h("Cannot join "+e.type.name+" onto "+t.type.name)}function p(t,e,r){let n=t.node(r);return u(n,e.node(r)),n}function f(t,e){let r=e.length-1;r>=0&&t.isText&&t.sameMarkup(e[r])?e[r]=t.withText(e[r].text+t.text):e.push(t)}function d(t,e,r,n){let i=(e||t).node(r),o=0,s=e?e.index(r):i.childCount;t&&(o=t.index(r),t.depth>r?o++:t.textOffset&&(f(t.nodeAfter,n),o++));for(let t=o;t<s;t++)f(i.child(t),n);e&&e.depth==r&&e.textOffset&&f(e.nodeBefore,n)}function m(t,e){return t.type.checkContent(e),t.copy(e)}function g(t,e,r){let n=[];return d(null,t,r,n),t.depth>r&&f(m(p(t,e,r+1),g(t,e,r+1)),n),d(e,null,r,n),new i(n)}c.empty=new c(i.empty,0,0);class y{constructor(t,e,r){this.pos=t,this.path=e,this.parentOffset=r,this.depth=e.length/3-1}resolveDepth(t){return null==t?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[3*this.resolveDepth(t)]}index(t){return this.path[3*this.resolveDepth(t)+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t!=this.depth||this.textOffset?1:0)}start(t){return 0==(t=this.resolveDepth(t))?0:this.path[3*t-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(!(t=this.resolveDepth(t)))throw RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]}after(t){if(!(t=this.resolveDepth(t)))throw RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]+this.path[3*t].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let r=this.pos-this.path[this.path.length-1],n=t.child(e);return r?t.child(e).cut(r):n}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):0==t?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let r=this.path[3*e],n=0==e?0:this.path[3*e-1]+1;for(let e=0;e<t;e++)n+=r.child(e).nodeSize;return n}marks(){let t=this.parent,e=this.index();if(0==t.content.size)return l.none;if(this.textOffset)return t.child(e).marks;let r=t.maybeChild(e-1),n=t.maybeChild(e);if(!r){let t=r;r=n,n=t}let i=r.marks;for(var o=0;o<i.length;o++)!1!==i[o].type.spec.inclusive||n&&i[o].isInSet(n.marks)||(i=i[o--].removeFromSet(i));return i}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let r=e.marks,n=t.parent.maybeChild(t.index());for(var i=0;i<r.length;i++)!1!==r[i].type.spec.inclusive||n&&r[i].isInSet(n.marks)||(r=r[i--].removeFromSet(r));return r}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let r=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);r>=0;r--)if(t.pos<=this.end(r)&&(!e||e(this.node(r))))return new k(this,t,r);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let e=1;e<=this.depth;e++)t+=(t?"/":"")+this.node(e).type.name+"_"+this.index(e-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw RangeError("Position "+e+" out of range");let r=[],n=0,i=e;for(let e=t;;){let{index:t,offset:o}=e.content.findIndex(i),s=i-o;if(r.push(e,t,n+o),!s||(e=e.child(t)).isText)break;i=s-1,n+=o+1}return new y(e,r,i)}static resolveCached(t,e){let r=v.get(t);if(r)for(let t=0;t<r.elts.length;t++){let n=r.elts[t];if(n.pos==e)return n}else v.set(t,r=new w);let n=r.elts[r.i]=y.resolve(t,e);return r.i=(r.i+1)%b,n}}class w{constructor(){this.elts=[],this.i=0}}let b=12,v=new WeakMap;class k{constructor(t,e,r){this.$from=t,this.$to=e,this.depth=r}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let S=Object.create(null);class x{constructor(t,e,r,n=l.none){this.type=t,this.attrs=e,this.marks=n,this.content=r||i.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,r,n=0){this.content.nodesBetween(t,e,r,n,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,r,n){return this.content.textBetween(t,e,r,n)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,r){return this.type==t&&a(this.attrs,e||t.defaultAttrs||S)&&l.sameSet(this.marks,r||l.none)}copy(t=null){return t==this.content?this:new x(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new x(this.type,this.attrs,this.content,t)}cut(t,e=this.content.size){return 0==t&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,r=!1){if(t==e)return c.empty;let n=this.resolve(t),i=this.resolve(e),o=r?0:n.sharedDepth(e),s=n.start(o);return new c(n.node(o).content.cut(n.pos-s,i.pos-s),n.depth-o,i.depth-o)}replace(t,e,r){var n=this.resolve(t),o=this.resolve(e);if(r.openStart>n.depth)throw new h("Inserted content deeper than insertion position");if(n.depth-r.openStart!=o.depth-r.openEnd)throw new h("Inconsistent open depths");return function t(e,r,n,o){let s=e.index(o),a=e.node(o);if(s==r.index(o)&&o<e.depth-n.openStart){let i=t(e,r,n,o+1);return a.copy(a.content.replaceChild(s,i))}if(!n.content.size)return m(a,g(e,r,o));if(n.openStart||n.openEnd||e.depth!=o||r.depth!=o){let{start:t,end:s}=function(t,e){let r=e.depth-t.openStart,n=e.node(r).copy(t.content);for(let t=r-1;t>=0;t--)n=e.node(t).copy(i.from(n));return{start:n.resolveNoCache(t.openStart+r),end:n.resolveNoCache(n.content.size-t.openEnd-r)}}(n,e);return m(a,function t(e,r,n,o,s){let a=e.depth>s&&p(e,r,s+1),l=o.depth>s&&p(n,o,s+1),h=[];return d(null,e,s,h),a&&l&&r.index(s)==n.index(s)?(u(a,l),f(m(a,t(e,r,n,o,s+1)),h)):(a&&f(m(a,g(e,r,s+1)),h),d(r,n,s,h),l&&f(m(l,g(n,o,s+1)),h)),d(o,null,s,h),new i(h)}(e,t,s,r,o))}{let t=e.parent,i=t.content;return m(t,i.cut(0,e.parentOffset).append(n.content).append(i.cut(r.parentOffset)))}}(n,o,r,0)}nodeAt(t){for(let e=this;;){let{index:r,offset:n}=e.content.findIndex(t);if(!(e=e.maybeChild(r)))return null;if(n==t||e.isText)return e;t-=n+1}}childAfter(t){let{index:e,offset:r}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:r}}childBefore(t){if(0==t)return{node:null,index:0,offset:0};let{index:e,offset:r}=this.content.findIndex(t);if(r<t)return{node:this.content.child(e),index:e,offset:r};let n=this.content.child(e-1);return{node:n,index:e-1,offset:r-n.nodeSize}}resolve(t){return y.resolveCached(this,t)}resolveNoCache(t){return y.resolve(this,t)}rangeHasMark(t,e,r){let n=!1;return e>t&&this.nodesBetween(t,e,t=>(r.isInSet(t.marks)&&(n=!0),!n)),n}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),E(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,r=i.empty,n=0,o=r.childCount){let s=this.contentMatchAt(t).matchFragment(r,n,o),a=s&&s.matchFragment(this.content,e);if(!a||!a.validEnd)return!1;for(let t=n;t<o;t++)if(!this.type.allowsMarks(r.child(t).marks))return!1;return!0}canReplaceWith(t,e,r,n){if(n&&!this.type.allowsMarks(n))return!1;let i=this.contentMatchAt(t).matchType(r),o=i&&i.matchFragment(this.content,e);return!!o&&o.validEnd}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let t=l.none;for(let e=0;e<this.marks.length;e++){let r=this.marks[e];r.type.checkAttrs(r.attrs),t=r.addToSet(t)}if(!l.sameSet(t,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map(t=>t.toJSON())),t}static fromJSON(t,e){let r;if(!e)throw RangeError("Invalid input for Node.fromJSON");if(e.marks){if(!Array.isArray(e.marks))throw RangeError("Invalid mark data for Node.fromJSON");r=e.marks.map(t.markFromJSON)}if("text"==e.type){if("string"!=typeof e.text)throw RangeError("Invalid text node in JSON");return t.text(e.text,r)}let n=i.fromJSON(t,e.content),o=t.nodeType(e.type).create(e.attrs,n,r);return o.type.checkAttrs(o.attrs),o}}x.prototype.text=void 0;class A extends x{constructor(t,e,r,n){if(super(t,e,null,n),!r)throw RangeError("Empty text nodes are not allowed");this.text=r}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):E(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new A(this.type,this.attrs,this.text,t)}withText(t){return t==this.text?this:new A(this.type,this.attrs,t,this.marks)}cut(t=0,e=this.text.length){return 0==t&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let t=super.toJSON();return t.text=this.text,t}}function E(t,e){for(let r=t.length-1;r>=0;r--)e=t[r].type.name+"("+e+")";return e}class C{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){var r;let n,i=new M(t,e);if(null==i.next)return C.empty;let o=function t(e){let r=[];do r.push(function(e){let r=[];do r.push(function(e){let r=function(e){if(e.eat("(")){let r=t(e);return e.eat(")")||e.err("Missing closing paren"),r}if(/\W/.test(e.next))e.err("Unexpected token '"+e.next+"'");else{let t=(function(t,e){let r=t.nodeTypes,n=r[e];if(n)return[n];let i=[];for(let t in r){let n=r[t];n.isInGroup(e)&&i.push(n)}return 0==i.length&&t.err("No node type or group '"+e+"' found"),i})(e,e.next).map(t=>(null==e.inline?e.inline=t.isInline:e.inline!=t.isInline&&e.err("Mixing inline and block content"),{type:"name",value:t}));return e.pos++,1==t.length?t[0]:{type:"choice",exprs:t}}}(e);for(;;)if(e.eat("+"))r={type:"plus",expr:r};else if(e.eat("*"))r={type:"star",expr:r};else if(e.eat("?"))r={type:"opt",expr:r};else if(e.eat("{"))r=function(t,e){let r=T(t),n=r;return t.eat(",")&&(n="}"!=t.next?T(t):-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:r,max:n,expr:e}}(e,r);else break;return r}(e));while(e.next&&")"!=e.next&&"|"!=e.next);return 1==r.length?r[0]:{type:"seq",exprs:r}}(e));while(e.eat("|"));return 1==r.length?r[0]:{type:"choice",exprs:r}}(i);i.next&&i.err("Unexpected trailing text");let s=(r=function(t){let e=[[]];return i(function t(e,o){if("choice"==e.type)return e.exprs.reduce((e,r)=>e.concat(t(r,o)),[]);if("seq"==e.type)for(let n=0;;n++){let s=t(e.exprs[n],o);if(n==e.exprs.length-1)return s;i(s,o=r())}else if("star"==e.type){let s=r();return n(o,s),i(t(e.expr,s),s),[n(s)]}else if("plus"==e.type){let s=r();return i(t(e.expr,o),s),i(t(e.expr,s),s),[n(s)]}else if("opt"==e.type)return[n(o)].concat(t(e.expr,o));else if("range"==e.type){let s=o;for(let n=0;n<e.min;n++){let n=r();i(t(e.expr,s),n),s=n}if(-1==e.max)i(t(e.expr,s),s);else for(let o=e.min;o<e.max;o++){let o=r();n(s,o),i(t(e.expr,s),o),s=o}return[n(s)]}else if("name"==e.type)return[n(o,void 0,e.value)];else throw Error("Unknown expr type")}(t,0),r()),e;function r(){return e.push([])-1}function n(t,r,n){let i={term:n,to:r};return e[t].push(i),i}function i(t,e){t.forEach(t=>t.to=e)}}(o),n=Object.create(null),function t(e){let i=[];e.forEach(t=>{r[t].forEach(({term:t,to:e})=>{let n;if(t){for(let e=0;e<i.length;e++)i[e][0]==t&&(n=i[e][1]);R(r,e).forEach(e=>{n||i.push([t,n=[]]),-1==n.indexOf(e)&&n.push(e)})}})});let o=n[e.join(",")]=new C(e.indexOf(r.length-1)>-1);for(let e=0;e<i.length;e++){let r=i[e][1].sort(O);o.next.push({type:i[e][0],next:n[r.join(",")]||t(r)})}return o}(R(r,0)));return function(t,e){for(let r=0,n=[t];r<n.length;r++){let t=n[r],i=!t.validEnd,o=[];for(let e=0;e<t.next.length;e++){let{type:r,next:s}=t.next[e];o.push(r.name),i&&!(r.isText||r.hasRequiredAttrs())&&(i=!1),-1==n.indexOf(s)&&n.push(s)}i&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(s,i),s}matchType(t){for(let e=0;e<this.next.length;e++)if(this.next[e].type==t)return this.next[e].next;return null}matchFragment(t,e=0,r=t.childCount){let n=this;for(let i=e;n&&i<r;i++)n=n.matchType(t.child(i).type);return n}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let t=0;t<this.next.length;t++){let{type:e}=this.next[t];if(!(e.isText||e.hasRequiredAttrs()))return e}return null}compatible(t){for(let e=0;e<this.next.length;e++)for(let r=0;r<t.next.length;r++)if(this.next[e].type==t.next[r].type)return!0;return!1}fillBefore(t,e=!1,r=0){let n=[this];return function o(s,a){let l=s.matchFragment(t,r);if(l&&(!e||l.validEnd))return i.from(a.map(t=>t.createAndFill()));for(let t=0;t<s.next.length;t++){let{type:e,next:r}=s.next[t];if(!(e.isText||e.hasRequiredAttrs())&&-1==n.indexOf(r)){n.push(r);let t=o(r,a.concat(e));if(t)return t}}return null}(this,[])}findWrapping(t){for(let e=0;e<this.wrapCache.length;e+=2)if(this.wrapCache[e]==t)return this.wrapCache[e+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),r=[{match:this,type:null,via:null}];for(;r.length;){let n=r.shift(),i=n.match;if(i.matchType(t)){let t=[];for(let e=n;e.type;e=e.via)t.push(e.type);return t.reverse()}for(let t=0;t<i.next.length;t++){let{type:o,next:s}=i.next[t];o.isLeaf||o.hasRequiredAttrs()||o.name in e||n.type&&!s.validEnd||(r.push({match:o.contentMatch,type:o,via:n}),e[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];return!function e(r){t.push(r);for(let n=0;n<r.next.length;n++)-1==t.indexOf(r.next[n].next)&&e(r.next[n].next)}(this),t.map((e,r)=>{let n=r+(e.validEnd?"*":" ")+" ";for(let r=0;r<e.next.length;r++)n+=(r?", ":"")+e.next[r].type.name+"->"+t.indexOf(e.next[r].next);return n}).join("\n")}}C.empty=new C(!0);class M{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw SyntaxError(t+" (in content expression '"+this.string+"')")}}function T(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function O(t,e){return e-t}function R(t,e){let r=[];return function e(n){let i=t[n];if(1==i.length&&!i[0].term)return e(i[0].to);r.push(n);for(let t=0;t<i.length;t++){let{term:n,to:o}=i[t];n||-1!=r.indexOf(o)||e(o)}}(e),r.sort(O)}function N(t){let e=Object.create(null);for(let r in t){let n=t[r];if(!n.hasDefault)return null;e[r]=n.default}return e}function I(t,e){let r=Object.create(null);for(let n in t){let i=e&&e[n];if(void 0===i){let e=t[n];if(e.hasDefault)i=e.default;else throw RangeError("No value supplied for attribute "+n)}r[n]=i}return r}function B(t,e,r,n){for(let n in e)if(!(n in t))throw RangeError(`Unsupported attribute ${n} for ${r} of type ${n}`);for(let r in t){let n=t[r];n.validate&&n.validate(e[r])}}function L(t,e){let r=Object.create(null);if(e)for(let n in e)r[n]=new F(t,n,e[n]);return r}class z{constructor(t,e,r){this.name=t,this.schema=e,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=L(t,r.attrs),this.defaultAttrs=N(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||"text"==t),this.isText="text"==t}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==C.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(t){return this.groups.indexOf(t)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:I(this.attrs,t)}create(t=null,e,r){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new x(this,this.computeAttrs(t),i.from(e),l.setFrom(r))}createChecked(t=null,e,r){return e=i.from(e),this.checkContent(e),new x(this,this.computeAttrs(t),e,l.setFrom(r))}createAndFill(t=null,e,r){if(t=this.computeAttrs(t),(e=i.from(e)).size){let t=this.contentMatch.fillBefore(e);if(!t)return null;e=t.append(e)}let n=this.contentMatch.matchFragment(e),o=n&&n.fillBefore(i.empty,!0);return o?new x(this,t,e.append(o),l.setFrom(r)):null}validContent(t){let e=this.contentMatch.matchFragment(t);if(!e||!e.validEnd)return!1;for(let e=0;e<t.childCount;e++)if(!this.allowsMarks(t.child(e).marks))return!1;return!0}checkContent(t){if(!this.validContent(t))throw RangeError(`Invalid content for node ${this.name}: ${t.toString().slice(0,50)}`)}checkAttrs(t){B(this.attrs,t,"node",this.name)}allowsMarkType(t){return null==this.markSet||this.markSet.indexOf(t)>-1}allowsMarks(t){if(null==this.markSet)return!0;for(let e=0;e<t.length;e++)if(!this.allowsMarkType(t[e].type))return!1;return!0}allowedMarks(t){let e;if(null==this.markSet)return t;for(let r=0;r<t.length;r++)this.allowsMarkType(t[r].type)?e&&e.push(t[r]):e||(e=t.slice(0,r));return e?e.length?e:l.none:t}static compile(t,e){let r=Object.create(null);t.forEach((t,n)=>r[t]=new z(t,e,n));let n=e.spec.topNode||"doc";if(!r[n])throw RangeError("Schema is missing its top node type ('"+n+"')");if(!r.text)throw RangeError("Every schema needs a 'text' type");for(let t in r.text.attrs)throw RangeError("The text node type should not have attributes");return r}}class F{constructor(t,e,r){this.hasDefault=Object.prototype.hasOwnProperty.call(r,"default"),this.default=r.default,this.validate="string"==typeof r.validate?function(t,e,r){let n=r.split("|");return r=>{let i=null===r?"null":typeof r;if(0>n.indexOf(i))throw RangeError(`Expected value of type ${n} for attribute ${e} on type ${t}, got ${i}`)}}(t,e,r.validate):r.validate}get isRequired(){return!this.hasDefault}}class j{constructor(t,e,r,n){this.name=t,this.rank=e,this.schema=r,this.spec=n,this.attrs=L(t,n.attrs),this.excluded=null;let i=N(this.attrs);this.instance=i?new l(this,i):null}create(t=null){return!t&&this.instance?this.instance:new l(this,I(this.attrs,t))}static compile(t,e){let r=Object.create(null),n=0;return t.forEach((t,i)=>r[t]=new j(t,n++,e,i)),r}removeFromSet(t){for(var e=0;e<t.length;e++)t[e].type==this&&(t=t.slice(0,e).concat(t.slice(e+1)),e--);return t}isInSet(t){for(let e=0;e<t.length;e++)if(t[e].type==this)return t[e]}checkAttrs(t){B(this.attrs,t,"mark",this.name)}excludes(t){return this.excluded.indexOf(t)>-1}}class P{constructor(t){this.linebreakReplacement=null,this.cached=Object.create(null);let e=this.spec={};for(let r in t)e[r]=t[r];e.nodes=n.from(t.nodes),e.marks=n.from(t.marks||{}),this.nodes=z.compile(this.spec.nodes,this),this.marks=j.compile(this.spec.marks,this);let r=Object.create(null);for(let t in this.nodes){if(t in this.marks)throw RangeError(t+" can not be both a node and a mark");let e=this.nodes[t],n=e.spec.content||"",i=e.spec.marks;if(e.contentMatch=r[n]||(r[n]=C.parse(n,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==i?null:i?D(this,i.split(" ")):""!=i&&e.inlineContent?null:[]}for(let t in this.marks){let e=this.marks[t],r=e.spec.excludes;e.excluded=null==r?[e]:""==r?[]:D(this,r.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,r,n){if("string"==typeof t)t=this.nodeType(t);else if(t instanceof z){if(t.schema!=this)throw RangeError("Node type from different schema used ("+t.name+")")}else throw RangeError("Invalid node type: "+t);return t.createChecked(e,r,n)}text(t,e){let r=this.nodes.text;return new A(r,r.defaultAttrs,t,l.setFrom(e))}mark(t,e){return"string"==typeof t&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return x.fromJSON(this,t)}markFromJSON(t){return l.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw RangeError("Unknown node type: "+t);return e}}function D(t,e){let r=[];for(let n=0;n<e.length;n++){let i=e[n],o=t.marks[i],s=o;if(o)r.push(o);else for(let e in t.marks){let n=t.marks[e];("_"==i||n.spec.group&&n.spec.group.split(" ").indexOf(i)>-1)&&r.push(s=n)}if(!s)throw SyntaxError("Unknown mark type: '"+e[n]+"'")}return r}class J{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[];let r=this.matchedStyles=[];e.forEach(t=>{if(null!=t.tag)this.tags.push(t);else if(null!=t.style){let e=/[^=]*/.exec(t.style)[0];0>r.indexOf(e)&&r.push(e),this.styles.push(t)}}),this.normalizeLists=!this.tags.some(e=>{if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;let r=t.nodes[e.node];return r.contentMatch.matchType(r)})}parse(t,e={}){let r=new q(this,e,!1);return r.addAll(t,l.none,e.from,e.to),r.finish()}parseSlice(t,e={}){let r=new q(this,e,!0);return r.addAll(t,l.none,e.from,e.to),c.maxOpen(r.finish())}matchTag(t,e,r){for(let o=r?this.tags.indexOf(r)+1:0;o<this.tags.length;o++){var n,i;let r=this.tags[o];if(n=t,i=r.tag,(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,i)&&(void 0===r.namespace||t.namespaceURI==r.namespace)&&(!r.context||e.matchesContext(r.context))){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}matchStyle(t,e,r,n){for(let i=n?this.styles.indexOf(n)+1:0;i<this.styles.length;i++){let n=this.styles[i],o=n.style;if(0==o.indexOf(t)&&(!n.context||r.matchesContext(n.context))&&(!(o.length>t.length)||61==o.charCodeAt(t.length)&&o.slice(t.length+1)==e)){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}static schemaRules(t){let e=[];function r(t){let r=null==t.priority?50:t.priority,n=0;for(;n<e.length;n++){let t=e[n];if((null==t.priority?50:t.priority)<r)break}e.splice(n,0,t)}for(let e in t.marks){let n=t.marks[e].spec.parseDOM;n&&n.forEach(t=>{r(t=V(t)),t.mark||t.ignore||t.clearMark||(t.mark=e)})}for(let e in t.nodes){let n=t.nodes[e].spec.parseDOM;n&&n.forEach(t=>{r(t=V(t)),t.node||t.ignore||t.mark||(t.node=e)})}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new J(t,J.schemaRules(t)))}}let U={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},_={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},$={ol:!0,ul:!0};function K(t,e,r){return null!=e?!!e|2*("full"===e):t&&"pre"==t.whitespace?3:-5&r}class H{constructor(t,e,r,n,i,o){this.type=t,this.attrs=e,this.marks=r,this.solid=n,this.options=o,this.content=[],this.activeMarks=l.none,this.match=i||(4&o?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(i.from(t));if(e)this.match=this.type.contentMatch.matchFragment(e);else{let e=this.type.contentMatch,r;return(r=e.findWrapping(t.type))?(this.match=e,r):null}}return this.match.findWrapping(t.type)}finish(t){if(!(1&this.options)){let t=this.content[this.content.length-1],e;t&&t.isText&&(e=/[ \t\r\n\u000c]+$/.exec(t.text))&&(t.text.length==e[0].length?this.content.pop():this.content[this.content.length-1]=t.withText(t.text.slice(0,t.text.length-e[0].length)))}let e=i.from(this.content);return!t&&this.match&&(e=e.append(this.match.fillBefore(i.empty,!0))),this.type?this.type.create(this.attrs,e,this.marks):e}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!U.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class q{constructor(t,e,r){this.parser=t,this.options=e,this.isOpen=r,this.open=0,this.localPreserveWS=!1;let n=e.topNode,i,o=K(null,e.preserveWhitespace,0)|4*!!r;i=n?new H(n.type,n.attrs,l.none,!0,e.topMatch||n.type.contentMatch,o):r?new H(null,null,l.none,!0,null,o):new H(t.schema.topNodeType,null,l.none,!0,null,o),this.nodes=[i],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t,e){3==t.nodeType?this.addTextNode(t,e):1==t.nodeType&&this.addElement(t,e)}addTextNode(t,e){let r=t.nodeValue,n=this.top,i=2&n.options?"full":this.localPreserveWS||(1&n.options)>0;if("full"===i||n.inlineContext(t)||/[^ \t\r\n\u000c]/.test(r)){if(i)r="full"!==i?r.replace(/\r?\n|\r/g," "):r.replace(/\r\n?/g,"\n");else if(r=r.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(r)&&this.open==this.nodes.length-1){let e=n.content[n.content.length-1],i=t.previousSibling;(!e||i&&"BR"==i.nodeName||e.isText&&/[ \t\r\n\u000c]$/.test(e.text))&&(r=r.slice(1))}r&&this.insertNode(this.parser.schema.text(r),e,!/\S/.test(r)),this.findInText(t)}else this.findInside(t)}addElement(t,e,r){let n=this.localPreserveWS,i=this.top;("PRE"==t.tagName||/pre/.test(t.style&&t.style.whiteSpace))&&(this.localPreserveWS=!0);let o=t.nodeName.toLowerCase(),s;$.hasOwnProperty(o)&&this.parser.normalizeLists&&function(t){for(let e=t.firstChild,r=null;e;e=e.nextSibling){let t=1==e.nodeType?e.nodeName.toLowerCase():null;t&&$.hasOwnProperty(t)&&r?(r.appendChild(e),e=r):"li"==t?r=e:t&&(r=null)}}(t);let a=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(s=this.parser.matchTag(t,this,r));t:if(a?a.ignore:_.hasOwnProperty(o))this.findInside(t),this.ignoreFallback(t,e);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(t=a.skip);let r,n=this.needsBlock;if(U.hasOwnProperty(o))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),r=!0,i.type||(this.needsBlock=!0);else if(!t.firstChild){this.leafFallback(t,e);break t}let s=a&&a.skip?e:this.readStyles(t,e);s&&this.addAll(t,s),r&&this.sync(i),this.needsBlock=n}else{let r=this.readStyles(t,e);r&&this.addElementByRule(t,a,r,!1===a.consuming?s:void 0)}this.localPreserveWS=n}leafFallback(t,e){"BR"==t.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode("\n"),e)}ignoreFallback(t,e){"BR"!=t.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),e,!0)}readStyles(t,e){let r=t.style;if(r&&r.length)for(let t=0;t<this.parser.matchedStyles.length;t++){let n=this.parser.matchedStyles[t],i=r.getPropertyValue(n);if(i)for(let t;;){let r=this.parser.matchStyle(n,i,this,t);if(!r)break;if(r.ignore)return null;if(e=r.clearMark?e.filter(t=>!r.clearMark(t)):e.concat(this.parser.schema.marks[r.mark].create(r.attrs)),!1===r.consuming)t=r;else break}}return e}addElementByRule(t,e,r,n){let i,o;if(e.node)if((o=this.parser.schema.nodes[e.node]).isLeaf)this.insertNode(o.create(e.attrs),r,"BR"==t.nodeName)||this.leafFallback(t,r);else{let t=this.enter(o,e.attrs||null,r,e.preserveWhitespace);t&&(i=!0,r=t)}else{let t=this.parser.schema.marks[e.mark];r=r.concat(t.create(e.attrs))}let s=this.top;if(o&&o.isLeaf)this.findInside(t);else if(n)this.addElement(t,r,n);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach(t=>this.insertNode(t,r,!1));else{let n=t;"string"==typeof e.contentElement?n=t.querySelector(e.contentElement):"function"==typeof e.contentElement?n=e.contentElement(t):e.contentElement&&(n=e.contentElement),this.findAround(t,n,!0),this.addAll(n,r),this.findAround(t,n,!1)}i&&this.sync(s)&&this.open--}addAll(t,e,r,n){let i=r||0;for(let o=r?t.childNodes[r]:t.firstChild,s=null==n?null:t.childNodes[n];o!=s;o=o.nextSibling,++i)this.findAtPoint(t,i),this.addDOM(o,e);this.findAtPoint(t,i)}findPlace(t,e,r){let n,i;for(let e=this.open,o=0;e>=0;e--){let s=this.nodes[e],a=s.findWrapping(t);if(a&&(!n||n.length>a.length+o)&&(n=a,i=s,!a.length))break;if(s.solid){if(r)break;o+=2}}if(!n)return null;this.sync(i);for(let t=0;t<n.length;t++)e=this.enterInner(n[t],null,e,!1);return e}insertNode(t,e,r){if(t.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&(e=this.enterInner(t,null,e))}let n=this.findPlace(t,e,r);if(n){this.closeExtra();let e=this.top;e.match&&(e.match=e.match.matchType(t.type));let r=l.none;for(let i of n.concat(t.marks))(e.type?e.type.allowsMarkType(i.type):W(i.type,t.type))&&(r=i.addToSet(r));return e.content.push(t.mark(r)),!0}return!1}enter(t,e,r,n){let i=this.findPlace(t.create(e),r,!1);return i&&(i=this.enterInner(t,e,r,!0,n)),i}enterInner(t,e,r,n=!1,i){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(t);let s=K(t,i,o.options);4&o.options&&0==o.content.length&&(s|=4);let a=l.none;return r=r.filter(e=>(o.type?!o.type.allowsMarkType(e.type):!W(e.type,t))||(a=e.addToSet(a),!1)),this.nodes.push(new H(t,e,a,n,null,s)),this.open++,r}closeExtra(t=!1){let e=this.nodes.length-1;if(e>this.open){for(;e>this.open;e--)this.nodes[e-1].content.push(this.nodes[e].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(t){for(let e=this.open;e>=0;e--)if(this.nodes[e]==t)return this.open=e,!0;else this.localPreserveWS&&(this.nodes[e].options|=1);return!1}get currentPos(){this.closeExtra();let t=0;for(let e=this.open;e>=0;e--){let r=this.nodes[e].content;for(let e=r.length-1;e>=0;e--)t+=r[e].nodeSize;e&&t++}return t}findAtPoint(t,e){if(this.find)for(let r=0;r<this.find.length;r++)this.find[r].node==t&&this.find[r].offset==e&&(this.find[r].pos=this.currentPos)}findInside(t){if(this.find)for(let e=0;e<this.find.length;e++)null==this.find[e].pos&&1==t.nodeType&&t.contains(this.find[e].node)&&(this.find[e].pos=this.currentPos)}findAround(t,e,r){if(t!=e&&this.find)for(let n=0;n<this.find.length;n++)null==this.find[n].pos&&1==t.nodeType&&t.contains(this.find[n].node)&&e.compareDocumentPosition(this.find[n].node)&(r?2:4)&&(this.find[n].pos=this.currentPos)}findInText(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].node==t&&(this.find[e].pos=this.currentPos-(t.nodeValue.length-this.find[e].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),r=this.options.context,n=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),i=-(r?r.depth+1:0)+ +!n,o=(t,s)=>{for(;t>=0;t--){let a=e[t];if(""==a){if(t==e.length-1||0==t)continue;for(;s>=i;s--)if(o(t-1,s))return!0;return!1}{let t=s>0||0==s&&n?this.nodes[s].type:r&&s>=i?r.node(s-i).type:null;if(!t||t.name!=a&&!t.isInGroup(a))return!1;s--}}return!0};return o(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let r=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(r&&r.isTextblock&&r.defaultAttrs)return r}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}}function V(t){let e={};for(let r in t)e[r]=t[r];return e}function W(t,e){let r=e.schema.nodes;for(let n in r){let i=r[n];if(!i.allowsMarkType(t))continue;let o=[],s=t=>{o.push(t);for(let r=0;r<t.edgeCount;r++){let{type:n,next:i}=t.edge(r);if(n==e||0>o.indexOf(i)&&s(i))return!0}};if(s(i.contentMatch))return!0}}class G{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},r){r||(r=Z(e).createDocumentFragment());let n=r,i=[];return t.forEach(t=>{if(i.length||t.marks.length){let r=0,o=0;for(;r<i.length&&o<t.marks.length;){let e=t.marks[o];if(!this.marks[e.type.name]){o++;continue}if(!e.eq(i[r][0])||!1===e.type.spec.spanning)break;r++,o++}for(;r<i.length;)n=i.pop()[1];for(;o<t.marks.length;){let r=t.marks[o++],s=this.serializeMark(r,t.isInline,e);s&&(i.push([r,n]),n.appendChild(s.dom),n=s.contentDOM||s.dom)}}n.appendChild(this.serializeNodeInner(t,e))}),r}serializeNodeInner(t,e){let{dom:r,contentDOM:n}=Q(Z(e),this.nodes[t.type.name](t),null,t.attrs);if(n){if(t.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,n)}return r}serializeNode(t,e={}){let r=this.serializeNodeInner(t,e);for(let n=t.marks.length-1;n>=0;n--){let i=this.serializeMark(t.marks[n],t.isInline,e);i&&((i.contentDOM||i.dom).appendChild(r),r=i.dom)}return r}serializeMark(t,e,r={}){let n=this.marks[t.type.name];return n&&Q(Z(r),n(t,e),null,t.attrs)}static renderSpec(t,e,r=null,n){return Q(t,e,r,n)}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new G(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=X(t.nodes);return e.text||(e.text=t=>t.text),e}static marksFromSchema(t){return X(t.marks)}}function X(t){let e={};for(let r in t){let n=t[r].spec.toDOM;n&&(e[r]=n)}return e}function Z(t){return t.document||window.document}let Y=new WeakMap;function Q(t,e,r,n){let i,o,s;if("string"==typeof e)return{dom:t.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let a=e[0],l;if("string"!=typeof a)throw RangeError("Invalid array passed to renderSpec");if(n&&(void 0===(o=Y.get(n))&&Y.set(n,(s=null,!function t(e){if(e&&"object"==typeof e)if(Array.isArray(e))if("string"==typeof e[0])s||(s=[]),s.push(e);else for(let r=0;r<e.length;r++)t(e[r]);else for(let r in e)t(e[r])}(n),o=s)),l=o)&&l.indexOf(e)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let h=a.indexOf(" ");h>0&&(r=a.slice(0,h),a=a.slice(h+1));let c=r?t.createElementNS(r,a):t.createElement(a),u=e[1],p=1;if(u&&"object"==typeof u&&null==u.nodeType&&!Array.isArray(u)){for(let t in p=2,u)if(null!=u[t]){let e=t.indexOf(" ");e>0?c.setAttributeNS(t.slice(0,e),t.slice(e+1),u[t]):c.setAttribute(t,u[t])}}for(let o=p;o<e.length;o++){let s=e[o];if(0===s){if(o<e.length-1||o>p)throw RangeError("Content hole must be the only child of its parent node");return{dom:c,contentDOM:c}}{let{dom:e,contentDOM:o}=Q(t,s,r,n);if(c.appendChild(e),o){if(i)throw RangeError("Multiple content holes");i=o}}}return{dom:c,contentDOM:i}}},290:(t,e,r)=>{"use strict";r.d(e,{$f:()=>N,G2:()=>v,I$:()=>E,Im:()=>B,Qv:()=>h,Sd:()=>w,Z1:()=>C,_G:()=>u,_e:()=>d,bh:()=>k,eB:()=>c,eT:()=>y,ec:()=>I,hy:()=>T,ic:()=>a,iz:()=>M,pC:()=>x,yY:()=>S,y_:()=>L});var n,i=r(808),o=r(156),s=r(2571);let a=(t,e)=>!t.selection.empty&&(e&&e(t.tr.deleteSelection().scrollIntoView()),!0);function l(t,e){let{$cursor:r}=t.selection;return r&&(e?e.endOfTextblock("backward",t):!(r.parentOffset>0))?r:null}let h=(t,e,r)=>{let n=l(t,r);if(!n)return!1;let a=m(n);if(!a){let r=n.blockRange(),o=r&&(0,i.jP)(r);return null!=o&&(e&&e(t.tr.lift(r,o).scrollIntoView()),!0)}let h=a.nodeBefore;if(O(t,a,e,-1))return!0;if(0==n.parent.content.size&&(f(h,"end")||s.nh.isSelectable(h)))for(let r=n.depth;;r--){let l=(0,i.$L)(t.doc,n.before(r),n.after(r),o.Ji.empty);if(l&&l.slice.size<l.to-l.from){if(e){let r=t.tr.step(l);r.setSelection(f(h,"end")?s.LN.findFrom(r.doc.resolve(r.mapping.map(a.pos,-1)),-1):s.nh.create(r.doc,a.pos-h.nodeSize)),e(r.scrollIntoView())}return!0}if(1==r||n.node(r-1).childCount>1)break}return!!h.isAtom&&a.depth==n.depth-1&&(e&&e(t.tr.delete(a.pos-h.nodeSize,a.pos).scrollIntoView()),!0)},c=(t,e,r)=>{let n=l(t,r);if(!n)return!1;let i=m(n);return!!i&&p(t,i,e)},u=(t,e,r)=>{let n=g(t,r);if(!n)return!1;let i=b(n);return!!i&&p(t,i,e)};function p(t,e,r){let n=e.nodeBefore,a=e.pos-1;for(;!n.isTextblock;a--){if(n.type.spec.isolating)return!1;let t=n.lastChild;if(!t)return!1;n=t}let l=e.nodeAfter,h=e.pos+1;for(;!l.isTextblock;h++){if(l.type.spec.isolating)return!1;let t=l.firstChild;if(!t)return!1;l=t}let c=(0,i.$L)(t.doc,a,h,o.Ji.empty);if(!c||c.from!=a||c instanceof i.Ln&&c.slice.size>=h-a)return!1;if(r){let e=t.tr.step(c);e.setSelection(s.U3.create(e.doc,a)),r(e.scrollIntoView())}return!0}function f(t,e,r=!1){for(let n=t;n;n="start"==e?n.firstChild:n.lastChild){if(n.isTextblock)return!0;if(r&&1!=n.childCount)break}return!1}let d=(t,e,r)=>{let{$head:n,empty:i}=t.selection,o=n;if(!i)return!1;if(n.parent.isTextblock){if(r?!r.endOfTextblock("backward",t):n.parentOffset>0)return!1;o=m(n)}let a=o&&o.nodeBefore;return!!a&&!!s.nh.isSelectable(a)&&(e&&e(t.tr.setSelection(s.nh.create(t.doc,o.pos-a.nodeSize)).scrollIntoView()),!0)};function m(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){if(t.index(e)>0)return t.doc.resolve(t.before(e+1));if(t.node(e).type.spec.isolating)break}return null}function g(t,e){let{$cursor:r}=t.selection;return r&&(e?e.endOfTextblock("forward",t):!(r.parentOffset<r.parent.content.size))?r:null}let y=(t,e,r)=>{let n=g(t,r);if(!n)return!1;let a=b(n);if(!a)return!1;let l=a.nodeAfter;if(O(t,a,e,1))return!0;if(0==n.parent.content.size&&(f(l,"start")||s.nh.isSelectable(l))){let r=(0,i.$L)(t.doc,n.before(),n.after(),o.Ji.empty);if(r&&r.slice.size<r.to-r.from){if(e){let n=t.tr.step(r);n.setSelection(f(l,"start")?s.LN.findFrom(n.doc.resolve(n.mapping.map(a.pos)),1):s.nh.create(n.doc,n.mapping.map(a.pos))),e(n.scrollIntoView())}return!0}}return!!l.isAtom&&a.depth==n.depth-1&&(e&&e(t.tr.delete(a.pos,a.pos+l.nodeSize).scrollIntoView()),!0)},w=(t,e,r)=>{let{$head:n,empty:i}=t.selection,o=n;if(!i)return!1;if(n.parent.isTextblock){if(r?!r.endOfTextblock("forward",t):n.parentOffset<n.parent.content.size)return!1;o=b(n)}let a=o&&o.nodeAfter;return!!a&&!!s.nh.isSelectable(a)&&(e&&e(t.tr.setSelection(s.nh.create(t.doc,o.pos)).scrollIntoView()),!0)};function b(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){let r=t.node(e);if(t.index(e)+1<r.childCount)return t.doc.resolve(t.after(e+1));if(r.type.spec.isolating)break}return null}let v=(t,e)=>{let r=t.selection,n=r instanceof s.nh,o;if(n){if(r.node.isTextblock||!(0,i.n9)(t.doc,r.from))return!1;o=r.from}else if(null==(o=(0,i.N0)(t.doc,r.from,-1)))return!1;if(e){let r=t.tr.join(o);n&&r.setSelection(s.nh.create(r.doc,o-t.doc.resolve(o).nodeBefore.nodeSize)),e(r.scrollIntoView())}return!0},k=(t,e)=>{let r=t.selection,n;if(r instanceof s.nh){if(r.node.isTextblock||!(0,i.n9)(t.doc,r.to))return!1;n=r.to}else if(null==(n=(0,i.N0)(t.doc,r.to,1)))return!1;return e&&e(t.tr.join(n).scrollIntoView()),!0},S=(t,e)=>{let{$from:r,$to:n}=t.selection,o=r.blockRange(n),s=o&&(0,i.jP)(o);return null!=s&&(e&&e(t.tr.lift(o,s).scrollIntoView()),!0)},x=(t,e)=>{let{$head:r,$anchor:n}=t.selection;return!!r.parent.type.spec.code&&!!r.sameParent(n)&&(e&&e(t.tr.insertText("\n").scrollIntoView()),!0)};function A(t){for(let e=0;e<t.edgeCount;e++){let{type:r}=t.edge(e);if(r.isTextblock&&!r.hasRequiredAttrs())return r}return null}let E=(t,e)=>{let{$head:r,$anchor:n}=t.selection;if(!r.parent.type.spec.code||!r.sameParent(n))return!1;let i=r.node(-1),o=r.indexAfter(-1),a=A(i.contentMatchAt(o));if(!a||!i.canReplaceWith(o,o,a))return!1;if(e){let n=r.after(),i=t.tr.replaceWith(n,n,a.createAndFill());i.setSelection(s.LN.near(i.doc.resolve(n),1)),e(i.scrollIntoView())}return!0},C=(t,e)=>{let r=t.selection,{$from:n,$to:i}=r;if(r instanceof s.i5||n.parent.inlineContent||i.parent.inlineContent)return!1;let o=A(i.parent.contentMatchAt(i.indexAfter()));if(!o||!o.isTextblock)return!1;if(e){let r=(!n.parentOffset&&i.index()<i.parent.childCount?n:i).pos,a=t.tr.insert(r,o.createAndFill());a.setSelection(s.U3.create(a.doc,r+1)),e(a.scrollIntoView())}return!0},M=(t,e)=>{let{$cursor:r}=t.selection;if(!r||r.parent.content.size)return!1;if(r.depth>1&&r.after()!=r.end(-1)){let n=r.before();if((0,i.zy)(t.doc,n))return e&&e(t.tr.split(n).scrollIntoView()),!0}let n=r.blockRange(),o=n&&(0,i.jP)(n);return null!=o&&(e&&e(t.tr.lift(n,o).scrollIntoView()),!0)},T=(t,e)=>{let{$from:r,to:n}=t.selection,i,o=r.sharedDepth(n);return 0!=o&&(i=r.before(o),e&&e(t.tr.setSelection(s.nh.create(t.doc,i))),!0)};function O(t,e,r,n){let a,l,h,c=e.nodeBefore,u=e.nodeAfter,p,d,m=c.type.spec.isolating||u.type.spec.isolating;if(!m&&(a=e.nodeBefore,l=e.nodeAfter,h=e.index(),a&&l&&a.type.compatibleContent(l.type)&&(!a.content.size&&e.parent.canReplace(h-1,h)?(r&&r(t.tr.delete(e.pos-a.nodeSize,e.pos).scrollIntoView()),!0):!!e.parent.canReplace(h,h+1)&&!!(l.isTextblock||(0,i.n9)(t.doc,e.pos))&&(r&&r(t.tr.join(e.pos).scrollIntoView()),!0))))return!0;let g=!m&&e.parent.canReplace(e.index(),e.index()+1);if(g&&(p=(d=c.contentMatchAt(c.childCount)).findWrapping(u.type))&&d.matchType(p[0]||u.type).validEnd){if(r){let n=e.pos+u.nodeSize,s=o.FK.empty;for(let t=p.length-1;t>=0;t--)s=o.FK.from(p[t].create(null,s));s=o.FK.from(c.copy(s));let a=t.tr.step(new i.Wg(e.pos-1,n,e.pos,n,new o.Ji(s,1,0),p.length,!0)),l=a.doc.resolve(n+2*p.length);l.nodeAfter&&l.nodeAfter.type==c.type&&(0,i.n9)(a.doc,l.pos)&&a.join(l.pos),r(a.scrollIntoView())}return!0}let y=u.type.spec.isolating||n>0&&m?null:s.LN.findFrom(e,1),w=y&&y.$from.blockRange(y.$to),b=w&&(0,i.jP)(w);if(null!=b&&b>=e.depth)return r&&r(t.tr.lift(w,b).scrollIntoView()),!0;if(g&&f(u,"start",!0)&&f(c,"end")){let n=c,s=[];for(;s.push(n),!n.isTextblock;)n=n.lastChild;let a=u,l=1;for(;!a.isTextblock;a=a.firstChild)l++;if(n.canReplace(n.childCount,n.childCount,a.content)){if(r){let n=o.FK.empty;for(let t=s.length-1;t>=0;t--)n=o.FK.from(s[t].copy(n));r(t.tr.step(new i.Wg(e.pos-s.length,e.pos+u.nodeSize,e.pos+l,e.pos+u.nodeSize-l,new o.Ji(n,s.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function R(t){return function(e,r){let n=e.selection,i=t<0?n.$from:n.$to,o=i.depth;for(;i.node(o).isInline;){if(!o)return!1;o--}return!!i.node(o).isTextblock&&(r&&r(e.tr.setSelection(s.U3.create(e.doc,t<0?i.start(o):i.end(o)))),!0)}}let N=R(-1),I=R(1);function B(t,e=null){return function(r,n){let{$from:o,$to:s}=r.selection,a=o.blockRange(s),l=a&&(0,i.oM)(a,t,e);return!!l&&(n&&n(r.tr.wrap(a,l).scrollIntoView()),!0)}}function L(t,e=null){return function(r,n){let i=!1;for(let n=0;n<r.selection.ranges.length&&!i;n++){let{$from:{pos:o},$to:{pos:s}}=r.selection.ranges[n];r.doc.nodesBetween(o,s,(n,o)=>{if(i)return!1;if(!(!n.isTextblock||n.hasMarkup(t,e)))if(n.type==t)i=!0;else{let e=r.doc.resolve(o),n=e.index();i=e.parent.canReplaceWith(n,n+1,t)}})}if(!i)return!1;if(n){let i=r.tr;for(let n=0;n<r.selection.ranges.length;n++){let{$from:{pos:o},$to:{pos:s}}=r.selection.ranges[n];i.setBlockType(o,s,t,e)}n(i.scrollIntoView())}return!0}}function z(...t){return function(e,r,n){for(let i=0;i<t.length;i++)if(t[i](e,r,n))return!0;return!1}}let F=z(a,h,d),j=z(a,y,w),P={Enter:z(x,C,M,(t,e)=>{let{$from:r,$to:n}=t.selection;if(t.selection instanceof s.nh&&t.selection.node.isBlock)return!!r.parentOffset&&!!(0,i.zy)(t.doc,r.pos)&&(e&&e(t.tr.split(r.pos).scrollIntoView()),!0);if(!r.depth)return!1;let o=[],a,l,h=!1,c=!1;for(let t=r.depth;;t--){if(r.node(t).isBlock){let e;h=r.end(t)==r.pos+(r.depth-t),c=r.start(t)==r.pos-(r.depth-t),l=A(r.node(t-1).contentMatchAt(r.indexAfter(t-1)));o.unshift(e||(h&&l?{type:l}:null)),a=t;break}if(1==t)return!1;o.unshift(null)}let u=t.tr;(t.selection instanceof s.U3||t.selection instanceof s.i5)&&u.deleteSelection();let p=u.mapping.map(r.pos),f=(0,i.zy)(u.doc,p,o.length,o);if(f||(o[0]=l?{type:l}:null,f=(0,i.zy)(u.doc,p,o.length,o)),!f)return!1;if(u.split(p,o.length,o),!h&&c&&r.node(a).type!=l){let t=u.mapping.map(r.before(a)),e=u.doc.resolve(t);l&&r.node(a-1).canReplaceWith(e.index(),e.index()+1,l)&&u.setNodeMarkup(u.mapping.map(r.before(a)),l)}return e&&e(u.scrollIntoView()),!0}),"Mod-Enter":E,Backspace:F,"Mod-Backspace":F,"Shift-Backspace":F,Delete:j,"Mod-Delete":j,"Mod-a":(t,e)=>(e&&e(t.tr.setSelection(new s.i5(t.doc))),!0)},D={"Ctrl-h":P.Backspace,"Alt-Backspace":P["Mod-Backspace"],"Ctrl-d":P.Delete,"Ctrl-Alt-Backspace":P["Mod-Delete"],"Alt-Delete":P["Mod-Delete"],"Alt-d":P["Mod-Delete"],"Ctrl-a":N,"Ctrl-e":I};for(let t in P)D[t]=P[t];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform()},381:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},808:(t,e,r)=>{"use strict";r.d(e,{$L:()=>O,Ln:()=>m,N0:()=>M,Um:()=>T,Wg:()=>g,X9:()=>s,dL:()=>J,jP:()=>b,n9:()=>E,oM:()=>v,zy:()=>A});var n=r(156);class i{constructor(t,e,r){this.pos=t,this.delInfo=e,this.recover=r}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class o{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&o.empty)return o.empty}recover(t){let e=0,r=65535&t;if(!this.inverted)for(let t=0;t<r;t++)e+=this.ranges[3*t+2]-this.ranges[3*t+1];return this.ranges[3*r]+e+(t-(65535&t))/65536}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,r){let n=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let a=0;a<this.ranges.length;a+=3){let l=this.ranges[a]-(this.inverted?n:0);if(l>t)break;let h=this.ranges[a+o],c=this.ranges[a+s],u=l+h;if(t<=u){let o=h?t==l?-1:t==u?1:e:e,s=l+n+(o<0?0:c);if(r)return s;let p=t==(e<0?l:u)?null:a/3+(t-l)*65536,f=t==l?2:t==u?1:4;return(e<0?t!=l:t!=u)&&(f|=8),new i(s,f,p)}n+=c-h}return r?t+n:new i(t+n,0,null)}touches(t,e){let r=0,n=65535&e,i=this.inverted?2:1,o=this.inverted?1:2;for(let e=0;e<this.ranges.length;e+=3){let s=this.ranges[e]-(this.inverted?r:0);if(s>t)break;let a=this.ranges[e+i];if(t<=s+a&&e==3*n)return!0;r+=this.ranges[e+o]-a}return!1}forEach(t){let e=this.inverted?2:1,r=this.inverted?1:2;for(let n=0,i=0;n<this.ranges.length;n+=3){let o=this.ranges[n],s=o-(this.inverted?i:0),a=o+(this.inverted?0:i),l=this.ranges[n+e],h=this.ranges[n+r];t(s,s+l,a,a+h),i+=h-l}}invert(){return new o(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return 0==t?o.empty:new o(t<0?[0,-t,0]:[0,0,t])}}o.empty=new o([]);class s{constructor(t,e,r=0,n=t?t.length:0){this.mirror=e,this.from=r,this.to=n,this._maps=t||[],this.ownData=!(t||e)}get maps(){return this._maps}slice(t=0,e=this.maps.length){return new s(this._maps,this.mirror,t,e)}appendMap(t,e){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(t),null!=e&&this.setMirror(this._maps.length-1,e)}appendMapping(t){for(let e=0,r=this._maps.length;e<t._maps.length;e++){let n=t.getMirror(e);this.appendMap(t._maps[e],null!=n&&n<e?r+n:void 0)}}getMirror(t){if(this.mirror){for(let e=0;e<this.mirror.length;e++)if(this.mirror[e]==t)return this.mirror[e+(e%2?-1:1)]}}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let e=t.maps.length-1,r=this._maps.length+t._maps.length;e>=0;e--){let n=t.getMirror(e);this.appendMap(t._maps[e].invert(),null!=n&&n>e?r-n-1:void 0)}}invert(){let t=new s;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let r=this.from;r<this.to;r++)t=this._maps[r].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,r){let n=0;for(let r=this.from;r<this.to;r++){let i=this._maps[r].mapResult(t,e);if(null!=i.recover){let e=this.getMirror(r);if(null!=e&&e>r&&e<this.to){r=e,t=this._maps[e].recover(i.recover);continue}}n|=i.delInfo,t=i.pos}return r?t:new i(t,n,null)}}let a=Object.create(null);class l{getMap(){return o.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw RangeError("Invalid input for Step.fromJSON");let r=a[e.stepType];if(!r)throw RangeError(`No step type ${e.stepType} defined`);return r.fromJSON(t,e)}static jsonID(t,e){if(t in a)throw RangeError("Duplicate use of step JSON ID "+t);return a[t]=e,e.prototype.jsonID=t,e}}class h{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new h(t,null)}static fail(t){return new h(null,t)}static fromReplace(t,e,r,i){try{return h.ok(t.replace(e,r,i))}catch(t){if(t instanceof n.vI)return h.fail(t.message);throw t}}}function c(t,e,r){let i=[];for(let n=0;n<t.childCount;n++){let o=t.child(n);o.content.size&&(o=o.copy(c(o.content,e,o))),o.isInline&&(o=e(o,r,n)),i.push(o)}return n.FK.fromArray(i)}class u extends l{constructor(t,e,r){super(),this.from=t,this.to=e,this.mark=r}apply(t){let e=t.slice(this.from,this.to),r=t.resolve(this.from),i=r.node(r.sharedDepth(this.to)),o=new n.Ji(c(e.content,(t,e)=>t.isAtom&&e.type.allowsMarkType(this.mark.type)?t.mark(this.mark.addToSet(t.marks)):t,i),e.openStart,e.openEnd);return h.fromReplace(t,this.from,this.to,o)}invert(){return new p(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),r=t.mapResult(this.to,-1);return e.deleted&&r.deleted||e.pos>=r.pos?null:new u(e.pos,r.pos,this.mark)}merge(t){return t instanceof u&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new u(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new u(e.from,e.to,t.markFromJSON(e.mark))}}l.jsonID("addMark",u);class p extends l{constructor(t,e,r){super(),this.from=t,this.to=e,this.mark=r}apply(t){let e=t.slice(this.from,this.to),r=new n.Ji(c(e.content,t=>t.mark(this.mark.removeFromSet(t.marks)),t),e.openStart,e.openEnd);return h.fromReplace(t,this.from,this.to,r)}invert(){return new u(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),r=t.mapResult(this.to,-1);return e.deleted&&r.deleted||e.pos>=r.pos?null:new p(e.pos,r.pos,this.mark)}merge(t){return t instanceof p&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new p(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new p(e.from,e.to,t.markFromJSON(e.mark))}}l.jsonID("removeMark",p);class f extends l{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at mark step's position");let r=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return h.fromReplace(t,this.pos,this.pos+1,new n.Ji(n.FK.from(r),0,+!e.isLeaf))}invert(t){let e=t.nodeAt(this.pos);if(e){let t=this.mark.addToSet(e.marks);if(t.length==e.marks.length){for(let r=0;r<e.marks.length;r++)if(!e.marks[r].isInSet(t))return new f(this.pos,e.marks[r]);return new f(this.pos,this.mark)}}return new d(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new f(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new f(e.pos,t.markFromJSON(e.mark))}}l.jsonID("addNodeMark",f);class d extends l{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at mark step's position");let r=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return h.fromReplace(t,this.pos,this.pos+1,new n.Ji(n.FK.from(r),0,+!e.isLeaf))}invert(t){let e=t.nodeAt(this.pos);return e&&this.mark.isInSet(e.marks)?new f(this.pos,this.mark):this}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new d(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new d(e.pos,t.markFromJSON(e.mark))}}l.jsonID("removeNodeMark",d);class m extends l{constructor(t,e,r,n=!1){super(),this.from=t,this.to=e,this.slice=r,this.structure=n}apply(t){return this.structure&&y(t,this.from,this.to)?h.fail("Structure replace would overwrite content"):h.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new o([this.from,this.to-this.from,this.slice.size])}invert(t){return new m(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),r=t.mapResult(this.to,-1);return e.deletedAcross&&r.deletedAcross?null:new m(e.pos,Math.max(e.pos,r.pos),this.slice,this.structure)}merge(t){if(!(t instanceof m)||t.structure||this.structure)return null;if(this.from+this.slice.size!=t.from||this.slice.openEnd||t.slice.openStart)if(t.to!=this.from||this.slice.openStart||t.slice.openEnd)return null;else{let e=this.slice.size+t.slice.size==0?n.Ji.empty:new n.Ji(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new m(t.from,this.to,e,this.structure)}{let e=this.slice.size+t.slice.size==0?n.Ji.empty:new n.Ji(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new m(this.from,this.to+(t.to-t.from),e,this.structure)}}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new m(e.from,e.to,n.Ji.fromJSON(t,e.slice),!!e.structure)}}l.jsonID("replace",m);class g extends l{constructor(t,e,r,n,i,o,s=!1){super(),this.from=t,this.to=e,this.gapFrom=r,this.gapTo=n,this.slice=i,this.insert=o,this.structure=s}apply(t){if(this.structure&&(y(t,this.from,this.gapFrom)||y(t,this.gapTo,this.to)))return h.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return h.fail("Gap is not a flat range");let r=this.slice.insertAt(this.insert,e.content);return r?h.fromReplace(t,this.from,this.to,r):h.fail("Content does not fit in gap")}getMap(){return new o([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new g(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),r=t.mapResult(this.to,-1),n=this.from==this.gapFrom?e.pos:t.map(this.gapFrom,-1),i=this.to==this.gapTo?r.pos:t.map(this.gapTo,1);return e.deletedAcross&&r.deletedAcross||n<e.pos||i>r.pos?null:new g(e.pos,r.pos,n,i,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to||"number"!=typeof e.gapFrom||"number"!=typeof e.gapTo||"number"!=typeof e.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new g(e.from,e.to,e.gapFrom,e.gapTo,n.Ji.fromJSON(t,e.slice),e.insert,!!e.structure)}}function y(t,e,r){let n=t.resolve(e),i=r-e,o=n.depth;for(;i>0&&o>0&&n.indexAfter(o)==n.node(o).childCount;)o--,i--;if(i>0){let t=n.node(o).maybeChild(n.indexAfter(o));for(;i>0;){if(!t||t.isLeaf)return!0;t=t.firstChild,i--}}return!1}function w(t,e,r,i=r.contentMatch,o=!0){let s=t.doc.nodeAt(e),a=[],l=e+1;for(let e=0;e<s.childCount;e++){let h=s.child(e),c=l+h.nodeSize,u=i.matchType(h.type);if(u){i=u;for(let e=0;e<h.marks.length;e++)r.allowsMarkType(h.marks[e].type)||t.step(new p(l,c,h.marks[e]));if(o&&h.isText&&"pre"!=r.whitespace){let t,e=/\r?\n|\r/g,i;for(;t=e.exec(h.text);)i||(i=new n.Ji(n.FK.from(r.schema.text(" ",r.allowedMarks(h.marks))),0,0)),a.push(new m(l+t.index,l+t.index+t[0].length,i))}}else a.push(new m(l,c,n.Ji.empty));l=c}if(!i.validEnd){let e=i.fillBefore(n.FK.empty,!0);t.replace(l,l,new n.Ji(e,0,0))}for(let e=a.length-1;e>=0;e--)t.step(a[e])}function b(t){let e=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let r=t.depth;;--r){let n=t.$from.node(r),i=t.$from.index(r),o=t.$to.indexAfter(r);if(r<t.depth&&n.canReplace(i,o,e))return r;if(0==r||n.type.spec.isolating||!((0==i||n.canReplace(i,n.childCount))&&(o==n.childCount||n.canReplace(0,o))))break}return null}function v(t,e,r=null,n=t){let i=function(t,e){let{parent:r,startIndex:n,endIndex:i}=t,o=r.contentMatchAt(n).findWrapping(e);if(!o)return null;let s=o.length?o[0]:e;return r.canReplaceWith(n,i,s)?o:null}(t,e),o=i&&function(t,e){let{parent:r,startIndex:n,endIndex:i}=t,o=r.child(n),s=e.contentMatch.findWrapping(o.type);if(!s)return null;let a=(s.length?s[s.length-1]:e).contentMatch;for(let t=n;a&&t<i;t++)a=a.matchType(r.child(t).type);return a&&a.validEnd?s:null}(n,e);return o?i.map(k).concat({type:e,attrs:r}).concat(o.map(k)):null}function k(t){return{type:t,attrs:null}}function S(t,e,r,n){e.forEach((i,o)=>{if(i.isText){let s,a=/\r?\n|\r/g;for(;s=a.exec(i.text);){let i=t.mapping.slice(n).map(r+1+o+s.index);t.replaceWith(i,i+1,e.type.schema.linebreakReplacement.create())}}})}function x(t,e,r,n){e.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let i=t.mapping.slice(n).map(r+1+o);t.replaceWith(i,i+1,e.type.schema.text("\n"))}})}function A(t,e,r=1,n){let i=t.resolve(e),o=i.depth-r,s=n&&n[n.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let t=i.depth-1,e=r-2;t>o;t--,e--){let r=i.node(t),o=i.index(t);if(r.type.spec.isolating)return!1;let s=r.content.cutByIndex(o,r.childCount),a=n&&n[e+1];a&&(s=s.replaceChild(0,a.type.create(a.attrs)));let l=n&&n[e]||r;if(!r.canReplace(o+1,r.childCount)||!l.type.validContent(s))return!1}let a=i.indexAfter(o),l=n&&n[0];return i.node(o).canReplaceWith(a,a,l?l.type:i.node(o+1).type)}function E(t,e){let r=t.resolve(e),n=r.index();return C(r.nodeBefore,r.nodeAfter)&&r.parent.canReplace(n,n+1)}function C(t,e){return!!(t&&e&&!t.isLeaf&&function(t,e){e.content.size||t.type.compatibleContent(e.type);let r=t.contentMatchAt(t.childCount),{linebreakReplacement:n}=t.type.schema;for(let i=0;i<e.childCount;i++){let o=e.child(i),s=o.type==n?t.type.schema.nodes.text:o.type;if(!(r=r.matchType(s))||!t.type.allowsMarks(o.marks))return!1}return r.validEnd}(t,e))}function M(t,e,r=-1){let n=t.resolve(e);for(let t=n.depth;;t--){let i,o,s=n.index(t);if(t==n.depth?(i=n.nodeBefore,o=n.nodeAfter):r>0?(i=n.node(t+1),s++,o=n.node(t).maybeChild(s)):(i=n.node(t).maybeChild(s-1),o=n.node(t+1)),i&&!i.isTextblock&&C(i,o)&&n.node(t).canReplace(s,s+1))return e;if(0==t)break;e=r<0?n.before(t):n.after(t)}}function T(t,e,r){let n=t.resolve(e);if(!r.content.size)return e;let i=r.content;for(let t=0;t<r.openStart;t++)i=i.firstChild.content;for(let t=1;t<=(0==r.openStart&&r.size?2:1);t++)for(let e=n.depth;e>=0;e--){let r=e==n.depth?0:n.pos<=(n.start(e+1)+n.end(e+1))/2?-1:1,o=n.index(e)+ +(r>0),s=n.node(e),a=!1;if(1==t)a=s.canReplace(o,o,i);else{let t=s.contentMatchAt(o).findWrapping(i.firstChild.type);a=t&&s.canReplaceWith(o,o,t[0])}if(a)return 0==r?n.pos:r<0?n.before(e+1):n.after(e+1)}return null}function O(t,e,r=e,i=n.Ji.empty){if(e==r&&!i.size)return null;let o=t.resolve(e),s=t.resolve(r);return R(o,s,i)?new m(e,r,i):new N(o,s,i).fit()}function R(t,e,r){return!r.openStart&&!r.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),r.content)}l.jsonID("replaceAround",g);class N{constructor(t,e,r){this.$from=t,this.$to=e,this.unplaced=r,this.frontier=[],this.placed=n.FK.empty;for(let e=0;e<=t.depth;e++){let r=t.node(e);this.frontier.push({type:r.type,match:r.contentMatchAt(t.indexAfter(e))})}for(let e=t.depth;e>0;e--)this.placed=n.FK.from(t.node(e).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let t=this.findFittable();t?this.placeNodes(t):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,r=this.$from,i=this.close(t<0?this.$to:r.doc.resolve(t));if(!i)return null;let o=this.placed,s=r.depth,a=i.depth;for(;s&&a&&1==o.childCount;)o=o.firstChild.content,s--,a--;let l=new n.Ji(o,s,a);return t>-1?new g(r.pos,t,this.$to.pos,this.$to.end(),l,e):l.size||r.pos!=this.$to.pos?new m(r.pos,i.pos,l):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,r=0,n=this.unplaced.openEnd;r<t;r++){let i=e.firstChild;if(e.childCount>1&&(n=0),i.type.spec.isolating&&n<=r){t=r;break}e=i.content}for(let e=1;e<=2;e++)for(let r=1==e?t:this.unplaced.openStart;r>=0;r--){let t,i=null,o=(r?(i=L(this.unplaced.content,r-1).firstChild).content:this.unplaced.content).firstChild;for(let t=this.depth;t>=0;t--){let{type:s,match:a}=this.frontier[t],l,h=null;if(1==e&&(o?a.matchType(o.type)||(h=a.fillBefore(n.FK.from(o),!1)):i&&s.compatibleContent(i.type)))return{sliceDepth:r,frontierDepth:t,parent:i,inject:h};if(2==e&&o&&(l=a.findWrapping(o.type)))return{sliceDepth:r,frontierDepth:t,parent:i,wrap:l};if(i&&a.matchType(i.type))break}}}openMore(){let{content:t,openStart:e,openEnd:r}=this.unplaced,i=L(t,e);return!!i.childCount&&!i.firstChild.isLeaf&&(this.unplaced=new n.Ji(t,e+1,Math.max(r,i.size+e>=t.size-r?e+1:0)),!0)}dropNode(){let{content:t,openStart:e,openEnd:r}=this.unplaced,i=L(t,e);if(i.childCount<=1&&e>0){let o=t.size-e<=e+i.size;this.unplaced=new n.Ji(I(t,e-1,1),e-1,o?e-1:r)}else this.unplaced=new n.Ji(I(t,e,1),e,r)}placeNodes({sliceDepth:t,frontierDepth:e,parent:r,inject:i,wrap:o}){for(;this.depth>e;)this.closeFrontierNode();if(o)for(let t=0;t<o.length;t++)this.openFrontierNode(o[t]);let s=this.unplaced,a=r?r.content:s.content,l=s.openStart-t,h=0,c=[],{match:u,type:p}=this.frontier[e];if(i){for(let t=0;t<i.childCount;t++)c.push(i.child(t));u=u.matchFragment(i)}let f=a.size+t-(s.content.size-s.openEnd);for(;h<a.childCount;){let t=a.child(h),e=u.matchType(t.type);if(!e)break;(++h>1||0==l||t.content.size)&&(u=e,c.push(function t(e,r,i){if(r<=0)return e;let o=e.content;return r>1&&(o=o.replaceChild(0,t(o.firstChild,r-1,1==o.childCount?i-1:0))),r>0&&(o=e.type.contentMatch.fillBefore(o).append(o),i<=0&&(o=o.append(e.type.contentMatch.matchFragment(o).fillBefore(n.FK.empty,!0)))),e.copy(o)}(t.mark(p.allowedMarks(t.marks)),1==h?l:0,h==a.childCount?f:-1)))}let d=h==a.childCount;d||(f=-1),this.placed=B(this.placed,e,n.FK.from(c)),this.frontier[e].match=u,d&&f<0&&r&&r.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let t=0,e=a;t<f;t++){let t=e.lastChild;this.frontier.push({type:t.type,match:t.contentMatchAt(t.childCount)}),e=t.content}this.unplaced=d?0==t?n.Ji.empty:new n.Ji(I(s.content,t-1,1),t-1,f<0?s.openEnd:t-1):new n.Ji(I(s.content,t,h),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let t=this.frontier[this.depth],e;if(!t.type.isTextblock||!z(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return -1;let{depth:r}=this.$to,n=this.$to.after(r);for(;r>1&&n==this.$to.end(--r);)++n;return n}findCloseLevel(t){e:for(let e=Math.min(this.depth,t.depth);e>=0;e--){let{match:r,type:n}=this.frontier[e],i=e<t.depth&&t.end(e+1)==t.pos+(t.depth-(e+1)),o=z(t,e,n,r,i);if(o){for(let r=e-1;r>=0;r--){let{match:e,type:n}=this.frontier[r],i=z(t,r,n,e,!0);if(!i||i.childCount)continue e}return{depth:e,fit:o,move:i?t.doc.resolve(t.after(e+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=B(this.placed,e.depth,e.fit)),t=e.move;for(let r=e.depth+1;r<=t.depth;r++){let e=t.node(r),n=e.type.contentMatch.fillBefore(e.content,!0,t.index(r));this.openFrontierNode(e.type,e.attrs,n)}return t}openFrontierNode(t,e=null,r){let i=this.frontier[this.depth];i.match=i.match.matchType(t),this.placed=B(this.placed,this.depth,n.FK.from(t.create(e,r))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(n.FK.empty,!0);t.childCount&&(this.placed=B(this.placed,this.frontier.length,t))}}function I(t,e,r){return 0==e?t.cutByIndex(r,t.childCount):t.replaceChild(0,t.firstChild.copy(I(t.firstChild.content,e-1,r)))}function B(t,e,r){return 0==e?t.append(r):t.replaceChild(t.childCount-1,t.lastChild.copy(B(t.lastChild.content,e-1,r)))}function L(t,e){for(let r=0;r<e;r++)t=t.firstChild.content;return t}function z(t,e,r,n,i){let o=t.node(e),s=i?t.indexAfter(e):t.index(e);if(s==o.childCount&&!r.compatibleContent(o.type))return null;let a=n.fillBefore(o.content,!0,s);return a&&!function(t,e,r){for(let n=r;n<e.childCount;n++)if(!t.allowsMarks(e.child(n).marks))return!0;return!1}(r,o.content,s)?a:null}function F(t,e){let r=[],n=Math.min(t.depth,e.depth);for(let i=n;i>=0;i--){let n=t.start(i);if(n<t.pos-(t.depth-i)||e.end(i)>e.pos+(e.depth-i)||t.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(n==e.start(i)||i==t.depth&&i==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==n-1)&&r.push(i)}return r}class j extends l{constructor(t,e,r){super(),this.pos=t,this.attr=e,this.value=r}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at attribute step's position");let r=Object.create(null);for(let t in e.attrs)r[t]=e.attrs[t];r[this.attr]=this.value;let i=e.type.create(r,null,e.marks);return h.fromReplace(t,this.pos,this.pos+1,new n.Ji(n.FK.from(i),0,+!e.isLeaf))}getMap(){return o.empty}invert(t){return new j(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new j(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if("number"!=typeof e.pos||"string"!=typeof e.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new j(e.pos,e.attr,e.value)}}l.jsonID("attr",j);class P extends l{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let r in t.attrs)e[r]=t.attrs[r];e[this.attr]=this.value;let r=t.type.create(e,t.content,t.marks);return h.ok(r)}getMap(){return o.empty}invert(t){return new P(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if("string"!=typeof e.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new P(e.attr,e.value)}}l.jsonID("docAttr",P);let D=class extends Error{};(D=function t(e){let r=Error.call(this,e);return r.__proto__=t.prototype,r}).prototype=Object.create(Error.prototype),D.prototype.constructor=D,D.prototype.name="TransformError";class J{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new s}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new D(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,r=n.Ji.empty){let i=O(this.doc,t,e,r);return i&&this.step(i),this}replaceWith(t,e,r){return this.replace(t,e,new n.Ji(n.FK.from(r),0,0))}delete(t,e){return this.replace(t,e,n.Ji.empty)}insert(t,e){return this.replaceWith(t,t,e)}replaceRange(t,e,r){return!function(t,e,r,i){if(!i.size)return t.deleteRange(e,r);let o=t.doc.resolve(e),s=t.doc.resolve(r);if(R(o,s,i))return t.step(new m(e,r,i));let a=F(o,t.doc.resolve(r));0==a[a.length-1]&&a.pop();let l=-(o.depth+1);a.unshift(l);for(let t=o.depth,e=o.pos-1;t>0;t--,e--){let r=o.node(t).type.spec;if(r.defining||r.definingAsContext||r.isolating)break;a.indexOf(t)>-1?l=t:o.before(t)==e&&a.splice(1,0,-t)}let h=a.indexOf(l),c=[],u=i.openStart;for(let t=i.content,e=0;;e++){let r=t.firstChild;if(c.push(r),e==i.openStart)break;t=r.content}for(let t=u-1;t>=0;t--){var p;let e=c[t],r=(p=e.type).spec.defining||p.spec.definingForContent;if(r&&!e.sameMarkup(o.node(Math.abs(l)-1)))u=t;else if(r||!e.type.isTextblock)break}for(let e=i.openStart;e>=0;e--){let l=(e+u+1)%(i.openStart+1),p=c[l];if(p)for(let e=0;e<a.length;e++){let c=a[(e+h)%a.length],u=!0;c<0&&(u=!1,c=-c);let f=o.node(c-1),d=o.index(c-1);if(f.canReplaceWith(d,d,p.type,p.marks))return t.replace(o.before(c),u?s.after(c):r,new n.Ji(function t(e,r,i,o,s){if(r<i){let n=e.firstChild;e=e.replaceChild(0,n.copy(t(n.content,r+1,i,o,n)))}if(r>o){let t=s.contentMatchAt(0),r=t.fillBefore(e).append(e);e=r.append(t.matchFragment(r).fillBefore(n.FK.empty,!0))}return e}(i.content,0,i.openStart,l),l,i.openEnd))}}let f=t.steps.length;for(let n=a.length-1;n>=0&&(t.replace(e,r,i),!(t.steps.length>f));n--){let t=a[n];t<0||(e=o.before(t),r=s.after(t))}}(this,t,e,r),this}replaceRangeWith(t,e,r){var i=t,o=e;if(!r.isInline&&i==o&&this.doc.resolve(i).parent.content.size){let t=function(t,e,r){let n=t.resolve(e);if(n.parent.canReplaceWith(n.index(),n.index(),r))return e;if(0==n.parentOffset)for(let t=n.depth-1;t>=0;t--){let e=n.index(t);if(n.node(t).canReplaceWith(e,e,r))return n.before(t+1);if(e>0)return null}if(n.parentOffset==n.parent.content.size)for(let t=n.depth-1;t>=0;t--){let e=n.indexAfter(t);if(n.node(t).canReplaceWith(e,e,r))return n.after(t+1);if(e<n.node(t).childCount)break}return null}(this.doc,i,r.type);null!=t&&(i=o=t)}return this.replaceRange(i,o,new n.Ji(n.FK.from(r),0,0)),this}deleteRange(t,e){return!function(t,e,r){let n=t.doc.resolve(e),i=t.doc.resolve(r),o=F(n,i);for(let e=0;e<o.length;e++){let r=o[e],s=e==o.length-1;if(s&&0==r||n.node(r).type.contentMatch.validEnd)return t.delete(n.start(r),i.end(r));if(r>0&&(s||n.node(r-1).canReplace(n.index(r-1),i.indexAfter(r-1))))return t.delete(n.before(r),i.after(r))}for(let o=1;o<=n.depth&&o<=i.depth;o++)if(e-n.start(o)==n.depth-o&&r>n.end(o)&&i.end(o)-r!=i.depth-o&&n.start(o-1)==i.start(o-1)&&n.node(o-1).canReplace(n.index(o-1),i.index(o-1)))return t.delete(n.before(o),r);t.delete(e,r)}(this,t,e),this}lift(t,e){return!function(t,e,r){let{$from:i,$to:o,depth:s}=e,a=i.before(s+1),l=o.after(s+1),h=a,c=l,u=n.FK.empty,p=0;for(let t=s,e=!1;t>r;t--)e||i.index(t)>0?(e=!0,u=n.FK.from(i.node(t).copy(u)),p++):h--;let f=n.FK.empty,d=0;for(let t=s,e=!1;t>r;t--)e||o.after(t+1)<o.end(t)?(e=!0,f=n.FK.from(o.node(t).copy(f)),d++):c++;t.step(new g(h,c,a,l,new n.Ji(u.append(f),p,d),u.size-p,!0))}(this,t,e),this}join(t,e=1){return!function(t,e,r){let i=null,{linebreakReplacement:o}=t.doc.type.schema,s=t.doc.resolve(e-r),a=s.node().type;if(o&&a.inlineContent){let t="pre"==a.whitespace,e=!!a.contentMatch.matchType(o);t&&!e?i=!1:!t&&e&&(i=!0)}let l=t.steps.length;if(!1===i){let n=t.doc.resolve(e+r);x(t,n.node(),n.before(),l)}a.inlineContent&&w(t,e+r-1,a,s.node().contentMatchAt(s.index()),null==i);let h=t.mapping.slice(l),c=h.map(e-r);if(t.step(new m(c,h.map(e+r,-1),n.Ji.empty,!0)),!0===i){let e=t.doc.resolve(c);S(t,e.node(),e.before(),t.steps.length)}}(this,t,e),this}wrap(t,e){return!function(t,e,r){let i=n.FK.empty;for(let t=r.length-1;t>=0;t--){if(i.size){let e=r[t].type.contentMatch.matchFragment(i);if(!e||!e.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=n.FK.from(r[t].type.create(r[t].attrs,i))}let o=e.start,s=e.end;t.step(new g(o,s,o,s,new n.Ji(i,0,0),r.length,!0))}(this,t,e),this}setBlockType(t,e=t,r,i=null){var o=this;if(!r.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let s=o.steps.length;return o.doc.nodesBetween(t,e,(t,e)=>{var a,l,h;let c,u,p="function"==typeof i?i(t):i;if(t.isTextblock&&!t.hasMarkup(r,p)&&(a=o.doc,l=o.mapping.slice(s).map(e),h=r,u=(c=a.resolve(l)).index(),c.parent.canReplaceWith(u,u+1,h))){let i=null;if(r.schema.linebreakReplacement){let t="pre"==r.whitespace,e=!!r.contentMatch.matchType(r.schema.linebreakReplacement);t&&!e?i=!1:!t&&e&&(i=!0)}!1===i&&x(o,t,e,s),w(o,o.mapping.slice(s).map(e,1),r,void 0,null===i);let a=o.mapping.slice(s),l=a.map(e,1),h=a.map(e+t.nodeSize,1);return o.step(new g(l,h,l+1,h-1,new n.Ji(n.FK.from(r.create(p,null,t.marks)),0,0),1,!0)),!0===i&&S(o,t,e,s),!1}}),this}setNodeMarkup(t,e,r=null,i){return!function(t,e,r,i,o){let s=t.doc.nodeAt(e);if(!s)throw RangeError("No node at given position");r||(r=s.type);let a=r.create(i,null,o||s.marks);if(s.isLeaf)return t.replaceWith(e,e+s.nodeSize,a);if(!r.validContent(s.content))throw RangeError("Invalid content for node type "+r.name);t.step(new g(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new n.Ji(n.FK.from(a),0,0),1,!0))}(this,t,e,r,i),this}setNodeAttribute(t,e,r){return this.step(new j(t,e,r)),this}setDocAttribute(t,e){return this.step(new P(t,e)),this}addNodeMark(t,e){return this.step(new f(t,e)),this}removeNodeMark(t,e){let r=this.doc.nodeAt(t);if(!r)throw RangeError("No node at position "+t);if(e instanceof n.CU)e.isInSet(r.marks)&&this.step(new d(t,e));else{let n=r.marks,i,o=[];for(;i=e.isInSet(n);)o.push(new d(t,i)),n=i.removeFromSet(n);for(let t=o.length-1;t>=0;t--)this.step(o[t])}return this}split(t,e=1,r){return!function(t,e,r=1,i){let o=t.doc.resolve(e),s=n.FK.empty,a=n.FK.empty;for(let t=o.depth,e=o.depth-r,l=r-1;t>e;t--,l--){s=n.FK.from(o.node(t).copy(s));let e=i&&i[l];a=n.FK.from(e?e.type.create(e.attrs,a):o.node(t).copy(a))}t.step(new m(e,e,new n.Ji(s.append(a),r,r),!0))}(this,t,e,r),this}addMark(t,e,r){var n;let i,o,s,a;return n=this,s=[],a=[],n.doc.nodesBetween(t,e,(n,l,h)=>{if(!n.isInline)return;let c=n.marks;if(!r.isInSet(c)&&h.type.allowsMarkType(r.type)){let h=Math.max(l,t),f=Math.min(l+n.nodeSize,e),d=r.addToSet(c);for(let t=0;t<c.length;t++)c[t].isInSet(d)||(i&&i.to==h&&i.mark.eq(c[t])?i.to=f:s.push(i=new p(h,f,c[t])));o&&o.to==h?o.to=f:a.push(o=new u(h,f,r))}}),s.forEach(t=>n.step(t)),a.forEach(t=>n.step(t)),this}removeMark(t,e,r){var i;let o,s;return i=this,o=[],s=0,i.doc.nodesBetween(t,e,(i,a)=>{if(!i.isInline)return;s++;let l=null;if(r instanceof n.sX){let t=i.marks,e;for(;e=r.isInSet(t);)(l||(l=[])).push(e),t=e.removeFromSet(t)}else r?r.isInSet(i.marks)&&(l=[r]):l=i.marks;if(l&&l.length){let r=Math.min(a+i.nodeSize,e);for(let e=0;e<l.length;e++){let n=l[e],i;for(let t=0;t<o.length;t++){let e=o[t];e.step==s-1&&n.eq(o[t].style)&&(i=e)}i?(i.to=r,i.step=s):o.push({style:n,from:Math.max(a,t),to:r,step:s})}}}),o.forEach(t=>i.step(new p(t.from,t.to,t.style))),this}clearIncompatible(t,e,r){return w(this,t,e,r),this}}},1154:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1514:(t,e,r)=>{"use strict";r.d(e,{$B:()=>c,Sd:()=>l,T2:()=>h});var n=r(808),i=r(156);let o=["ol",0],s=["ul",0],a=["li",0];function l(t,e=null){return function(r,o){let{$from:s,$to:a}=r.selection,l=s.blockRange(a);if(!l)return!1;let h=o?r.tr:null;return!!function(t,e,r,o=null){let s=!1,a=e,l=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(r)&&0==e.startIndex){if(0==e.$from.index(e.depth-1))return!1;let t=l.resolve(e.start-2);a=new i.u$(t,t,e.depth),e.endIndex<e.parent.childCount&&(e=new i.u$(e.$from,l.resolve(e.$to.end(e.depth)),e.depth)),s=!0}let h=(0,n.oM)(a,r,o,e);return!!h&&(t&&function(t,e,r,o,s){let a=i.FK.empty;for(let t=r.length-1;t>=0;t--)a=i.FK.from(r[t].type.create(r[t].attrs,a));t.step(new n.Wg(e.start-2*!!o,e.end,e.start,e.end,new i.Ji(a,0,0),r.length,!0));let l=0;for(let t=0;t<r.length;t++)r[t].type==s&&(l=t+1);let h=r.length-l,c=e.start+r.length-2*!!o,u=e.parent;for(let r=e.startIndex,i=e.endIndex,o=!0;r<i;r++,o=!1)!o&&(0,n.zy)(t.doc,c,h)&&(t.split(c,h),c+=2*h),c+=u.child(r).nodeSize}(t,e,h,s,r),!0)}(h,l,t,e)&&(o&&o(h.scrollIntoView()),!0)}}function h(t){return function(e,r){let{$from:o,$to:s}=e.selection,a=o.blockRange(s,e=>e.childCount>0&&e.firstChild.type==t);return!!a&&(!r||(o.node(a.depth-1).type==t?function(t,e,r,o){let s=t.tr,a=o.end,l=o.$to.end(o.depth);a<l&&(s.step(new n.Wg(a-1,l,a,l,new i.Ji(i.FK.from(r.create(null,o.parent.copy())),1,0),1,!0)),o=new i.u$(s.doc.resolve(o.$from.pos),s.doc.resolve(l),o.depth));let h=(0,n.jP)(o);if(null==h)return!1;s.lift(o,h);let c=s.doc.resolve(s.mapping.map(a,-1)-1);return(0,n.n9)(s.doc,c.pos)&&c.nodeBefore.type==c.nodeAfter.type&&s.join(c.pos),e(s.scrollIntoView()),!0}(e,r,t,a):function(t,e,r){let o=t.tr,s=r.parent;for(let t=r.end,e=r.endIndex-1,n=r.startIndex;e>n;e--)t-=s.child(e).nodeSize,o.delete(t-1,t+1);let a=o.doc.resolve(r.start),l=a.nodeAfter;if(o.mapping.map(r.end)!=r.start+a.nodeAfter.nodeSize)return!1;let h=0==r.startIndex,c=r.endIndex==s.childCount,u=a.node(-1),p=a.index(-1);if(!u.canReplace(p+ +!h,p+1,l.content.append(c?i.FK.empty:i.FK.from(s))))return!1;let f=a.pos,d=f+l.nodeSize;return o.step(new n.Wg(f-!!h,d+ +!!c,f+1,d-1,new i.Ji((h?i.FK.empty:i.FK.from(s.copy(i.FK.empty))).append(c?i.FK.empty:i.FK.from(s.copy(i.FK.empty))),+!h,+!c),+!h)),e(o.scrollIntoView()),!0}(e,r,a)))}}function c(t){return function(e,r){let{$from:o,$to:s}=e.selection,a=o.blockRange(s,e=>e.childCount>0&&e.firstChild.type==t);if(!a)return!1;let l=a.startIndex;if(0==l)return!1;let h=a.parent,c=h.child(l-1);if(c.type!=t)return!1;if(r){let o=c.lastChild&&c.lastChild.type==h.type,s=i.FK.from(o?t.create():null),l=new i.Ji(i.FK.from(t.create(null,i.FK.from(h.type.create(null,s)))),o?3:1,0),u=a.start,p=a.end;r(e.tr.step(new n.Wg(u-(o?3:1),p,u,p,l,1,!0)).scrollIntoView())}return!0}}},1788:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1981:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("maximize-2",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]])},2098:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2118:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},2178:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},2571:(t,e,r)=>{"use strict";r.d(e,{$t:()=>x,LN:()=>s,U3:()=>c,hs:()=>M,i5:()=>d,k_:()=>A,nh:()=>p});var n=r(156),i=r(808);let o=Object.create(null);class s{constructor(t,e,r){this.$anchor=t,this.$head=e,this.ranges=r||[new a(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let e=0;e<t.length;e++)if(t[e].$from.pos!=t[e].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(t,e=n.Ji.empty){let r=e.content.lastChild,i=null;for(let t=0;t<e.openEnd;t++)i=r,r=r.lastChild;let o=t.steps.length,s=this.ranges;for(let a=0;a<s.length;a++){let{$from:l,$to:h}=s[a],c=t.mapping.slice(o);t.replaceRange(c.map(l.pos),c.map(h.pos),a?n.Ji.empty:e),0==a&&y(t,o,(r?r.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(t,e){let r=t.steps.length,n=this.ranges;for(let i=0;i<n.length;i++){let{$from:o,$to:s}=n[i],a=t.mapping.slice(r),l=a.map(o.pos),h=a.map(s.pos);i?t.deleteRange(l,h):(t.replaceRangeWith(l,h,e),y(t,r,e.isInline?-1:1))}}static findFrom(t,e,r=!1){let n=t.parent.inlineContent?new c(t):g(t.node(0),t.parent,t.pos,t.index(),e,r);if(n)return n;for(let n=t.depth-1;n>=0;n--){let i=e<0?g(t.node(0),t.node(n),t.before(n+1),t.index(n),e,r):g(t.node(0),t.node(n),t.after(n+1),t.index(n)+1,e,r);if(i)return i}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new d(t.node(0))}static atStart(t){return g(t,t,0,0,1)||new d(t)}static atEnd(t){return g(t,t,t.content.size,t.childCount,-1)||new d(t)}static fromJSON(t,e){if(!e||!e.type)throw RangeError("Invalid input for Selection.fromJSON");let r=o[e.type];if(!r)throw RangeError(`No selection type ${e.type} defined`);return r.fromJSON(t,e)}static jsonID(t,e){if(t in o)throw RangeError("Duplicate use of selection JSON ID "+t);return o[t]=e,e.prototype.jsonID=t,e}getBookmark(){return c.between(this.$anchor,this.$head).getBookmark()}}s.prototype.visible=!0;class a{constructor(t,e){this.$from=t,this.$to=e}}let l=!1;function h(t){l||t.parent.inlineContent||(l=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}class c extends s{constructor(t,e=t){h(t),h(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let r=t.resolve(e.map(this.head));if(!r.parent.inlineContent)return s.near(r);let n=t.resolve(e.map(this.anchor));return new c(n.parent.inlineContent?n:r,r)}replace(t,e=n.Ji.empty){if(super.replace(t,e),e==n.Ji.empty){let e=this.$from.marksAcross(this.$to);e&&t.ensureMarks(e)}}eq(t){return t instanceof c&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new u(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if("number"!=typeof e.anchor||"number"!=typeof e.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new c(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,r=e){let n=t.resolve(e);return new this(n,r==e?n:t.resolve(r))}static between(t,e,r){let n=t.pos-e.pos;if((!r||n)&&(r=n>=0?1:-1),!e.parent.inlineContent){let t=s.findFrom(e,r,!0)||s.findFrom(e,-r,!0);if(!t)return s.near(e,r);e=t.$head}return t.parent.inlineContent||(0==n?t=e:(t=(s.findFrom(t,-r,!0)||s.findFrom(t,r,!0)).$anchor).pos<e.pos!=n<0&&(t=e)),new c(t,e)}}s.jsonID("text",c);class u{constructor(t,e){this.anchor=t,this.head=e}map(t){return new u(t.map(this.anchor),t.map(this.head))}resolve(t){return c.between(t.resolve(this.anchor),t.resolve(this.head))}}class p extends s{constructor(t){let e=t.nodeAfter;super(t,t.node(0).resolve(t.pos+e.nodeSize)),this.node=e}map(t,e){let{deleted:r,pos:n}=e.mapResult(this.anchor),i=t.resolve(n);return r?s.near(i):new p(i)}content(){return new n.Ji(n.FK.from(this.node),0,0)}eq(t){return t instanceof p&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new f(this.anchor)}static fromJSON(t,e){if("number"!=typeof e.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new p(t.resolve(e.anchor))}static create(t,e){return new p(t.resolve(e))}static isSelectable(t){return!t.isText&&!1!==t.type.spec.selectable}}p.prototype.visible=!1,s.jsonID("node",p);class f{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:r}=t.mapResult(this.anchor);return e?new u(r,r):new f(r)}resolve(t){let e=t.resolve(this.anchor),r=e.nodeAfter;return r&&p.isSelectable(r)?new p(e):s.near(e)}}class d extends s{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(t,e=n.Ji.empty){if(e==n.Ji.empty){t.delete(0,t.doc.content.size);let e=s.atStart(t.doc);e.eq(t.selection)||t.setSelection(e)}else super.replace(t,e)}toJSON(){return{type:"all"}}static fromJSON(t){return new d(t)}map(t){return new d(t)}eq(t){return t instanceof d}getBookmark(){return m}}s.jsonID("all",d);let m={map(){return this},resolve:t=>new d(t)};function g(t,e,r,n,i,o=!1){if(e.inlineContent)return c.create(t,r);for(let s=n-(i>0?0:1);i>0?s<e.childCount:s>=0;s+=i){let n=e.child(s);if(n.isAtom){if(!o&&p.isSelectable(n))return p.create(t,r-(i<0?n.nodeSize:0))}else{let e=g(t,n,r+i,i<0?n.childCount:0,i,o);if(e)return e}r+=n.nodeSize*i}return null}function y(t,e,r){let n,o=t.steps.length-1;if(o<e)return;let a=t.steps[o];(a instanceof i.Ln||a instanceof i.Wg)&&(t.mapping.maps[o].forEach((t,e,r,i)=>{null==n&&(n=i)}),t.setSelection(s.near(t.doc.resolve(n),r)))}class w extends i.dL{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=2,this}ensureMarks(t){return n.CU.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(t){return this.ensureMarks(t.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(t){return this.ensureMarks(t.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(t,e){super.addStep(t,e),this.updated=-3&this.updated,this.storedMarks=null}setTime(t){return this.time=t,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let r=this.selection;return e&&(t=t.mark(this.storedMarks||(r.empty?r.$from.marks():r.$from.marksAcross(r.$to)||n.CU.none))),r.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(t,e,r){let n=this.doc.type.schema;if(null==e)return t?this.replaceSelectionWith(n.text(t),!0):this.deleteSelection();{if(null==r&&(r=e),r=null==r?e:r,!t)return this.deleteRange(e,r);let i=this.storedMarks;if(!i){let t=this.doc.resolve(e);i=r==e?t.marks():t.marksAcross(this.doc.resolve(r))}return this.replaceRangeWith(e,r,n.text(t,i)),this.selection.empty||this.setSelection(s.near(this.selection.$to)),this}}setMeta(t,e){return this.meta["string"==typeof t?t:t.key]=e,this}getMeta(t){return this.meta["string"==typeof t?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function b(t,e){return e&&t?t.bind(e):t}class v{constructor(t,e,r){this.name=t,this.init=b(e.init,r),this.apply=b(e.apply,r)}}let k=[new v("doc",{init:t=>t.doc||t.schema.topNodeType.createAndFill(),apply:t=>t.doc}),new v("selection",{init:(t,e)=>t.selection||s.atStart(e.doc),apply:t=>t.selection}),new v("storedMarks",{init:t=>t.storedMarks||null,apply:(t,e,r,n)=>n.selection.$cursor?t.storedMarks:null}),new v("scrollToSelection",{init:()=>0,apply:(t,e)=>t.scrolledIntoView?e+1:e})];class S{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=k.slice(),e&&e.forEach(t=>{if(this.pluginsByKey[t.key])throw RangeError("Adding different instances of a keyed plugin ("+t.key+")");this.plugins.push(t),this.pluginsByKey[t.key]=t,t.spec.state&&this.fields.push(new v(t.key,t.spec.state,t))})}}class x{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(t){return this.applyTransaction(t).state}filterTransaction(t,e=-1){for(let r=0;r<this.config.plugins.length;r++)if(r!=e){let e=this.config.plugins[r];if(e.spec.filterTransaction&&!e.spec.filterTransaction.call(e,t,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],r=this.applyInner(t),n=null;for(;;){let i=!1;for(let o=0;o<this.config.plugins.length;o++){let s=this.config.plugins[o];if(s.spec.appendTransaction){let a=n?n[o].n:0,l=n?n[o].state:this,h=a<e.length&&s.spec.appendTransaction.call(s,a?e.slice(a):e,l,r);if(h&&r.filterTransaction(h,o)){if(h.setMeta("appendedTransaction",t),!n){n=[];for(let t=0;t<this.config.plugins.length;t++)n.push(t<o?{state:r,n:e.length}:{state:this,n:0})}e.push(h),r=r.applyInner(h),i=!0}n&&(n[o]={state:r,n:e.length})}}if(!i)return{state:r,transactions:e}}}applyInner(t){if(!t.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let e=new x(this.config),r=this.config.fields;for(let n=0;n<r.length;n++){let i=r[n];e[i.name]=i.apply(t,this[i.name],this,e)}return e}get tr(){return new w(this)}static create(t){let e=new S(t.doc?t.doc.type.schema:t.schema,t.plugins),r=new x(e);for(let n=0;n<e.fields.length;n++)r[e.fields[n].name]=e.fields[n].init(t,r);return r}reconfigure(t){let e=new S(this.schema,t.plugins),r=e.fields,n=new x(e);for(let e=0;e<r.length;e++){let i=r[e].name;n[i]=this.hasOwnProperty(i)?this[i]:r[e].init(t,n)}return n}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map(t=>t.toJSON())),t&&"object"==typeof t)for(let r in t){if("doc"==r||"selection"==r)throw RangeError("The JSON fields `doc` and `selection` are reserved");let n=t[r],i=n.spec.state;i&&i.toJSON&&(e[r]=i.toJSON.call(n,this[n.key]))}return e}static fromJSON(t,e,r){if(!e)throw RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw RangeError("Required config field 'schema' missing");let i=new S(t.schema,t.plugins),o=new x(i);return i.fields.forEach(i=>{if("doc"==i.name)o.doc=n.bP.fromJSON(t.schema,e.doc);else if("selection"==i.name)o.selection=s.fromJSON(o.doc,e.selection);else if("storedMarks"==i.name)e.storedMarks&&(o.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(r)for(let n in r){let s=r[n],a=s.spec.state;if(s.key==i.name&&a&&a.fromJSON&&Object.prototype.hasOwnProperty.call(e,n)){o[i.name]=a.fromJSON.call(s,t,e[n],o);return}}o[i.name]=i.init(t,o)}}),o}}class A{constructor(t){this.spec=t,this.props={},t.props&&function t(e,r,n){for(let i in e){let o=e[i];o instanceof Function?o=o.bind(r):"handleDOMEvents"==i&&(o=t(o,r,{})),n[i]=o}return n}(t.props,this,this.props),this.key=t.key?t.key.key:C("plugin")}getState(t){return t[this.key]}}let E=Object.create(null);function C(t){return t in E?t+"$"+ ++E[t]:(E[t]=0,t+"$")}class M{constructor(t="key"){this.key=C(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}},2657:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3032:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("file-video",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m10 11 5 3-5 3v-6Z",key:"7ntvm4"}]])},3127:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},3311:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},3464:(t,e,r)=>{"use strict";let n;r.d(e,{A:()=>eh});var i,o,s,a={};function l(t,e){return function(){return t.apply(e,arguments)}}r.r(a),r.d(a,{hasBrowserEnv:()=>tp,hasStandardBrowserEnv:()=>td,hasStandardBrowserWebWorkerEnv:()=>tm,navigator:()=>tf,origin:()=>tg});var h=r(9509);let{toString:c}=Object.prototype,{getPrototypeOf:u}=Object,{iterator:p,toStringTag:f}=Symbol,d=(t=>e=>{let r=c.call(e);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),m=t=>(t=t.toLowerCase(),e=>d(e)===t),g=t=>e=>typeof e===t,{isArray:y}=Array,w=g("undefined"),b=m("ArrayBuffer"),v=g("string"),k=g("function"),S=g("number"),x=t=>null!==t&&"object"==typeof t,A=t=>{if("object"!==d(t))return!1;let e=u(t);return(null===e||e===Object.prototype||null===Object.getPrototypeOf(e))&&!(f in t)&&!(p in t)},E=m("Date"),C=m("File"),M=m("Blob"),T=m("FileList"),O=m("URLSearchParams"),[R,N,I,B]=["ReadableStream","Request","Response","Headers"].map(m);function L(t,e,{allOwnKeys:r=!1}={}){let n,i;if(null!=t)if("object"!=typeof t&&(t=[t]),y(t))for(n=0,i=t.length;n<i;n++)e.call(null,t[n],n,t);else{let i,o=r?Object.getOwnPropertyNames(t):Object.keys(t),s=o.length;for(n=0;n<s;n++)i=o[n],e.call(null,t[i],i,t)}}function z(t,e){let r;e=e.toLowerCase();let n=Object.keys(t),i=n.length;for(;i-- >0;)if(e===(r=n[i]).toLowerCase())return r;return null}let F="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,j=t=>!w(t)&&t!==F,P=(t=>e=>t&&e instanceof t)("undefined"!=typeof Uint8Array&&u(Uint8Array)),D=m("HTMLFormElement"),J=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),U=m("RegExp"),_=(t,e)=>{let r=Object.getOwnPropertyDescriptors(t),n={};L(r,(r,i)=>{let o;!1!==(o=e(r,i,t))&&(n[i]=o||r)}),Object.defineProperties(t,n)},$=m("AsyncFunction"),K=(i="function"==typeof setImmediate,o=k(F.postMessage),i?setImmediate:o?((t,e)=>(F.addEventListener("message",({source:r,data:n})=>{r===F&&n===t&&e.length&&e.shift()()},!1),r=>{e.push(r),F.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t)),H="undefined"!=typeof queueMicrotask?queueMicrotask.bind(F):void 0!==h&&h.nextTick||K,q={isArray:y,isArrayBuffer:b,isBuffer:function(t){return null!==t&&!w(t)&&null!==t.constructor&&!w(t.constructor)&&k(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||k(t.append)&&("formdata"===(e=d(t))||"object"===e&&k(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&b(t.buffer)},isString:v,isNumber:S,isBoolean:t=>!0===t||!1===t,isObject:x,isPlainObject:A,isReadableStream:R,isRequest:N,isResponse:I,isHeaders:B,isUndefined:w,isDate:E,isFile:C,isBlob:M,isRegExp:U,isFunction:k,isStream:t=>x(t)&&k(t.pipe),isURLSearchParams:O,isTypedArray:P,isFileList:T,forEach:L,merge:function t(){let{caseless:e}=j(this)&&this||{},r={},n=(n,i)=>{let o=e&&z(r,i)||i;A(r[o])&&A(n)?r[o]=t(r[o],n):A(n)?r[o]=t({},n):y(n)?r[o]=n.slice():r[o]=n};for(let t=0,e=arguments.length;t<e;t++)arguments[t]&&L(arguments[t],n);return r},extend:(t,e,r,{allOwnKeys:n}={})=>(L(e,(e,n)=>{r&&k(e)?t[n]=l(e,r):t[n]=e},{allOwnKeys:n}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&Object.assign(t.prototype,r)},toFlatObject:(t,e,r,n)=>{let i,o,s,a={};if(e=e||{},null==t)return e;do{for(o=(i=Object.getOwnPropertyNames(t)).length;o-- >0;)s=i[o],(!n||n(s,t,e))&&!a[s]&&(e[s]=t[s],a[s]=!0);t=!1!==r&&u(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},kindOf:d,kindOfTest:m,endsWith:(t,e,r)=>{t=String(t),(void 0===r||r>t.length)&&(r=t.length),r-=e.length;let n=t.indexOf(e,r);return -1!==n&&n===r},toArray:t=>{if(!t)return null;if(y(t))return t;let e=t.length;if(!S(e))return null;let r=Array(e);for(;e-- >0;)r[e]=t[e];return r},forEachEntry:(t,e)=>{let r,n=(t&&t[p]).call(t);for(;(r=n.next())&&!r.done;){let n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let r,n=[];for(;null!==(r=t.exec(e));)n.push(r);return n},isHTMLForm:D,hasOwnProperty:J,hasOwnProp:J,reduceDescriptors:_,freezeMethods:t=>{_(t,(e,r)=>{if(k(t)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(k(t[r])){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(t,e)=>{let r={};return(y(t)?t:String(t).split(e)).forEach(t=>{r[t]=!0}),r},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,e,r){return e.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t*=1)?t:e,findKey:z,global:F,isContextDefined:j,isSpecCompliantForm:function(t){return!!(t&&k(t.append)&&"FormData"===t[f]&&t[p])},toJSONObject:t=>{let e=Array(10),r=(t,n)=>{if(x(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[n]=t;let i=y(t)?[]:{};return L(t,(t,e)=>{let o=r(t,n+1);w(o)||(i[e]=o)}),e[n]=void 0,i}}return t};return r(t,0)},isAsyncFn:$,isThenable:t=>t&&(x(t)||k(t))&&k(t.then)&&k(t.catch),setImmediate:K,asap:H,isIterable:t=>null!=t&&k(t[p])};function V(t,e,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}q.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:q.toJSONObject(this.config),code:this.code,status:this.status}}});let W=V.prototype,G={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{G[t]={value:t}}),Object.defineProperties(V,G),Object.defineProperty(W,"isAxiosError",{value:!0}),V.from=(t,e,r,n,i,o)=>{let s=Object.create(W);return q.toFlatObject(t,s,function(t){return t!==Error.prototype},t=>"isAxiosError"!==t),V.call(s,t.message,e,r,n,i),s.cause=t,s.name=t.name,o&&Object.assign(s,o),s};var X=r(9641).Buffer;function Z(t){return q.isPlainObject(t)||q.isArray(t)}function Y(t){return q.endsWith(t,"[]")?t.slice(0,-2):t}function Q(t,e,r){return t?t.concat(e).map(function(t,e){return t=Y(t),!r&&e?"["+t+"]":t}).join(r?".":""):e}let tt=q.toFlatObject(q,{},null,function(t){return/^is[A-Z]/.test(t)}),te=function(t,e,r){if(!q.isObject(t))throw TypeError("target must be an object");e=e||new FormData;let n=(r=q.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(t,e){return!q.isUndefined(e[t])})).metaTokens,i=r.visitor||h,o=r.dots,s=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&q.isSpecCompliantForm(e);if(!q.isFunction(i))throw TypeError("visitor must be a function");function l(t){if(null===t)return"";if(q.isDate(t))return t.toISOString();if(q.isBoolean(t))return t.toString();if(!a&&q.isBlob(t))throw new V("Blob is not supported. Use a Buffer instead.");return q.isArrayBuffer(t)||q.isTypedArray(t)?a&&"function"==typeof Blob?new Blob([t]):X.from(t):t}function h(t,r,i){let a=t;if(t&&!i&&"object"==typeof t)if(q.endsWith(r,"{}"))r=n?r:r.slice(0,-2),t=JSON.stringify(t);else{var h;if(q.isArray(t)&&(h=t,q.isArray(h)&&!h.some(Z))||(q.isFileList(t)||q.endsWith(r,"[]"))&&(a=q.toArray(t)))return r=Y(r),a.forEach(function(t,n){q.isUndefined(t)||null===t||e.append(!0===s?Q([r],n,o):null===s?r:r+"[]",l(t))}),!1}return!!Z(t)||(e.append(Q(i,r,o),l(t)),!1)}let c=[],u=Object.assign(tt,{defaultVisitor:h,convertValue:l,isVisitable:Z});if(!q.isObject(t))throw TypeError("data must be an object");return!function t(r,n){if(!q.isUndefined(r)){if(-1!==c.indexOf(r))throw Error("Circular reference detected in "+n.join("."));c.push(r),q.forEach(r,function(r,o){!0===(!(q.isUndefined(r)||null===r)&&i.call(e,r,q.isString(o)?o.trim():o,n,u))&&t(r,n?n.concat(o):[o])}),c.pop()}}(t),e};function tr(t){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(t){return e[t]})}function tn(t,e){this._pairs=[],t&&te(t,this,e)}let ti=tn.prototype;function to(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ts(t,e,r){let n;if(!e)return t;let i=r&&r.encode||to;q.isFunction(r)&&(r={serialize:r});let o=r&&r.serialize;if(n=o?o(e,r):q.isURLSearchParams(e)?e.toString():new tn(e,r).toString(i)){let e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+n}return t}ti.append=function(t,e){this._pairs.push([t,e])},ti.toString=function(t){let e=t?function(e){return t.call(this,e,tr)}:tr;return this._pairs.map(function(t){return e(t[0])+"="+e(t[1])},"").join("&")};class ta{constructor(){this.handlers=[]}use(t,e,r){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){q.forEach(this.handlers,function(e){null!==e&&t(e)})}}let tl={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},th="undefined"!=typeof URLSearchParams?URLSearchParams:tn,tc="undefined"!=typeof FormData?FormData:null,tu="undefined"!=typeof Blob?Blob:null,tp="undefined"!=typeof window&&"undefined"!=typeof document,tf="object"==typeof navigator&&navigator||void 0,td=tp&&(!tf||0>["ReactNative","NativeScript","NS"].indexOf(tf.product)),tm="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,tg=tp&&window.location.href||"http://localhost",ty={...a,isBrowser:!0,classes:{URLSearchParams:th,FormData:tc,Blob:tu},protocols:["http","https","file","blob","url","data"]},tw=function(t){if(q.isFormData(t)&&q.isFunction(t.entries)){let e={};return q.forEachEntry(t,(t,r)=>{!function t(e,r,n,i){let o=e[i++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),a=i>=e.length;return(o=!o&&q.isArray(n)?n.length:o,a)?q.hasOwnProp(n,o)?n[o]=[n[o],r]:n[o]=r:(n[o]&&q.isObject(n[o])||(n[o]=[]),t(e,r,n[o],i)&&q.isArray(n[o])&&(n[o]=function(t){let e,r,n={},i=Object.keys(t),o=i.length;for(e=0;e<o;e++)n[r=i[e]]=t[r];return n}(n[o]))),!s}(q.matchAll(/\w+|\[(\w*)]/g,t).map(t=>"[]"===t[0]?"":t[1]||t[0]),r,e,0)}),e}return null},tb={transitional:tl,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){let r,n=e.getContentType()||"",i=n.indexOf("application/json")>-1,o=q.isObject(t);if(o&&q.isHTMLForm(t)&&(t=new FormData(t)),q.isFormData(t))return i?JSON.stringify(tw(t)):t;if(q.isArrayBuffer(t)||q.isBuffer(t)||q.isStream(t)||q.isFile(t)||q.isBlob(t)||q.isReadableStream(t))return t;if(q.isArrayBufferView(t))return t.buffer;if(q.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1){var s,a;return(s=t,a=this.formSerializer,te(s,new ty.classes.URLSearchParams,Object.assign({visitor:function(t,e,r,n){return ty.isNode&&q.isBuffer(t)?(this.append(e,t.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},a))).toString()}if((r=q.isFileList(t))||n.indexOf("multipart/form-data")>-1){let e=this.env&&this.env.FormData;return te(r?{"files[]":t}:t,e&&new e,this.formSerializer)}}if(o||i){e.setContentType("application/json",!1);var l=t;if(q.isString(l))try{return(0,JSON.parse)(l),q.trim(l)}catch(t){if("SyntaxError"!==t.name)throw t}return(0,JSON.stringify)(l)}return t}],transformResponse:[function(t){let e=this.transitional||tb.transitional,r=e&&e.forcedJSONParsing,n="json"===this.responseType;if(q.isResponse(t)||q.isReadableStream(t))return t;if(t&&q.isString(t)&&(r&&!this.responseType||n)){let r=e&&e.silentJSONParsing;try{return JSON.parse(t)}catch(t){if(!r&&n){if("SyntaxError"===t.name)throw V.from(t,V.ERR_BAD_RESPONSE,this,null,this.response);throw t}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ty.classes.FormData,Blob:ty.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};q.forEach(["delete","get","head","post","put","patch"],t=>{tb.headers[t]={}});let tv=q.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),tk=t=>{let e,r,n,i={};return t&&t.split("\n").forEach(function(t){n=t.indexOf(":"),e=t.substring(0,n).trim().toLowerCase(),r=t.substring(n+1).trim(),!e||i[e]&&tv[e]||("set-cookie"===e?i[e]?i[e].push(r):i[e]=[r]:i[e]=i[e]?i[e]+", "+r:r)}),i},tS=Symbol("internals");function tx(t){return t&&String(t).trim().toLowerCase()}function tA(t){return!1===t||null==t?t:q.isArray(t)?t.map(tA):String(t)}let tE=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function tC(t,e,r,n,i){if(q.isFunction(n))return n.call(this,e,r);if(i&&(e=r),q.isString(e)){if(q.isString(n))return -1!==e.indexOf(n);if(q.isRegExp(n))return n.test(e)}}class tM{constructor(t){t&&this.set(t)}set(t,e,r){let n=this;function i(t,e,r){let i=tx(e);if(!i)throw Error("header name must be a non-empty string");let o=q.findKey(n,i);o&&void 0!==n[o]&&!0!==r&&(void 0!==r||!1===n[o])||(n[o||e]=tA(t))}let o=(t,e)=>q.forEach(t,(t,r)=>i(t,r,e));if(q.isPlainObject(t)||t instanceof this.constructor)o(t,e);else if(q.isString(t)&&(t=t.trim())&&!tE(t))o(tk(t),e);else if(q.isObject(t)&&q.isIterable(t)){let r={},n,i;for(let e of t){if(!q.isArray(e))throw TypeError("Object iterator must return a key-value pair");r[i=e[0]]=(n=r[i])?q.isArray(n)?[...n,e[1]]:[n,e[1]]:e[1]}o(r,e)}else null!=t&&i(e,t,r);return this}get(t,e){if(t=tx(t)){let r=q.findKey(this,t);if(r){let t=this[r];if(!e)return t;if(!0===e){let e,r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;e=n.exec(t);)r[e[1]]=e[2];return r}if(q.isFunction(e))return e.call(this,t,r);if(q.isRegExp(e))return e.exec(t);throw TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=tx(t)){let r=q.findKey(this,t);return!!(r&&void 0!==this[r]&&(!e||tC(this,this[r],r,e)))}return!1}delete(t,e){let r=this,n=!1;function i(t){if(t=tx(t)){let i=q.findKey(r,t);i&&(!e||tC(r,r[i],i,e))&&(delete r[i],n=!0)}}return q.isArray(t)?t.forEach(i):i(t),n}clear(t){let e=Object.keys(this),r=e.length,n=!1;for(;r--;){let i=e[r];(!t||tC(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){let e=this,r={};return q.forEach(this,(n,i)=>{let o=q.findKey(r,i);if(o){e[o]=tA(n),delete e[i];return}let s=t?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,e,r)=>e.toUpperCase()+r):String(i).trim();s!==i&&delete e[i],e[s]=tA(n),r[s]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){let e=Object.create(null);return q.forEach(this,(r,n)=>{null!=r&&!1!==r&&(e[n]=t&&q.isArray(r)?r.join(", "):r)}),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,e])=>t+": "+e).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){let r=new this(t);return e.forEach(t=>r.set(t)),r}static accessor(t){let e=(this[tS]=this[tS]={accessors:{}}).accessors,r=this.prototype;function n(t){let n=tx(t);if(!e[n]){let i=q.toCamelCase(" "+t);["get","set","has"].forEach(e=>{Object.defineProperty(r,e+i,{value:function(r,n,i){return this[e].call(this,t,r,n,i)},configurable:!0})}),e[n]=!0}}return q.isArray(t)?t.forEach(n):n(t),this}}function tT(t,e){let r=this||tb,n=e||r,i=tM.from(n.headers),o=n.data;return q.forEach(t,function(t){o=t.call(r,o,i.normalize(),e?e.status:void 0)}),i.normalize(),o}function tO(t){return!!(t&&t.__CANCEL__)}function tR(t,e,r){V.call(this,null==t?"canceled":t,V.ERR_CANCELED,e,r),this.name="CanceledError"}function tN(t,e,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):e(new V("Request failed with status code "+r.status,[V.ERR_BAD_REQUEST,V.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}tM.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),q.reduceDescriptors(tM.prototype,({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[r]=t}}}),q.freezeMethods(tM),q.inherits(tR,V,{__CANCEL__:!0});let tI=function(t,e){let r,n=Array(t=t||10),i=Array(t),o=0,s=0;return e=void 0!==e?e:1e3,function(a){let l=Date.now(),h=i[s];r||(r=l),n[o]=a,i[o]=l;let c=s,u=0;for(;c!==o;)u+=n[c++],c%=t;if((o=(o+1)%t)===s&&(s=(s+1)%t),l-r<e)return;let p=h&&l-h;return p?Math.round(1e3*u/p):void 0}},tB=function(t,e){let r,n,i=0,o=1e3/e,s=(e,o=Date.now())=>{i=o,r=null,n&&(clearTimeout(n),n=null),t.apply(null,e)};return[(...t)=>{let e=Date.now(),a=e-i;a>=o?s(t,e):(r=t,n||(n=setTimeout(()=>{n=null,s(r)},o-a)))},()=>r&&s(r)]},tL=(t,e,r=3)=>{let n=0,i=tI(50,250);return tB(r=>{let o=r.loaded,s=r.lengthComputable?r.total:void 0,a=o-n,l=i(a);n=o,t({loaded:o,total:s,progress:s?o/s:void 0,bytes:a,rate:l||void 0,estimated:l&&s&&o<=s?(s-o)/l:void 0,event:r,lengthComputable:null!=s,[e?"download":"upload"]:!0})},r)},tz=(t,e)=>{let r=null!=t;return[n=>e[0]({lengthComputable:r,total:t,loaded:n}),e[1]]},tF=t=>(...e)=>q.asap(()=>t(...e)),tj=ty.hasStandardBrowserEnv?((t,e)=>r=>(r=new URL(r,ty.origin),t.protocol===r.protocol&&t.host===r.host&&(e||t.port===r.port)))(new URL(ty.origin),ty.navigator&&/(msie|trident)/i.test(ty.navigator.userAgent)):()=>!0,tP=ty.hasStandardBrowserEnv?{write(t,e,r,n,i,o){let s=[t+"="+encodeURIComponent(e)];q.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),q.isString(n)&&s.push("path="+n),q.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(t){let e=document.cookie.match(RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function tD(t,e,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(n||!1==r)?e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t:e}let tJ=t=>t instanceof tM?{...t}:t;function tU(t,e){e=e||{};let r={};function n(t,e,r,n){return q.isPlainObject(t)&&q.isPlainObject(e)?q.merge.call({caseless:n},t,e):q.isPlainObject(e)?q.merge({},e):q.isArray(e)?e.slice():e}function i(t,e,r,i){return q.isUndefined(e)?q.isUndefined(t)?void 0:n(void 0,t,r,i):n(t,e,r,i)}function o(t,e){if(!q.isUndefined(e))return n(void 0,e)}function s(t,e){return q.isUndefined(e)?q.isUndefined(t)?void 0:n(void 0,t):n(void 0,e)}function a(r,i,o){return o in e?n(r,i):o in t?n(void 0,r):void 0}let l={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(t,e,r)=>i(tJ(t),tJ(e),r,!0)};return q.forEach(Object.keys(Object.assign({},t,e)),function(n){let o=l[n]||i,s=o(t[n],e[n],n);q.isUndefined(s)&&o!==a||(r[n]=s)}),r}let t_=t=>{let e,r=tU({},t),{data:n,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:a,auth:l}=r;if(r.headers=a=tM.from(a),r.url=ts(tD(r.baseURL,r.url,r.allowAbsoluteUrls),t.params,t.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),q.isFormData(n)){if(ty.hasStandardBrowserEnv||ty.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(e=a.getContentType())){let[t,...r]=e?e.split(";").map(t=>t.trim()).filter(Boolean):[];a.setContentType([t||"multipart/form-data",...r].join("; "))}}if(ty.hasStandardBrowserEnv&&(i&&q.isFunction(i)&&(i=i(r)),i||!1!==i&&tj(r.url))){let t=o&&s&&tP.read(s);t&&a.set(o,t)}return r},t$="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise(function(e,r){let n,i,o,s,a,l=t_(t),h=l.data,c=tM.from(l.headers).normalize(),{responseType:u,onUploadProgress:p,onDownloadProgress:f}=l;function d(){s&&s(),a&&a(),l.cancelToken&&l.cancelToken.unsubscribe(n),l.signal&&l.signal.removeEventListener("abort",n)}let m=new XMLHttpRequest;function g(){if(!m)return;let n=tM.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());tN(function(t){e(t),d()},function(t){r(t),d()},{data:u&&"text"!==u&&"json"!==u?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:n,config:t,request:m}),m=null}m.open(l.method.toUpperCase(),l.url,!0),m.timeout=l.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(r(new V("Request aborted",V.ECONNABORTED,t,m)),m=null)},m.onerror=function(){r(new V("Network Error",V.ERR_NETWORK,t,m)),m=null},m.ontimeout=function(){let e=l.timeout?"timeout of "+l.timeout+"ms exceeded":"timeout exceeded",n=l.transitional||tl;l.timeoutErrorMessage&&(e=l.timeoutErrorMessage),r(new V(e,n.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,t,m)),m=null},void 0===h&&c.setContentType(null),"setRequestHeader"in m&&q.forEach(c.toJSON(),function(t,e){m.setRequestHeader(e,t)}),q.isUndefined(l.withCredentials)||(m.withCredentials=!!l.withCredentials),u&&"json"!==u&&(m.responseType=l.responseType),f&&([o,a]=tL(f,!0),m.addEventListener("progress",o)),p&&m.upload&&([i,s]=tL(p),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",s)),(l.cancelToken||l.signal)&&(n=e=>{m&&(r(!e||e.type?new tR(null,t,m):e),m.abort(),m=null)},l.cancelToken&&l.cancelToken.subscribe(n),l.signal&&(l.signal.aborted?n():l.signal.addEventListener("abort",n)));let y=function(t){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(l.url);if(y&&-1===ty.protocols.indexOf(y))return void r(new V("Unsupported protocol "+y+":",V.ERR_BAD_REQUEST,t));m.send(h||null)})},tK=(t,e)=>{let{length:r}=t=t?t.filter(Boolean):[];if(e||r){let r,n=new AbortController,i=function(t){if(!r){r=!0,s();let e=t instanceof Error?t:this.reason;n.abort(e instanceof V?e:new tR(e instanceof Error?e.message:e))}},o=e&&setTimeout(()=>{o=null,i(new V(`timeout ${e} of ms exceeded`,V.ETIMEDOUT))},e),s=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach(t=>{t.unsubscribe?t.unsubscribe(i):t.removeEventListener("abort",i)}),t=null)};t.forEach(t=>t.addEventListener("abort",i));let{signal:a}=n;return a.unsubscribe=()=>q.asap(s),a}},tH=function*(t,e){let r,n=t.byteLength;if(!e||n<e)return void(yield t);let i=0;for(;i<n;)r=i+e,yield t.slice(i,r),i=r},tq=async function*(t,e){for await(let r of tV(t))yield*tH(r,e)},tV=async function*(t){if(t[Symbol.asyncIterator])return void(yield*t);let e=t.getReader();try{for(;;){let{done:t,value:r}=await e.read();if(t)break;yield r}}finally{await e.cancel()}},tW=(t,e,r,n)=>{let i,o=tq(t,e),s=0,a=t=>{!i&&(i=!0,n&&n(t))};return new ReadableStream({async pull(t){try{let{done:e,value:n}=await o.next();if(e){a(),t.close();return}let i=n.byteLength;if(r){let t=s+=i;r(t)}t.enqueue(new Uint8Array(n))}catch(t){throw a(t),t}},cancel:t=>(a(t),o.return())},{highWaterMark:2})},tG="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tX=tG&&"function"==typeof ReadableStream,tZ=tG&&("function"==typeof TextEncoder?(n=new TextEncoder,t=>n.encode(t)):async t=>new Uint8Array(await new Response(t).arrayBuffer())),tY=(t,...e)=>{try{return!!t(...e)}catch(t){return!1}},tQ=tX&&tY(()=>{let t=!1,e=new Request(ty.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),t0=tX&&tY(()=>q.isReadableStream(new Response("").body)),t1={stream:t0&&(t=>t.body)};tG&&(s=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(t=>{t1[t]||(t1[t]=q.isFunction(s[t])?e=>e[t]():(e,r)=>{throw new V(`Response type '${t}' is not supported`,V.ERR_NOT_SUPPORT,r)})}));let t2=async t=>{if(null==t)return 0;if(q.isBlob(t))return t.size;if(q.isSpecCompliantForm(t)){let e=new Request(ty.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return q.isArrayBufferView(t)||q.isArrayBuffer(t)?t.byteLength:(q.isURLSearchParams(t)&&(t+=""),q.isString(t))?(await tZ(t)).byteLength:void 0},t5=async(t,e)=>{let r=q.toFiniteNumber(t.getContentLength());return null==r?t2(e):r},t4={http:null,xhr:t$,fetch:tG&&(async t=>{let e,r,{url:n,method:i,data:o,signal:s,cancelToken:a,timeout:l,onDownloadProgress:h,onUploadProgress:c,responseType:u,headers:p,withCredentials:f="same-origin",fetchOptions:d}=t_(t);u=u?(u+"").toLowerCase():"text";let m=tK([s,a&&a.toAbortSignal()],l),g=m&&m.unsubscribe&&(()=>{m.unsubscribe()});try{if(c&&tQ&&"get"!==i&&"head"!==i&&0!==(r=await t5(p,o))){let t,e=new Request(n,{method:"POST",body:o,duplex:"half"});if(q.isFormData(o)&&(t=e.headers.get("content-type"))&&p.setContentType(t),e.body){let[t,n]=tz(r,tL(tF(c)));o=tW(e.body,65536,t,n)}}q.isString(f)||(f=f?"include":"omit");let s="credentials"in Request.prototype;e=new Request(n,{...d,signal:m,method:i.toUpperCase(),headers:p.normalize().toJSON(),body:o,duplex:"half",credentials:s?f:void 0});let a=await fetch(e,d),l=t0&&("stream"===u||"response"===u);if(t0&&(h||l&&g)){let t={};["status","statusText","headers"].forEach(e=>{t[e]=a[e]});let e=q.toFiniteNumber(a.headers.get("content-length")),[r,n]=h&&tz(e,tL(tF(h),!0))||[];a=new Response(tW(a.body,65536,r,()=>{n&&n(),g&&g()}),t)}u=u||"text";let y=await t1[q.findKey(t1,u)||"text"](a,t);return!l&&g&&g(),await new Promise((r,n)=>{tN(r,n,{data:y,headers:tM.from(a.headers),status:a.status,statusText:a.statusText,config:t,request:e})})}catch(r){if(g&&g(),r&&"TypeError"===r.name&&/Load failed|fetch/i.test(r.message))throw Object.assign(new V("Network Error",V.ERR_NETWORK,t,e),{cause:r.cause||r});throw V.from(r,r&&r.code,t,e)}})};q.forEach(t4,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(t){}Object.defineProperty(t,"adapterName",{value:e})}});let t3=t=>`- ${t}`,t6=t=>q.isFunction(t)||null===t||!1===t,t8={getAdapter:t=>{let e,r,{length:n}=t=q.isArray(t)?t:[t],i={};for(let o=0;o<n;o++){let n;if(r=e=t[o],!t6(e)&&void 0===(r=t4[(n=String(e)).toLowerCase()]))throw new V(`Unknown adapter '${n}'`);if(r)break;i[n||"#"+o]=r}if(!r){let t=Object.entries(i).map(([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build"));throw new V("There is no suitable adapter to dispatch the request "+(n?t.length>1?"since :\n"+t.map(t3).join("\n"):" "+t3(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r}};function t9(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new tR(null,t)}function t7(t){return t9(t),t.headers=tM.from(t.headers),t.data=tT.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1),t8.getAdapter(t.adapter||tb.adapter)(t).then(function(e){return t9(t),e.data=tT.call(t,t.transformResponse,e),e.headers=tM.from(e.headers),e},function(e){return!tO(e)&&(t9(t),e&&e.response&&(e.response.data=tT.call(t,t.transformResponse,e.response),e.response.headers=tM.from(e.response.headers))),Promise.reject(e)})}let et="1.10.0",ee={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{ee[t]=function(r){return typeof r===t||"a"+(e<1?"n ":" ")+t}});let er={};ee.transitional=function(t,e,r){function n(t,e){return"[Axios v"+et+"] Transitional option '"+t+"'"+e+(r?". "+r:"")}return(r,i,o)=>{if(!1===t)throw new V(n(i," has been removed"+(e?" in "+e:"")),V.ERR_DEPRECATED);return e&&!er[i]&&(er[i]=!0,console.warn(n(i," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(r,i,o)}},ee.spelling=function(t){return(e,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};let en={assertOptions:function(t,e,r){if("object"!=typeof t)throw new V("options must be an object",V.ERR_BAD_OPTION_VALUE);let n=Object.keys(t),i=n.length;for(;i-- >0;){let o=n[i],s=e[o];if(s){let e=t[o],r=void 0===e||s(e,o,t);if(!0!==r)throw new V("option "+o+" must be "+r,V.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new V("Unknown option "+o,V.ERR_BAD_OPTION)}},validators:ee},ei=en.validators;class eo{constructor(t){this.defaults=t||{},this.interceptors={request:new ta,response:new ta}}async request(t,e){try{return await this._request(t,e)}catch(t){if(t instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=Error();let r=e.stack?e.stack.replace(/^.+\n/,""):"";try{t.stack?r&&!String(t.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(t.stack+="\n"+r):t.stack=r}catch(t){}}throw t}}_request(t,e){let r,n;"string"==typeof t?(e=e||{}).url=t:e=t||{};let{transitional:i,paramsSerializer:o,headers:s}=e=tU(this.defaults,e);void 0!==i&&en.assertOptions(i,{silentJSONParsing:ei.transitional(ei.boolean),forcedJSONParsing:ei.transitional(ei.boolean),clarifyTimeoutError:ei.transitional(ei.boolean)},!1),null!=o&&(q.isFunction(o)?e.paramsSerializer={serialize:o}:en.assertOptions(o,{encode:ei.function,serialize:ei.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),en.assertOptions(e,{baseUrl:ei.spelling("baseURL"),withXsrfToken:ei.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let a=s&&q.merge(s.common,s[e.method]);s&&q.forEach(["delete","get","head","post","put","patch","common"],t=>{delete s[t]}),e.headers=tM.concat(a,s);let l=[],h=!0;this.interceptors.request.forEach(function(t){("function"!=typeof t.runWhen||!1!==t.runWhen(e))&&(h=h&&t.synchronous,l.unshift(t.fulfilled,t.rejected))});let c=[];this.interceptors.response.forEach(function(t){c.push(t.fulfilled,t.rejected)});let u=0;if(!h){let t=[t7.bind(this),void 0];for(t.unshift.apply(t,l),t.push.apply(t,c),n=t.length,r=Promise.resolve(e);u<n;)r=r.then(t[u++],t[u++]);return r}n=l.length;let p=e;for(u=0;u<n;){let t=l[u++],e=l[u++];try{p=t(p)}catch(t){e.call(this,t);break}}try{r=t7.call(this,p)}catch(t){return Promise.reject(t)}for(u=0,n=c.length;u<n;)r=r.then(c[u++],c[u++]);return r}getUri(t){return ts(tD((t=tU(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}}q.forEach(["delete","get","head","options"],function(t){eo.prototype[t]=function(e,r){return this.request(tU(r||{},{method:t,url:e,data:(r||{}).data}))}}),q.forEach(["post","put","patch"],function(t){function e(e){return function(r,n,i){return this.request(tU(i||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}eo.prototype[t]=e(),eo.prototype[t+"Form"]=e(!0)});class es{constructor(t){let e;if("function"!=typeof t)throw TypeError("executor must be a function.");this.promise=new Promise(function(t){e=t});let r=this;this.promise.then(t=>{if(!r._listeners)return;let e=r._listeners.length;for(;e-- >0;)r._listeners[e](t);r._listeners=null}),this.promise.then=t=>{let e,n=new Promise(t=>{r.subscribe(t),e=t}).then(t);return n.cancel=function(){r.unsubscribe(e)},n},t(function(t,n,i){r.reason||(r.reason=new tR(t,n,i),e(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason)return void t(this.reason);this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;let e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){let t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let t;return{token:new es(function(e){t=e}),cancel:t}}}let ea={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ea).forEach(([t,e])=>{ea[e]=t});let el=function t(e){let r=new eo(e),n=l(eo.prototype.request,r);return q.extend(n,eo.prototype,r,{allOwnKeys:!0}),q.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return t(tU(e,r))},n}(tb);el.Axios=eo,el.CanceledError=tR,el.CancelToken=es,el.isCancel=tO,el.VERSION=et,el.toFormData=te,el.AxiosError=V,el.Cancel=el.CanceledError,el.all=function(t){return Promise.all(t)},el.spread=function(t){return function(e){return t.apply(null,e)}},el.isAxiosError=function(t){return q.isObject(t)&&!0===t.isAxiosError},el.mergeConfig=tU,el.AxiosHeaders=tM,el.formToJSON=t=>tw(q.isHTMLForm(t)?new FormData(t):t),el.getAdapter=t8.getAdapter,el.HttpStatusCode=ea,el.default=el;let eh=el},3500:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},3509:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4311:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("minimize-2",[["path",{d:"m14 10 7-7",key:"oa77jy"}],["path",{d:"M20 10h-6V4",key:"mjg0md"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M4 14h6v6",key:"rmj7iw"}]])},4449:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},4589:(t,e,r)=>{"use strict";r.d(e,{Q:()=>n}),r(6377);let n=r(4701).YY.create({name:"color",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:t=>{var e;return null==(e=t.style.color)?void 0:e.replace(/['"]+/g,"")},renderHTML:t=>t.color?{style:`color: ${t.color}`}:{}}}}]},addCommands:()=>({setColor:t=>({chain:e})=>e().setMark("textStyle",{color:t}).run(),unsetColor:()=>({chain:t})=>t().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()})})},4652:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>s});var n=r(4701);let i=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,o=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,s=n.CU.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:t}){return["mark",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[(0,n.OX)({find:i,type:this.type})]},addPasteRules(){return[(0,n.Zc)({find:o,type:this.type})]}})},5040:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5109:(t,e,r)=>{"use strict";r.d(e,{$Z:()=>m,hG:()=>E});var n,i,o=r(2115),s=r(7650),a=r(4701),l={exports:{}},h={};l.exports=function(){if(n)return h;n=1;var t="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},e=o.useState,r=o.useEffect,i=o.useLayoutEffect,s=o.useDebugValue;function a(e){var r=e.getSnapshot;e=e.value;try{var n=r();return!t(e,n)}catch(t){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,n){var o=n(),l=e({inst:{value:o,getSnapshot:n}}),h=l[0].inst,c=l[1];return i(function(){h.value=o,h.getSnapshot=n,a(h)&&c({inst:h})},[t,o,n]),r(function(){return a(h)&&c({inst:h}),t(function(){a(h)&&c({inst:h})})},[t]),s(o),o};return h.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:l,h}();var c=l.exports;let u=(...t)=>e=>{t.forEach(t=>{"function"==typeof t?t(e):t&&(t.current=e)})},p=({contentComponent:t})=>{let e=c.useSyncExternalStore(t.subscribe,t.getSnapshot,t.getServerSnapshot);return o.createElement(o.Fragment,null,Object.values(e))};class f extends o.Component{constructor(t){var e;super(t),this.editorContentRef=o.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(e=t.editor)?void 0:e.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let t=this.props.editor;if(t&&!t.isDestroyed&&t.options.element){if(t.contentComponent)return;let e=this.editorContentRef.current;e.append(...t.options.element.childNodes),t.setOptions({element:e}),t.contentComponent=function(){let t=new Set,e={};return{subscribe:e=>(t.add(e),()=>{t.delete(e)}),getSnapshot:()=>e,getServerSnapshot:()=>e,setRenderer(r,n){e={...e,[r]:s.createPortal(n.reactElement,n.element,r)},t.forEach(t=>t())},removeRenderer(r){let n={...e};delete n[r],e=n,t.forEach(t=>t())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=t.contentComponent.subscribe(()=>{this.setState(t=>t.hasContentComponentInitialized?t:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),t.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let t=this.props.editor;if(!t||(this.initialized=!1,t.isDestroyed||t.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),t.contentComponent=null,!t.options.element.firstChild))return;let e=document.createElement("div");e.append(...t.options.element.childNodes),t.setOptions({element:e})}render(){let{editor:t,innerRef:e,...r}=this.props;return o.createElement(o.Fragment,null,o.createElement("div",{ref:u(e,this.editorContentRef),...r}),(null==t?void 0:t.contentComponent)&&o.createElement(p,{contentComponent:t.contentComponent}))}}let d=(0,o.forwardRef)((t,e)=>{let r=o.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[t.editor]);return o.createElement(f,{key:r,innerRef:e,...t})}),m=o.memo(d);var g=function(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}(function t(e,r){if(e===r)return!0;if(e&&r&&"object"==typeof e&&"object"==typeof r){if(e.constructor!==r.constructor)return!1;if(Array.isArray(e)){if((n=e.length)!=r.length)return!1;for(i=n;0!=i--;)if(!t(e[i],r[i]))return!1;return!0}if(e instanceof Map&&r instanceof Map){if(e.size!==r.size)return!1;for(i of e.entries())if(!r.has(i[0]))return!1;for(i of e.entries())if(!t(i[1],r.get(i[0])))return!1;return!0}if(e instanceof Set&&r instanceof Set){if(e.size!==r.size)return!1;for(i of e.entries())if(!r.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(r)){if((n=e.length)!=r.length)return!1;for(i=n;0!=i--;)if(e[i]!==r[i])return!1;return!0}if(e.constructor===RegExp)return e.source===r.source&&e.flags===r.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===r.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===r.toString();if((n=(o=Object.keys(e)).length)!==Object.keys(r).length)return!1;for(i=n;0!=i--;)if(!Object.prototype.hasOwnProperty.call(r,o[i]))return!1;for(i=n;0!=i--;){var n,i,o,s=o[i];if(("_owner"!==s||!e.$$typeof)&&!t(e[s],r[s]))return!1}return!0}return e!=e&&r!=r}),y={exports:{}},w={};y.exports=function(){if(i)return w;i=1;var t="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},e=c.useSyncExternalStore,r=o.useRef,n=o.useEffect,s=o.useMemo,a=o.useDebugValue;return w.useSyncExternalStoreWithSelector=function(i,o,l,h,c){var u=r(null);if(null===u.current){var p={hasValue:!1,value:null};u.current=p}else p=u.current;var f=e(i,(u=s(function(){function e(e){if(!i){if(i=!0,r=e,e=h(e),void 0!==c&&p.hasValue){var o=p.value;if(c(o,e))return n=o}return n=e}if(o=n,t(r,e))return o;var s=h(e);return void 0!==c&&c(o,s)?o:(r=e,n=s)}var r,n,i=!1,s=void 0===l?null:l;return[function(){return e(o())},null===s?void 0:function(){return e(s())}]},[o,l,h,c]))[0],u[1]);return n(function(){p.hasValue=!0,p.value=f},[f]),a(f),f},w}();var b=y.exports;let v="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;class k{constructor(t){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=t,this.lastSnapshot={editor:t,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(t){return this.subscribers.add(t),()=>{this.subscribers.delete(t)}}watch(t){if(this.editor=t,this.editor){let t=()=>{this.transactionNumber+=1,this.subscribers.forEach(t=>t())},e=this.editor;return e.on("transaction",t),()=>{e.off("transaction",t)}}}}let S="undefined"==typeof window,x=S||!!("undefined"!=typeof window&&window.next);class A{constructor(t){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=t,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(t){this.editor=t,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(t=>t())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?S||x?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){let t={...this.options.current,onBeforeCreate:(...t)=>{var e,r;return null==(r=(e=this.options.current).onBeforeCreate)?void 0:r.call(e,...t)},onBlur:(...t)=>{var e,r;return null==(r=(e=this.options.current).onBlur)?void 0:r.call(e,...t)},onCreate:(...t)=>{var e,r;return null==(r=(e=this.options.current).onCreate)?void 0:r.call(e,...t)},onDestroy:(...t)=>{var e,r;return null==(r=(e=this.options.current).onDestroy)?void 0:r.call(e,...t)},onFocus:(...t)=>{var e,r;return null==(r=(e=this.options.current).onFocus)?void 0:r.call(e,...t)},onSelectionUpdate:(...t)=>{var e,r;return null==(r=(e=this.options.current).onSelectionUpdate)?void 0:r.call(e,...t)},onTransaction:(...t)=>{var e,r;return null==(r=(e=this.options.current).onTransaction)?void 0:r.call(e,...t)},onUpdate:(...t)=>{var e,r;return null==(r=(e=this.options.current).onUpdate)?void 0:r.call(e,...t)},onContentError:(...t)=>{var e,r;return null==(r=(e=this.options.current).onContentError)?void 0:r.call(e,...t)},onDrop:(...t)=>{var e,r;return null==(r=(e=this.options.current).onDrop)?void 0:r.call(e,...t)},onPaste:(...t)=>{var e,r;return null==(r=(e=this.options.current).onPaste)?void 0:r.call(e,...t)}};return new a.KE(t)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(t){return this.subscriptions.add(t),()=>{this.subscriptions.delete(t)}}static compareOptions(t,e){return Object.keys(t).every(r=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(r)||("extensions"===r&&t.extensions&&e.extensions?t.extensions.length===e.extensions.length&&t.extensions.every((t,r)=>{var n;return t===(null==(n=e.extensions)?void 0:n[r])}):t[r]===e[r]))}onRender(t){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===t.length?A.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(t),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(t){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=t;return}if(this.previousDeps.length===t.length&&this.previousDeps.every((e,r)=>e===t[r]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=t}scheduleDestroy(){let t=this.instanceId,e=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===t){e&&e.setOptions(this.options.current);return}e&&!e.isDestroyed&&(e.destroy(),this.instanceId===t&&this.setEditor(null))},1)}}function E(t={},e=[]){let r=(0,o.useRef)(t);r.current=t;let[n]=(0,o.useState)(()=>new A(r)),i=c.useSyncExternalStore(n.subscribe,n.getEditor,n.getServerSnapshot);return(0,o.useDebugValue)(i),(0,o.useEffect)(n.onRender(e)),!function(t){var e;let[r]=(0,o.useState)(()=>new k(t.editor)),n=b.useSyncExternalStoreWithSelector(r.subscribe,r.getSnapshot,r.getServerSnapshot,t.selector,null!=(e=t.equalityFn)?e:g);v(()=>r.watch(t.editor),[t.editor,r]),(0,o.useDebugValue)(n)}({editor:i,selector:({transactionNumber:e})=>!1===t.shouldRerenderOnTransaction?null:t.immediatelyRender&&0===e?0:e+1}),i}let C=((0,o.createContext)({editor:null}).Consumer,(0,o.createContext)({onDragStart:void 0})),M=()=>(0,o.useContext)(C);o.forwardRef((t,e)=>{let{onDragStart:r}=M(),n=t.as||"div";return o.createElement(n,{...t,ref:e,"data-node-view-wrapper":"",onDragStart:r,style:{whiteSpace:"normal",...t.style}})});class T{constructor(t,{editor:e,props:r={},as:n="div",className:i=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=t,this.editor=e,this.props=r,this.element=document.createElement(n),this.element.classList.add("react-renderer"),i&&this.element.classList.add(...i.split(" ")),this.editor.isInitialized?flushSync(()=>{this.render()}):this.render()}render(){var t,e;let r=this.component,n=this.props,i=this.editor,o=function(){try{if(version)return parseInt(version.split(".")[0],10)>=19}catch{}return!1}(),s=!!("function"==typeof r&&r.prototype&&r.prototype.isReactComponent),a="object"==typeof r&&(null==(e=r.$$typeof)?void 0:e.toString())==="Symbol(react.forward_ref)",l={...n};!l.ref&&(o?l.ref=t=>{this.ref=t}:(s||a)&&(l.ref=t=>{this.ref=t})),this.reactElement=React.createElement(r,{...l}),null==(t=null==i?void 0:i.contentComponent)||t.setRenderer(this.id,this)}updateProps(t={}){this.props={...this.props,...t},this.render()}destroy(){var t;let e=this.editor;null==(t=null==e?void 0:e.contentComponent)||t.removeRenderer(this.id)}updateAttributes(t){Object.keys(t).forEach(e=>{this.element.setAttribute(e,t[e])})}}},5339:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5690:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6377:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(4701);let i=t=>{if(!t.children.length)return;let e=t.querySelectorAll("span");e&&e.forEach(t=>{var e,r;let n=t.getAttribute("style"),i=null==(r=null==(e=t.parentElement)?void 0:e.closest("span"))?void 0:r.getAttribute("style");t.setAttribute("style",`${i};${n}`)})},o=n.CU.create({name:"textStyle",priority:101,addOptions:()=>({HTMLAttributes:{},mergeNestedSpanStyles:!1}),parseHTML(){return[{tag:"span",getAttrs:t=>!!t.hasAttribute("style")&&(this.options.mergeNestedSpanStyles&&i(t),{})}]},renderHTML({HTMLAttributes:t}){return["span",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{removeEmptyTextStyle:()=>({tr:t})=>{let{selection:e}=t;return t.doc.nodesBetween(e.from,e.to,(e,r)=>{if(e.isTextblock)return!0;e.marks.filter(t=>t.type===this.type).some(t=>Object.values(t.attrs).some(t=>!!t))||t.removeMark(r,r+e.nodeSize,this.type)}),!0}}}})},6517:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},6675:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("skip-forward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]])},6770:(t,e,r)=>{"use strict";r.d(e,{K:()=>d,w:()=>f});for(var n={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},i={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},o="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),s="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),a=0;a<10;a++)n[48+a]=n[96+a]=String(a);for(var a=1;a<=24;a++)n[a+111]="F"+a;for(var a=65;a<=90;a++)n[a]=String.fromCharCode(a+32),i[a]=String.fromCharCode(a);for(var l in n)i.hasOwnProperty(l)||(i[l]=n[l]);var h=r(2571);let c="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),u="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function p(t,e,r=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),r&&e.shiftKey&&(t="Shift-"+t),t}function f(t){return new h.k_({props:{handleKeyDown:d(t)}})}function d(t){let e=function(t){let e=Object.create(null);for(let r in t)e[function(t){let e,r,n,i,o=t.split(/-(?!$)/),s=o[o.length-1];"Space"==s&&(s=" ");for(let t=0;t<o.length-1;t++){let s=o[t];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))e=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else if(/^s(hift)?$/i.test(s))n=!0;else if(/^mod$/i.test(s))c?i=!0:r=!0;else throw Error("Unrecognized modifier name: "+s)}return e&&(s="Alt-"+s),r&&(s="Ctrl-"+s),i&&(s="Meta-"+s),n&&(s="Shift-"+s),s}(r)]=t[r];return e}(t);return function(t,r){var a;let l=("Esc"==(a=!(o&&r.metaKey&&r.shiftKey&&!r.ctrlKey&&!r.altKey||s&&r.shiftKey&&r.key&&1==r.key.length||"Unidentified"==r.key)&&r.key||(r.shiftKey?i:n)[r.keyCode]||r.key||"Unidentified")&&(a="Escape"),"Del"==a&&(a="Delete"),"Left"==a&&(a="ArrowLeft"),"Up"==a&&(a="ArrowUp"),"Right"==a&&(a="ArrowRight"),"Down"==a&&(a="ArrowDown"),a),h,c=e[p(l,r)];if(c&&c(t.state,t.dispatch,t))return!0;if(1==l.length&&" "!=l){if(r.shiftKey){let n=e[p(l,r,!1)];if(n&&n(t.state,t.dispatch,t))return!0}if((r.altKey||r.metaKey||r.ctrlKey)&&!(u&&r.ctrlKey&&r.altKey)&&(h=n[r.keyCode])&&h!=l){let n=e[p(h,r)];if(n&&n(t.state,t.dispatch,t))return!0}}return!1}}},7108:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7213:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7434:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7580:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8264:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("presentation",[["path",{d:"M2 3h20",key:"91anmk"}],["path",{d:"M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3",key:"2k9sn8"}],["path",{d:"m7 21 5-5 5 5",key:"bip4we"}]])},8292:(t,e,r)=>{"use strict";r.d(e,{A:()=>ty});var n=r(4701);let i=/^\s*>\s$/,o=n.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:t}){return["blockquote",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,n.tG)({find:i,type:this.type})]}}),s=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,a=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,l=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,h=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,c=n.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>"normal"!==t.style.fontWeight&&null},{style:"font-weight=400",clearMark:t=>t.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return["strong",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,n.OX)({find:s,type:this.type}),(0,n.OX)({find:l,type:this.type})]},addPasteRules(){return[(0,n.Zc)({find:a,type:this.type}),(0,n.Zc)({find:h,type:this.type})]}}),u="textStyle",p=/^\s*([-+*])\s$/,f=n.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(u)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=(0,n.tG)({find:p,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,n.tG)({find:p,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(u),editor:this.editor})),[t]}}),d=/(^|[^`])`([^`]+)`(?!`)/,m=/(^|[^`])`([^`]+)`(?!`)/g,g=n.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:t}){return["code",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,n.OX)({find:d,type:this.type})]},addPasteRules(){return[(0,n.Zc)({find:m,type:this.type})]}});var y=r(2571);let w=/^```([a-z]+)?[\s\n]$/,b=/^~~~([a-z]+)?[\s\n]$/,v=n.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:t=>{var e;let{languageClassPrefix:r}=this.options,n=[...(null==(e=t.firstElementChild)?void 0:e.classList)||[]].filter(t=>t.startsWith(r)).map(t=>t.replace(r,""))[0];return n||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:t,HTMLAttributes:e}){return["pre",(0,n.KV)(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:t,$anchor:e}=this.editor.state.selection,r=1===e.pos;return!!t&&e.parent.type.name===this.name&&(!!r||!e.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:e}=t,{selection:r}=e,{$from:n,empty:i}=r;if(!i||n.parent.type!==this.type)return!1;let o=n.parentOffset===n.parent.nodeSize-2,s=n.parent.textContent.endsWith("\n\n");return!!o&&!!s&&t.chain().command(({tr:t})=>(t.delete(n.pos-2,n.pos),!0)).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;let{state:e}=t,{selection:r,doc:n}=e,{$from:i,empty:o}=r;if(!o||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let s=i.after();return void 0!==s&&(n.nodeAt(s)?t.commands.command(({tr:t})=>(t.setSelection(y.LN.near(n.resolve(s))),!0)):t.commands.exitCode())}}},addInputRules(){return[(0,n.JJ)({find:w,type:this.type,getAttributes:t=>({language:t[1]})}),(0,n.JJ)({find:b,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new y.k_({key:new y.hs("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;let r=e.clipboardData.getData("text/plain"),n=e.clipboardData.getData("vscode-editor-data"),i=n?JSON.parse(n):void 0,o=null==i?void 0:i.mode;if(!r||!o)return!1;let{tr:s,schema:a}=t.state,l=a.text(r.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:o},l)),s.selection.$from.parent.type!==this.type&&s.setSelection(y.U3.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),t.dispatch(s),!0}}})]}}),k=n.bP.create({name:"doc",topNode:!0,content:"block+"});var S=r(808);class x{constructor(t,e){var r;this.editorView=t,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(r=e.width)?r:1,this.color=!1===e.color?void 0:e.color||"black",this.class=e.class,this.handlers=["dragover","dragend","drop","dragleave"].map(e=>{let r=t=>{this[e](t)};return t.dom.addEventListener(e,r),{name:e,handler:r}})}destroy(){this.handlers.forEach(({name:t,handler:e})=>this.editorView.dom.removeEventListener(t,e))}update(t,e){null!=this.cursorPos&&e.doc!=t.state.doc&&(this.cursorPos>t.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(t){t!=this.cursorPos&&(this.cursorPos=t,null==t?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let t,e,r=this.editorView.state.doc.resolve(this.cursorPos),n=!r.parent.inlineContent,i,o=this.editorView.dom,s=o.getBoundingClientRect(),a=s.width/o.offsetWidth,l=s.height/o.offsetHeight;if(n){let t=r.nodeBefore,e=r.nodeAfter;if(t||e){let r=this.editorView.nodeDOM(this.cursorPos-(t?t.nodeSize:0));if(r){let n=r.getBoundingClientRect(),o=t?n.bottom:n.top;t&&e&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let s=this.width/2*l;i={left:n.left,right:n.right,top:o-s,bottom:o+s}}}}if(!i){let t=this.editorView.coordsAtPos(this.cursorPos),e=this.width/2*a;i={left:t.left-e,right:t.left+e,top:t.top,bottom:t.bottom}}let h=this.editorView.dom.offsetParent;if(!this.element&&(this.element=h.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",n),this.element.classList.toggle("prosemirror-dropcursor-inline",!n),h&&(h!=document.body||"static"!=getComputedStyle(h).position)){let r=h.getBoundingClientRect(),n=r.width/h.offsetWidth,i=r.height/h.offsetHeight;t=r.left-h.scrollLeft*n,e=r.top-h.scrollTop*i}else t=-pageXOffset,e=-pageYOffset;this.element.style.left=(i.left-t)/a+"px",this.element.style.top=(i.top-e)/l+"px",this.element.style.width=(i.right-i.left)/a+"px",this.element.style.height=(i.bottom-i.top)/l+"px"}scheduleRemoval(t){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),t)}dragover(t){if(!this.editorView.editable)return;let e=this.editorView.posAtCoords({left:t.clientX,top:t.clientY}),r=e&&e.inside>=0&&this.editorView.state.doc.nodeAt(e.inside),n=r&&r.type.spec.disableDropCursor,i="function"==typeof n?n(this.editorView,e,t):n;if(e&&!i){let t=e.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let e=(0,S.Um)(this.editorView.state.doc,t,this.editorView.dragging.slice);null!=e&&(t=e)}this.setCursor(t),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(t){this.editorView.dom.contains(t.relatedTarget)||this.setCursor(null)}}let A=n.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(t={}){return new y.k_({view:e=>new x(e,t)})}(this.options)]}});var E=r(6770),C=r(156),M=r(2695);class T extends y.LN{constructor(t){super(t,t)}map(t,e){let r=t.resolve(e.map(this.head));return T.valid(r)?new T(r):y.LN.near(r)}content(){return C.Ji.empty}eq(t){return t instanceof T&&t.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new T(t.resolve(e.pos))}getBookmark(){return new O(this.anchor)}static valid(t){let e=t.parent;if(e.isTextblock||!function(t){for(let e=t.depth;e>=0;e--){let r=t.index(e),n=t.node(e);if(0==r){if(n.type.spec.isolating)return!0;continue}for(let t=n.child(r-1);;t=t.lastChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}}return!0}(t)||!function(t){for(let e=t.depth;e>=0;e--){let r=t.indexAfter(e),n=t.node(e);if(r==n.childCount){if(n.type.spec.isolating)return!0;continue}for(let t=n.child(r);;t=t.firstChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}}return!0}(t))return!1;let r=e.type.spec.allowGapCursor;if(null!=r)return r;let n=e.contentMatchAt(t.index()).defaultType;return n&&n.isTextblock}static findGapCursorFrom(t,e,r=!1){r:for(;;){if(!r&&T.valid(t))return t;let n=t.pos,i=null;for(let r=t.depth;;r--){let o=t.node(r);if(e>0?t.indexAfter(r)<o.childCount:t.index(r)>0){i=o.child(e>0?t.indexAfter(r):t.index(r)-1);break}if(0==r)return null;n+=e;let s=t.doc.resolve(n);if(T.valid(s))return s}for(;;){let o=e>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!y.nh.isSelectable(i)){t=t.doc.resolve(n+i.nodeSize*e),r=!1;continue r}break}i=o,n+=e;let s=t.doc.resolve(n);if(T.valid(s))return s}return null}}}T.prototype.visible=!1,T.findFrom=T.findGapCursorFrom,y.LN.jsonID("gapcursor",T);class O{constructor(t){this.pos=t}map(t){return new O(t.map(this.pos))}resolve(t){let e=t.resolve(this.pos);return T.valid(e)?new T(e):y.LN.near(e)}}let R=(0,E.K)({ArrowLeft:N("horiz",-1),ArrowRight:N("horiz",1),ArrowUp:N("vert",-1),ArrowDown:N("vert",1)});function N(t,e){let r="vert"==t?e>0?"down":"up":e>0?"right":"left";return function(t,n,i){let o=t.selection,s=e>0?o.$to:o.$from,a=o.empty;if(o instanceof y.U3){if(!i.endOfTextblock(r)||0==s.depth)return!1;a=!1,s=t.doc.resolve(e>0?s.after():s.before())}let l=T.findGapCursorFrom(s,e,a);return!!l&&(n&&n(t.tr.setSelection(new T(l))),!0)}}function I(t,e,r){if(!t||!t.editable)return!1;let n=t.state.doc.resolve(e);if(!T.valid(n))return!1;let i=t.posAtCoords({left:r.clientX,top:r.clientY});return!(i&&i.inside>-1&&y.nh.isSelectable(t.state.doc.nodeAt(i.inside)))&&(t.dispatch(t.state.tr.setSelection(new T(n))),!0)}function B(t,e){if("insertCompositionText"!=e.inputType||!(t.state.selection instanceof T))return!1;let{$from:r}=t.state.selection,n=r.parent.contentMatchAt(r.index()).findWrapping(t.state.schema.nodes.text);if(!n)return!1;let i=C.FK.empty;for(let t=n.length-1;t>=0;t--)i=C.FK.from(n[t].createAndFill(null,i));let o=t.state.tr.replace(r.pos,r.pos,new C.Ji(i,0,0));return o.setSelection(y.U3.near(o.doc.resolve(r.pos+1))),t.dispatch(o),!1}function L(t){if(!(t.selection instanceof T))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",M.zF.create(t.doc,[M.NZ.widget(t.selection.head,e,{key:"gapcursor"})])}let z=n.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new y.k_({props:{decorations:L,createSelectionBetween:(t,e,r)=>e.pos==r.pos&&T.valid(r)?new T(r):null,handleClick:I,handleKeyDown:R,handleDOMEvents:{beforeinput:B}}})],extendNodeSchema(t){var e;let r={name:t.name,options:t.options,storage:t.storage};return{allowGapCursor:null!=(e=(0,n.gk)((0,n.iI)(t,"allowGapCursor",r)))?e:null}}}),F=n.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:t}){return["br",(0,n.KV)(this.options.HTMLAttributes,t)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:t,chain:e,state:r,editor:n})=>t.first([()=>t.exitCode(),()=>t.command(()=>{let{selection:t,storedMarks:i}=r;if(t.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:s}=n.extensionManager,a=i||t.$to.parentOffset&&t.$from.marks();return e().insertContent({type:this.name}).command(({tr:t,dispatch:e})=>{if(e&&a&&o){let e=a.filter(t=>s.includes(t.type.name));t.ensureMarks(e)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),j=n.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(t=>({tag:`h${t}`,attrs:{level:t}}))},renderHTML({node:t,HTMLAttributes:e}){let r=this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0];return[`h${r}`,(0,n.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.setNode(this.name,t),toggleHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return this.options.levels.reduce((t,e)=>({...t,...{[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}}),{})},addInputRules(){return this.options.levels.map(t=>(0,n.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}}))}});var P=function(){};P.prototype.append=function(t){return t.length?(t=P.from(t),!this.length&&t||t.length<200&&this.leafAppend(t)||this.length<200&&t.leafPrepend(this)||this.appendInner(t)):this},P.prototype.prepend=function(t){return t.length?P.from(t).append(this):this},P.prototype.appendInner=function(t){return new J(this,t)},P.prototype.slice=function(t,e){return(void 0===t&&(t=0),void 0===e&&(e=this.length),t>=e)?P.empty:this.sliceInner(Math.max(0,t),Math.min(this.length,e))},P.prototype.get=function(t){if(!(t<0)&&!(t>=this.length))return this.getInner(t)},P.prototype.forEach=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=this.length),e<=r?this.forEachInner(t,e,r,0):this.forEachInvertedInner(t,e,r,0)},P.prototype.map=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=this.length);var n=[];return this.forEach(function(e,r){return n.push(t(e,r))},e,r),n},P.from=function(t){return t instanceof P?t:t&&t.length?new D(t):P.empty};var D=function(t){function e(e){t.call(this),this.values=e}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(t,r){return 0==t&&r==this.length?this:new e(this.values.slice(t,r))},e.prototype.getInner=function(t){return this.values[t]},e.prototype.forEachInner=function(t,e,r,n){for(var i=e;i<r;i++)if(!1===t(this.values[i],n+i))return!1},e.prototype.forEachInvertedInner=function(t,e,r,n){for(var i=e-1;i>=r;i--)if(!1===t(this.values[i],n+i))return!1},e.prototype.leafAppend=function(t){if(this.length+t.length<=200)return new e(this.values.concat(t.flatten()))},e.prototype.leafPrepend=function(t){if(this.length+t.length<=200)return new e(t.flatten().concat(this.values))},r.length.get=function(){return this.values.length},r.depth.get=function(){return 0},Object.defineProperties(e.prototype,r),e}(P);P.empty=new D([]);var J=function(t){function e(e,r){t.call(this),this.left=e,this.right=r,this.length=e.length+r.length,this.depth=Math.max(e.depth,r.depth)+1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,e,r,n){var i=this.left.length;if(e<i&&!1===this.left.forEachInner(t,e,Math.min(r,i),n)||r>i&&!1===this.right.forEachInner(t,Math.max(e-i,0),Math.min(this.length,r)-i,n+i))return!1},e.prototype.forEachInvertedInner=function(t,e,r,n){var i=this.left.length;if(e>i&&!1===this.right.forEachInvertedInner(t,e-i,Math.max(r,i)-i,n+i)||r<i&&!1===this.left.forEachInvertedInner(t,Math.min(e,i),r,n))return!1},e.prototype.sliceInner=function(t,e){if(0==t&&e==this.length)return this;var r=this.left.length;return e<=r?this.left.slice(t,e):t>=r?this.right.slice(t-r,e-r):this.left.slice(t,r).append(this.right.slice(0,e-r))},e.prototype.leafAppend=function(t){var r=this.right.leafAppend(t);if(r)return new e(this.left,r)},e.prototype.leafPrepend=function(t){var r=this.left.leafPrepend(t);if(r)return new e(r,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(P);class U{constructor(t,e){this.items=t,this.eventCount=e}popEvent(t,e){let r,n,i,o;if(0==this.eventCount)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}e&&(n=(r=this.remapping(s,this.items.length)).maps.length);let a=t.tr,l=[],h=[];return this.items.forEach((t,e)=>{if(!t.step){r||(n=(r=this.remapping(s,e+1)).maps.length),n--,h.push(t);return}if(r){h.push(new _(t.map));let e=t.step.map(r.slice(n)),i;e&&a.maybeStep(e).doc&&(i=a.mapping.maps[a.mapping.maps.length-1],l.push(new _(i,void 0,void 0,l.length+h.length))),n--,i&&r.appendMap(i,n)}else a.maybeStep(t.step);if(t.selection)return i=r?t.selection.map(r.slice(n)):t.selection,o=new U(this.items.slice(0,s).append(h.reverse().concat(l)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:a,selection:i}}addTransform(t,e,r,n){var i,o;let s,a=[],l=this.eventCount,h=this.items,c=!n&&h.length?h.get(h.length-1):null;for(let r=0;r<t.steps.length;r++){let i=t.steps[r].invert(t.docs[r]),o=new _(t.mapping.maps[r],i,e),s;(s=c&&c.merge(o))&&(o=s,r?a.pop():h=h.slice(0,h.length-1)),a.push(o),e&&(l++,e=void 0),n||(c=o)}let u=l-r.depth;return u>K&&(i=h,o=u,i.forEach((t,e)=>{if(t.selection&&0==o--)return s=e,!1}),h=i.slice(s),l-=u),new U(h.append(a),l)}remapping(t,e){let r=new S.X9;return this.items.forEach((e,n)=>{let i=null!=e.mirrorOffset&&n-e.mirrorOffset>=t?r.maps.length-e.mirrorOffset:void 0;r.appendMap(e.map,i)},t,e),r}addMaps(t){return 0==this.eventCount?this:new U(this.items.append(t.map(t=>new _(t))),this.eventCount)}rebased(t,e){if(!this.eventCount)return this;let r=[],n=Math.max(0,this.items.length-e),i=t.mapping,o=t.steps.length,s=this.eventCount;this.items.forEach(t=>{t.selection&&s--},n);let a=e;this.items.forEach(e=>{let n=i.getMirror(--a);if(null==n)return;o=Math.min(o,n);let l=i.maps[n];if(e.step){let o=t.steps[n].invert(t.docs[n]),h=e.selection&&e.selection.map(i.slice(a+1,n));h&&s++,r.push(new _(l,o,h))}else r.push(new _(l))},n);let l=[];for(let t=e;t<o;t++)l.push(new _(i.maps[t]));let h=new U(this.items.slice(0,n).append(l).append(r),s);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-r.length)),h}emptyItemCount(){let t=0;return this.items.forEach(e=>{!e.step&&t++}),t}compress(t=this.items.length){let e=this.remapping(0,t),r=e.maps.length,n=[],i=0;return this.items.forEach((o,s)=>{if(s>=t)n.push(o),o.selection&&i++;else if(o.step){let t=o.step.map(e.slice(r)),s=t&&t.getMap();if(r--,s&&e.appendMap(s,r),t){let a=o.selection&&o.selection.map(e.slice(r));a&&i++;let l=new _(s.invert(),t,a),h,c=n.length-1;(h=n.length&&n[c].merge(l))?n[c]=h:n.push(l)}}else o.map&&r--},this.items.length,0),new U(P.from(n.reverse()),i)}}U.empty=new U(P.empty,0);class _{constructor(t,e,r,n){this.map=t,this.step=e,this.selection=r,this.mirrorOffset=n}merge(t){if(this.step&&t.step&&!t.selection){let e=t.step.merge(this.step);if(e)return new _(e.getMap().invert(),e,this.selection)}}}class ${constructor(t,e,r,n,i){this.done=t,this.undone=e,this.prevRanges=r,this.prevTime=n,this.prevComposition=i}}let K=20;function H(t){let e=[];for(let r=t.length-1;r>=0&&0==e.length;r--)t[r].forEach((t,r,n,i)=>e.push(n,i));return e}function q(t,e){if(!t)return null;let r=[];for(let n=0;n<t.length;n+=2){let i=e.map(t[n],1),o=e.map(t[n+1],-1);i<=o&&r.push(i,o)}return r}let V=!1,W=null;function G(t){let e=t.plugins;if(W!=e){V=!1,W=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){V=!0;break}}return V}let X=new y.hs("history"),Z=new y.hs("closeHistory");function Y(t,e){return(r,n)=>{let i=X.getState(r);if(!i||0==(t?i.undone:i.done).eventCount)return!1;if(n){let o=function(t,e,r){let n=G(e),i=X.get(e).spec.config,o=(r?t.undone:t.done).popEvent(e,n);if(!o)return null;let s=o.selection.resolve(o.transform.doc),a=(r?t.done:t.undone).addTransform(o.transform,e.selection.getBookmark(),i,n),l=new $(r?a:o.remaining,r?o.remaining:a,null,0,-1);return o.transform.setSelection(s).setMeta(X,{redo:r,historyState:l})}(i,r,t);o&&n(e?o.scrollIntoView():o)}return!0}}let Q=Y(!1,!0),tt=Y(!0,!0);Y(!1,!1),Y(!0,!1);let te=n.YY.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:t,dispatch:e})=>Q(t,e),redo:()=>({state:t,dispatch:e})=>tt(t,e)}),addProseMirrorPlugins(){return[function(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new y.k_({key:X,state:{init:()=>new $(U.empty,U.empty,null,0,-1),apply:(e,r,n)=>(function(t,e,r,n){let i=r.getMeta(X),o;if(i)return i.historyState;r.getMeta(Z)&&(t=new $(t.done,t.undone,null,0,-1));let s=r.getMeta("appendedTransaction");if(0==r.steps.length)return t;if(s&&s.getMeta(X))if(s.getMeta(X).redo)return new $(t.done.addTransform(r,void 0,n,G(e)),t.undone,H(r.mapping.maps),t.prevTime,t.prevComposition);else return new $(t.done,t.undone.addTransform(r,void 0,n,G(e)),null,t.prevTime,t.prevComposition);if(!1===r.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))if(o=r.getMeta("rebased"))return new $(t.done.rebased(r,o),t.undone.rebased(r,o),q(t.prevRanges,r.mapping),t.prevTime,t.prevComposition);else return new $(t.done.addMaps(r.mapping.maps),t.undone.addMaps(r.mapping.maps),q(t.prevRanges,r.mapping),t.prevTime,t.prevComposition);{let i=r.getMeta("composition"),o=0==t.prevTime||!s&&t.prevComposition!=i&&(t.prevTime<(r.time||0)-n.newGroupDelay||!function(t,e){if(!e)return!1;if(!t.docChanged)return!0;let r=!1;return t.mapping.maps[0].forEach((t,n)=>{for(let i=0;i<e.length;i+=2)t<=e[i+1]&&n>=e[i]&&(r=!0)}),r}(r,t.prevRanges)),a=s?q(t.prevRanges,r.mapping):H(r.mapping.maps);return new $(t.done.addTransform(r,o?e.selection.getBookmark():void 0,n,G(e)),U.empty,a,r.time,null==i?t.prevComposition:i)}})(r,n,e,t)},config:t,props:{handleDOMEvents:{beforeinput(t,e){let r=e.inputType,n="historyUndo"==r?Q:"historyRedo"==r?tt:null;return!!n&&(e.preventDefault(),n(t.state,t.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),tr=n.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:t}){return["hr",(0,n.KV)(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{let{selection:r}=e,{$from:i,$to:o}=r,s=t();return 0===i.parentOffset?s.insertContentAt({from:Math.max(i.pos-1,0),to:o.pos},{type:this.name}):(0,n.BQ)(r)?s.insertContentAt(o.pos,{type:this.name}):s.insertContent({type:this.name}),s.command(({tr:t,dispatch:e})=>{var r;if(e){let{$to:e}=t.selection,n=e.end();if(e.nodeAfter)e.nodeAfter.isTextblock?t.setSelection(y.U3.create(t.doc,e.pos+1)):e.nodeAfter.isBlock?t.setSelection(y.nh.create(t.doc,e.pos)):t.setSelection(y.U3.create(t.doc,e.pos));else{let i=null==(r=e.parent.type.contentMatch.defaultType)?void 0:r.create();i&&(t.insert(n,i),t.setSelection(y.U3.create(t.doc,n+1)))}t.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,n.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),tn=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,ti=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,to=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,ts=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,ta=n.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>"normal"!==t.style.fontStyle&&null},{style:"font-style=normal",clearMark:t=>t.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,n.OX)({find:tn,type:this.type}),(0,n.OX)({find:to,type:this.type})]},addPasteRules(){return[(0,n.Zc)({find:ti,type:this.type}),(0,n.Zc)({find:ts,type:this.type})]}}),tl=n.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",(0,n.KV)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),th="textStyle",tc=/^(\d+)\.\s$/,tu=n.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1},type:{default:null,parseHTML:t=>t.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:t}){let{start:e,...r}=t;return 1===e?["ol",(0,n.KV)(this.options.HTMLAttributes,r),0]:["ol",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(th)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=(0,n.tG)({find:tc,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,n.tG)({find:tc,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(th)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[t]}}),tp=n.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),tf=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,td=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,tm=n.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:t=>!!t.includes("line-through")&&{}}],renderHTML({HTMLAttributes:t}){return["s",(0,n.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,n.OX)({find:tf,type:this.type})]},addPasteRules(){return[(0,n.Zc)({find:td,type:this.type})]}}),tg=n.bP.create({name:"text",group:"inline"}),ty=n.YY.create({name:"starterKit",addExtensions(){let t=[];return!1!==this.options.bold&&t.push(c.configure(this.options.bold)),!1!==this.options.blockquote&&t.push(o.configure(this.options.blockquote)),!1!==this.options.bulletList&&t.push(f.configure(this.options.bulletList)),!1!==this.options.code&&t.push(g.configure(this.options.code)),!1!==this.options.codeBlock&&t.push(v.configure(this.options.codeBlock)),!1!==this.options.document&&t.push(k.configure(this.options.document)),!1!==this.options.dropcursor&&t.push(A.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&t.push(z.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&t.push(F.configure(this.options.hardBreak)),!1!==this.options.heading&&t.push(j.configure(this.options.heading)),!1!==this.options.history&&t.push(te.configure(this.options.history)),!1!==this.options.horizontalRule&&t.push(tr.configure(this.options.horizontalRule)),!1!==this.options.italic&&t.push(ta.configure(this.options.italic)),!1!==this.options.listItem&&t.push(tl.configure(this.options.listItem)),!1!==this.options.orderedList&&t.push(tu.configure(this.options.orderedList)),!1!==this.options.paragraph&&t.push(tp.configure(this.options.paragraph)),!1!==this.options.strike&&t.push(tm.configure(this.options.strike)),!1!==this.options.text&&t.push(tg.configure(this.options.text)),t}})},9376:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9389:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("skip-back",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]])},9397:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=l(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=l(t),s=o[0],a=o[1],h=new i((s+a)*3/4-a),c=0,u=a>0?s-4:s;for(r=0;r<u;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],h[c++]=e>>16&255,h[c++]=e>>8&255,h[c++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,h[c++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,h[c++]=e>>8&255,h[c++]=255&e),h},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(function(t,e,n){for(var i,o=[],s=e;s<n;s+=3)i=(t[s]<<16&0xff0000)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,s,s+16383>a?a:s+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function l(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(t,e,r){"use strict";var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return l(t,e,r)}function l(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|f(n,i),l=s(o),h=l.write(n,i);return h!==o&&(l=l.slice(0,h)),l}if(ArrayBuffer.isView(t))return u(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(O(t,ArrayBuffer)||t&&O(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(O(t,SharedArrayBuffer)||t&&O(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var c=t.valueOf&&t.valueOf();if(null!=c&&c!==t)return a.from(c,e,r);var d=function(t){if(a.isBuffer(t)){var e=0|p(t.length),r=s(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?s(0):u(t):"Buffer"===t.type&&Array.isArray(t.data)?u(t.data):void 0}(t);if(d)return d;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function h(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return h(t),s(t<0?0:0|p(t))}function u(t){for(var e=t.length<0?0:0|p(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return l(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(h(t),t<=0)?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)},a.allocUnsafe=function(t){return c(t)},a.allocUnsafeSlow=function(t){return c(t)};function p(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function f(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||O(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return E(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return M(t).length;default:if(i)return n?-1:E(t).length;e=(""+e).toLowerCase(),i=!0}}function d(t,e,r){var i,o,s,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=R[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return w(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,o=e,s=r,0===o&&s===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function m(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function g(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:y(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return y(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function y(t,e,r,n,i){var o,s=1,a=t.length,l=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;s=2,a/=2,l/=2,r/=2}function h(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var c=-1;for(o=r;o<a;o++)if(h(t,o)===h(e,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===l)return c*s}else -1!==c&&(o-=o-c),c=-1}else for(r+l>a&&(r=a-l),o=r;o>=0;o--){for(var u=!0,p=0;p<l;p++)if(h(t,o+p)!==h(e,p)){u=!1;break}if(u)return o}return -1}a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(O(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),O(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(O(o,Uint8Array)&&(o=a.from(o)),!a.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},a.byteLength=f,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)m(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)m(this,e,e+3),m(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)m(this,e,e+7),m(this,e+1,e+6),m(this,e+2,e+5),m(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?w(this,0,t):d.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(O(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,s=r-e,l=Math.min(o,s),h=this.slice(n,i),c=t.slice(e,r),u=0;u<l;++u)if(h[u]!==c[u]){o=h[u],s=c[u];break}return o<s?-1:+(s<o)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)};function w(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,l,h=t[i],c=null,u=h>239?4:h>223?3:h>191?2:1;if(i+u<=r)switch(u){case 1:h<128&&(c=h);break;case 2:(192&(o=t[i+1]))==128&&(l=(31&h)<<6|63&o)>127&&(c=l);break;case 3:o=t[i+1],s=t[i+2],(192&o)==128&&(192&s)==128&&(l=(15&h)<<12|(63&o)<<6|63&s)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],(192&o)==128&&(192&s)==128&&(192&a)==128&&(l=(15&h)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(c=l)}null===c?(c=65533,u=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=u}var p=n,f=p.length;if(f<=4096)return String.fromCharCode.apply(String,p);for(var d="",m=0;m<f;)d+=String.fromCharCode.apply(String,p.slice(m,m+=4096));return d}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function v(t,e,r,n,i,o){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function k(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function S(t,e,r,n,o){return e*=1,r>>>=0,o||k(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function x(t,e,r,n,o){return e*=1,r>>>=0,o||k(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,a,l,h,c,u,p=this.length-e;if((void 0===r||r>p)&&(r=p),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var f=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a,l=parseInt(e.substr(2*s,2),16);if((a=l)!=a)break;t[r+s]=l}return s}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,T(E(t,this.length-i),this,i,o);case"ascii":return s=e,a=r,T(C(t),this,s,a);case"latin1":case"binary":return function(t,e,r,n){return T(C(e),t,r,n)}(this,t,e,r);case"base64":return l=e,h=r,T(M(t),this,l,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=e,u=r,T(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-c),this,c,u);default:if(f)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),f=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},a.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;v(this,t,e,r,i,0)}var o=1,s=0;for(this[e]=255&t;++s<r&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;v(this,t,e,r,i,0)}var o=r-1,s=1;for(this[e+o]=255&t;--o>=0&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);v(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);v(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||v(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return S(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return S(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return x(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return x(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var o=i-1;o>=0;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var s=a.isBuffer(t)?t:a.from(t,n),l=s.length;if(0===l)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=s[i%l]}return this};var A=/[^+/0-9A-Za-z-_]/g;function E(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function C(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function M(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(A,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function T(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function O(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var R=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,l=(1<<a)-1,h=l>>1,c=-7,u=r?i-1:0,p=r?-1:1,f=t[e+u];for(u+=p,o=f&(1<<-c)-1,f>>=-c,c+=a;c>0;o=256*o+t[e+u],u+=p,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=n;c>0;s=256*s+t[e+u],u+=p,c-=8);if(0===o)o=1-h;else{if(o===l)return s?NaN:1/0*(f?-1:1);s+=Math.pow(2,n),o-=h}return(f?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,l,h=8*o-i-1,c=(1<<h)-1,u=c>>1,p=5960464477539062e-23*(23===i),f=n?0:o-1,d=n?1:-1,m=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),s=c):(s=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+u>=1?e+=p/l:e+=p*Math.pow(2,1-u),e*l>=2&&(s++,l/=2),s+u>=c?(a=0,s=c):s+u>=1?(a=(e*l-1)*Math.pow(2,i),s+=u):(a=e*Math.pow(2,u-1)*Math.pow(2,i),s=0));i>=8;t[r+f]=255&a,f+=d,a/=256,i-=8);for(s=s<<i|a,h+=i;h>0;t[r+f]=255&s,f+=d,s/=256,h-=8);t[r+f-d]|=128*m}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var o=r[t]={exports:{}},s=!0;try{e[t](o,o.exports,n),s=!1}finally{s&&delete r[t]}return o.exports}n.ab="//",t.exports=n(72)}()},9803:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},9946:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(2115);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase()),s=t=>{let e=o(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((t,e)=>{let{color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:u,iconNode:p,...f}=t;return(0,n.createElement)("svg",{ref:e,...h,width:i,height:i,stroke:r,strokeWidth:s?24*Number(o)/Number(i):o,className:a("lucide",c),...!u&&!l(f)&&{"aria-hidden":"true"},...f},[...p.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(u)?u:[u]])}),u=(t,e)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:l,...h}=r;return(0,n.createElement)(c,{ref:o,iconNode:e,className:a("lucide-".concat(i(s(t))),"lucide-".concat(t),l),...h})});return r.displayName=s(t),r}}}]);