'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Highlight from '@tiptap/extension-highlight';
import TextStyle from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import { useState, useCallback } from 'react';
import { Sparkles, Type, Palette, Eye, Brain, Loader2 } from 'lucide-react';
import { useAnalysis } from '@/hooks/useAnalysis';

interface TextEditorProps {
  onTextSelect?: (text: string) => void;
  onContentChange?: (content: string) => void;
  onAnalysisComplete?: (analysis: any) => void;
}

export default function TextEditor({ onTextSelect, onContentChange, onAnalysisComplete }: TextEditorProps) {
  const [selectedText, setSelectedText] = useState('');
  const [showToolbar, setShowToolbar] = useState(false);
  const { analyzeText, loading: analyzing, error: analysisError } = useAnalysis();

  const editor = useEditor({
    extensions: [
      StarterKit,
      Highlight.configure({
        multicolor: true,
      }),
      TextStyle,
      Color,
    ],
    content: `
      <p>A <mark data-color="#fbbf24">mitose</mark> é um processo fundamental da divisão celular onde uma célula se divide para formar duas células filhas geneticamente idênticas.</p>
      
      <p>Durante a <mark data-color="#fbbf24">prófase</mark>, os cromossomos se condensam e tornam-se visíveis ao microscópio. O envelope nuclear começa a se desintegrar.</p>
      
      <p>Na <mark data-color="#fbbf24">metáfase</mark>, os cromossomos se alinham no centro da célula, formando a placa metafásica. Este é um momento crucial para garantir que cada célula filha receba o número correto de cromossomos.</p>
      
      <p>Durante a <mark data-color="#fbbf24">anáfase</mark>, as cromátides irmãs se separam e migram para polos opostos da célula.</p>
      
      <p>Finalmente, na <mark data-color="#fbbf24">telófase</mark>, novos envelopes nucleares se formam ao redor de cada conjunto de cromossomos, completando o processo de divisão.</p>
    `,
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      onContentChange?.(content);
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection;
      const text = editor.state.doc.textBetween(from, to, '');
      
      if (text.trim().length > 0) {
        setSelectedText(text.trim());
        setShowToolbar(true);
        onTextSelect?.(text.trim());
      } else {
        setSelectedText('');
        setShowToolbar(false);
      }
    },
  });

  const highlightSelection = useCallback((color: string) => {
    if (editor && selectedText) {
      editor.chain().focus().toggleHighlight({ color }).run();
      onTextSelect?.(selectedText);
    }
  }, [editor, selectedText, onTextSelect]);

  const generateVisualization = useCallback(async (visualizationType: 'image' | 'video' = 'image') => {
    if (selectedText && !analyzing) {
      try {
        const analysis = await analyzeText(selectedText, '', visualizationType);
        onAnalysisComplete?.(analysis);
        onTextSelect?.(selectedText);
      } catch (error) {
        console.error('Analysis failed:', error);
      }
    }
  }, [selectedText, analyzing, analyzeText, onAnalysisComplete, onTextSelect]);

  if (!editor) {
    return (
      <div className="border rounded-lg p-4 min-h-[400px] bg-gray-50 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Floating Toolbar */}
      {showToolbar && selectedText && (
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mb-2 z-10">
          <div className="bg-white border rounded-lg shadow-lg p-2 flex items-center space-x-2">
            <span className="text-xs text-gray-600 px-2">"{selectedText.slice(0, 20)}..."</span>
            <div className="w-px h-6 bg-gray-200"></div>
            
            {/* Highlight Colors */}
            <button
              onClick={() => highlightSelection('#fbbf24')}
              className="w-6 h-6 bg-yellow-300 rounded hover:scale-110 transition-transform"
              title="Destacar em amarelo"
            />
            <button
              onClick={() => highlightSelection('#60a5fa')}
              className="w-6 h-6 bg-blue-400 rounded hover:scale-110 transition-transform"
              title="Destacar em azul"
            />
            <button
              onClick={() => highlightSelection('#34d399')}
              className="w-6 h-6 bg-green-400 rounded hover:scale-110 transition-transform"
              title="Destacar em verde"
            />
            
            <div className="w-px h-6 bg-gray-200"></div>
            
            {/* Generate Visualization Buttons */}
            <button
              onClick={() => generateVisualization('image')}
              disabled={analyzing}
              className="flex items-center space-x-1 px-3 py-1 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {analyzing ? <Loader2 className="h-3 w-3 animate-spin" /> : <Sparkles className="h-3 w-3" />}
              <span>Imagem</span>
            </button>

            <button
              onClick={() => generateVisualization('video')}
              disabled={analyzing}
              className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {analyzing ? <Loader2 className="h-3 w-3 animate-spin" /> : <Brain className="h-3 w-3" />}
              <span>Vídeo</span>
            </button>
          </div>
        </div>
      )}

      {/* Editor Toolbar */}
      <div className="border-b p-2 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`p-2 rounded hover:bg-gray-200 transition-colors ${
              editor.isActive('bold') ? 'bg-gray-200' : ''
            }`}
          >
            <Type className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`p-2 rounded hover:bg-gray-200 transition-colors ${
              editor.isActive('italic') ? 'bg-gray-200' : ''
            }`}
          >
            <span className="text-sm font-serif italic">I</span>
          </button>

          <div className="w-px h-6 bg-gray-300"></div>

          <button
            onClick={() => editor.chain().focus().toggleHighlight({ color: '#fbbf24' }).run()}
            className={`p-2 rounded hover:bg-gray-200 transition-colors ${
              editor.isActive('highlight') ? 'bg-gray-200' : ''
            }`}
          >
            <Palette className="h-4 w-4" />
          </button>

          <div className="flex-1"></div>

          <div className="flex items-center space-x-2 text-xs text-gray-600">
            <Eye className="h-4 w-4" />
            <span>Destaque termos para gerar visualizações</span>
          </div>
        </div>
      </div>

      {/* Editor Content */}
      <div className="border border-t-0 rounded-b-lg p-4 min-h-[400px] bg-white prose prose-sm max-w-none">
        <EditorContent editor={editor} />
      </div>

      {/* Selected Text Info */}
      {selectedText && (
        <div className="mt-2 p-2 bg-indigo-50 border border-indigo-200 rounded text-sm">
          <span className="text-indigo-700 font-medium">Texto selecionado:</span>
          <span className="text-indigo-600 ml-2">"{selectedText}"</span>
          {analyzing && (
            <div className="flex items-center mt-2 text-indigo-600">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span>Analisando com IA...</span>
            </div>
          )}
        </div>
      )}

      {/* Analysis Error */}
      {analysisError && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm">
          <span className="text-red-700 font-medium">Erro na análise:</span>
          <span className="text-red-600 ml-2">{analysisError}</span>
        </div>
      )}
    </div>
  );
}
