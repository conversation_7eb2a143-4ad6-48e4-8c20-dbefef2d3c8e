name: Deploy StudyVision AI

on:
  push:
    branches: [main, production]
  pull_request:
    branches: [main]

jobs:
  lint-and-type-check:
    runs-on: ubuntu-latest
    name: <PERSON><PERSON> and Type Check
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: npm run lint
        
      - name: Run Type Check
        run: npm run type-check

  build:
    runs-on: ubuntu-latest
    name: Build Application
    needs: lint-and-type-check
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: .next/

  deploy-vercel:
    runs-on: ubuntu-latest
    name: Deploy to Vercel
    needs: build
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          
  deploy-docker:
    runs-on: ubuntu-latest
    name: Build and Push Docker Image
    needs: build
    if: github.ref == 'refs/heads/production' && github.event_name == 'push'
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
          
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            studyvision/app:latest
            studyvision/app:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  notify:
    runs-on: ubuntu-latest
    name: Notify Deployment
    needs: [deploy-vercel, deploy-docker]
    if: always()
    
    steps:
      - name: Notify Success
        if: needs.deploy-vercel.result == 'success' || needs.deploy-docker.result == 'success'
        run: |
          echo "🚀 Deployment successful!"
          echo "✅ StudyVision AI has been deployed successfully"
          
      - name: Notify Failure
        if: needs.deploy-vercel.result == 'failure' || needs.deploy-docker.result == 'failure'
        run: |
          echo "❌ Deployment failed!"
          echo "🔧 Please check the logs and fix the issues"
