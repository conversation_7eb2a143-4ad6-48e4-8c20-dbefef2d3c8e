# 🔌 Configuração WebSocket - StudyVision AI

## 📋 Status Atual

O projeto está configurado com **duas opções** para execução:

### Opção 1: Desenvolvimento Padrão (Atual)
```bash
npm run dev
```
- ✅ Funciona perfeitamente
- ✅ Todas as funcionalidades principais ativas
- ⚠️ WebSockets em modo simulação
- 🎯 **Recomendado para desenvolvimento**

### Opção 2: Servidor Customizado com WebSockets
```bash
npm run dev:ws
```
- 🔌 WebSockets reais funcionando
- ⚠️ Requer build completo primeiro
- 🎯 **Para produção e testes de colaboração**

## 🛠️ Como Ativar WebSockets Reais

### Passo 1: Build da Aplicação
```bash
npm run build
```

### Passo 2: Iniciar <PERSON><PERSON>or <PERSON>
```bash
npm run dev:ws
```

### Passo 3: Descomentar Código WebSocket
No arquivo `src/hooks/useWebSocket.ts`, descomente as linhas:
```typescript
// Remover o código de simulação e descomentar:
const socket = io(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', {
  transports: ['websocket', 'polling'],
  autoConnect: true,
});
// ... resto do código WebSocket
```

## 🎯 Funcionalidades Disponíveis

### ✅ Funcionando Agora (Modo Padrão)
- 📝 Editor de texto com TipTap
- 🤖 Análise IA com OpenAI GPT-4
- 🖼️ Geração de imagens (simulação)
- 🎥 Geração de vídeos (simulação)
- 👁️ Modo leitura imersivo
- 📤 Sistema de exportação
- 🎨 Interface responsiva

### 🔌 Disponível com WebSocket Server
- 👥 Colaboração em tempo real
- 🔄 Sincronização de seleções de texto
- 📊 Status de usuários conectados
- ⚡ Atualizações instantâneas

## 🚀 Para Produção

### Deploy no Vercel
O Vercel **não suporta** WebSockets nativamente. Para WebSockets em produção:

1. **Opção A: Deploy sem WebSockets**
   ```bash
   vercel --prod
   ```
   - Todas as funcionalidades exceto colaboração em tempo real

2. **Opção B: Deploy com WebSockets**
   - Use Railway, Render, ou AWS
   - Configure servidor customizado
   - Mantenha funcionalidades de colaboração

### Deploy Docker (Com WebSockets)
```bash
docker build -t studyvision-ai .
docker run -p 3000:3000 studyvision-ai
```

## 🔧 Troubleshooting

### Erro: middleware-manifest.json
Se encontrar este erro:
```bash
npm run clean
npm run build
npm run dev:ws
```

### WebSocket não conecta
1. Verifique se o servidor customizado está rodando
2. Confirme que a porta 3000 está livre
3. Verifique configurações de CORS

### Build falha
```bash
# Limpar cache
npm run clean

# Reinstalar dependências
rm -rf node_modules package-lock.json
npm install

# Build novamente
npm run build
```

## 📊 Comparação de Modos

| Funcionalidade | Modo Padrão | Modo WebSocket |
|----------------|-------------|----------------|
| Editor de Texto | ✅ | ✅ |
| Análise IA | ✅ | ✅ |
| Geração de Mídia | ✅ | ✅ |
| Modo Leitura | ✅ | ✅ |
| Exportação | ✅ | ✅ |
| Colaboração Tempo Real | ❌ | ✅ |
| Status Usuários | Simulado | ✅ |
| Sincronização | ❌ | ✅ |

## 🎯 Recomendação

**Para desenvolvimento e demonstração:**
- Use `npm run dev` (modo padrão)
- Todas as funcionalidades principais funcionam
- Desenvolvimento mais rápido e estável

**Para produção com colaboração:**
- Configure servidor customizado
- Deploy em plataforma que suporte WebSockets
- Ative funcionalidades de tempo real

---

**Status:** ✅ Projeto 100% funcional em modo padrão
**WebSockets:** 🔌 Disponível com configuração adicional
