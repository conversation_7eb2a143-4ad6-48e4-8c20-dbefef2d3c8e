"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-transform";
exports.ids = ["vendor-chunks/prosemirror-transform"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-transform/dist/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/prosemirror-transform/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddMarkStep: () => (/* binding */ AddMarkStep),\n/* harmony export */   AddNodeMarkStep: () => (/* binding */ AddNodeMarkStep),\n/* harmony export */   AttrStep: () => (/* binding */ AttrStep),\n/* harmony export */   DocAttrStep: () => (/* binding */ DocAttrStep),\n/* harmony export */   MapResult: () => (/* binding */ MapResult),\n/* harmony export */   Mapping: () => (/* binding */ Mapping),\n/* harmony export */   RemoveMarkStep: () => (/* binding */ RemoveMarkStep),\n/* harmony export */   RemoveNodeMarkStep: () => (/* binding */ RemoveNodeMarkStep),\n/* harmony export */   ReplaceAroundStep: () => (/* binding */ ReplaceAroundStep),\n/* harmony export */   ReplaceStep: () => (/* binding */ ReplaceStep),\n/* harmony export */   Step: () => (/* binding */ Step),\n/* harmony export */   StepMap: () => (/* binding */ StepMap),\n/* harmony export */   StepResult: () => (/* binding */ StepResult),\n/* harmony export */   Transform: () => (/* binding */ Transform),\n/* harmony export */   TransformError: () => (/* binding */ TransformError),\n/* harmony export */   canJoin: () => (/* binding */ canJoin),\n/* harmony export */   canSplit: () => (/* binding */ canSplit),\n/* harmony export */   dropPoint: () => (/* binding */ dropPoint),\n/* harmony export */   findWrapping: () => (/* binding */ findWrapping),\n/* harmony export */   insertPoint: () => (/* binding */ insertPoint),\n/* harmony export */   joinPoint: () => (/* binding */ joinPoint),\n/* harmony export */   liftTarget: () => (/* binding */ liftTarget),\n/* harmony export */   replaceStep: () => (/* binding */ replaceStep)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n\n\n// Recovery values encode a range index and an offset. They are\n// represented as numbers, because tons of them will be created when\n// mapping, for example, a large number of decorations. The number's\n// lower 16 bits provide the index, the remaining bits the offset.\n//\n// Note: We intentionally don't use bit shift operators to en- and\n// decode these, since those clip to 32 bits, which we might in rare\n// cases want to overflow. A 64-bit float can represent 48-bit\n// integers precisely.\nconst lower16 = 0xffff;\nconst factor16 = Math.pow(2, 16);\nfunction makeRecover(index, offset) { return index + offset * factor16; }\nfunction recoverIndex(value) { return value & lower16; }\nfunction recoverOffset(value) { return (value - (value & lower16)) / factor16; }\nconst DEL_BEFORE = 1, DEL_AFTER = 2, DEL_ACROSS = 4, DEL_SIDE = 8;\n/**\nAn object representing a mapped position with extra\ninformation.\n*/\nclass MapResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The mapped version of the position.\n    */\n    pos, \n    /**\n    @internal\n    */\n    delInfo, \n    /**\n    @internal\n    */\n    recover) {\n        this.pos = pos;\n        this.delInfo = delInfo;\n        this.recover = recover;\n    }\n    /**\n    Tells you whether the position was deleted, that is, whether the\n    step removed the token on the side queried (via the `assoc`)\n    argument from the document.\n    */\n    get deleted() { return (this.delInfo & DEL_SIDE) > 0; }\n    /**\n    Tells you whether the token before the mapped position was deleted.\n    */\n    get deletedBefore() { return (this.delInfo & (DEL_BEFORE | DEL_ACROSS)) > 0; }\n    /**\n    True when the token after the mapped position was deleted.\n    */\n    get deletedAfter() { return (this.delInfo & (DEL_AFTER | DEL_ACROSS)) > 0; }\n    /**\n    Tells whether any of the steps mapped through deletes across the\n    position (including both the token before and after the\n    position).\n    */\n    get deletedAcross() { return (this.delInfo & DEL_ACROSS) > 0; }\n}\n/**\nA map describing the deletions and insertions made by a step, which\ncan be used to find the correspondence between positions in the\npre-step version of a document and the same position in the\npost-step version.\n*/\nclass StepMap {\n    /**\n    Create a position map. The modifications to the document are\n    represented as an array of numbers, in which each group of three\n    represents a modified chunk as `[start, oldSize, newSize]`.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    ranges, \n    /**\n    @internal\n    */\n    inverted = false) {\n        this.ranges = ranges;\n        this.inverted = inverted;\n        if (!ranges.length && StepMap.empty)\n            return StepMap.empty;\n    }\n    /**\n    @internal\n    */\n    recover(value) {\n        let diff = 0, index = recoverIndex(value);\n        if (!this.inverted)\n            for (let i = 0; i < index; i++)\n                diff += this.ranges[i * 3 + 2] - this.ranges[i * 3 + 1];\n        return this.ranges[index * 3] + diff + recoverOffset(value);\n    }\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    map(pos, assoc = 1) { return this._map(pos, assoc, true); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let diff = 0, oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex], end = start + oldSize;\n            if (pos <= end) {\n                let side = !oldSize ? assoc : pos == start ? -1 : pos == end ? 1 : assoc;\n                let result = start + diff + (side < 0 ? 0 : newSize);\n                if (simple)\n                    return result;\n                let recover = pos == (assoc < 0 ? start : end) ? null : makeRecover(i / 3, pos - start);\n                let del = pos == start ? DEL_AFTER : pos == end ? DEL_BEFORE : DEL_ACROSS;\n                if (assoc < 0 ? pos != start : pos != end)\n                    del |= DEL_SIDE;\n                return new MapResult(result, del, recover);\n            }\n            diff += newSize - oldSize;\n        }\n        return simple ? pos + diff : new MapResult(pos + diff, 0, null);\n    }\n    /**\n    @internal\n    */\n    touches(pos, recover) {\n        let diff = 0, index = recoverIndex(recover);\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], end = start + oldSize;\n            if (pos <= end && i == index * 3)\n                return true;\n            diff += this.ranges[i + newIndex] - oldSize;\n        }\n        return false;\n    }\n    /**\n    Calls the given function on each of the changed ranges included in\n    this map.\n    */\n    forEach(f) {\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0, diff = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i], oldStart = start - (this.inverted ? diff : 0), newStart = start + (this.inverted ? 0 : diff);\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex];\n            f(oldStart, oldStart + oldSize, newStart, newStart + newSize);\n            diff += newSize - oldSize;\n        }\n    }\n    /**\n    Create an inverted version of this map. The result can be used to\n    map positions in the post-step document to the pre-step document.\n    */\n    invert() {\n        return new StepMap(this.ranges, !this.inverted);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return (this.inverted ? \"-\" : \"\") + JSON.stringify(this.ranges);\n    }\n    /**\n    Create a map that moves all positions by offset `n` (which may be\n    negative). This can be useful when applying steps meant for a\n    sub-document to a larger document, or vice-versa.\n    */\n    static offset(n) {\n        return n == 0 ? StepMap.empty : new StepMap(n < 0 ? [0, -n, 0] : [0, 0, n]);\n    }\n}\n/**\nA StepMap that contains no changed ranges.\n*/\nStepMap.empty = new StepMap([]);\n/**\nA mapping represents a pipeline of zero or more [step\nmaps](https://prosemirror.net/docs/ref/#transform.StepMap). It has special provisions for losslessly\nhandling mapping positions through a series of steps in which some\nsteps are inverted versions of earlier steps. (This comes up when\n‘[rebasing](https://prosemirror.net/docs/guide/#transform.rebasing)’ steps for\ncollaboration or history management.)\n*/\nclass Mapping {\n    /**\n    Create a new mapping with the given position maps.\n    */\n    constructor(maps, \n    /**\n    @internal\n    */\n    mirror, \n    /**\n    The starting position in the `maps` array, used when `map` or\n    `mapResult` is called.\n    */\n    from = 0, \n    /**\n    The end position in the `maps` array.\n    */\n    to = maps ? maps.length : 0) {\n        this.mirror = mirror;\n        this.from = from;\n        this.to = to;\n        this._maps = maps || [];\n        this.ownData = !(maps || mirror);\n    }\n    /**\n    The step maps in this mapping.\n    */\n    get maps() { return this._maps; }\n    /**\n    Create a mapping that maps only through a part of this one.\n    */\n    slice(from = 0, to = this.maps.length) {\n        return new Mapping(this._maps, this.mirror, from, to);\n    }\n    /**\n    Add a step map to the end of this mapping. If `mirrors` is\n    given, it should be the index of the step map that is the mirror\n    image of this one.\n    */\n    appendMap(map, mirrors) {\n        if (!this.ownData) {\n            this._maps = this._maps.slice();\n            this.mirror = this.mirror && this.mirror.slice();\n            this.ownData = true;\n        }\n        this.to = this._maps.push(map);\n        if (mirrors != null)\n            this.setMirror(this._maps.length - 1, mirrors);\n    }\n    /**\n    Add all the step maps in a given mapping to this one (preserving\n    mirroring information).\n    */\n    appendMapping(mapping) {\n        for (let i = 0, startSize = this._maps.length; i < mapping._maps.length; i++) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i], mirr != null && mirr < i ? startSize + mirr : undefined);\n        }\n    }\n    /**\n    Finds the offset of the step map that mirrors the map at the\n    given offset, in this mapping (as per the second argument to\n    `appendMap`).\n    */\n    getMirror(n) {\n        if (this.mirror)\n            for (let i = 0; i < this.mirror.length; i++)\n                if (this.mirror[i] == n)\n                    return this.mirror[i + (i % 2 ? -1 : 1)];\n    }\n    /**\n    @internal\n    */\n    setMirror(n, m) {\n        if (!this.mirror)\n            this.mirror = [];\n        this.mirror.push(n, m);\n    }\n    /**\n    Append the inverse of the given mapping to this one.\n    */\n    appendMappingInverted(mapping) {\n        for (let i = mapping.maps.length - 1, totalSize = this._maps.length + mapping._maps.length; i >= 0; i--) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i].invert(), mirr != null && mirr > i ? totalSize - mirr - 1 : undefined);\n        }\n    }\n    /**\n    Create an inverted version of this mapping.\n    */\n    invert() {\n        let inverse = new Mapping;\n        inverse.appendMappingInverted(this);\n        return inverse;\n    }\n    /**\n    Map a position through this mapping.\n    */\n    map(pos, assoc = 1) {\n        if (this.mirror)\n            return this._map(pos, assoc, true);\n        for (let i = this.from; i < this.to; i++)\n            pos = this._maps[i].map(pos, assoc);\n        return pos;\n    }\n    /**\n    Map a position through this mapping, returning a mapping\n    result.\n    */\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let delInfo = 0;\n        for (let i = this.from; i < this.to; i++) {\n            let map = this._maps[i], result = map.mapResult(pos, assoc);\n            if (result.recover != null) {\n                let corr = this.getMirror(i);\n                if (corr != null && corr > i && corr < this.to) {\n                    i = corr;\n                    pos = this._maps[corr].recover(result.recover);\n                    continue;\n                }\n            }\n            delInfo |= result.delInfo;\n            pos = result.pos;\n        }\n        return simple ? pos : new MapResult(pos, delInfo, null);\n    }\n}\n\nconst stepsByID = Object.create(null);\n/**\nA step object represents an atomic change. It generally applies\nonly to the document it was created for, since the positions\nstored in it will only make sense for that document.\n\nNew steps are defined by creating classes that extend `Step`,\noverriding the `apply`, `invert`, `map`, `getMap` and `fromJSON`\nmethods, and registering your class with a unique\nJSON-serialization identifier using\n[`Step.jsonID`](https://prosemirror.net/docs/ref/#transform.Step^jsonID).\n*/\nclass Step {\n    /**\n    Get the step map that represents the changes made by this step,\n    and which can be used to transform between positions in the old\n    and the new document.\n    */\n    getMap() { return StepMap.empty; }\n    /**\n    Try to merge this step with another one, to be applied directly\n    after it. Returns the merged step when possible, null if the\n    steps can't be merged.\n    */\n    merge(other) { return null; }\n    /**\n    Deserialize a step from its JSON representation. Will call\n    through to the step class' own implementation of this method.\n    */\n    static fromJSON(schema, json) {\n        if (!json || !json.stepType)\n            throw new RangeError(\"Invalid input for Step.fromJSON\");\n        let type = stepsByID[json.stepType];\n        if (!type)\n            throw new RangeError(`No step type ${json.stepType} defined`);\n        return type.fromJSON(schema, json);\n    }\n    /**\n    To be able to serialize steps to JSON, each step needs a string\n    ID to attach to its JSON representation. Use this method to\n    register an ID for your step classes. Try to pick something\n    that's unlikely to clash with steps from other modules.\n    */\n    static jsonID(id, stepClass) {\n        if (id in stepsByID)\n            throw new RangeError(\"Duplicate use of step JSON ID \" + id);\n        stepsByID[id] = stepClass;\n        stepClass.prototype.jsonID = id;\n        return stepClass;\n    }\n}\n/**\nThe result of [applying](https://prosemirror.net/docs/ref/#transform.Step.apply) a step. Contains either a\nnew document or a failure value.\n*/\nclass StepResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The transformed document, if successful.\n    */\n    doc, \n    /**\n    The failure message, if unsuccessful.\n    */\n    failed) {\n        this.doc = doc;\n        this.failed = failed;\n    }\n    /**\n    Create a successful step result.\n    */\n    static ok(doc) { return new StepResult(doc, null); }\n    /**\n    Create a failed step result.\n    */\n    static fail(message) { return new StepResult(null, message); }\n    /**\n    Call [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) with the given\n    arguments. Create a successful result if it succeeds, and a\n    failed one if it throws a `ReplaceError`.\n    */\n    static fromReplace(doc, from, to, slice) {\n        try {\n            return StepResult.ok(doc.replace(from, to, slice));\n        }\n        catch (e) {\n            if (e instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.ReplaceError)\n                return StepResult.fail(e.message);\n            throw e;\n        }\n    }\n}\n\nfunction mapFragment(fragment, f, parent) {\n    let mapped = [];\n    for (let i = 0; i < fragment.childCount; i++) {\n        let child = fragment.child(i);\n        if (child.content.size)\n            child = child.copy(mapFragment(child.content, f, child));\n        if (child.isInline)\n            child = f(child, parent, i);\n        mapped.push(child);\n    }\n    return prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.fromArray(mapped);\n}\n/**\nAdd a mark to all inline content between two positions.\n*/\nclass AddMarkStep extends Step {\n    /**\n    Create a mark step.\n    */\n    constructor(\n    /**\n    The start of the marked range.\n    */\n    from, \n    /**\n    The end of the marked range.\n    */\n    to, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to), $from = doc.resolve(this.from);\n        let parent = $from.node($from.sharedDepth(this.to));\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(mapFragment(oldSlice.content, (node, parent) => {\n            if (!node.isAtom || !parent.type.allowsMarkType(this.mark.type))\n                return node;\n            return node.mark(this.mark.addToSet(node.marks));\n        }, parent), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new RemoveMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new AddMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof AddMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new AddMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"addMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for AddMarkStep.fromJSON\");\n        return new AddMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addMark\", AddMarkStep);\n/**\nRemove a mark from all inline content between two positions.\n*/\nclass RemoveMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The start of the unmarked range.\n    */\n    from, \n    /**\n    The end of the unmarked range.\n    */\n    to, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to);\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(mapFragment(oldSlice.content, node => {\n            return node.mark(this.mark.removeFromSet(node.marks));\n        }, doc), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new AddMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new RemoveMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof RemoveMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new RemoveMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"removeMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for RemoveMarkStep.fromJSON\");\n        return new RemoveMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeMark\", RemoveMarkStep);\n/**\nAdd a mark to a specific node.\n*/\nclass AddNodeMarkStep extends Step {\n    /**\n    Create a node mark step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.addToSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (node) {\n            let newSet = this.mark.addToSet(node.marks);\n            if (newSet.length == node.marks.length) {\n                for (let i = 0; i < node.marks.length; i++)\n                    if (!node.marks[i].isInSet(newSet))\n                        return new AddNodeMarkStep(this.pos, node.marks[i]);\n                return new AddNodeMarkStep(this.pos, this.mark);\n            }\n        }\n        return new RemoveNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AddNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"addNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for AddNodeMarkStep.fromJSON\");\n        return new AddNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addNodeMark\", AddNodeMarkStep);\n/**\nRemove a mark from a specific node.\n*/\nclass RemoveNodeMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.removeFromSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node || !this.mark.isInSet(node.marks))\n            return this;\n        return new AddNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new RemoveNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"removeNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for RemoveNodeMarkStep.fromJSON\");\n        return new RemoveNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeNodeMark\", RemoveNodeMarkStep);\n\n/**\nReplace a part of the document with a slice of new content.\n*/\nclass ReplaceStep extends Step {\n    /**\n    The given `slice` should fit the 'gap' between `from` and\n    `to`—the depths must line up, and the surrounding nodes must be\n    able to be joined with the open sides of the slice. When\n    `structure` is true, the step will fail if the content between\n    from and to is not just a sequence of closing and then opening\n    tokens (this is to guard against rebased replace steps\n    overwriting something they weren't supposed to).\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.slice = slice;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && contentBetween(doc, this.from, this.to))\n            return StepResult.fail(\"Structure replace would overwrite content\");\n        return StepResult.fromReplace(doc, this.from, this.to, this.slice);\n    }\n    getMap() {\n        return new StepMap([this.from, this.to - this.from, this.slice.size]);\n    }\n    invert(doc) {\n        return new ReplaceStep(this.from, this.from + this.slice.size, doc.slice(this.from, this.to));\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deletedAcross && to.deletedAcross)\n            return null;\n        return new ReplaceStep(from.pos, Math.max(from.pos, to.pos), this.slice, this.structure);\n    }\n    merge(other) {\n        if (!(other instanceof ReplaceStep) || other.structure || this.structure)\n            return null;\n        if (this.from + this.slice.size == other.from && !this.slice.openEnd && !other.slice.openStart) {\n            let slice = this.slice.size + other.slice.size == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(this.slice.content.append(other.slice.content), this.slice.openStart, other.slice.openEnd);\n            return new ReplaceStep(this.from, this.to + (other.to - other.from), slice, this.structure);\n        }\n        else if (other.to == this.from && !this.slice.openStart && !other.slice.openEnd) {\n            let slice = this.slice.size + other.slice.size == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(other.slice.content.append(this.slice.content), other.slice.openStart, this.slice.openEnd);\n            return new ReplaceStep(other.from, this.to, slice, this.structure);\n        }\n        else {\n            return null;\n        }\n    }\n    toJSON() {\n        let json = { stepType: \"replace\", from: this.from, to: this.to };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceStep.fromJSON\");\n        return new ReplaceStep(json.from, json.to, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.fromJSON(schema, json.slice), !!json.structure);\n    }\n}\nStep.jsonID(\"replace\", ReplaceStep);\n/**\nReplace a part of the document with a slice of content, but\npreserve a range of the replaced content by moving it into the\nslice.\n*/\nclass ReplaceAroundStep extends Step {\n    /**\n    Create a replace-around step with the given range and gap.\n    `insert` should be the point in the slice into which the content\n    of the gap should be moved. `structure` has the same meaning as\n    it has in the [`ReplaceStep`](https://prosemirror.net/docs/ref/#transform.ReplaceStep) class.\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The start of preserved range.\n    */\n    gapFrom, \n    /**\n    The end of preserved range.\n    */\n    gapTo, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    The position in the slice where the preserved range should be\n    inserted.\n    */\n    insert, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.gapFrom = gapFrom;\n        this.gapTo = gapTo;\n        this.slice = slice;\n        this.insert = insert;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && (contentBetween(doc, this.from, this.gapFrom) ||\n            contentBetween(doc, this.gapTo, this.to)))\n            return StepResult.fail(\"Structure gap-replace would overwrite content\");\n        let gap = doc.slice(this.gapFrom, this.gapTo);\n        if (gap.openStart || gap.openEnd)\n            return StepResult.fail(\"Gap is not a flat range\");\n        let inserted = this.slice.insertAt(this.insert, gap.content);\n        if (!inserted)\n            return StepResult.fail(\"Content does not fit in gap\");\n        return StepResult.fromReplace(doc, this.from, this.to, inserted);\n    }\n    getMap() {\n        return new StepMap([this.from, this.gapFrom - this.from, this.insert,\n            this.gapTo, this.to - this.gapTo, this.slice.size - this.insert]);\n    }\n    invert(doc) {\n        let gap = this.gapTo - this.gapFrom;\n        return new ReplaceAroundStep(this.from, this.from + this.slice.size + gap, this.from + this.insert, this.from + this.insert + gap, doc.slice(this.from, this.to).removeBetween(this.gapFrom - this.from, this.gapTo - this.from), this.gapFrom - this.from, this.structure);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        let gapFrom = this.from == this.gapFrom ? from.pos : mapping.map(this.gapFrom, -1);\n        let gapTo = this.to == this.gapTo ? to.pos : mapping.map(this.gapTo, 1);\n        if ((from.deletedAcross && to.deletedAcross) || gapFrom < from.pos || gapTo > to.pos)\n            return null;\n        return new ReplaceAroundStep(from.pos, to.pos, gapFrom, gapTo, this.slice, this.insert, this.structure);\n    }\n    toJSON() {\n        let json = { stepType: \"replaceAround\", from: this.from, to: this.to,\n            gapFrom: this.gapFrom, gapTo: this.gapTo, insert: this.insert };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\" ||\n            typeof json.gapFrom != \"number\" || typeof json.gapTo != \"number\" || typeof json.insert != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceAroundStep.fromJSON\");\n        return new ReplaceAroundStep(json.from, json.to, json.gapFrom, json.gapTo, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.fromJSON(schema, json.slice), json.insert, !!json.structure);\n    }\n}\nStep.jsonID(\"replaceAround\", ReplaceAroundStep);\nfunction contentBetween(doc, from, to) {\n    let $from = doc.resolve(from), dist = to - from, depth = $from.depth;\n    while (dist > 0 && depth > 0 && $from.indexAfter(depth) == $from.node(depth).childCount) {\n        depth--;\n        dist--;\n    }\n    if (dist > 0) {\n        let next = $from.node(depth).maybeChild($from.indexAfter(depth));\n        while (dist > 0) {\n            if (!next || next.isLeaf)\n                return true;\n            next = next.firstChild;\n            dist--;\n        }\n    }\n    return false;\n}\n\nfunction addMark(tr, from, to, mark) {\n    let removed = [], added = [];\n    let removing, adding;\n    tr.doc.nodesBetween(from, to, (node, pos, parent) => {\n        if (!node.isInline)\n            return;\n        let marks = node.marks;\n        if (!mark.isInSet(marks) && parent.type.allowsMarkType(mark.type)) {\n            let start = Math.max(pos, from), end = Math.min(pos + node.nodeSize, to);\n            let newSet = mark.addToSet(marks);\n            for (let i = 0; i < marks.length; i++) {\n                if (!marks[i].isInSet(newSet)) {\n                    if (removing && removing.to == start && removing.mark.eq(marks[i]))\n                        removing.to = end;\n                    else\n                        removed.push(removing = new RemoveMarkStep(start, end, marks[i]));\n                }\n            }\n            if (adding && adding.to == start)\n                adding.to = end;\n            else\n                added.push(adding = new AddMarkStep(start, end, mark));\n        }\n    });\n    removed.forEach(s => tr.step(s));\n    added.forEach(s => tr.step(s));\n}\nfunction removeMark(tr, from, to, mark) {\n    let matched = [], step = 0;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        if (!node.isInline)\n            return;\n        step++;\n        let toRemove = null;\n        if (mark instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.MarkType) {\n            let set = node.marks, found;\n            while (found = mark.isInSet(set)) {\n                (toRemove || (toRemove = [])).push(found);\n                set = found.removeFromSet(set);\n            }\n        }\n        else if (mark) {\n            if (mark.isInSet(node.marks))\n                toRemove = [mark];\n        }\n        else {\n            toRemove = node.marks;\n        }\n        if (toRemove && toRemove.length) {\n            let end = Math.min(pos + node.nodeSize, to);\n            for (let i = 0; i < toRemove.length; i++) {\n                let style = toRemove[i], found;\n                for (let j = 0; j < matched.length; j++) {\n                    let m = matched[j];\n                    if (m.step == step - 1 && style.eq(matched[j].style))\n                        found = m;\n                }\n                if (found) {\n                    found.to = end;\n                    found.step = step;\n                }\n                else {\n                    matched.push({ style, from: Math.max(pos, from), to: end, step });\n                }\n            }\n        }\n    });\n    matched.forEach(m => tr.step(new RemoveMarkStep(m.from, m.to, m.style)));\n}\nfunction clearIncompatible(tr, pos, parentType, match = parentType.contentMatch, clearNewlines = true) {\n    let node = tr.doc.nodeAt(pos);\n    let replSteps = [], cur = pos + 1;\n    for (let i = 0; i < node.childCount; i++) {\n        let child = node.child(i), end = cur + child.nodeSize;\n        let allowed = match.matchType(child.type);\n        if (!allowed) {\n            replSteps.push(new ReplaceStep(cur, end, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty));\n        }\n        else {\n            match = allowed;\n            for (let j = 0; j < child.marks.length; j++)\n                if (!parentType.allowsMarkType(child.marks[j].type))\n                    tr.step(new RemoveMarkStep(cur, end, child.marks[j]));\n            if (clearNewlines && child.isText && parentType.whitespace != \"pre\") {\n                let m, newline = /\\r?\\n|\\r/g, slice;\n                while (m = newline.exec(child.text)) {\n                    if (!slice)\n                        slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(parentType.schema.text(\" \", parentType.allowedMarks(child.marks))), 0, 0);\n                    replSteps.push(new ReplaceStep(cur + m.index, cur + m.index + m[0].length, slice));\n                }\n            }\n        }\n        cur = end;\n    }\n    if (!match.validEnd) {\n        let fill = match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true);\n        tr.replace(cur, cur, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(fill, 0, 0));\n    }\n    for (let i = replSteps.length - 1; i >= 0; i--)\n        tr.step(replSteps[i]);\n}\n\nfunction canCut(node, start, end) {\n    return (start == 0 || node.canReplace(start, node.childCount)) &&\n        (end == node.childCount || node.canReplace(0, end));\n}\n/**\nTry to find a target depth to which the content in the given range\ncan be lifted. Will not go across\n[isolating](https://prosemirror.net/docs/ref/#model.NodeSpec.isolating) parent nodes.\n*/\nfunction liftTarget(range) {\n    let parent = range.parent;\n    let content = parent.content.cutByIndex(range.startIndex, range.endIndex);\n    for (let depth = range.depth;; --depth) {\n        let node = range.$from.node(depth);\n        let index = range.$from.index(depth), endIndex = range.$to.indexAfter(depth);\n        if (depth < range.depth && node.canReplace(index, endIndex, content))\n            return depth;\n        if (depth == 0 || node.type.spec.isolating || !canCut(node, index, endIndex))\n            break;\n    }\n    return null;\n}\nfunction lift(tr, range, target) {\n    let { $from, $to, depth } = range;\n    let gapStart = $from.before(depth + 1), gapEnd = $to.after(depth + 1);\n    let start = gapStart, end = gapEnd;\n    let before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, openStart = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $from.index(d) > 0) {\n            splitting = true;\n            before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(d).copy(before));\n            openStart++;\n        }\n        else {\n            start--;\n        }\n    let after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, openEnd = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $to.after(d + 1) < $to.end(d)) {\n            splitting = true;\n            after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($to.node(d).copy(after));\n            openEnd++;\n        }\n        else {\n            end++;\n        }\n    tr.step(new ReplaceAroundStep(start, end, gapStart, gapEnd, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(before.append(after), openStart, openEnd), before.size - openStart, true));\n}\n/**\nTry to find a valid way to wrap the content in the given range in a\nnode of the given type. May introduce extra nodes around and inside\nthe wrapper node, if necessary. Returns null if no valid wrapping\ncould be found. When `innerRange` is given, that range's content is\nused as the content to fit into the wrapping, instead of the\ncontent of `range`.\n*/\nfunction findWrapping(range, nodeType, attrs = null, innerRange = range) {\n    let around = findWrappingOutside(range, nodeType);\n    let inner = around && findWrappingInside(innerRange, nodeType);\n    if (!inner)\n        return null;\n    return around.map(withAttrs)\n        .concat({ type: nodeType, attrs }).concat(inner.map(withAttrs));\n}\nfunction withAttrs(type) { return { type, attrs: null }; }\nfunction findWrappingOutside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let around = parent.contentMatchAt(startIndex).findWrapping(type);\n    if (!around)\n        return null;\n    let outer = around.length ? around[0] : type;\n    return parent.canReplaceWith(startIndex, endIndex, outer) ? around : null;\n}\nfunction findWrappingInside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let inner = parent.child(startIndex);\n    let inside = type.contentMatch.findWrapping(inner.type);\n    if (!inside)\n        return null;\n    let lastType = inside.length ? inside[inside.length - 1] : type;\n    let innerMatch = lastType.contentMatch;\n    for (let i = startIndex; innerMatch && i < endIndex; i++)\n        innerMatch = innerMatch.matchType(parent.child(i).type);\n    if (!innerMatch || !innerMatch.validEnd)\n        return null;\n    return inside;\n}\nfunction wrap(tr, range, wrappers) {\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--) {\n        if (content.size) {\n            let match = wrappers[i].type.contentMatch.matchFragment(content);\n            if (!match || !match.validEnd)\n                throw new RangeError(\"Wrapper type given to Transform.wrap does not form valid content of its parent wrapper\");\n        }\n        content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    }\n    let start = range.start, end = range.end;\n    tr.step(new ReplaceAroundStep(start, end, start, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, 0, 0), wrappers.length, true));\n}\nfunction setBlockType(tr, from, to, type, attrs) {\n    if (!type.isTextblock)\n        throw new RangeError(\"Type given to setBlockType should be a textblock\");\n    let mapFrom = tr.steps.length;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        let attrsHere = typeof attrs == \"function\" ? attrs(node) : attrs;\n        if (node.isTextblock && !node.hasMarkup(type, attrsHere) &&\n            canChangeType(tr.doc, tr.mapping.slice(mapFrom).map(pos), type)) {\n            let convertNewlines = null;\n            if (type.schema.linebreakReplacement) {\n                let pre = type.whitespace == \"pre\", supportLinebreak = !!type.contentMatch.matchType(type.schema.linebreakReplacement);\n                if (pre && !supportLinebreak)\n                    convertNewlines = false;\n                else if (!pre && supportLinebreak)\n                    convertNewlines = true;\n            }\n            // Ensure all markup that isn't allowed in the new node type is cleared\n            if (convertNewlines === false)\n                replaceLinebreaks(tr, node, pos, mapFrom);\n            clearIncompatible(tr, tr.mapping.slice(mapFrom).map(pos, 1), type, undefined, convertNewlines === null);\n            let mapping = tr.mapping.slice(mapFrom);\n            let startM = mapping.map(pos, 1), endM = mapping.map(pos + node.nodeSize, 1);\n            tr.step(new ReplaceAroundStep(startM, endM, startM + 1, endM - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(type.create(attrsHere, null, node.marks)), 0, 0), 1, true));\n            if (convertNewlines === true)\n                replaceNewlines(tr, node, pos, mapFrom);\n            return false;\n        }\n    });\n}\nfunction replaceNewlines(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.isText) {\n            let m, newline = /\\r?\\n|\\r/g;\n            while (m = newline.exec(child.text)) {\n                let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset + m.index);\n                tr.replaceWith(start, start + 1, node.type.schema.linebreakReplacement.create());\n            }\n        }\n    });\n}\nfunction replaceLinebreaks(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.type == child.type.schema.linebreakReplacement) {\n            let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset);\n            tr.replaceWith(start, start + 1, node.type.schema.text(\"\\n\"));\n        }\n    });\n}\nfunction canChangeType(doc, pos, type) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return $pos.parent.canReplaceWith(index, index + 1, type);\n}\n/**\nChange the type, attributes, and/or marks of the node at `pos`.\nWhen `type` isn't given, the existing node type is preserved,\n*/\nfunction setNodeMarkup(tr, pos, type, attrs, marks) {\n    let node = tr.doc.nodeAt(pos);\n    if (!node)\n        throw new RangeError(\"No node at given position\");\n    if (!type)\n        type = node.type;\n    let newNode = type.create(attrs, null, marks || node.marks);\n    if (node.isLeaf)\n        return tr.replaceWith(pos, pos + node.nodeSize, newNode);\n    if (!type.validContent(node.content))\n        throw new RangeError(\"Invalid content for node type \" + type.name);\n    tr.step(new ReplaceAroundStep(pos, pos + node.nodeSize, pos + 1, pos + node.nodeSize - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(newNode), 0, 0), 1, true));\n}\n/**\nCheck whether splitting at the given position is allowed.\n*/\nfunction canSplit(doc, pos, depth = 1, typesAfter) {\n    let $pos = doc.resolve(pos), base = $pos.depth - depth;\n    let innerType = (typesAfter && typesAfter[typesAfter.length - 1]) || $pos.parent;\n    if (base < 0 || $pos.parent.type.spec.isolating ||\n        !$pos.parent.canReplace($pos.index(), $pos.parent.childCount) ||\n        !innerType.type.validContent($pos.parent.content.cutByIndex($pos.index(), $pos.parent.childCount)))\n        return false;\n    for (let d = $pos.depth - 1, i = depth - 2; d > base; d--, i--) {\n        let node = $pos.node(d), index = $pos.index(d);\n        if (node.type.spec.isolating)\n            return false;\n        let rest = node.content.cutByIndex(index, node.childCount);\n        let overrideChild = typesAfter && typesAfter[i + 1];\n        if (overrideChild)\n            rest = rest.replaceChild(0, overrideChild.type.create(overrideChild.attrs));\n        let after = (typesAfter && typesAfter[i]) || node;\n        if (!node.canReplace(index + 1, node.childCount) || !after.type.validContent(rest))\n            return false;\n    }\n    let index = $pos.indexAfter(base);\n    let baseType = typesAfter && typesAfter[0];\n    return $pos.node(base).canReplaceWith(index, index, baseType ? baseType.type : $pos.node(base + 1).type);\n}\nfunction split(tr, pos, depth = 1, typesAfter) {\n    let $pos = tr.doc.resolve(pos), before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let d = $pos.depth, e = $pos.depth - depth, i = depth - 1; d > e; d--, i--) {\n        before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($pos.node(d).copy(before));\n        let typeAfter = typesAfter && typesAfter[i];\n        after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(typeAfter ? typeAfter.type.create(typeAfter.attrs, after) : $pos.node(d).copy(after));\n    }\n    tr.step(new ReplaceStep(pos, pos, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(before.append(after), depth, depth), true));\n}\n/**\nTest whether the blocks before and after a given position can be\njoined.\n*/\nfunction canJoin(doc, pos) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return joinable($pos.nodeBefore, $pos.nodeAfter) &&\n        $pos.parent.canReplace(index, index + 1);\n}\nfunction canAppendWithSubstitutedLinebreaks(a, b) {\n    if (!b.content.size)\n        a.type.compatibleContent(b.type);\n    let match = a.contentMatchAt(a.childCount);\n    let { linebreakReplacement } = a.type.schema;\n    for (let i = 0; i < b.childCount; i++) {\n        let child = b.child(i);\n        let type = child.type == linebreakReplacement ? a.type.schema.nodes.text : child.type;\n        match = match.matchType(type);\n        if (!match)\n            return false;\n        if (!a.type.allowsMarks(child.marks))\n            return false;\n    }\n    return match.validEnd;\n}\nfunction joinable(a, b) {\n    return !!(a && b && !a.isLeaf && canAppendWithSubstitutedLinebreaks(a, b));\n}\n/**\nFind an ancestor of the given position that can be joined to the\nblock before (or after if `dir` is positive). Returns the joinable\npoint, if any.\n*/\nfunction joinPoint(doc, pos, dir = -1) {\n    let $pos = doc.resolve(pos);\n    for (let d = $pos.depth;; d--) {\n        let before, after, index = $pos.index(d);\n        if (d == $pos.depth) {\n            before = $pos.nodeBefore;\n            after = $pos.nodeAfter;\n        }\n        else if (dir > 0) {\n            before = $pos.node(d + 1);\n            index++;\n            after = $pos.node(d).maybeChild(index);\n        }\n        else {\n            before = $pos.node(d).maybeChild(index - 1);\n            after = $pos.node(d + 1);\n        }\n        if (before && !before.isTextblock && joinable(before, after) &&\n            $pos.node(d).canReplace(index, index + 1))\n            return pos;\n        if (d == 0)\n            break;\n        pos = dir < 0 ? $pos.before(d) : $pos.after(d);\n    }\n}\nfunction join(tr, pos, depth) {\n    let convertNewlines = null;\n    let { linebreakReplacement } = tr.doc.type.schema;\n    let $before = tr.doc.resolve(pos - depth), beforeType = $before.node().type;\n    if (linebreakReplacement && beforeType.inlineContent) {\n        let pre = beforeType.whitespace == \"pre\";\n        let supportLinebreak = !!beforeType.contentMatch.matchType(linebreakReplacement);\n        if (pre && !supportLinebreak)\n            convertNewlines = false;\n        else if (!pre && supportLinebreak)\n            convertNewlines = true;\n    }\n    let mapFrom = tr.steps.length;\n    if (convertNewlines === false) {\n        let $after = tr.doc.resolve(pos + depth);\n        replaceLinebreaks(tr, $after.node(), $after.before(), mapFrom);\n    }\n    if (beforeType.inlineContent)\n        clearIncompatible(tr, pos + depth - 1, beforeType, $before.node().contentMatchAt($before.index()), convertNewlines == null);\n    let mapping = tr.mapping.slice(mapFrom), start = mapping.map(pos - depth);\n    tr.step(new ReplaceStep(start, mapping.map(pos + depth, -1), prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty, true));\n    if (convertNewlines === true) {\n        let $full = tr.doc.resolve(start);\n        replaceNewlines(tr, $full.node(), $full.before(), tr.steps.length);\n    }\n    return tr;\n}\n/**\nTry to find a point where a node of the given type can be inserted\nnear `pos`, by searching up the node hierarchy when `pos` itself\nisn't a valid place but is at the start or end of a node. Return\nnull if no position was found.\n*/\nfunction insertPoint(doc, pos, nodeType) {\n    let $pos = doc.resolve(pos);\n    if ($pos.parent.canReplaceWith($pos.index(), $pos.index(), nodeType))\n        return pos;\n    if ($pos.parentOffset == 0)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.index(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.before(d + 1);\n            if (index > 0)\n                return null;\n        }\n    if ($pos.parentOffset == $pos.parent.content.size)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.indexAfter(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.after(d + 1);\n            if (index < $pos.node(d).childCount)\n                return null;\n        }\n    return null;\n}\n/**\nFinds a position at or around the given position where the given\nslice can be inserted. Will look at parent nodes' nearest boundary\nand try there, even if the original position wasn't directly at the\nstart or end of that node. Returns null when no position was found.\n*/\nfunction dropPoint(doc, pos, slice) {\n    let $pos = doc.resolve(pos);\n    if (!slice.content.size)\n        return pos;\n    let content = slice.content;\n    for (let i = 0; i < slice.openStart; i++)\n        content = content.firstChild.content;\n    for (let pass = 1; pass <= (slice.openStart == 0 && slice.size ? 2 : 1); pass++) {\n        for (let d = $pos.depth; d >= 0; d--) {\n            let bias = d == $pos.depth ? 0 : $pos.pos <= ($pos.start(d + 1) + $pos.end(d + 1)) / 2 ? -1 : 1;\n            let insertPos = $pos.index(d) + (bias > 0 ? 1 : 0);\n            let parent = $pos.node(d), fits = false;\n            if (pass == 1) {\n                fits = parent.canReplace(insertPos, insertPos, content);\n            }\n            else {\n                let wrapping = parent.contentMatchAt(insertPos).findWrapping(content.firstChild.type);\n                fits = wrapping && parent.canReplaceWith(insertPos, insertPos, wrapping[0]);\n            }\n            if (fits)\n                return bias == 0 ? $pos.pos : bias < 0 ? $pos.before(d + 1) : $pos.after(d + 1);\n        }\n    }\n    return null;\n}\n\n/**\n‘Fit’ a slice into a given position in the document, producing a\n[step](https://prosemirror.net/docs/ref/#transform.Step) that inserts it. Will return null if\nthere's no meaningful way to insert the slice here, or inserting it\nwould be a no-op (an empty slice over an empty range).\n*/\nfunction replaceStep(doc, from, to = from, slice = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n    if (from == to && !slice.size)\n        return null;\n    let $from = doc.resolve(from), $to = doc.resolve(to);\n    // Optimization -- avoid work if it's obvious that it's not needed.\n    if (fitsTrivially($from, $to, slice))\n        return new ReplaceStep(from, to, slice);\n    return new Fitter($from, $to, slice).fit();\n}\nfunction fitsTrivially($from, $to, slice) {\n    return !slice.openStart && !slice.openEnd && $from.start() == $to.start() &&\n        $from.parent.canReplace($from.index(), $to.index(), slice.content);\n}\n// Algorithm for 'placing' the elements of a slice into a gap:\n//\n// We consider the content of each node that is open to the left to be\n// independently placeable. I.e. in <p(\"foo\"), p(\"bar\")>, when the\n// paragraph on the left is open, \"foo\" can be placed (somewhere on\n// the left side of the replacement gap) independently from p(\"bar\").\n//\n// This class tracks the state of the placement progress in the\n// following properties:\n//\n//  - `frontier` holds a stack of `{type, match}` objects that\n//    represent the open side of the replacement. It starts at\n//    `$from`, then moves forward as content is placed, and is finally\n//    reconciled with `$to`.\n//\n//  - `unplaced` is a slice that represents the content that hasn't\n//    been placed yet.\n//\n//  - `placed` is a fragment of placed content. Its open-start value\n//    is implicit in `$from`, and its open-end value in `frontier`.\nclass Fitter {\n    constructor($from, $to, unplaced) {\n        this.$from = $from;\n        this.$to = $to;\n        this.unplaced = unplaced;\n        this.frontier = [];\n        this.placed = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n        for (let i = 0; i <= $from.depth; i++) {\n            let node = $from.node(i);\n            this.frontier.push({\n                type: node.type,\n                match: node.contentMatchAt($from.indexAfter(i))\n            });\n        }\n        for (let i = $from.depth; i > 0; i--)\n            this.placed = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(i).copy(this.placed));\n    }\n    get depth() { return this.frontier.length - 1; }\n    fit() {\n        // As long as there's unplaced content, try to place some of it.\n        // If that fails, either increase the open score of the unplaced\n        // slice, or drop nodes from it, and then try again.\n        while (this.unplaced.size) {\n            let fit = this.findFittable();\n            if (fit)\n                this.placeNodes(fit);\n            else\n                this.openMore() || this.dropNode();\n        }\n        // When there's inline content directly after the frontier _and_\n        // directly after `this.$to`, we must generate a `ReplaceAround`\n        // step that pulls that content into the node after the frontier.\n        // That means the fitting must be done to the end of the textblock\n        // node after `this.$to`, not `this.$to` itself.\n        let moveInline = this.mustMoveInline(), placedSize = this.placed.size - this.depth - this.$from.depth;\n        let $from = this.$from, $to = this.close(moveInline < 0 ? this.$to : $from.doc.resolve(moveInline));\n        if (!$to)\n            return null;\n        // If closing to `$to` succeeded, create a step\n        let content = this.placed, openStart = $from.depth, openEnd = $to.depth;\n        while (openStart && openEnd && content.childCount == 1) { // Normalize by dropping open parent nodes\n            content = content.firstChild.content;\n            openStart--;\n            openEnd--;\n        }\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, openStart, openEnd);\n        if (moveInline > -1)\n            return new ReplaceAroundStep($from.pos, moveInline, this.$to.pos, this.$to.end(), slice, placedSize);\n        if (slice.size || $from.pos != this.$to.pos) // Don't generate no-op steps\n            return new ReplaceStep($from.pos, $to.pos, slice);\n        return null;\n    }\n    // Find a position on the start spine of `this.unplaced` that has\n    // content that can be moved somewhere on the frontier. Returns two\n    // depths, one for the slice and one for the frontier.\n    findFittable() {\n        let startDepth = this.unplaced.openStart;\n        for (let cur = this.unplaced.content, d = 0, openEnd = this.unplaced.openEnd; d < startDepth; d++) {\n            let node = cur.firstChild;\n            if (cur.childCount > 1)\n                openEnd = 0;\n            if (node.type.spec.isolating && openEnd <= d) {\n                startDepth = d;\n                break;\n            }\n            cur = node.content;\n        }\n        // Only try wrapping nodes (pass 2) after finding a place without\n        // wrapping failed.\n        for (let pass = 1; pass <= 2; pass++) {\n            for (let sliceDepth = pass == 1 ? startDepth : this.unplaced.openStart; sliceDepth >= 0; sliceDepth--) {\n                let fragment, parent = null;\n                if (sliceDepth) {\n                    parent = contentAt(this.unplaced.content, sliceDepth - 1).firstChild;\n                    fragment = parent.content;\n                }\n                else {\n                    fragment = this.unplaced.content;\n                }\n                let first = fragment.firstChild;\n                for (let frontierDepth = this.depth; frontierDepth >= 0; frontierDepth--) {\n                    let { type, match } = this.frontier[frontierDepth], wrap, inject = null;\n                    // In pass 1, if the next node matches, or there is no next\n                    // node but the parents look compatible, we've found a\n                    // place.\n                    if (pass == 1 && (first ? match.matchType(first.type) || (inject = match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(first), false))\n                        : parent && type.compatibleContent(parent.type)))\n                        return { sliceDepth, frontierDepth, parent, inject };\n                    // In pass 2, look for a set of wrapping nodes that make\n                    // `first` fit here.\n                    else if (pass == 2 && first && (wrap = match.findWrapping(first.type)))\n                        return { sliceDepth, frontierDepth, parent, wrap };\n                    // Don't continue looking further up if the parent node\n                    // would fit here.\n                    if (parent && match.matchType(parent.type))\n                        break;\n                }\n            }\n        }\n    }\n    openMore() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (!inner.childCount || inner.firstChild.isLeaf)\n            return false;\n        this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, openStart + 1, Math.max(openEnd, inner.size + openStart >= content.size - openEnd ? openStart + 1 : 0));\n        return true;\n    }\n    dropNode() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (inner.childCount <= 1 && openStart > 0) {\n            let openAtEnd = content.size - openStart <= openStart + inner.size;\n            this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(content, openStart - 1, 1), openStart - 1, openAtEnd ? openStart - 1 : openEnd);\n        }\n        else {\n            this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(content, openStart, 1), openStart, openEnd);\n        }\n    }\n    // Move content from the unplaced slice at `sliceDepth` to the\n    // frontier node at `frontierDepth`. Close that frontier node when\n    // applicable.\n    placeNodes({ sliceDepth, frontierDepth, parent, inject, wrap }) {\n        while (this.depth > frontierDepth)\n            this.closeFrontierNode();\n        if (wrap)\n            for (let i = 0; i < wrap.length; i++)\n                this.openFrontierNode(wrap[i]);\n        let slice = this.unplaced, fragment = parent ? parent.content : slice.content;\n        let openStart = slice.openStart - sliceDepth;\n        let taken = 0, add = [];\n        let { match, type } = this.frontier[frontierDepth];\n        if (inject) {\n            for (let i = 0; i < inject.childCount; i++)\n                add.push(inject.child(i));\n            match = match.matchFragment(inject);\n        }\n        // Computes the amount of (end) open nodes at the end of the\n        // fragment. When 0, the parent is open, but no more. When\n        // negative, nothing is open.\n        let openEndCount = (fragment.size + sliceDepth) - (slice.content.size - slice.openEnd);\n        // Scan over the fragment, fitting as many child nodes as\n        // possible.\n        while (taken < fragment.childCount) {\n            let next = fragment.child(taken), matches = match.matchType(next.type);\n            if (!matches)\n                break;\n            taken++;\n            if (taken > 1 || openStart == 0 || next.content.size) { // Drop empty open nodes\n                match = matches;\n                add.push(closeNodeStart(next.mark(type.allowedMarks(next.marks)), taken == 1 ? openStart : 0, taken == fragment.childCount ? openEndCount : -1));\n            }\n        }\n        let toEnd = taken == fragment.childCount;\n        if (!toEnd)\n            openEndCount = -1;\n        this.placed = addToFragment(this.placed, frontierDepth, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(add));\n        this.frontier[frontierDepth].match = match;\n        // If the parent types match, and the entire node was moved, and\n        // it's not open, close this frontier node right away.\n        if (toEnd && openEndCount < 0 && parent && parent.type == this.frontier[this.depth].type && this.frontier.length > 1)\n            this.closeFrontierNode();\n        // Add new frontier nodes for any open nodes at the end.\n        for (let i = 0, cur = fragment; i < openEndCount; i++) {\n            let node = cur.lastChild;\n            this.frontier.push({ type: node.type, match: node.contentMatchAt(node.childCount) });\n            cur = node.content;\n        }\n        // Update `this.unplaced`. Drop the entire node from which we\n        // placed it we got to its end, otherwise just drop the placed\n        // nodes.\n        this.unplaced = !toEnd ? new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(slice.content, sliceDepth, taken), slice.openStart, slice.openEnd)\n            : sliceDepth == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(slice.content, sliceDepth - 1, 1), sliceDepth - 1, openEndCount < 0 ? slice.openEnd : sliceDepth - 1);\n    }\n    mustMoveInline() {\n        if (!this.$to.parent.isTextblock)\n            return -1;\n        let top = this.frontier[this.depth], level;\n        if (!top.type.isTextblock || !contentAfterFits(this.$to, this.$to.depth, top.type, top.match, false) ||\n            (this.$to.depth == this.depth && (level = this.findCloseLevel(this.$to)) && level.depth == this.depth))\n            return -1;\n        let { depth } = this.$to, after = this.$to.after(depth);\n        while (depth > 1 && after == this.$to.end(--depth))\n            ++after;\n        return after;\n    }\n    findCloseLevel($to) {\n        scan: for (let i = Math.min(this.depth, $to.depth); i >= 0; i--) {\n            let { match, type } = this.frontier[i];\n            let dropInner = i < $to.depth && $to.end(i + 1) == $to.pos + ($to.depth - (i + 1));\n            let fit = contentAfterFits($to, i, type, match, dropInner);\n            if (!fit)\n                continue;\n            for (let d = i - 1; d >= 0; d--) {\n                let { match, type } = this.frontier[d];\n                let matches = contentAfterFits($to, d, type, match, true);\n                if (!matches || matches.childCount)\n                    continue scan;\n            }\n            return { depth: i, fit, move: dropInner ? $to.doc.resolve($to.after(i + 1)) : $to };\n        }\n    }\n    close($to) {\n        let close = this.findCloseLevel($to);\n        if (!close)\n            return null;\n        while (this.depth > close.depth)\n            this.closeFrontierNode();\n        if (close.fit.childCount)\n            this.placed = addToFragment(this.placed, close.depth, close.fit);\n        $to = close.move;\n        for (let d = close.depth + 1; d <= $to.depth; d++) {\n            let node = $to.node(d), add = node.type.contentMatch.fillBefore(node.content, true, $to.index(d));\n            this.openFrontierNode(node.type, node.attrs, add);\n        }\n        return $to;\n    }\n    openFrontierNode(type, attrs = null, content) {\n        let top = this.frontier[this.depth];\n        top.match = top.match.matchType(type);\n        this.placed = addToFragment(this.placed, this.depth, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(type.create(attrs, content)));\n        this.frontier.push({ type, match: type.contentMatch });\n    }\n    closeFrontierNode() {\n        let open = this.frontier.pop();\n        let add = open.match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true);\n        if (add.childCount)\n            this.placed = addToFragment(this.placed, this.frontier.length, add);\n    }\n}\nfunction dropFromFragment(fragment, depth, count) {\n    if (depth == 0)\n        return fragment.cutByIndex(count, fragment.childCount);\n    return fragment.replaceChild(0, fragment.firstChild.copy(dropFromFragment(fragment.firstChild.content, depth - 1, count)));\n}\nfunction addToFragment(fragment, depth, content) {\n    if (depth == 0)\n        return fragment.append(content);\n    return fragment.replaceChild(fragment.childCount - 1, fragment.lastChild.copy(addToFragment(fragment.lastChild.content, depth - 1, content)));\n}\nfunction contentAt(fragment, depth) {\n    for (let i = 0; i < depth; i++)\n        fragment = fragment.firstChild.content;\n    return fragment;\n}\nfunction closeNodeStart(node, openStart, openEnd) {\n    if (openStart <= 0)\n        return node;\n    let frag = node.content;\n    if (openStart > 1)\n        frag = frag.replaceChild(0, closeNodeStart(frag.firstChild, openStart - 1, frag.childCount == 1 ? openEnd - 1 : 0));\n    if (openStart > 0) {\n        frag = node.type.contentMatch.fillBefore(frag).append(frag);\n        if (openEnd <= 0)\n            frag = frag.append(node.type.contentMatch.matchFragment(frag).fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true));\n    }\n    return node.copy(frag);\n}\nfunction contentAfterFits($to, depth, type, match, open) {\n    let node = $to.node(depth), index = open ? $to.indexAfter(depth) : $to.index(depth);\n    if (index == node.childCount && !type.compatibleContent(node.type))\n        return null;\n    let fit = match.fillBefore(node.content, true, index);\n    return fit && !invalidMarks(type, node.content, index) ? fit : null;\n}\nfunction invalidMarks(type, fragment, start) {\n    for (let i = start; i < fragment.childCount; i++)\n        if (!type.allowsMarks(fragment.child(i).marks))\n            return true;\n    return false;\n}\nfunction definesContent(type) {\n    return type.spec.defining || type.spec.definingForContent;\n}\nfunction replaceRange(tr, from, to, slice) {\n    if (!slice.size)\n        return tr.deleteRange(from, to);\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    if (fitsTrivially($from, $to, slice))\n        return tr.step(new ReplaceStep(from, to, slice));\n    let targetDepths = coveredDepths($from, tr.doc.resolve(to));\n    // Can't replace the whole document, so remove 0 if it's present\n    if (targetDepths[targetDepths.length - 1] == 0)\n        targetDepths.pop();\n    // Negative numbers represent not expansion over the whole node at\n    // that depth, but replacing from $from.before(-D) to $to.pos.\n    let preferredTarget = -($from.depth + 1);\n    targetDepths.unshift(preferredTarget);\n    // This loop picks a preferred target depth, if one of the covering\n    // depths is not outside of a defining node, and adds negative\n    // depths for any depth that has $from at its start and does not\n    // cross a defining node.\n    for (let d = $from.depth, pos = $from.pos - 1; d > 0; d--, pos--) {\n        let spec = $from.node(d).type.spec;\n        if (spec.defining || spec.definingAsContext || spec.isolating)\n            break;\n        if (targetDepths.indexOf(d) > -1)\n            preferredTarget = d;\n        else if ($from.before(d) == pos)\n            targetDepths.splice(1, 0, -d);\n    }\n    // Try to fit each possible depth of the slice into each possible\n    // target depth, starting with the preferred depths.\n    let preferredTargetIndex = targetDepths.indexOf(preferredTarget);\n    let leftNodes = [], preferredDepth = slice.openStart;\n    for (let content = slice.content, i = 0;; i++) {\n        let node = content.firstChild;\n        leftNodes.push(node);\n        if (i == slice.openStart)\n            break;\n        content = node.content;\n    }\n    // Back up preferredDepth to cover defining textblocks directly\n    // above it, possibly skipping a non-defining textblock.\n    for (let d = preferredDepth - 1; d >= 0; d--) {\n        let leftNode = leftNodes[d], def = definesContent(leftNode.type);\n        if (def && !leftNode.sameMarkup($from.node(Math.abs(preferredTarget) - 1)))\n            preferredDepth = d;\n        else if (def || !leftNode.type.isTextblock)\n            break;\n    }\n    for (let j = slice.openStart; j >= 0; j--) {\n        let openDepth = (j + preferredDepth + 1) % (slice.openStart + 1);\n        let insert = leftNodes[openDepth];\n        if (!insert)\n            continue;\n        for (let i = 0; i < targetDepths.length; i++) {\n            // Loop over possible expansion levels, starting with the\n            // preferred one\n            let targetDepth = targetDepths[(i + preferredTargetIndex) % targetDepths.length], expand = true;\n            if (targetDepth < 0) {\n                expand = false;\n                targetDepth = -targetDepth;\n            }\n            let parent = $from.node(targetDepth - 1), index = $from.index(targetDepth - 1);\n            if (parent.canReplaceWith(index, index, insert.type, insert.marks))\n                return tr.replace($from.before(targetDepth), expand ? $to.after(targetDepth) : to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(closeFragment(slice.content, 0, slice.openStart, openDepth), openDepth, slice.openEnd));\n        }\n    }\n    let startSteps = tr.steps.length;\n    for (let i = targetDepths.length - 1; i >= 0; i--) {\n        tr.replace(from, to, slice);\n        if (tr.steps.length > startSteps)\n            break;\n        let depth = targetDepths[i];\n        if (depth < 0)\n            continue;\n        from = $from.before(depth);\n        to = $to.after(depth);\n    }\n}\nfunction closeFragment(fragment, depth, oldOpen, newOpen, parent) {\n    if (depth < oldOpen) {\n        let first = fragment.firstChild;\n        fragment = fragment.replaceChild(0, first.copy(closeFragment(first.content, depth + 1, oldOpen, newOpen, first)));\n    }\n    if (depth > newOpen) {\n        let match = parent.contentMatchAt(0);\n        let start = match.fillBefore(fragment).append(fragment);\n        fragment = start.append(match.matchFragment(start).fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true));\n    }\n    return fragment;\n}\nfunction replaceRangeWith(tr, from, to, node) {\n    if (!node.isInline && from == to && tr.doc.resolve(from).parent.content.size) {\n        let point = insertPoint(tr.doc, from, node.type);\n        if (point != null)\n            from = to = point;\n    }\n    tr.replaceRange(from, to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(node), 0, 0));\n}\nfunction deleteRange(tr, from, to) {\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    let covered = coveredDepths($from, $to);\n    for (let i = 0; i < covered.length; i++) {\n        let depth = covered[i], last = i == covered.length - 1;\n        if ((last && depth == 0) || $from.node(depth).type.contentMatch.validEnd)\n            return tr.delete($from.start(depth), $to.end(depth));\n        if (depth > 0 && (last || $from.node(depth - 1).canReplace($from.index(depth - 1), $to.indexAfter(depth - 1))))\n            return tr.delete($from.before(depth), $to.after(depth));\n    }\n    for (let d = 1; d <= $from.depth && d <= $to.depth; d++) {\n        if (from - $from.start(d) == $from.depth - d && to > $from.end(d) && $to.end(d) - to != $to.depth - d &&\n            $from.start(d - 1) == $to.start(d - 1) && $from.node(d - 1).canReplace($from.index(d - 1), $to.index(d - 1)))\n            return tr.delete($from.before(d), to);\n    }\n    tr.delete(from, to);\n}\n// Returns an array of all depths for which $from - $to spans the\n// whole content of the nodes at that depth.\nfunction coveredDepths($from, $to) {\n    let result = [], minDepth = Math.min($from.depth, $to.depth);\n    for (let d = minDepth; d >= 0; d--) {\n        let start = $from.start(d);\n        if (start < $from.pos - ($from.depth - d) ||\n            $to.end(d) > $to.pos + ($to.depth - d) ||\n            $from.node(d).type.spec.isolating ||\n            $to.node(d).type.spec.isolating)\n            break;\n        if (start == $to.start(d) ||\n            (d == $from.depth && d == $to.depth && $from.parent.inlineContent && $to.parent.inlineContent &&\n                d && $to.start(d - 1) == start - 1))\n            result.push(d);\n    }\n    return result;\n}\n\n/**\nUpdate an attribute in a specific node.\n*/\nclass AttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.pos = pos;\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at attribute step's position\");\n        let attrs = Object.create(null);\n        for (let name in node.attrs)\n            attrs[name] = node.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = node.type.create(attrs, null, node.marks);\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new AttrStep(this.pos, this.attr, doc.nodeAt(this.pos).attrs[this.attr]);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AttrStep(pos.pos, this.attr, this.value);\n    }\n    toJSON() {\n        return { stepType: \"attr\", pos: this.pos, attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\" || typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for AttrStep.fromJSON\");\n        return new AttrStep(json.pos, json.attr, json.value);\n    }\n}\nStep.jsonID(\"attr\", AttrStep);\n/**\nUpdate an attribute in the doc node.\n*/\nclass DocAttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let attrs = Object.create(null);\n        for (let name in doc.attrs)\n            attrs[name] = doc.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = doc.type.create(attrs, doc.content, doc.marks);\n        return StepResult.ok(updated);\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new DocAttrStep(this.attr, doc.attrs[this.attr]);\n    }\n    map(mapping) {\n        return this;\n    }\n    toJSON() {\n        return { stepType: \"docAttr\", attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for DocAttrStep.fromJSON\");\n        return new DocAttrStep(json.attr, json.value);\n    }\n}\nStep.jsonID(\"docAttr\", DocAttrStep);\n\n/**\n@internal\n*/\nlet TransformError = class extends Error {\n};\nTransformError = function TransformError(message) {\n    let err = Error.call(this, message);\n    err.__proto__ = TransformError.prototype;\n    return err;\n};\nTransformError.prototype = Object.create(Error.prototype);\nTransformError.prototype.constructor = TransformError;\nTransformError.prototype.name = \"TransformError\";\n/**\nAbstraction to build up and track an array of\n[steps](https://prosemirror.net/docs/ref/#transform.Step) representing a document transformation.\n\nMost transforming methods return the `Transform` object itself, so\nthat they can be chained.\n*/\nclass Transform {\n    /**\n    Create a transform that starts with the given document.\n    */\n    constructor(\n    /**\n    The current document (the result of applying the steps in the\n    transform).\n    */\n    doc) {\n        this.doc = doc;\n        /**\n        The steps in this transform.\n        */\n        this.steps = [];\n        /**\n        The documents before each of the steps.\n        */\n        this.docs = [];\n        /**\n        A mapping with the maps for each of the steps in this transform.\n        */\n        this.mapping = new Mapping;\n    }\n    /**\n    The starting document.\n    */\n    get before() { return this.docs.length ? this.docs[0] : this.doc; }\n    /**\n    Apply a new step in this transform, saving the result. Throws an\n    error when the step fails.\n    */\n    step(step) {\n        let result = this.maybeStep(step);\n        if (result.failed)\n            throw new TransformError(result.failed);\n        return this;\n    }\n    /**\n    Try to apply a step in this transformation, ignoring it if it\n    fails. Returns the step result.\n    */\n    maybeStep(step) {\n        let result = step.apply(this.doc);\n        if (!result.failed)\n            this.addStep(step, result.doc);\n        return result;\n    }\n    /**\n    True when the document has been changed (when there are any\n    steps).\n    */\n    get docChanged() {\n        return this.steps.length > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        this.docs.push(this.doc);\n        this.steps.push(step);\n        this.mapping.appendMap(step.getMap());\n        this.doc = doc;\n    }\n    /**\n    Replace the part of the document between `from` and `to` with the\n    given `slice`.\n    */\n    replace(from, to = from, slice = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        let step = replaceStep(this.doc, from, to, slice);\n        if (step)\n            this.step(step);\n        return this;\n    }\n    /**\n    Replace the given range with the given content, which may be a\n    fragment, node, or array of nodes.\n    */\n    replaceWith(from, to, content) {\n        return this.replace(from, to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(content), 0, 0));\n    }\n    /**\n    Delete the content between the given positions.\n    */\n    delete(from, to) {\n        return this.replace(from, to, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty);\n    }\n    /**\n    Insert the given content at the given position.\n    */\n    insert(pos, content) {\n        return this.replaceWith(pos, pos, content);\n    }\n    /**\n    Replace a range of the document with a given slice, using\n    `from`, `to`, and the slice's\n    [`openStart`](https://prosemirror.net/docs/ref/#model.Slice.openStart) property as hints, rather\n    than fixed start and end points. This method may grow the\n    replaced area or close open nodes in the slice in order to get a\n    fit that is more in line with WYSIWYG expectations, by dropping\n    fully covered parent nodes of the replaced region when they are\n    marked [non-defining as\n    context](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext), or including an\n    open parent node from the slice that _is_ marked as [defining\n    its content](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n    \n    This is the method, for example, to handle paste. The similar\n    [`replace`](https://prosemirror.net/docs/ref/#transform.Transform.replace) method is a more\n    primitive tool which will _not_ move the start and end of its given\n    range, and is useful in situations where you need more precise\n    control over what happens.\n    */\n    replaceRange(from, to, slice) {\n        replaceRange(this, from, to, slice);\n        return this;\n    }\n    /**\n    Replace the given range with a node, but use `from` and `to` as\n    hints, rather than precise positions. When from and to are the same\n    and are at the start or end of a parent node in which the given\n    node doesn't fit, this method may _move_ them out towards a parent\n    that does allow the given node to be placed. When the given range\n    completely covers a parent node, this method may completely replace\n    that parent node.\n    */\n    replaceRangeWith(from, to, node) {\n        replaceRangeWith(this, from, to, node);\n        return this;\n    }\n    /**\n    Delete the given range, expanding it to cover fully covered\n    parent nodes until a valid replace is found.\n    */\n    deleteRange(from, to) {\n        deleteRange(this, from, to);\n        return this;\n    }\n    /**\n    Split the content in the given range off from its parent, if there\n    is sibling content before or after it, and move it up the tree to\n    the depth specified by `target`. You'll probably want to use\n    [`liftTarget`](https://prosemirror.net/docs/ref/#transform.liftTarget) to compute `target`, to make\n    sure the lift is valid.\n    */\n    lift(range, target) {\n        lift(this, range, target);\n        return this;\n    }\n    /**\n    Join the blocks around the given position. If depth is 2, their\n    last and first siblings are also joined, and so on.\n    */\n    join(pos, depth = 1) {\n        join(this, pos, depth);\n        return this;\n    }\n    /**\n    Wrap the given [range](https://prosemirror.net/docs/ref/#model.NodeRange) in the given set of wrappers.\n    The wrappers are assumed to be valid in this position, and should\n    probably be computed with [`findWrapping`](https://prosemirror.net/docs/ref/#transform.findWrapping).\n    */\n    wrap(range, wrappers) {\n        wrap(this, range, wrappers);\n        return this;\n    }\n    /**\n    Set the type of all textblocks (partly) between `from` and `to` to\n    the given node type with the given attributes.\n    */\n    setBlockType(from, to = from, type, attrs = null) {\n        setBlockType(this, from, to, type, attrs);\n        return this;\n    }\n    /**\n    Change the type, attributes, and/or marks of the node at `pos`.\n    When `type` isn't given, the existing node type is preserved,\n    */\n    setNodeMarkup(pos, type, attrs = null, marks) {\n        setNodeMarkup(this, pos, type, attrs, marks);\n        return this;\n    }\n    /**\n    Set a single attribute on a given node to a new value.\n    The `pos` addresses the document content. Use `setDocAttribute`\n    to set attributes on the document itself.\n    */\n    setNodeAttribute(pos, attr, value) {\n        this.step(new AttrStep(pos, attr, value));\n        return this;\n    }\n    /**\n    Set a single attribute on the document to a new value.\n    */\n    setDocAttribute(attr, value) {\n        this.step(new DocAttrStep(attr, value));\n        return this;\n    }\n    /**\n    Add a mark to the node at position `pos`.\n    */\n    addNodeMark(pos, mark) {\n        this.step(new AddNodeMarkStep(pos, mark));\n        return this;\n    }\n    /**\n    Remove a mark (or all marks of the given type) from the node at\n    position `pos`.\n    */\n    removeNodeMark(pos, mark) {\n        let node = this.doc.nodeAt(pos);\n        if (!node)\n            throw new RangeError(\"No node at position \" + pos);\n        if (mark instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark) {\n            if (mark.isInSet(node.marks))\n                this.step(new RemoveNodeMarkStep(pos, mark));\n        }\n        else {\n            let set = node.marks, found, steps = [];\n            while (found = mark.isInSet(set)) {\n                steps.push(new RemoveNodeMarkStep(pos, found));\n                set = found.removeFromSet(set);\n            }\n            for (let i = steps.length - 1; i >= 0; i--)\n                this.step(steps[i]);\n        }\n        return this;\n    }\n    /**\n    Split the node at the given position, and optionally, if `depth` is\n    greater than one, any number of nodes above that. By default, the\n    parts split off will inherit the node type of the original node.\n    This can be changed by passing an array of types and attributes to\n    use after the split (with the outermost nodes coming first).\n    */\n    split(pos, depth = 1, typesAfter) {\n        split(this, pos, depth, typesAfter);\n        return this;\n    }\n    /**\n    Add the given mark to the inline content between `from` and `to`.\n    */\n    addMark(from, to, mark) {\n        addMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Remove marks from inline nodes between `from` and `to`. When\n    `mark` is a single mark, remove precisely that mark. When it is\n    a mark type, remove all marks of that type. When it is null,\n    remove all marks of any type.\n    */\n    removeMark(from, to, mark) {\n        removeMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Removes all marks and nodes from the content of the node at\n    `pos` that don't match the given new parent node type. Accepts\n    an optional starting [content match](https://prosemirror.net/docs/ref/#model.ContentMatch) as\n    third argument.\n    */\n    clearIncompatible(pos, parentType, match) {\n        clearIncompatible(this, pos, parentType, match);\n        return this;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-transform/dist/index.js\n");

/***/ })

};
;