(()=>{var e={};e.id=620,e.ids=[620],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4875:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>v,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var o={};a.r(o),a.d(o,{OPTIONS:()=>u,POST:()=>d});var r=a(6559),s=a(8088),i=a(7719),n=a(2190);async function d(e){try{let{format:t,items:a,content:o}=await e.json();if(!t||!a||!Array.isArray(a))return n.NextResponse.json({error:"Invalid export request"},{status:400});switch(t){case"presentation":return p(a,o);case"pdf":return l(a,o);case"zip":return x(a);default:return n.NextResponse.json({error:"Unsupported export format"},{status:400})}}catch(e){return console.error("Export API error:",e),n.NextResponse.json({error:"Export failed"},{status:500})}}async function p(e,t){let a=`
<!DOCTYPE html>
<html>
<head>
    <title>StudyVision - Apresenta\xe7\xe3o</title>
    <meta charset="UTF-8">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 0; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .presentation-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .slide { 
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 40px; 
            padding: 40px;
            page-break-after: always;
        }
        .slide h1 { 
            color: #4f46e5; 
            font-size: 2.5em;
            margin-bottom: 20px;
            text-align: center;
        }
        .slide h2 { 
            color: #4f46e5; 
            border-bottom: 3px solid #4f46e5; 
            padding-bottom: 15px; 
            margin-bottom: 30px;
            font-size: 1.8em;
        }
        .slide img, .slide video { 
            max-width: 100%; 
            height: auto; 
            margin: 20px 0; 
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .slide p { 
            line-height: 1.8; 
            margin: 15px 0; 
            font-size: 1.1em;
            color: #374151;
        }
        .metadata {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4f46e5;
        }
        .footer {
            text-align: center;
            color: #6b7280;
            font-size: 0.9em;
            margin-top: 40px;
        }
        @media print {
            body { background: white; }
            .slide { box-shadow: none; border: 1px solid #e5e7eb; }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="slide">
            <h1>📚 StudyVision - Estudo Imersivo</h1>
            <div class="metadata">
                <p><strong>📅 Data de Gera\xe7\xe3o:</strong> ${new Date().toLocaleDateString("pt-BR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}</p>
                <p><strong>🎯 Visualiza\xe7\xf5es:</strong> ${e.length} itens</p>
                <p><strong>🤖 Gerado por:</strong> StudyVision AI</p>
            </div>
        </div>
        
        ${e.map((e,t)=>`
        <div class="slide">
            <h2>📊 Visualiza\xe7\xe3o ${t+1}: ${e.text}</h2>
            <div style="text-align: center;">
                ${"video"===e.type?`<video controls style="max-height: 400px;"><source src="${e.url}" type="video/mp4">Seu navegador n\xe3o suporta v\xeddeo HTML5.</video>`:`<img src="${e.url}" alt="${e.text}" style="max-height: 400px;">`}
            </div>
            <div class="metadata">
                <p><strong>🔬 Conceito:</strong> ${e.text}</p>
                <p><strong>📱 Tipo:</strong> ${"video"===e.type?"\uD83C\uDFA5 V\xeddeo Educativo":"\uD83D\uDDBC️ Imagem Ilustrativa"}</p>
                <p><strong>⏰ Criado em:</strong> ${new Date(e.timestamp).toLocaleString("pt-BR")}</p>
            </div>
        </div>
        `).join("")}
        
        <div class="slide">
            <h2>📝 Conte\xfado Original</h2>
            <div style="background: #f9fafb; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                ${t||"<p>Nenhum conte\xfado dispon\xedvel.</p>"}
            </div>
            <div class="footer">
                <p>Apresenta\xe7\xe3o gerada automaticamente pelo StudyVision AI</p>
                <p>Transformando texto em visualiza\xe7\xf5es imersivas para melhor aprendizado</p>
            </div>
        </div>
    </div>
</body>
</html>`;return new n.NextResponse(a,{headers:{"Content-Type":"text/html","Content-Disposition":`attachment; filename="studyvision_apresentacao_${Date.now()}.html"`}})}async function l(e,t){let a=`
STUDYVISION - RELAT\xd3RIO DE ESTUDO IMERSIVO
==========================================

📅 Data: ${new Date().toLocaleDateString("pt-BR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}
🕒 Hora: ${new Date().toLocaleTimeString("pt-BR")}
📊 Total de Visualiza\xe7\xf5es: ${e.length}

RESUMO EXECUTIVO
================
Este relat\xf3rio cont\xe9m as visualiza\xe7\xf5es geradas durante uma sess\xe3o de estudo
imersivo utilizando a plataforma StudyVision AI. Cada visualiza\xe7\xe3o foi criada
automaticamente a partir de conceitos cient\xedficos destacados no texto.

CONTE\xdaDO ESTUDADO
=================
${t?t.replace(/<[^>]*>/g,"").slice(0,1e3):"Nenhum conte\xfado dispon\xedvel"}
${t&&t.length>1e3?"...\n[Conte\xfado truncado para o relat\xf3rio]":""}

VISUALIZA\xc7\xd5ES GERADAS
=====================

${e.map((e,t)=>`
${t+1}. CONCEITO: ${e.text}
   ────────────────────────────────────────
   📱 Tipo: ${"video"===e.type?"\uD83C\uDFA5 V\xeddeo Educativo":"\uD83D\uDDBC️ Imagem Ilustrativa"}
   🔗 URL: ${e.url}
   ⏰ Data/Hora: ${new Date(e.timestamp).toLocaleString("pt-BR")}
   📏 Tamanho: ${"video"===e.type?"V\xeddeo de 5 segundos":"Imagem 800x600px"}
   
`).join("")}

ESTAT\xcdSTICAS DA SESS\xc3O
======================
• Total de conceitos visualizados: ${e.length}
• Imagens geradas: ${e.filter(e=>"image"===e.type).length}
• V\xeddeos gerados: ${e.filter(e=>"video"===e.type).length}
• Dura\xe7\xe3o da sess\xe3o: ${e.length>0?Math.round((Math.max(...e.map(e=>e.timestamp))-Math.min(...e.map(e=>e.timestamp)))/6e4):0} minutos

RECOMENDA\xc7\xd5ES PARA ESTUDO
=========================
1. 📚 Revise cada visualiza\xe7\xe3o para refor\xe7ar o aprendizado
2. 🔄 Pratique explicando os conceitos com suas pr\xf3prias palavras
3. 🎯 Conecte os conceitos visualizados com exemplos do mundo real
4. 📝 Fa\xe7a anota\xe7\xf5es adicionais sobre insights obtidos
5. 🤝 Compartilhe as visualiza\xe7\xf5es com colegas para discuss\xe3o

SOBRE O STUDYVISION AI
======================
O StudyVision AI \xe9 uma plataforma inovadora que transforma texto em 
visualiza\xe7\xf5es imersivas, facilitando o aprendizado de conceitos complexos
atrav\xe9s de imagens e v\xeddeos gerados por intelig\xeancia artificial.

🌐 Website: studyvision.ai
📧 Suporte: <EMAIL>
📱 Vers\xe3o: 1.0.0

────────────────────────────────────────────────────────────────
Relat\xf3rio gerado automaticamente em ${new Date().toLocaleString("pt-BR")}
\xa9 ${new Date().getFullYear()} StudyVision AI - Todos os direitos reservados
`;return new n.NextResponse(a,{headers:{"Content-Type":"text/plain; charset=utf-8","Content-Disposition":`attachment; filename="studyvision_relatorio_${Date.now()}.txt"`}})}async function x(e){let t={export_info:{timestamp:new Date().toISOString(),total_items:e.length,generated_by:"StudyVision AI"},items:e.map((e,t)=>({id:e.id,filename:`${t+1}_${e.text.replace(/\s+/g,"_").slice(0,30)}.${"video"===e.type?"mp4":"jpg"}`,original_text:e.text,type:e.type,url:e.url,timestamp:e.timestamp}))};return n.NextResponse.json(t,{headers:{"Content-Disposition":`attachment; filename="studyvision_export_manifest_${Date.now()}.json"`}})}async function u(){return new n.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let c=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/export/route",pathname:"/api/export",filename:"route",bundlePath:"app/api/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\export\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:v}=c;function h(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[447,580],()=>a(4875));module.exports=o})();