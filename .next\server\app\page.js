/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RvdWdsYXMlNUMlNUNEZXNrdG9wJTVDJTVDY2FudmEtc2FhcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBZ0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERvdWdsYXNcXFxcRGVza3RvcFxcXFxjYW52YS1zYWFzXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRG91Z2xhc1xcRGVza3RvcFxcY2FudmEtc2Fhc1xcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5b355e34f61c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERvdWdsYXNcXERlc2t0b3BcXGNhbnZhLXNhYXNcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjViMzU1ZTM0ZjYxY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRG91Z2xhc1xcRGVza3RvcFxcY2FudmEtc2Fhc1xcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RvdWdsYXMlNUMlNUNEZXNrdG9wJTVDJTVDY2FudmEtc2FhcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBZ0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERvdWdsYXNcXFxcRGVza3RvcFxcXFxjYW52YS1zYWFzXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDouglas%5C%5CDesktop%5C%5Ccanva-saas%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Eye,Play,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Eye,Play,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Eye,Play,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Eye,Play,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Download,Eye,Play,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_editor_text_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/editor/text-editor */ \"(ssr)/./src/components/editor/text-editor.tsx\");\n/* harmony import */ var _components_visualization_visualization_panel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/visualization/visualization-panel */ \"(ssr)/./src/components/visualization/visualization-panel.tsx\");\n/* harmony import */ var _components_realtime_realtime_status__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/realtime/realtime-status */ \"(ssr)/./src/components/realtime/realtime-status.tsx\");\n/* harmony import */ var _components_reading_reading_mode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/reading/reading-mode */ \"(ssr)/./src/components/reading/reading-mode.tsx\");\n/* harmony import */ var _components_export_export_manager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/export/export-manager */ \"(ssr)/./src/components/export/export-manager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisData, setAnalysisData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showReadingMode, setShowReadingMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [visualizations, setVisualizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleTextSelect = (text)=>{\n        setSelectedText(text);\n    };\n    const handleAnalysisComplete = (analysis)=>{\n        setAnalysisData(analysis);\n        console.log('Analysis completed:', analysis);\n    };\n    const handleGenerate = (text, type)=>{\n        setIsGenerating(true);\n        // Here we'll integrate with media generation APIs\n        console.log('Generating', type, 'for:', text);\n    };\n    const handleContentChange = (content)=>{\n        setEditorContent(content);\n        console.log('Content changed:', content);\n    };\n    const addVisualization = (text, url, type)=>{\n        const newVisualization = {\n            id: `viz-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            text,\n            url,\n            type,\n            timestamp: Date.now()\n        };\n        setVisualizations((prev)=>[\n                newVisualization,\n                ...prev\n            ]);\n    };\n    const enterReadingMode = ()=>{\n        setShowReadingMode(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"StudyVision AI ✨\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: enterReadingMode,\n                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Modo Leitura\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700\",\n                                        children: \"Come\\xe7ar Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Transforme Texto em Visualiza\\xe7\\xf5es Imersivas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Destaque qualquer conceito cient\\xedfico e veja-o ganhar vida atrav\\xe9s de v\\xeddeos e imagens geradas por IA em tempo real.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-3 gap-8 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_text_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    onTextSelect: handleTextSelect,\n                                    onContentChange: handleContentChange,\n                                    onAnalysisComplete: handleAnalysisComplete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_visualization_panel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        selectedText: selectedText,\n                                        analysisData: analysisData,\n                                        onGenerate: handleGenerate,\n                                        onVisualizationGenerated: addVisualization\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_realtime_realtime_status__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onTextSelected: (data)=>console.log('Text selected by user:', data),\n                                        onAnalysisUpdate: (data)=>console.log('Analysis update:', data),\n                                        onGenerationUpdate: (data)=>console.log('Generation update:', data)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_export_export_manager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        visualizations: visualizations,\n                                        content: editorContent,\n                                        onExport: (format, items)=>console.log('Exported:', format, items)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-8 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-indigo-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"IA Avan\\xe7ada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Powered by GPT-4 e VEO3 para gerar visualiza\\xe7\\xf5es precisas e educativas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-indigo-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Tempo Real\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Visualiza\\xe7\\xf5es geradas instantaneamente conforme voc\\xea estuda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Download_Eye_Play_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-indigo-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Exporta\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Baixe v\\xeddeos e imagens para usar em apresenta\\xe7\\xf5es e estudos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-8 py-4 bg-indigo-600 text-white text-lg font-semibold rounded-lg hover:bg-indigo-700 transition-colors\",\n                                children: \"Come\\xe7ar Agora - Gr\\xe1tis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mt-2\",\n                                children: \"10 visualiza\\xe7\\xf5es gratuitas • Sem cart\\xe3o de cr\\xe9dito\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            showReadingMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reading_reading_mode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                content: editorContent || `\n            <h1>Exemplo de Conteúdo Científico</h1>\n            <p>A <mark>mitose</mark> é um processo fundamental da divisão celular onde uma célula se divide para formar duas células filhas geneticamente idênticas.</p>\n            <p>Durante a <mark>prófase</mark>, os cromossomos se condensam e tornam-se visíveis ao microscópio. O envelope nuclear começa a se desintegrar.</p>\n            <p>Na <mark>metáfase</mark>, os cromossomos se alinham no centro da célula, formando a placa metafásica. Este é um momento crucial para garantir que cada célula filha receba o número correto de cromossomos.</p>\n            <p>Durante a <mark>anáfase</mark>, as cromátides irmãs se separam e migram para polos opostos da célula.</p>\n            <p>Finalmente, na <mark>telófase</mark>, novos envelopes nucleares se formam ao redor de cada conjunto de cromossomos, completando o processo de divisão.</p>\n          `,\n                visualizations: visualizations,\n                onClose: ()=>setShowReadingMode(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/editor/text-editor.tsx":
/*!***********************************************!*\
  !*** ./src/components/editor/text-editor.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tiptap/react */ \"(ssr)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(ssr)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _tiptap_extension_highlight__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/extension-highlight */ \"(ssr)/./node_modules/@tiptap/extension-highlight/dist/index.js\");\n/* harmony import */ var _tiptap_extension_text_style__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-text-style */ \"(ssr)/./node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var _tiptap_extension_color__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-color */ \"(ssr)/./node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,Loader2,Palette,Sparkles,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,Loader2,Palette,Sparkles,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,Loader2,Palette,Sparkles,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,Loader2,Palette,Sparkles,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,Loader2,Palette,Sparkles,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,Loader2,Palette,Sparkles,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _hooks_useAnalysis__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAnalysis */ \"(ssr)/./src/hooks/useAnalysis.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction TextEditor({ onTextSelect, onContentChange, onAnalysisComplete }) {\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('');\n    const [showToolbar, setShowToolbar] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const { analyzeText, loading: analyzing, error: analysisError } = (0,_hooks_useAnalysis__WEBPACK_IMPORTED_MODULE_6__.useAnalysis)();\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_7__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            _tiptap_extension_highlight__WEBPACK_IMPORTED_MODULE_2__[\"default\"].configure({\n                multicolor: true\n            }),\n            _tiptap_extension_text_style__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            _tiptap_extension_color__WEBPACK_IMPORTED_MODULE_4__.Color\n        ],\n        content: `\n      <p>A <mark data-color=\"#fbbf24\">mitose</mark> é um processo fundamental da divisão celular onde uma célula se divide para formar duas células filhas geneticamente idênticas.</p>\n      \n      <p>Durante a <mark data-color=\"#fbbf24\">prófase</mark>, os cromossomos se condensam e tornam-se visíveis ao microscópio. O envelope nuclear começa a se desintegrar.</p>\n      \n      <p>Na <mark data-color=\"#fbbf24\">metáfase</mark>, os cromossomos se alinham no centro da célula, formando a placa metafásica. Este é um momento crucial para garantir que cada célula filha receba o número correto de cromossomos.</p>\n      \n      <p>Durante a <mark data-color=\"#fbbf24\">anáfase</mark>, as cromátides irmãs se separam e migram para polos opostos da célula.</p>\n      \n      <p>Finalmente, na <mark data-color=\"#fbbf24\">telófase</mark>, novos envelopes nucleares se formam ao redor de cada conjunto de cromossomos, completando o processo de divisão.</p>\n    `,\n        onUpdate: {\n            \"TextEditor.useEditor[editor]\": ({ editor })=>{\n                const content = editor.getHTML();\n                onContentChange?.(content);\n            }\n        }[\"TextEditor.useEditor[editor]\"],\n        onSelectionUpdate: {\n            \"TextEditor.useEditor[editor]\": ({ editor })=>{\n                const { from, to } = editor.state.selection;\n                const text = editor.state.doc.textBetween(from, to, '');\n                if (text.trim().length > 0) {\n                    setSelectedText(text.trim());\n                    setShowToolbar(true);\n                    onTextSelect?.(text.trim());\n                } else {\n                    setSelectedText('');\n                    setShowToolbar(false);\n                }\n            }\n        }[\"TextEditor.useEditor[editor]\"]\n    });\n    const highlightSelection = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"TextEditor.useCallback[highlightSelection]\": (color)=>{\n            if (editor && selectedText) {\n                editor.chain().focus().toggleHighlight({\n                    color\n                }).run();\n                onTextSelect?.(selectedText);\n            }\n        }\n    }[\"TextEditor.useCallback[highlightSelection]\"], [\n        editor,\n        selectedText,\n        onTextSelect\n    ]);\n    const generateVisualization = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"TextEditor.useCallback[generateVisualization]\": async (visualizationType = 'image')=>{\n            if (selectedText && !analyzing) {\n                try {\n                    const analysis = await analyzeText(selectedText, '', visualizationType);\n                    onAnalysisComplete?.(analysis);\n                    onTextSelect?.(selectedText);\n                } catch (error) {\n                    console.error('Analysis failed:', error);\n                }\n            }\n        }\n    }[\"TextEditor.useCallback[generateVisualization]\"], [\n        selectedText,\n        analyzing,\n        analyzeText,\n        onAnalysisComplete,\n        onTextSelect\n    ]);\n    if (!editor) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border rounded-lg p-4 min-h-[400px] bg-gray-50 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-5/6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            showToolbar && selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mb-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white border rounded-lg shadow-lg p-2 flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-gray-600 px-2\",\n                            children: [\n                                '\"',\n                                selectedText.slice(0, 20),\n                                '...\"'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-px h-6 bg-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>highlightSelection('#fbbf24'),\n                            className: \"w-6 h-6 bg-yellow-300 rounded hover:scale-110 transition-transform\",\n                            title: \"Destacar em amarelo\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>highlightSelection('#60a5fa'),\n                            className: \"w-6 h-6 bg-blue-400 rounded hover:scale-110 transition-transform\",\n                            title: \"Destacar em azul\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>highlightSelection('#34d399'),\n                            className: \"w-6 h-6 bg-green-400 rounded hover:scale-110 transition-transform\",\n                            title: \"Destacar em verde\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-px h-6 bg-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>generateVisualization('image'),\n                            disabled: analyzing,\n                            className: \"flex items-center space-x-1 px-3 py-1 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                analyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-3 w-3 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 28\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 75\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Imagem\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>generateVisualization('video'),\n                            disabled: analyzing,\n                            className: \"flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                analyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-3 w-3 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 28\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 75\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"V\\xeddeo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b p-2 bg-gray-50 rounded-t-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>editor.chain().focus().toggleBold().run(),\n                            className: `p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('bold') ? 'bg-gray-200' : ''}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>editor.chain().focus().toggleItalic().run(),\n                            className: `p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('italic') ? 'bg-gray-200' : ''}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-serif italic\",\n                                children: \"I\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-px h-6 bg-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>editor.chain().focus().toggleHighlight({\n                                    color: '#fbbf24'\n                                }).run(),\n                            className: `p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('highlight') ? 'bg-gray-200' : ''}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Destaque termos para gerar visualiza\\xe7\\xf5es\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border border-t-0 rounded-b-lg p-4 min-h-[400px] bg-white prose prose-sm max-w-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tiptap_react__WEBPACK_IMPORTED_MODULE_7__.EditorContent, {\n                    editor: editor\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 p-2 bg-indigo-50 border border-indigo-200 rounded text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-indigo-700 font-medium\",\n                        children: \"Texto selecionado:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-indigo-600 ml-2\",\n                        children: [\n                            '\"',\n                            selectedText,\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    analyzing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-indigo-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_Loader2_Palette_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 animate-spin mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Analisando com IA...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this),\n            analysisError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-700 font-medium\",\n                        children: \"Erro na an\\xe1lise:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-600 ml-2\",\n                        children: analysisError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\editor\\\\text-editor.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9lZGl0b3IvdGV4dC1lZGl0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5RDtBQUNaO0FBQ087QUFDQztBQUNMO0FBQ0Y7QUFDOEI7QUFDMUI7QUFRbkMsU0FBU2UsV0FBVyxFQUFFQyxZQUFZLEVBQUVDLGVBQWUsRUFBRUMsa0JBQWtCLEVBQW1CO0lBQ3ZHLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdkLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2UsYUFBYUMsZUFBZSxHQUFHaEIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxFQUFFaUIsV0FBVyxFQUFFQyxTQUFTQyxTQUFTLEVBQUVDLE9BQU9DLGFBQWEsRUFBRSxHQUFHYiwrREFBV0E7SUFFN0UsTUFBTWMsU0FBUzVCLHdEQUFTQSxDQUFDO1FBQ3ZCNkIsWUFBWTtZQUNWM0IsMkRBQVVBO1lBQ1ZDLG1FQUFTQSxDQUFDMkIsU0FBUyxDQUFDO2dCQUNsQkMsWUFBWTtZQUNkO1lBQ0EzQixvRUFBU0E7WUFDVEMsMERBQUtBO1NBQ047UUFDRDJCLFNBQVMsQ0FBQzs7Ozs7Ozs7OztJQVVWLENBQUM7UUFDREMsUUFBUTs0Q0FBRSxDQUFDLEVBQUVMLE1BQU0sRUFBRTtnQkFDbkIsTUFBTUksVUFBVUosT0FBT00sT0FBTztnQkFDOUJqQixrQkFBa0JlO1lBQ3BCOztRQUNBRyxpQkFBaUI7NENBQUUsQ0FBQyxFQUFFUCxNQUFNLEVBQUU7Z0JBQzVCLE1BQU0sRUFBRVEsSUFBSSxFQUFFQyxFQUFFLEVBQUUsR0FBR1QsT0FBT1UsS0FBSyxDQUFDQyxTQUFTO2dCQUMzQyxNQUFNQyxPQUFPWixPQUFPVSxLQUFLLENBQUNHLEdBQUcsQ0FBQ0MsV0FBVyxDQUFDTixNQUFNQyxJQUFJO2dCQUVwRCxJQUFJRyxLQUFLRyxJQUFJLEdBQUdDLE1BQU0sR0FBRyxHQUFHO29CQUMxQnhCLGdCQUFnQm9CLEtBQUtHLElBQUk7b0JBQ3pCckIsZUFBZTtvQkFDZk4sZUFBZXdCLEtBQUtHLElBQUk7Z0JBQzFCLE9BQU87b0JBQ0x2QixnQkFBZ0I7b0JBQ2hCRSxlQUFlO2dCQUNqQjtZQUNGOztJQUNGO0lBRUEsTUFBTXVCLHFCQUFxQnRDLGtEQUFXQTtzREFBQyxDQUFDdUM7WUFDdEMsSUFBSWxCLFVBQVVULGNBQWM7Z0JBQzFCUyxPQUFPbUIsS0FBSyxHQUFHQyxLQUFLLEdBQUdDLGVBQWUsQ0FBQztvQkFBRUg7Z0JBQU0sR0FBR0ksR0FBRztnQkFDckRsQyxlQUFlRztZQUNqQjtRQUNGO3FEQUFHO1FBQUNTO1FBQVFUO1FBQWNIO0tBQWE7SUFFdkMsTUFBTW1DLHdCQUF3QjVDLGtEQUFXQTt5REFBQyxPQUFPNkMsb0JBQXVDLE9BQU87WUFDN0YsSUFBSWpDLGdCQUFnQixDQUFDTSxXQUFXO2dCQUM5QixJQUFJO29CQUNGLE1BQU00QixXQUFXLE1BQU05QixZQUFZSixjQUFjLElBQUlpQztvQkFDckRsQyxxQkFBcUJtQztvQkFDckJyQyxlQUFlRztnQkFDakIsRUFBRSxPQUFPTyxPQUFPO29CQUNkNEIsUUFBUTVCLEtBQUssQ0FBQyxvQkFBb0JBO2dCQUNwQztZQUNGO1FBQ0Y7d0RBQUc7UUFBQ1A7UUFBY007UUFBV0Y7UUFBYUw7UUFBb0JGO0tBQWE7SUFFM0UsSUFBSSxDQUFDWSxRQUFRO1FBQ1gscUJBQ0UsOERBQUMyQjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFDZiw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFDZiw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7OztJQUdyQjtJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOztZQUVabkMsZUFBZUYsOEJBQ2QsOERBQUNvQztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBS0QsV0FBVTs7Z0NBQTZCO2dDQUFFckMsYUFBYXVDLEtBQUssQ0FBQyxHQUFHO2dDQUFJOzs7Ozs7O3NDQUN6RSw4REFBQ0g7NEJBQUlDLFdBQVU7Ozs7OztzQ0FHZiw4REFBQ0c7NEJBQ0NDLFNBQVMsSUFBTWYsbUJBQW1COzRCQUNsQ1csV0FBVTs0QkFDVkssT0FBTTs7Ozs7O3NDQUVSLDhEQUFDRjs0QkFDQ0MsU0FBUyxJQUFNZixtQkFBbUI7NEJBQ2xDVyxXQUFVOzRCQUNWSyxPQUFNOzs7Ozs7c0NBRVIsOERBQUNGOzRCQUNDQyxTQUFTLElBQU1mLG1CQUFtQjs0QkFDbENXLFdBQVU7NEJBQ1ZLLE9BQU07Ozs7OztzQ0FHUiw4REFBQ047NEJBQUlDLFdBQVU7Ozs7OztzQ0FHZiw4REFBQ0c7NEJBQ0NDLFNBQVMsSUFBTVQsc0JBQXNCOzRCQUNyQ1csVUFBVXJDOzRCQUNWK0IsV0FBVTs7Z0NBRVQvQiwwQkFBWSw4REFBQ1osbUhBQU9BO29DQUFDMkMsV0FBVTs7Ozs7eURBQTRCLDhEQUFDaEQsbUhBQVFBO29DQUFDZ0QsV0FBVTs7Ozs7OzhDQUNoRiw4REFBQ0M7OENBQUs7Ozs7Ozs7Ozs7OztzQ0FHUiw4REFBQ0U7NEJBQ0NDLFNBQVMsSUFBTVQsc0JBQXNCOzRCQUNyQ1csVUFBVXJDOzRCQUNWK0IsV0FBVTs7Z0NBRVQvQiwwQkFBWSw4REFBQ1osbUhBQU9BO29DQUFDMkMsV0FBVTs7Ozs7eURBQTRCLDhEQUFDNUMsb0hBQUtBO29DQUFDNEMsV0FBVTs7Ozs7OzhDQUM3RSw4REFBQ0M7OENBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9kLDhEQUFDRjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFDQ0MsU0FBUyxJQUFNaEMsT0FBT21CLEtBQUssR0FBR0MsS0FBSyxHQUFHZSxVQUFVLEdBQUdiLEdBQUc7NEJBQ3RETSxXQUFXLENBQUMsZ0RBQWdELEVBQzFENUIsT0FBT29DLFFBQVEsQ0FBQyxVQUFVLGdCQUFnQixJQUMxQztzQ0FFRiw0RUFBQ3ZELG9IQUFJQTtnQ0FBQytDLFdBQVU7Ozs7Ozs7Ozs7O3NDQUdsQiw4REFBQ0c7NEJBQ0NDLFNBQVMsSUFBTWhDLE9BQU9tQixLQUFLLEdBQUdDLEtBQUssR0FBR2lCLFlBQVksR0FBR2YsR0FBRzs0QkFDeERNLFdBQVcsQ0FBQyxnREFBZ0QsRUFDMUQ1QixPQUFPb0MsUUFBUSxDQUFDLFlBQVksZ0JBQWdCLElBQzVDO3NDQUVGLDRFQUFDUDtnQ0FBS0QsV0FBVTswQ0FBNEI7Ozs7Ozs7Ozs7O3NDQUc5Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7Ozs7OztzQ0FFZiw4REFBQ0c7NEJBQ0NDLFNBQVMsSUFBTWhDLE9BQU9tQixLQUFLLEdBQUdDLEtBQUssR0FBR0MsZUFBZSxDQUFDO29DQUFFSCxPQUFPO2dDQUFVLEdBQUdJLEdBQUc7NEJBQy9FTSxXQUFXLENBQUMsZ0RBQWdELEVBQzFENUIsT0FBT29DLFFBQVEsQ0FBQyxlQUFlLGdCQUFnQixJQUMvQztzQ0FFRiw0RUFBQ3RELG9IQUFPQTtnQ0FBQzhDLFdBQVU7Ozs7Ozs7Ozs7O3NDQUdyQiw4REFBQ0Q7NEJBQUlDLFdBQVU7Ozs7OztzQ0FFZiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDN0Msb0hBQUdBO29DQUFDNkMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDQzs4Q0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVosOERBQUNGO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDdkQsd0RBQWFBO29CQUFDMkIsUUFBUUE7Ozs7Ozs7Ozs7O1lBSXhCVCw4QkFDQyw4REFBQ29DO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUtELFdBQVU7a0NBQThCOzs7Ozs7a0NBQzlDLDhEQUFDQzt3QkFBS0QsV0FBVTs7NEJBQXVCOzRCQUFFckM7NEJBQWE7Ozs7Ozs7b0JBQ3JETSwyQkFDQyw4REFBQzhCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzNDLG1IQUFPQTtnQ0FBQzJDLFdBQVU7Ozs7OzswQ0FDbkIsOERBQUNDOzBDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPYjlCLCtCQUNDLDhEQUFDNEI7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBS0QsV0FBVTtrQ0FBMkI7Ozs7OztrQ0FDM0MsOERBQUNDO3dCQUFLRCxXQUFVO2tDQUFxQjdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLL0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRG91Z2xhc1xcRGVza3RvcFxcY2FudmEtc2Fhc1xcc3JjXFxjb21wb25lbnRzXFxlZGl0b3JcXHRleHQtZWRpdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVkaXRvciwgRWRpdG9yQ29udGVudCB9IGZyb20gJ0B0aXB0YXAvcmVhY3QnO1xuaW1wb3J0IFN0YXJ0ZXJLaXQgZnJvbSAnQHRpcHRhcC9zdGFydGVyLWtpdCc7XG5pbXBvcnQgSGlnaGxpZ2h0IGZyb20gJ0B0aXB0YXAvZXh0ZW5zaW9uLWhpZ2hsaWdodCc7XG5pbXBvcnQgVGV4dFN0eWxlIGZyb20gJ0B0aXB0YXAvZXh0ZW5zaW9uLXRleHQtc3R5bGUnO1xuaW1wb3J0IHsgQ29sb3IgfSBmcm9tICdAdGlwdGFwL2V4dGVuc2lvbi1jb2xvcic7XG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTcGFya2xlcywgVHlwZSwgUGFsZXR0ZSwgRXllLCBCcmFpbiwgTG9hZGVyMiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyB1c2VBbmFseXNpcyB9IGZyb20gJ0AvaG9va3MvdXNlQW5hbHlzaXMnO1xuXG5pbnRlcmZhY2UgVGV4dEVkaXRvclByb3BzIHtcbiAgb25UZXh0U2VsZWN0PzogKHRleHQ6IHN0cmluZykgPT4gdm9pZDtcbiAgb25Db250ZW50Q2hhbmdlPzogKGNvbnRlbnQ6IHN0cmluZykgPT4gdm9pZDtcbiAgb25BbmFseXNpc0NvbXBsZXRlPzogKGFuYWx5c2lzOiBhbnkpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRleHRFZGl0b3IoeyBvblRleHRTZWxlY3QsIG9uQ29udGVudENoYW5nZSwgb25BbmFseXNpc0NvbXBsZXRlIH06IFRleHRFZGl0b3JQcm9wcykge1xuICBjb25zdCBbc2VsZWN0ZWRUZXh0LCBzZXRTZWxlY3RlZFRleHRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbc2hvd1Rvb2xiYXIsIHNldFNob3dUb29sYmFyXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgeyBhbmFseXplVGV4dCwgbG9hZGluZzogYW5hbHl6aW5nLCBlcnJvcjogYW5hbHlzaXNFcnJvciB9ID0gdXNlQW5hbHlzaXMoKTtcblxuICBjb25zdCBlZGl0b3IgPSB1c2VFZGl0b3Ioe1xuICAgIGV4dGVuc2lvbnM6IFtcbiAgICAgIFN0YXJ0ZXJLaXQsXG4gICAgICBIaWdobGlnaHQuY29uZmlndXJlKHtcbiAgICAgICAgbXVsdGljb2xvcjogdHJ1ZSxcbiAgICAgIH0pLFxuICAgICAgVGV4dFN0eWxlLFxuICAgICAgQ29sb3IsXG4gICAgXSxcbiAgICBjb250ZW50OiBgXG4gICAgICA8cD5BIDxtYXJrIGRhdGEtY29sb3I9XCIjZmJiZjI0XCI+bWl0b3NlPC9tYXJrPiDDqSB1bSBwcm9jZXNzbyBmdW5kYW1lbnRhbCBkYSBkaXZpc8OjbyBjZWx1bGFyIG9uZGUgdW1hIGPDqWx1bGEgc2UgZGl2aWRlIHBhcmEgZm9ybWFyIGR1YXMgY8OpbHVsYXMgZmlsaGFzIGdlbmV0aWNhbWVudGUgaWTDqm50aWNhcy48L3A+XG4gICAgICBcbiAgICAgIDxwPkR1cmFudGUgYSA8bWFyayBkYXRhLWNvbG9yPVwiI2ZiYmYyNFwiPnByw7NmYXNlPC9tYXJrPiwgb3MgY3JvbW9zc29tb3Mgc2UgY29uZGVuc2FtIGUgdG9ybmFtLXNlIHZpc8OtdmVpcyBhbyBtaWNyb3Njw7NwaW8uIE8gZW52ZWxvcGUgbnVjbGVhciBjb21lw6dhIGEgc2UgZGVzaW50ZWdyYXIuPC9wPlxuICAgICAgXG4gICAgICA8cD5OYSA8bWFyayBkYXRhLWNvbG9yPVwiI2ZiYmYyNFwiPm1ldMOhZmFzZTwvbWFyaz4sIG9zIGNyb21vc3NvbW9zIHNlIGFsaW5oYW0gbm8gY2VudHJvIGRhIGPDqWx1bGEsIGZvcm1hbmRvIGEgcGxhY2EgbWV0YWbDoXNpY2EuIEVzdGUgw6kgdW0gbW9tZW50byBjcnVjaWFsIHBhcmEgZ2FyYW50aXIgcXVlIGNhZGEgY8OpbHVsYSBmaWxoYSByZWNlYmEgbyBuw7ptZXJvIGNvcnJldG8gZGUgY3JvbW9zc29tb3MuPC9wPlxuICAgICAgXG4gICAgICA8cD5EdXJhbnRlIGEgPG1hcmsgZGF0YS1jb2xvcj1cIiNmYmJmMjRcIj5hbsOhZmFzZTwvbWFyaz4sIGFzIGNyb23DoXRpZGVzIGlybcOjcyBzZSBzZXBhcmFtIGUgbWlncmFtIHBhcmEgcG9sb3Mgb3Bvc3RvcyBkYSBjw6lsdWxhLjwvcD5cbiAgICAgIFxuICAgICAgPHA+RmluYWxtZW50ZSwgbmEgPG1hcmsgZGF0YS1jb2xvcj1cIiNmYmJmMjRcIj50ZWzDs2Zhc2U8L21hcms+LCBub3ZvcyBlbnZlbG9wZXMgbnVjbGVhcmVzIHNlIGZvcm1hbSBhbyByZWRvciBkZSBjYWRhIGNvbmp1bnRvIGRlIGNyb21vc3NvbW9zLCBjb21wbGV0YW5kbyBvIHByb2Nlc3NvIGRlIGRpdmlzw6NvLjwvcD5cbiAgICBgLFxuICAgIG9uVXBkYXRlOiAoeyBlZGl0b3IgfSkgPT4ge1xuICAgICAgY29uc3QgY29udGVudCA9IGVkaXRvci5nZXRIVE1MKCk7XG4gICAgICBvbkNvbnRlbnRDaGFuZ2U/Lihjb250ZW50KTtcbiAgICB9LFxuICAgIG9uU2VsZWN0aW9uVXBkYXRlOiAoeyBlZGl0b3IgfSkgPT4ge1xuICAgICAgY29uc3QgeyBmcm9tLCB0byB9ID0gZWRpdG9yLnN0YXRlLnNlbGVjdGlvbjtcbiAgICAgIGNvbnN0IHRleHQgPSBlZGl0b3Iuc3RhdGUuZG9jLnRleHRCZXR3ZWVuKGZyb20sIHRvLCAnJyk7XG4gICAgICBcbiAgICAgIGlmICh0ZXh0LnRyaW0oKS5sZW5ndGggPiAwKSB7XG4gICAgICAgIHNldFNlbGVjdGVkVGV4dCh0ZXh0LnRyaW0oKSk7XG4gICAgICAgIHNldFNob3dUb29sYmFyKHRydWUpO1xuICAgICAgICBvblRleHRTZWxlY3Q/Lih0ZXh0LnRyaW0oKSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRTZWxlY3RlZFRleHQoJycpO1xuICAgICAgICBzZXRTaG93VG9vbGJhcihmYWxzZSk7XG4gICAgICB9XG4gICAgfSxcbiAgfSk7XG5cbiAgY29uc3QgaGlnaGxpZ2h0U2VsZWN0aW9uID0gdXNlQ2FsbGJhY2soKGNvbG9yOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoZWRpdG9yICYmIHNlbGVjdGVkVGV4dCkge1xuICAgICAgZWRpdG9yLmNoYWluKCkuZm9jdXMoKS50b2dnbGVIaWdobGlnaHQoeyBjb2xvciB9KS5ydW4oKTtcbiAgICAgIG9uVGV4dFNlbGVjdD8uKHNlbGVjdGVkVGV4dCk7XG4gICAgfVxuICB9LCBbZWRpdG9yLCBzZWxlY3RlZFRleHQsIG9uVGV4dFNlbGVjdF0pO1xuXG4gIGNvbnN0IGdlbmVyYXRlVmlzdWFsaXphdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jICh2aXN1YWxpemF0aW9uVHlwZTogJ2ltYWdlJyB8ICd2aWRlbycgPSAnaW1hZ2UnKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkVGV4dCAmJiAhYW5hbHl6aW5nKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBhbmFseXNpcyA9IGF3YWl0IGFuYWx5emVUZXh0KHNlbGVjdGVkVGV4dCwgJycsIHZpc3VhbGl6YXRpb25UeXBlKTtcbiAgICAgICAgb25BbmFseXNpc0NvbXBsZXRlPy4oYW5hbHlzaXMpO1xuICAgICAgICBvblRleHRTZWxlY3Q/LihzZWxlY3RlZFRleHQpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQW5hbHlzaXMgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzZWxlY3RlZFRleHQsIGFuYWx5emluZywgYW5hbHl6ZVRleHQsIG9uQW5hbHlzaXNDb21wbGV0ZSwgb25UZXh0U2VsZWN0XSk7XG5cbiAgaWYgKCFlZGl0b3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXIgcm91bmRlZC1sZyBwLTQgbWluLWgtWzQwMHB4XSBiZy1ncmF5LTUwIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS0yMDAgcm91bmRlZCB3LTMvNCBtYi0yXCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzIgbWItMlwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTIwMCByb3VuZGVkIHctNS82XCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICB7LyogRmxvYXRpbmcgVG9vbGJhciAqL31cbiAgICAgIHtzaG93VG9vbGJhciAmJiBzZWxlY3RlZFRleHQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIC10cmFuc2xhdGUteS1mdWxsIG1iLTIgei0xMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtMiBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMCBweC0yXCI+XCJ7c2VsZWN0ZWRUZXh0LnNsaWNlKDAsIDIwKX0uLi5cIjwvc3Bhbj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1weCBoLTYgYmctZ3JheS0yMDBcIj48L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIEhpZ2hsaWdodCBDb2xvcnMgKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhpZ2hsaWdodFNlbGVjdGlvbignI2ZiYmYyNCcpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLXllbGxvdy0zMDAgcm91bmRlZCBob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm1cIlxuICAgICAgICAgICAgICB0aXRsZT1cIkRlc3RhY2FyIGVtIGFtYXJlbG9cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGlnaGxpZ2h0U2VsZWN0aW9uKCcjNjBhNWZhJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgYmctYmx1ZS00MDAgcm91bmRlZCBob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm1cIlxuICAgICAgICAgICAgICB0aXRsZT1cIkRlc3RhY2FyIGVtIGF6dWxcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGlnaGxpZ2h0U2VsZWN0aW9uKCcjMzRkMzk5Jyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgYmctZ3JlZW4tNDAwIHJvdW5kZWQgaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtXCJcbiAgICAgICAgICAgICAgdGl0bGU9XCJEZXN0YWNhciBlbSB2ZXJkZVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctcHggaC02IGJnLWdyYXktMjAwXCI+PC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBHZW5lcmF0ZSBWaXN1YWxpemF0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGdlbmVyYXRlVmlzdWFsaXphdGlvbignaW1hZ2UnKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FuYWx5emluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHB4LTMgcHktMSBiZy1pbmRpZ28tNjAwIHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkIGhvdmVyOmJnLWluZGlnby03MDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7YW5hbHl6aW5nID8gPExvYWRlcjIgY2xhc3NOYW1lPVwiaC0zIHctMyBhbmltYXRlLXNwaW5cIiAvPiA6IDxTcGFya2xlcyBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz59XG4gICAgICAgICAgICAgIDxzcGFuPkltYWdlbTwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGdlbmVyYXRlVmlzdWFsaXphdGlvbigndmlkZW8nKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FuYWx5emluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHB4LTMgcHktMSBiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkIGhvdmVyOmJnLXB1cnBsZS03MDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7YW5hbHl6aW5nID8gPExvYWRlcjIgY2xhc3NOYW1lPVwiaC0zIHctMyBhbmltYXRlLXNwaW5cIiAvPiA6IDxCcmFpbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz59XG4gICAgICAgICAgICAgIDxzcGFuPlbDrWRlbzwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBFZGl0b3IgVG9vbGJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgcC0yIGJnLWdyYXktNTAgcm91bmRlZC10LWxnXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZWRpdG9yLmNoYWluKCkuZm9jdXMoKS50b2dnbGVCb2xkKCkucnVuKCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZCBob3ZlcjpiZy1ncmF5LTIwMCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICBlZGl0b3IuaXNBY3RpdmUoJ2JvbGQnKSA/ICdiZy1ncmF5LTIwMCcgOiAnJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFR5cGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZWRpdG9yLmNoYWluKCkuZm9jdXMoKS50b2dnbGVJdGFsaWMoKS5ydW4oKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMiByb3VuZGVkIGhvdmVyOmJnLWdyYXktMjAwIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgIGVkaXRvci5pc0FjdGl2ZSgnaXRhbGljJykgPyAnYmctZ3JheS0yMDAnIDogJydcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZXJpZiBpdGFsaWNcIj5JPC9zcGFuPlxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LXB4IGgtNiBiZy1ncmF5LTMwMFwiPjwvZGl2PlxuXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZWRpdG9yLmNoYWluKCkuZm9jdXMoKS50b2dnbGVIaWdobGlnaHQoeyBjb2xvcjogJyNmYmJmMjQnIH0pLnJ1bigpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQgaG92ZXI6YmctZ3JheS0yMDAgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgZWRpdG9yLmlzQWN0aXZlKCdoaWdobGlnaHQnKSA/ICdiZy1ncmF5LTIwMCcgOiAnJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFBhbGV0dGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPjwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC14cyB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPHNwYW4+RGVzdGFxdWUgdGVybW9zIHBhcmEgZ2VyYXIgdmlzdWFsaXphw6fDtWVzPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRWRpdG9yIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItdC0wIHJvdW5kZWQtYi1sZyBwLTQgbWluLWgtWzQwMHB4XSBiZy13aGl0ZSBwcm9zZSBwcm9zZS1zbSBtYXgtdy1ub25lXCI+XG4gICAgICAgIDxFZGl0b3JDb250ZW50IGVkaXRvcj17ZWRpdG9yfSAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTZWxlY3RlZCBUZXh0IEluZm8gKi99XG4gICAgICB7c2VsZWN0ZWRUZXh0ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHAtMiBiZy1pbmRpZ28tNTAgYm9yZGVyIGJvcmRlci1pbmRpZ28tMjAwIHJvdW5kZWQgdGV4dC1zbVwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTcwMCBmb250LW1lZGl1bVwiPlRleHRvIHNlbGVjaW9uYWRvOjwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWluZGlnby02MDAgbWwtMlwiPlwie3NlbGVjdGVkVGV4dH1cIjwvc3Bhbj5cbiAgICAgICAgICB7YW5hbHl6aW5nICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMiB0ZXh0LWluZGlnby02MDBcIj5cbiAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW4gbXItMlwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPkFuYWxpc2FuZG8gY29tIElBLi4uPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogQW5hbHlzaXMgRXJyb3IgKi99XG4gICAgICB7YW5hbHlzaXNFcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBwLTIgYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkIHRleHQtc21cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDAgZm9udC1tZWRpdW1cIj5FcnJvIG5hIGFuw6FsaXNlOjwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWwtMlwiPnthbmFseXNpc0Vycm9yfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVkaXRvciIsIkVkaXRvckNvbnRlbnQiLCJTdGFydGVyS2l0IiwiSGlnaGxpZ2h0IiwiVGV4dFN0eWxlIiwiQ29sb3IiLCJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwiU3BhcmtsZXMiLCJUeXBlIiwiUGFsZXR0ZSIsIkV5ZSIsIkJyYWluIiwiTG9hZGVyMiIsInVzZUFuYWx5c2lzIiwiVGV4dEVkaXRvciIsIm9uVGV4dFNlbGVjdCIsIm9uQ29udGVudENoYW5nZSIsIm9uQW5hbHlzaXNDb21wbGV0ZSIsInNlbGVjdGVkVGV4dCIsInNldFNlbGVjdGVkVGV4dCIsInNob3dUb29sYmFyIiwic2V0U2hvd1Rvb2xiYXIiLCJhbmFseXplVGV4dCIsImxvYWRpbmciLCJhbmFseXppbmciLCJlcnJvciIsImFuYWx5c2lzRXJyb3IiLCJlZGl0b3IiLCJleHRlbnNpb25zIiwiY29uZmlndXJlIiwibXVsdGljb2xvciIsImNvbnRlbnQiLCJvblVwZGF0ZSIsImdldEhUTUwiLCJvblNlbGVjdGlvblVwZGF0ZSIsImZyb20iLCJ0byIsInN0YXRlIiwic2VsZWN0aW9uIiwidGV4dCIsImRvYyIsInRleHRCZXR3ZWVuIiwidHJpbSIsImxlbmd0aCIsImhpZ2hsaWdodFNlbGVjdGlvbiIsImNvbG9yIiwiY2hhaW4iLCJmb2N1cyIsInRvZ2dsZUhpZ2hsaWdodCIsInJ1biIsImdlbmVyYXRlVmlzdWFsaXphdGlvbiIsInZpc3VhbGl6YXRpb25UeXBlIiwiYW5hbHlzaXMiLCJjb25zb2xlIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsInNsaWNlIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwiZGlzYWJsZWQiLCJ0b2dnbGVCb2xkIiwiaXNBY3RpdmUiLCJ0b2dnbGVJdGFsaWMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/editor/text-editor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/export/export-manager.tsx":
/*!**************************************************!*\
  !*** ./src/components/export/export-manager.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExportManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-video.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/presentation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileImage,FileText,FileVideo,Loader2,Package,Presentation!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ExportManager({ visualizations, content, onExport }) {\n    const [selectedItems, setSelectedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportFormat, setExportFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('individual');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exportStatus, setExportStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const toggleItemSelection = (id)=>{\n        setSelectedItems((prev)=>prev.includes(id) ? prev.filter((item)=>item !== id) : [\n                ...prev,\n                id\n            ]);\n    };\n    const selectAll = ()=>{\n        setSelectedItems(visualizations.map((item)=>item.id));\n    };\n    const clearSelection = ()=>{\n        setSelectedItems([]);\n    };\n    const downloadFile = async (url, filename)=>{\n        try {\n            const response = await fetch(url);\n            const blob = await response.blob();\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = downloadUrl;\n            link.download = filename;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(downloadUrl);\n        } catch (error) {\n            console.error('Download failed:', error);\n            throw error;\n        }\n    };\n    const exportIndividual = async ()=>{\n        const selectedVisualizations = visualizations.filter((item)=>selectedItems.includes(item.id));\n        for (const item of selectedVisualizations){\n            const extension = item.type === 'video' ? 'mp4' : 'jpg';\n            const filename = `${item.text.replace(/\\s+/g, '_').slice(0, 30)}_${item.timestamp}.${extension}`;\n            await downloadFile(item.url, filename);\n        }\n    };\n    const exportAsZip = async ()=>{\n        // For demo purposes, we'll simulate zip creation\n        // In production, you'd use a library like JSZip\n        const selectedVisualizations = visualizations.filter((item)=>selectedItems.includes(item.id));\n        // Simulate zip creation delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        // Create a simple text file with links for demo\n        const zipContent = selectedVisualizations.map((item)=>`${item.text}: ${item.url}`).join('\\n');\n        const blob = new Blob([\n            zipContent\n        ], {\n            type: 'text/plain'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `visualizations_${Date.now()}.txt`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n    };\n    const exportAsPresentation = async ()=>{\n        // For demo purposes, create a simple HTML presentation\n        const selectedVisualizations = visualizations.filter((item)=>selectedItems.includes(item.id));\n        const presentationHTML = `\n<!DOCTYPE html>\n<html>\n<head>\n    <title>StudyVision - Apresentação</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }\n        .slide { page-break-after: always; margin-bottom: 40px; }\n        .slide h2 { color: #4f46e5; border-bottom: 2px solid #4f46e5; padding-bottom: 10px; }\n        .slide img, .slide video { max-width: 100%; height: auto; margin: 20px 0; }\n        .slide p { line-height: 1.6; margin: 15px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"slide\">\n        <h1>StudyVision - Estudo Imersivo</h1>\n        <p>Apresentação gerada automaticamente</p>\n        <p>Data: ${new Date().toLocaleDateString('pt-BR')}</p>\n    </div>\n    \n    ${selectedVisualizations.map((item, index)=>`\n    <div class=\"slide\">\n        <h2>Slide ${index + 1}: ${item.text}</h2>\n        ${item.type === 'video' ? `<video controls><source src=\"${item.url}\" type=\"video/mp4\"></video>` : `<img src=\"${item.url}\" alt=\"${item.text}\">`}\n        <p><strong>Conceito:</strong> ${item.text}</p>\n        <p><strong>Tipo:</strong> ${item.type === 'video' ? 'Vídeo' : 'Imagem'}</p>\n    </div>\n    `).join('')}\n</body>\n</html>`;\n        const blob = new Blob([\n            presentationHTML\n        ], {\n            type: 'text/html'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `apresentacao_${Date.now()}.html`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n    };\n    const exportAsPDF = async ()=>{\n        // For demo purposes, create a simple text file\n        // In production, you'd use a library like jsPDF\n        const selectedVisualizations = visualizations.filter((item)=>selectedItems.includes(item.id));\n        const pdfContent = `\nSTUDYVISION - RELATÓRIO DE ESTUDO\n================================\n\nData: ${new Date().toLocaleDateString('pt-BR')}\nVisualizações: ${selectedVisualizations.length}\n\nCONTEÚDO:\n${content.replace(/<[^>]*>/g, '').slice(0, 500)}...\n\nVISUALIZAÇÕES GERADAS:\n${selectedVisualizations.map((item, index)=>`\n${index + 1}. ${item.text}\n   Tipo: ${item.type === 'video' ? 'Vídeo' : 'Imagem'}\n   URL: ${item.url}\n   Data: ${new Date(item.timestamp).toLocaleString('pt-BR')}\n`).join('')}\n\nGerado por StudyVision AI\n`;\n        const blob = new Blob([\n            pdfContent\n        ], {\n            type: 'text/plain'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `relatorio_estudo_${Date.now()}.txt`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n    };\n    const handleExport = async ()=>{\n        if (selectedItems.length === 0) {\n            alert('Selecione pelo menos uma visualização para exportar.');\n            return;\n        }\n        setIsExporting(true);\n        setExportStatus('idle');\n        try {\n            switch(exportFormat){\n                case 'individual':\n                    await exportIndividual();\n                    break;\n                case 'zip':\n                    await exportAsZip();\n                    break;\n                case 'presentation':\n                    await exportAsPresentation();\n                    break;\n                case 'pdf':\n                    await exportAsPDF();\n                    break;\n            }\n            setExportStatus('success');\n            onExport?.(exportFormat, visualizations.filter((item)=>selectedItems.includes(item.id)));\n        } catch (error) {\n            console.error('Export failed:', error);\n            setExportStatus('error');\n        } finally{\n            setIsExporting(false);\n            setTimeout(()=>setExportStatus('idle'), 3000);\n        }\n    };\n    if (visualizations.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white border rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Nenhuma Visualiza\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Gere algumas visualiza\\xe7\\xf5es primeiro para poder export\\xe1-las.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border rounded-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Exportar Visualiza\\xe7\\xf5es\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            exportStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-green-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Exportado!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            exportStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Erro na exporta\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: selectAll,\n                                className: \"text-sm text-indigo-600 hover:text-indigo-700\",\n                                children: \"Selecionar Todos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"|\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearSelection,\n                                className: \"text-sm text-gray-600 hover:text-gray-700\",\n                                children: \"Limpar Sele\\xe7\\xe3o\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            selectedItems.length,\n                            \" de \",\n                            visualizations.length,\n                            \" selecionados\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-6 max-h-48 overflow-y-auto\",\n                children: visualizations.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${selectedItems.includes(item.id) ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'}`,\n                        onClick: ()=>toggleItemSelection(item.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: selectedItems.includes(item.id),\n                                onChange: ()=>toggleItemSelection(item.id),\n                                className: \"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: item.type === 'video' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                        children: item.text\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: new Date(item.timestamp).toLocaleString('pt-BR')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                        children: \"Formato de Exporta\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setExportFormat('individual'),\n                                className: `flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${exportFormat === 'individual' ? 'border-indigo-300 bg-indigo-50 text-indigo-700' : 'border-gray-200 hover:border-gray-300'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Individual\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setExportFormat('zip'),\n                                className: `flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${exportFormat === 'zip' ? 'border-indigo-300 bg-indigo-50 text-indigo-700' : 'border-gray-200 hover:border-gray-300'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"ZIP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setExportFormat('presentation'),\n                                className: `flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${exportFormat === 'presentation' ? 'border-indigo-300 bg-indigo-50 text-indigo-700' : 'border-gray-200 hover:border-gray-300'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Apresenta\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setExportFormat('pdf'),\n                                className: `flex items-center space-x-2 p-3 border rounded-lg text-left transition-colors ${exportFormat === 'pdf' ? 'border-indigo-300 bg-indigo-50 text-indigo-700' : 'border-gray-200 hover:border-gray-300'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Relat\\xf3rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleExport,\n                disabled: isExporting || selectedItems.length === 0,\n                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                children: [\n                    isExporting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileImage_FileText_FileVideo_Loader2_Package_Presentation_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: isExporting ? 'Exportando...' : `Exportar ${selectedItems.length} item${selectedItems.length !== 1 ? 's' : ''}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\export\\\\export-manager.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/export/export-manager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/reading/reading-mode.tsx":
/*!*************************************************!*\
  !*** ./src/components/reading/reading-mode.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReadingMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/skip-back.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Maximize2,Minimize2,Moon,Pause,Play,Settings,SkipBack,SkipForward,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ReadingMode({ content, visualizations, onClose }) {\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [lineHeight, setLineHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.6);\n    const [isAutoScroll, setIsAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrollSpeed, setScrollSpeed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [currentVisualization, setCurrentVisualization] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingProgress, setReadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReadingMode.useEffect\": ()=>{\n            if (isAutoScroll && contentRef.current) {\n                scrollIntervalRef.current = setInterval({\n                    \"ReadingMode.useEffect\": ()=>{\n                        if (contentRef.current) {\n                            const { scrollTop, scrollHeight, clientHeight } = contentRef.current;\n                            const maxScroll = scrollHeight - clientHeight;\n                            if (scrollTop < maxScroll) {\n                                contentRef.current.scrollTop += 1;\n                                setReadingProgress(scrollTop / maxScroll * 100);\n                            } else {\n                                setIsAutoScroll(false);\n                            }\n                        }\n                    }\n                }[\"ReadingMode.useEffect\"], 100 - scrollSpeed);\n            } else {\n                if (scrollIntervalRef.current) {\n                    clearInterval(scrollIntervalRef.current);\n                    scrollIntervalRef.current = null;\n                }\n            }\n            return ({\n                \"ReadingMode.useEffect\": ()=>{\n                    if (scrollIntervalRef.current) {\n                        clearInterval(scrollIntervalRef.current);\n                    }\n                }\n            })[\"ReadingMode.useEffect\"];\n        }\n    }[\"ReadingMode.useEffect\"], [\n        isAutoScroll,\n        scrollSpeed\n    ]);\n    // Handle scroll progress\n    const handleScroll = ()=>{\n        if (contentRef.current && !isAutoScroll) {\n            const { scrollTop, scrollHeight, clientHeight } = contentRef.current;\n            const maxScroll = scrollHeight - clientHeight;\n            setReadingProgress(scrollTop / maxScroll * 100);\n        }\n    };\n    // Fullscreen toggle\n    const toggleFullscreen = ()=>{\n        if (!document.fullscreenElement) {\n            document.documentElement.requestFullscreen();\n            setIsFullscreen(true);\n        } else {\n            document.exitFullscreen();\n            setIsFullscreen(false);\n        }\n    };\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReadingMode.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ReadingMode.useEffect.handleKeyPress\": (e)=>{\n                    if (e.key === 'Escape') {\n                        if (isFullscreen) {\n                            toggleFullscreen();\n                        } else {\n                            onClose?.();\n                        }\n                    } else if (e.key === 'f' || e.key === 'F') {\n                        toggleFullscreen();\n                    } else if (e.key === 'd' || e.key === 'D') {\n                        setIsDarkMode(!isDarkMode);\n                    } else if (e.key === ' ') {\n                        e.preventDefault();\n                        setIsAutoScroll(!isAutoScroll);\n                    }\n                }\n            }[\"ReadingMode.useEffect.handleKeyPress\"];\n            document.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ReadingMode.useEffect\": ()=>document.removeEventListener('keydown', handleKeyPress)\n            })[\"ReadingMode.useEffect\"];\n        }\n    }[\"ReadingMode.useEffect\"], [\n        isFullscreen,\n        isDarkMode,\n        isAutoScroll,\n        onClose\n    ]);\n    const nextVisualization = ()=>{\n        setCurrentVisualization((prev)=>prev < visualizations.length - 1 ? prev + 1 : 0);\n    };\n    const prevVisualization = ()=>{\n        setCurrentVisualization((prev)=>prev > 0 ? prev - 1 : visualizations.length - 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed inset-0 z-50 ${isDarkMode ? 'bg-gray-900' : 'bg-white'} transition-colors`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex items-center justify-between p-4 border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n                                children: \"Modo Leitura Imersivo\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-32 h-2 rounded-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2 bg-indigo-600 rounded-full transition-all\",\n                                            style: {\n                                                width: `${readingProgress}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                                        children: [\n                                            Math.round(readingProgress),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsAutoScroll(!isAutoScroll),\n                                className: `p-2 rounded-lg transition-colors ${isAutoScroll ? 'bg-indigo-600 text-white' : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}`,\n                                children: isAutoScroll ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 61\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsDarkMode(!isDarkMode),\n                                className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}`,\n                                children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 27\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: `p-2 rounded-lg transition-colors ${showSettings ? 'bg-indigo-600 text-white' : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleFullscreen,\n                                className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}`,\n                                children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 65\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `p-4 border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: `block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`,\n                                    children: \"Tamanho da Fonte\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"12\",\n                                    max: \"24\",\n                                    value: fontSize,\n                                    onChange: (e)=>setFontSize(Number(e.target.value)),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                                    children: [\n                                        fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: `block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`,\n                                    children: \"Espa\\xe7amento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"1.2\",\n                                    max: \"2.0\",\n                                    step: \"0.1\",\n                                    value: lineHeight,\n                                    onChange: (e)=>setLineHeight(Number(e.target.value)),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                                    children: [\n                                        lineHeight,\n                                        \"x\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: `block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`,\n                                    children: \"Velocidade Auto-scroll\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"90\",\n                                    value: scrollSpeed,\n                                    onChange: (e)=>setScrollSpeed(Number(e.target.value)),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                                    children: [\n                                        scrollSpeed,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: contentRef,\n                            onScroll: handleScroll,\n                            className: `flex-1 overflow-y-auto p-8 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`,\n                            style: {\n                                fontSize: `${fontSize}px`,\n                                lineHeight: lineHeight,\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: content\n                                },\n                                className: \"prose prose-lg max-w-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    visualizations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-96 border-l ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: `font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`,\n                                            children: \"Visualiza\\xe7\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: prevVisualization,\n                                                    className: `p-1 rounded transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                                                    children: [\n                                                        currentVisualization + 1,\n                                                        \"/\",\n                                                        visualizations.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: nextVisualization,\n                                                    className: `p-1 rounded transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Maximize2_Minimize2_Moon_Pause_Play_Settings_SkipBack_SkipForward_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                visualizations[currentVisualization] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video rounded-lg overflow-hidden bg-gray-200\",\n                                            children: visualizations[currentVisualization].type === 'video' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                controls: true,\n                                                className: \"w-full h-full object-cover\",\n                                                src: visualizations[currentVisualization].url\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: visualizations[currentVisualization].url,\n                                                alt: visualizations[currentVisualization].text,\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Conceito:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" \",\n                                                visualizations[currentVisualization].text\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute bottom-4 left-4 text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"ESC: Sair | F: Tela cheia | D: Modo escuro | ESPA\\xc7O: Auto-scroll\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\reading\\\\reading-mode.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/reading/reading-mode.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/realtime/realtime-status.tsx":
/*!*****************************************************!*\
  !*** ./src/components/realtime/realtime-status.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Eye,Users,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Eye,Users,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Eye,Users,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Eye,Users,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Eye,Users,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(ssr)/./src/hooks/useWebSocket.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction RealtimeStatus({ onTextSelected, onAnalysisUpdate, onGenerationUpdate }) {\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"RealtimeStatus.useState\": ()=>`session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    }[\"RealtimeStatus.useState\"]);\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addActivity = (activity)=>{\n        setRecentActivity((prev)=>[\n                activity,\n                ...prev.slice(0, 4)\n            ]);\n    };\n    const { connected, users, error, joinSession, leaveSession } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__.useWebSocket)({\n        onTextSelected: {\n            \"RealtimeStatus.useWebSocket\": (data)=>{\n                addActivity(`Usuário selecionou: \"${data.selectedText.slice(0, 30)}...\"`);\n                onTextSelected?.(data);\n            }\n        }[\"RealtimeStatus.useWebSocket\"],\n        onUserJoined: {\n            \"RealtimeStatus.useWebSocket\": (data)=>{\n                addActivity(`Usuário ${data.userId.slice(-6)} entrou na sessão`);\n            }\n        }[\"RealtimeStatus.useWebSocket\"],\n        onUserLeft: {\n            \"RealtimeStatus.useWebSocket\": (data)=>{\n                addActivity(`Usuário ${data.userId.slice(-6)} saiu da sessão`);\n            }\n        }[\"RealtimeStatus.useWebSocket\"],\n        onAnalysisStarted: {\n            \"RealtimeStatus.useWebSocket\": (data)=>{\n                addActivity(`Análise iniciada: ${data.text.slice(0, 30)}...`);\n                onAnalysisUpdate?.(data);\n            }\n        }[\"RealtimeStatus.useWebSocket\"],\n        onAnalysisCompleted: {\n            \"RealtimeStatus.useWebSocket\": (data)=>{\n                addActivity(`Análise concluída`);\n                onAnalysisUpdate?.(data);\n            }\n        }[\"RealtimeStatus.useWebSocket\"],\n        onGenerationStarted: {\n            \"RealtimeStatus.useWebSocket\": (data)=>{\n                addActivity(`Geração ${data.type} iniciada`);\n                onGenerationUpdate?.(data);\n            }\n        }[\"RealtimeStatus.useWebSocket\"],\n        onGenerationCompleted: {\n            \"RealtimeStatus.useWebSocket\": (data)=>{\n                addActivity(`Geração ${data.type} concluída`);\n                onGenerationUpdate?.(data);\n            }\n        }[\"RealtimeStatus.useWebSocket\"]\n    });\n    // Auto-join session on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeStatus.useEffect\": ()=>{\n            if (connected) {\n                joinSession(sessionId);\n            }\n            return ({\n                \"RealtimeStatus.useEffect\": ()=>{\n                    leaveSession();\n                }\n            })[\"RealtimeStatus.useEffect\"];\n        }\n    }[\"RealtimeStatus.useEffect\"], [\n        connected,\n        sessionId,\n        joinSession,\n        leaveSession\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border rounded-lg p-4 shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            connected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `text-sm font-medium ${connected ? 'text-green-600' : 'text-red-600'}`,\n                                children: connected ? 'Conectado' : 'Desconectado'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    users.length + 1,\n                                    \" usu\\xe1rio\",\n                                    users.length !== 0 ? 's' : ''\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600\",\n                children: [\n                    \"Erro de conex\\xe3o: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-2 bg-gray-50 rounded text-xs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-1 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Sess\\xe3o: \",\n                                sessionId.slice(-8)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            users.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                        children: \"Usu\\xe1rios Ativos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: users.map((userId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Usu\\xe1rio \",\n                                            userId.slice(-6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, userId, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this),\n            recentActivity.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Eye_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: \"Atividade Recente\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 max-h-32 overflow-y-auto\",\n                        children: recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-600 p-2 bg-gray-50 rounded\",\n                                style: {\n                                    opacity: 1 - index * 0.2\n                                },\n                                children: activity\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this),\n            !connected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800 font-medium mb-1\",\n                        children: \"Conectando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-600\",\n                        children: \"O sistema de tempo real permite colabora\\xe7\\xe3o e atualiza\\xe7\\xf5es instant\\xe2neas.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\realtime\\\\realtime-status.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/realtime/realtime-status.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/visualization/visualization-panel.tsx":
/*!**************************************************************!*\
  !*** ./src/components/visualization/visualization-panel.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisualizationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Image,Loader2,Play,Sparkles,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Image,Loader2,Play,Sparkles,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Image,Loader2,Play,Sparkles,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Image,Loader2,Play,Sparkles,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Image,Loader2,Play,Sparkles,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Image,Loader2,Play,Sparkles,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Image,Loader2,Play,Sparkles,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _hooks_useMediaGeneration__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useMediaGeneration */ \"(ssr)/./src/hooks/useMediaGeneration.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction VisualizationPanel({ selectedText, analysisData, onGenerate, onVisualizationGenerated }) {\n    const [generatedContent, setGeneratedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generationType, setGenerationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('image');\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { generateMedia, loading: isGenerating, error: generationError, progress } = (0,_hooks_useMediaGeneration__WEBPACK_IMPORTED_MODULE_2__.useMediaGeneration)();\n    // Generate content using real API\n    const generateContent = async (text, type)=>{\n        try {\n            // Use enhanced prompt from analysis if available\n            const prompt = analysisData?.visualizationPrompt?.enhancedPrompt || `Visualização científica educativa de: ${text}`;\n            const result = await generateMedia(prompt, type, {\n                style: 'educational',\n                duration: type === 'video' ? 5 : undefined,\n                dimensions: {\n                    width: 800,\n                    height: 600\n                }\n            });\n            const newContent = {\n                type,\n                url: result.url,\n                prompt: result.metadata.prompt,\n                timestamp: Date.now()\n            };\n            setGeneratedContent(newContent);\n            setHistory((prev)=>[\n                    newContent,\n                    ...prev.slice(0, 4)\n                ]); // Keep last 5\n            // Notify parent component about new visualization\n            onVisualizationGenerated?.(text, result.url, type);\n            onGenerate?.(text, type);\n        } catch (error) {\n            console.error('Content generation failed:', error);\n        }\n    };\n    const handleGenerate = ()=>{\n        if (selectedText.trim()) {\n            generateContent(selectedText, generationType);\n        }\n    };\n    const downloadContent = ()=>{\n        if (generatedContent) {\n            const link = document.createElement('a');\n            link.href = generatedContent.url;\n            link.download = `${selectedText.replace(/\\s+/g, '_')}_${generatedContent.type}.${generatedContent.type === 'video' ? 'mp4' : 'jpg'}`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Visualiza\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: generatedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{},\n                                    className: \"p-2 text-gray-600 hover:text-indigo-600 transition-colors\",\n                                    title: \"Reproduzir\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: downloadContent,\n                                    className: \"p-2 text-gray-600 hover:text-indigo-600 transition-colors\",\n                                    title: \"Baixar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"Tipo:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 rounded-lg p-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setGenerationType('image'),\n                                className: `flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${generationType === 'image' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Imagem\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setGenerationType('video'),\n                                className: `flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${generationType === 'video' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"V\\xeddeo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-lg min-h-[350px] bg-gray-50 relative overflow-hidden\",\n                children: isGenerating ? // Loading State with Progress\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-white/90\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-8 w-8 text-indigo-600 animate-spin mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-2\",\n                                children: [\n                                    \"Gerando \",\n                                    generationType === 'video' ? 'vídeo' : 'imagem',\n                                    \"...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mb-4\",\n                                children: [\n                                    '\"',\n                                    selectedText.slice(0, 50),\n                                    '...\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: `${progress}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    Math.round(progress),\n                                    \"% completo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this) : generationError ? // Error State\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-red-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-8 w-8 text-red-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-2\",\n                                children: \"Erro na gera\\xe7\\xe3o\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-500 mb-4\",\n                                children: generationError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>generateContent(selectedText, generationType),\n                                className: \"px-4 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors\",\n                                children: \"Tentar Novamente\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, this) : generatedContent ? // Generated Content\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        generatedContent.type === 'video' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            controls: true,\n                            className: \"w-full h-64 rounded-lg bg-black\",\n                            poster: \"https://via.placeholder.com/600x400/4f46e5/ffffff?text=Video+Preview\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                    src: generatedContent.url,\n                                    type: \"video/mp4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this),\n                                \"Seu navegador n\\xe3o suporta v\\xeddeo HTML5.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: generatedContent.url,\n                            alt: generatedContent.prompt,\n                            className: \"w-full h-64 object-cover rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-white rounded-lg border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-1\",\n                                    children: \"Prompt gerado:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-900\",\n                                    children: generatedContent.prompt\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this) : // Empty State\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-8 w-8 text-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: selectedText ? `Pronto para visualizar: \"${selectedText.slice(0, 30)}${selectedText.length > 30 ? '...' : ''}\"` : 'Destaque um termo no texto para gerar visualização'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGenerate,\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Gerar \",\n                                            generationType === 'video' ? 'Vídeo' : 'Imagem'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            analysisData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                        children: \"An\\xe1lise IA\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-800\",\n                                        children: \"Conceito:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-700 ml-2\",\n                                        children: analysisData.concept\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-800\",\n                                        children: \"Defini\\xe7\\xe3o:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-700 mt-1\",\n                                        children: analysisData.definition\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-800\",\n                                        children: \"N\\xedvel:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-700 ml-2 capitalize\",\n                                        children: analysisData.difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            analysisData.relatedConcepts?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-800\",\n                                        children: \"Conceitos relacionados:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                        children: analysisData.relatedConcepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded\",\n                                                children: concept\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, this),\n            history.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                        children: \"Hist\\xf3rico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2 overflow-x-auto pb-2\",\n                        children: history.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setGeneratedContent(item),\n                                className: \"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-transparent hover:border-indigo-300 transition-colors\",\n                                children: item.type === 'video' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Image_Loader2_Play_Sparkles_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: item.url,\n                                    alt: `Histórico ${index + 1}`,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.timestamp, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\canva-saas\\\\src\\\\components\\\\visualization\\\\visualization-panel.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy92aXN1YWxpemF0aW9uL3Zpc3VhbGl6YXRpb24tcGFuZWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQzZEO0FBQ3pDO0FBZ0JqRCxTQUFTVSxtQkFBbUIsRUFBRUMsWUFBWSxFQUFFQyxZQUFZLEVBQUVDLFVBQVUsRUFBRUMsd0JBQXdCLEVBQTJCO0lBQ3RJLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR2hCLCtDQUFRQSxDQUEwQjtJQUNsRixNQUFNLENBQUNpQixnQkFBZ0JDLGtCQUFrQixHQUFHbEIsK0NBQVFBLENBQW9CO0lBQ3hFLE1BQU0sQ0FBQ21CLFNBQVNDLFdBQVcsR0FBR3BCLCtDQUFRQSxDQUFxQixFQUFFO0lBRTdELE1BQU0sRUFDSnFCLGFBQWEsRUFDYkMsU0FBU0MsWUFBWSxFQUNyQkMsT0FBT0MsZUFBZSxFQUN0QkMsUUFBUSxFQUNULEdBQUdqQiw2RUFBa0JBO0lBRXRCLGtDQUFrQztJQUNsQyxNQUFNa0Isa0JBQWtCLE9BQU9DLE1BQWNDO1FBQzNDLElBQUk7WUFDRixpREFBaUQ7WUFDakQsTUFBTUMsU0FBU2xCLGNBQWNtQixxQkFBcUJDLGtCQUNwQyxDQUFDLHNDQUFzQyxFQUFFSixNQUFNO1lBRTdELE1BQU1LLFNBQVMsTUFBTVosY0FBY1MsUUFBUUQsTUFBTTtnQkFDL0NLLE9BQU87Z0JBQ1BDLFVBQVVOLFNBQVMsVUFBVSxJQUFJTztnQkFDakNDLFlBQVk7b0JBQUVDLE9BQU87b0JBQUtDLFFBQVE7Z0JBQUk7WUFDeEM7WUFFQSxNQUFNQyxhQUErQjtnQkFDbkNYO2dCQUNBWSxLQUFLUixPQUFPUSxHQUFHO2dCQUNmWCxRQUFRRyxPQUFPUyxRQUFRLENBQUNaLE1BQU07Z0JBQzlCYSxXQUFXQyxLQUFLQyxHQUFHO1lBQ3JCO1lBRUE3QixvQkFBb0J3QjtZQUNwQnBCLFdBQVcwQixDQUFBQSxPQUFRO29CQUFDTjt1QkFBZU0sS0FBS0MsS0FBSyxDQUFDLEdBQUc7aUJBQUcsR0FBRyxjQUFjO1lBRXJFLGtEQUFrRDtZQUNsRGpDLDJCQUEyQmMsTUFBTUssT0FBT1EsR0FBRyxFQUFFWjtZQUM3Q2hCLGFBQWFlLE1BQU1DO1FBQ3JCLEVBQUUsT0FBT0wsT0FBTztZQUNkd0IsUUFBUXhCLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzlDO0lBQ0Y7SUFFQSxNQUFNeUIsaUJBQWlCO1FBQ3JCLElBQUl0QyxhQUFhdUMsSUFBSSxJQUFJO1lBQ3ZCdkIsZ0JBQWdCaEIsY0FBY007UUFDaEM7SUFDRjtJQUVBLE1BQU1rQyxrQkFBa0I7UUFDdEIsSUFBSXBDLGtCQUFrQjtZQUNwQixNQUFNcUMsT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1lBQ3BDRixLQUFLRyxJQUFJLEdBQUd4QyxpQkFBaUIwQixHQUFHO1lBQ2hDVyxLQUFLSSxRQUFRLEdBQUcsR0FBRzdDLGFBQWE4QyxPQUFPLENBQUMsUUFBUSxLQUFLLENBQUMsRUFBRTFDLGlCQUFpQmMsSUFBSSxDQUFDLENBQUMsRUFBRWQsaUJBQWlCYyxJQUFJLEtBQUssVUFBVSxRQUFRLE9BQU87WUFDcEl3QixTQUFTSyxJQUFJLENBQUNDLFdBQVcsQ0FBQ1A7WUFDMUJBLEtBQUtRLEtBQUs7WUFDVlAsU0FBU0ssSUFBSSxDQUFDRyxXQUFXLENBQUNUO1FBQzVCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ1U7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQXNDOzs7Ozs7a0NBQ3BELDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWmhELGtDQUNDOzs4Q0FDRSw4REFBQ2tEO29DQUNDQyxTQUFTLEtBQStCO29DQUN4Q0gsV0FBVTtvQ0FDVkksT0FBTTs4Q0FFTiw0RUFBQ2xFLGtJQUFJQTt3Q0FBQzhELFdBQVU7Ozs7Ozs7Ozs7OzhDQUVsQiw4REFBQ0U7b0NBQ0NDLFNBQVNmO29DQUNUWSxXQUFVO29DQUNWSSxPQUFNOzhDQUVOLDRFQUFDakUsa0lBQVFBO3dDQUFDNkQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVE5Qiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDSzt3QkFBS0wsV0FBVTtrQ0FBd0I7Ozs7OztrQ0FDeEMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQ0NDLFNBQVMsSUFBTWhELGtCQUFrQjtnQ0FDakM2QyxXQUFXLENBQUMsd0VBQXdFLEVBQ2xGOUMsbUJBQW1CLFVBQ2YsdUNBQ0EscUNBQ0o7O2tEQUVGLDhEQUFDWCxrSUFBU0E7d0NBQUN5RCxXQUFVOzs7Ozs7a0RBQ3JCLDhEQUFDSztrREFBSzs7Ozs7Ozs7Ozs7OzBDQUVSLDhEQUFDSDtnQ0FDQ0MsU0FBUyxJQUFNaEQsa0JBQWtCO2dDQUNqQzZDLFdBQVcsQ0FBQyx3RUFBd0UsRUFDbEY5QyxtQkFBbUIsVUFDZix1Q0FDQSxxQ0FDSjs7a0RBRUYsOERBQUNWLGtJQUFLQTt3Q0FBQ3dELFdBQVU7Ozs7OztrREFDakIsOERBQUNLO2tEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVosOERBQUNOO2dCQUFJQyxXQUFVOzBCQUNaeEMsZUFDQyw4QkFBOEI7OEJBQzlCLDhEQUFDdUM7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzNELGtJQUFPQTtnQ0FBQzJELFdBQVU7Ozs7OzswQ0FDbkIsOERBQUNNO2dDQUFFTixXQUFVOztvQ0FBcUI7b0NBQVM5QyxtQkFBbUIsVUFBVSxVQUFVO29DQUFTOzs7Ozs7OzBDQUMzRiw4REFBQ29EO2dDQUFFTixXQUFVOztvQ0FBNkI7b0NBQUVwRCxhQUFhb0MsS0FBSyxDQUFDLEdBQUc7b0NBQUk7Ozs7Ozs7MENBR3RFLDhEQUFDZTtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQ0NDLFdBQVU7b0NBQ1Y3QixPQUFPO3dDQUFFSSxPQUFPLEdBQUdaLFNBQVMsQ0FBQyxDQUFDO29DQUFDOzs7Ozs7Ozs7OzswQ0FHbkMsOERBQUMyQztnQ0FBRU4sV0FBVTs7b0NBQXlCTyxLQUFLQyxLQUFLLENBQUM3QztvQ0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MkJBRzdERCxrQkFDRixjQUFjOzhCQUNkLDhEQUFDcUM7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3ZELGtJQUFXQTtnQ0FBQ3VELFdBQVU7Ozs7OzswQ0FDdkIsOERBQUNNO2dDQUFFTixXQUFVOzBDQUFvQjs7Ozs7OzBDQUNqQyw4REFBQ007Z0NBQUVOLFdBQVU7MENBQTZCdEM7Ozs7OzswQ0FDMUMsOERBQUN3QztnQ0FDQ0MsU0FBUyxJQUFNdkMsZ0JBQWdCaEIsY0FBY007Z0NBQzdDOEMsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7OzsyQkFLSGhELG1CQUNGLG9CQUFvQjs4QkFDcEIsOERBQUMrQztvQkFBSUMsV0FBVTs7d0JBQ1poRCxpQkFBaUJjLElBQUksS0FBSyx3QkFDekIsOERBQUMyQzs0QkFDQ0MsUUFBUTs0QkFDUlYsV0FBVTs0QkFDVlcsUUFBTzs7OENBRVAsOERBQUNDO29DQUFPQyxLQUFLN0QsaUJBQWlCMEIsR0FBRztvQ0FBRVosTUFBSzs7Ozs7O2dDQUFjOzs7Ozs7aURBSXhELDhEQUFDZ0Q7NEJBQ0NELEtBQUs3RCxpQkFBaUIwQixHQUFHOzRCQUN6QnFDLEtBQUsvRCxpQkFBaUJlLE1BQU07NEJBQzVCaUMsV0FBVTs7Ozs7O3NDQUlkLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNNO29DQUFFTixXQUFVOzhDQUE2Qjs7Ozs7OzhDQUMxQyw4REFBQ007b0NBQUVOLFdBQVU7OENBQXlCaEQsaUJBQWlCZSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7OzsyQkFJakUsY0FBYzs4QkFDZCw4REFBQ2dDO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDNUQsa0lBQVFBO29DQUFDNEQsV0FBVTs7Ozs7Ozs7Ozs7MENBRXRCLDhEQUFDTTtnQ0FBRU4sV0FBVTswQ0FDVnBELGVBQ0csQ0FBQyx5QkFBeUIsRUFBRUEsYUFBYW9DLEtBQUssQ0FBQyxHQUFHLE1BQU1wQyxhQUFhb0UsTUFBTSxHQUFHLEtBQUssUUFBUSxHQUFHLENBQUMsQ0FBQyxHQUNoRzs7Ozs7OzRCQUdMcEUsOEJBQ0MsOERBQUNzRDtnQ0FDQ0MsU0FBU2pCO2dDQUNUYyxXQUFVOztrREFFViw4REFBQzVELGtJQUFRQTt3Q0FBQzRELFdBQVU7Ozs7OztrREFDcEIsOERBQUNLOzs0Q0FBSzs0Q0FBT25ELG1CQUFtQixVQUFVLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUy9ETCw4QkFDQyw4REFBQ2tEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2lCO3dCQUFHakIsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDTTt3Q0FBS0wsV0FBVTtrREFBNEI7Ozs7OztrREFDNUMsOERBQUNLO3dDQUFLTCxXQUFVO2tEQUFzQm5ELGFBQWFxRSxPQUFPOzs7Ozs7Ozs7Ozs7MENBRTVELDhEQUFDbkI7O2tEQUNDLDhEQUFDTTt3Q0FBS0wsV0FBVTtrREFBNEI7Ozs7OztrREFDNUMsOERBQUNNO3dDQUFFTixXQUFVO2tEQUFzQm5ELGFBQWFzRSxVQUFVOzs7Ozs7Ozs7Ozs7MENBRTVELDhEQUFDcEI7O2tEQUNDLDhEQUFDTTt3Q0FBS0wsV0FBVTtrREFBNEI7Ozs7OztrREFDNUMsOERBQUNLO3dDQUFLTCxXQUFVO2tEQUFpQ25ELGFBQWF1RSxVQUFVOzs7Ozs7Ozs7Ozs7NEJBRXpFdkUsYUFBYXdFLGVBQWUsRUFBRUwsU0FBUyxtQkFDdEMsOERBQUNqQjs7a0RBQ0MsOERBQUNNO3dDQUFLTCxXQUFVO2tEQUE0Qjs7Ozs7O2tEQUM1Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1puRCxhQUFhd0UsZUFBZSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0osU0FBaUJLLHNCQUNsRCw4REFBQ2xCO2dEQUFpQkwsV0FBVTswREFDekJrQjsrQ0FEUUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFZeEJuRSxRQUFRNEQsTUFBTSxHQUFHLG1CQUNoQiw4REFBQ2pCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2lCO3dCQUFHakIsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaNUMsUUFBUWtFLEdBQUcsQ0FBQyxDQUFDRSxNQUFNRCxzQkFDbEIsOERBQUNyQjtnQ0FFQ0MsU0FBUyxJQUFNbEQsb0JBQW9CdUU7Z0NBQ25DeEIsV0FBVTswQ0FFVHdCLEtBQUsxRCxJQUFJLEtBQUssd0JBQ2IsOERBQUNpQztvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ3hELGtJQUFLQTt3Q0FBQ3dELFdBQVU7Ozs7Ozs7Ozs7eURBR25CLDhEQUFDYztvQ0FDQ0QsS0FBS1csS0FBSzlDLEdBQUc7b0NBQ2JxQyxLQUFLLENBQUMsVUFBVSxFQUFFUSxRQUFRLEdBQUc7b0NBQzdCdkIsV0FBVTs7Ozs7OytCQVpUd0IsS0FBSzVDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFzQm5DIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERvdWdsYXNcXERlc2t0b3BcXGNhbnZhLXNhYXNcXHNyY1xcY29tcG9uZW50c1xcdmlzdWFsaXphdGlvblxcdmlzdWFsaXphdGlvbi1wYW5lbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUGxheSwgRG93bmxvYWQsIFNwYXJrbGVzLCBMb2FkZXIyLCBJbWFnZSBhcyBJbWFnZUljb24sIFZpZGVvLCBBbGVydENpcmNsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyB1c2VNZWRpYUdlbmVyYXRpb24gfSBmcm9tICdAL2hvb2tzL3VzZU1lZGlhR2VuZXJhdGlvbic7XG5cbmludGVyZmFjZSBWaXN1YWxpemF0aW9uUGFuZWxQcm9wcyB7XG4gIHNlbGVjdGVkVGV4dDogc3RyaW5nO1xuICBhbmFseXNpc0RhdGE/OiBhbnk7XG4gIG9uR2VuZXJhdGU/OiAodGV4dDogc3RyaW5nLCB0eXBlOiAnaW1hZ2UnIHwgJ3ZpZGVvJykgPT4gdm9pZDtcbiAgb25WaXN1YWxpemF0aW9uR2VuZXJhdGVkPzogKHRleHQ6IHN0cmluZywgdXJsOiBzdHJpbmcsIHR5cGU6ICdpbWFnZScgfCAndmlkZW8nKSA9PiB2b2lkO1xufVxuXG5pbnRlcmZhY2UgR2VuZXJhdGVkQ29udGVudCB7XG4gIHR5cGU6ICdpbWFnZScgfCAndmlkZW8nO1xuICB1cmw6IHN0cmluZztcbiAgcHJvbXB0OiBzdHJpbmc7XG4gIHRpbWVzdGFtcDogbnVtYmVyO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBWaXN1YWxpemF0aW9uUGFuZWwoeyBzZWxlY3RlZFRleHQsIGFuYWx5c2lzRGF0YSwgb25HZW5lcmF0ZSwgb25WaXN1YWxpemF0aW9uR2VuZXJhdGVkIH06IFZpc3VhbGl6YXRpb25QYW5lbFByb3BzKSB7XG4gIGNvbnN0IFtnZW5lcmF0ZWRDb250ZW50LCBzZXRHZW5lcmF0ZWRDb250ZW50XSA9IHVzZVN0YXRlPEdlbmVyYXRlZENvbnRlbnQgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2dlbmVyYXRpb25UeXBlLCBzZXRHZW5lcmF0aW9uVHlwZV0gPSB1c2VTdGF0ZTwnaW1hZ2UnIHwgJ3ZpZGVvJz4oJ2ltYWdlJyk7XG4gIGNvbnN0IFtoaXN0b3J5LCBzZXRIaXN0b3J5XSA9IHVzZVN0YXRlPEdlbmVyYXRlZENvbnRlbnRbXT4oW10pO1xuXG4gIGNvbnN0IHtcbiAgICBnZW5lcmF0ZU1lZGlhLFxuICAgIGxvYWRpbmc6IGlzR2VuZXJhdGluZyxcbiAgICBlcnJvcjogZ2VuZXJhdGlvbkVycm9yLFxuICAgIHByb2dyZXNzXG4gIH0gPSB1c2VNZWRpYUdlbmVyYXRpb24oKTtcblxuICAvLyBHZW5lcmF0ZSBjb250ZW50IHVzaW5nIHJlYWwgQVBJXG4gIGNvbnN0IGdlbmVyYXRlQ29udGVudCA9IGFzeW5jICh0ZXh0OiBzdHJpbmcsIHR5cGU6ICdpbWFnZScgfCAndmlkZW8nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFVzZSBlbmhhbmNlZCBwcm9tcHQgZnJvbSBhbmFseXNpcyBpZiBhdmFpbGFibGVcbiAgICAgIGNvbnN0IHByb21wdCA9IGFuYWx5c2lzRGF0YT8udmlzdWFsaXphdGlvblByb21wdD8uZW5oYW5jZWRQcm9tcHQgfHxcbiAgICAgICAgICAgICAgICAgICAgYFZpc3VhbGl6YcOnw6NvIGNpZW50w61maWNhIGVkdWNhdGl2YSBkZTogJHt0ZXh0fWA7XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGdlbmVyYXRlTWVkaWEocHJvbXB0LCB0eXBlLCB7XG4gICAgICAgIHN0eWxlOiAnZWR1Y2F0aW9uYWwnLFxuICAgICAgICBkdXJhdGlvbjogdHlwZSA9PT0gJ3ZpZGVvJyA/IDUgOiB1bmRlZmluZWQsXG4gICAgICAgIGRpbWVuc2lvbnM6IHsgd2lkdGg6IDgwMCwgaGVpZ2h0OiA2MDAgfVxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IG5ld0NvbnRlbnQ6IEdlbmVyYXRlZENvbnRlbnQgPSB7XG4gICAgICAgIHR5cGUsXG4gICAgICAgIHVybDogcmVzdWx0LnVybCxcbiAgICAgICAgcHJvbXB0OiByZXN1bHQubWV0YWRhdGEucHJvbXB0LFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICAgIH07XG5cbiAgICAgIHNldEdlbmVyYXRlZENvbnRlbnQobmV3Q29udGVudCk7XG4gICAgICBzZXRIaXN0b3J5KHByZXYgPT4gW25ld0NvbnRlbnQsIC4uLnByZXYuc2xpY2UoMCwgNCldKTsgLy8gS2VlcCBsYXN0IDVcblxuICAgICAgLy8gTm90aWZ5IHBhcmVudCBjb21wb25lbnQgYWJvdXQgbmV3IHZpc3VhbGl6YXRpb25cbiAgICAgIG9uVmlzdWFsaXphdGlvbkdlbmVyYXRlZD8uKHRleHQsIHJlc3VsdC51cmwsIHR5cGUpO1xuICAgICAgb25HZW5lcmF0ZT8uKHRleHQsIHR5cGUpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdDb250ZW50IGdlbmVyYXRpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlR2VuZXJhdGUgPSAoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkVGV4dC50cmltKCkpIHtcbiAgICAgIGdlbmVyYXRlQ29udGVudChzZWxlY3RlZFRleHQsIGdlbmVyYXRpb25UeXBlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZG93bmxvYWRDb250ZW50ID0gKCkgPT4ge1xuICAgIGlmIChnZW5lcmF0ZWRDb250ZW50KSB7XG4gICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xuICAgICAgbGluay5ocmVmID0gZ2VuZXJhdGVkQ29udGVudC51cmw7XG4gICAgICBsaW5rLmRvd25sb2FkID0gYCR7c2VsZWN0ZWRUZXh0LnJlcGxhY2UoL1xccysvZywgJ18nKX1fJHtnZW5lcmF0ZWRDb250ZW50LnR5cGV9LiR7Z2VuZXJhdGVkQ29udGVudC50eXBlID09PSAndmlkZW8nID8gJ21wNCcgOiAnanBnJ31gO1xuICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICAgIGxpbmsuY2xpY2soKTtcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1sZyBwLTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlZpc3VhbGl6YcOnw6NvPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICB7Z2VuZXJhdGVkQ29udGVudCAmJiAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHsvKiBQbGF5IGZ1bmN0aW9uYWxpdHkgKi99fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtaW5kaWdvLTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJSZXByb2R1emlyXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtkb3dubG9hZENvbnRlbnR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1pbmRpZ28tNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIkJhaXhhclwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENvbnRlbnQgVHlwZSBTZWxlY3RvciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTRcIj5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+VGlwbzo8L3NwYW4+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIHAtMVwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEdlbmVyYXRpb25UeXBlKCdpbWFnZScpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHB4LTMgcHktMSByb3VuZGVkIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgZ2VuZXJhdGlvblR5cGUgPT09ICdpbWFnZScgXG4gICAgICAgICAgICAgICAgPyAnYmctd2hpdGUgdGV4dC1pbmRpZ28tNjAwIHNoYWRvdy1zbScgXG4gICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEltYWdlSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDxzcGFuPkltYWdlbTwvc3Bhbj5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRHZW5lcmF0aW9uVHlwZSgndmlkZW8nKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBweC0zIHB5LTEgcm91bmRlZCB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgIGdlbmVyYXRpb25UeXBlID09PSAndmlkZW8nIFxuICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtaW5kaWdvLTYwMCBzaGFkb3ctc20nIFxuICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxWaWRlbyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDxzcGFuPlbDrWRlbzwvc3Bhbj5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXIgcm91bmRlZC1sZyBtaW4taC1bMzUwcHhdIGJnLWdyYXktNTAgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIHtpc0dlbmVyYXRpbmcgPyAoXG4gICAgICAgICAgLy8gTG9hZGluZyBTdGF0ZSB3aXRoIFByb2dyZXNzXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXdoaXRlLzkwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1heC13LXhzXCI+XG4gICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1pbmRpZ28tNjAwIGFuaW1hdGUtc3BpbiBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTJcIj5HZXJhbmRvIHtnZW5lcmF0aW9uVHlwZSA9PT0gJ3ZpZGVvJyA/ICd2w61kZW8nIDogJ2ltYWdlbSd9Li4uPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbWItNFwiPlwie3NlbGVjdGVkVGV4dC5zbGljZSgwLCA1MCl9Li4uXCI8L3A+XG5cbiAgICAgICAgICAgICAgey8qIFByb2dyZXNzIEJhciAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctaW5kaWdvLTYwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7cHJvZ3Jlc3N9JWAgfX1cbiAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj57TWF0aC5yb3VuZChwcm9ncmVzcyl9JSBjb21wbGV0bzwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogZ2VuZXJhdGlvbkVycm9yID8gKFxuICAgICAgICAgIC8vIEVycm9yIFN0YXRlXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXJlZC01MFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYXgtdy14c1wiPlxuICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LXJlZC02MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIG1iLTJcIj5FcnJvIG5hIGdlcmHDp8OjbzwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC01MDAgbWItNFwiPntnZW5lcmF0aW9uRXJyb3J9PC9wPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZ2VuZXJhdGVDb250ZW50KHNlbGVjdGVkVGV4dCwgZ2VuZXJhdGlvblR5cGUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1yZWQtNjAwIHRleHQtd2hpdGUgdGV4dC1zbSByb3VuZGVkIGhvdmVyOmJnLXJlZC03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgVGVudGFyIE5vdmFtZW50ZVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogZ2VuZXJhdGVkQ29udGVudCA/IChcbiAgICAgICAgICAvLyBHZW5lcmF0ZWQgQ29udGVudFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICB7Z2VuZXJhdGVkQ29udGVudC50eXBlID09PSAndmlkZW8nID8gKFxuICAgICAgICAgICAgICA8dmlkZW8gXG4gICAgICAgICAgICAgICAgY29udHJvbHMgXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtNjQgcm91bmRlZC1sZyBiZy1ibGFja1wiXG4gICAgICAgICAgICAgICAgcG9zdGVyPVwiaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzYwMHg0MDAvNGY0NmU1L2ZmZmZmZj90ZXh0PVZpZGVvK1ByZXZpZXdcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHNvdXJjZSBzcmM9e2dlbmVyYXRlZENvbnRlbnQudXJsfSB0eXBlPVwidmlkZW8vbXA0XCIgLz5cbiAgICAgICAgICAgICAgICBTZXUgbmF2ZWdhZG9yIG7Do28gc3Vwb3J0YSB2w61kZW8gSFRNTDUuXG4gICAgICAgICAgICAgIDwvdmlkZW8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8aW1nIFxuICAgICAgICAgICAgICAgIHNyYz17Z2VuZXJhdGVkQ29udGVudC51cmx9IFxuICAgICAgICAgICAgICAgIGFsdD17Z2VuZXJhdGVkQ29udGVudC5wcm9tcHR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtNjQgb2JqZWN0LWNvdmVyIHJvdW5kZWQtbGdcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHAtMyBiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlclwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMVwiPlByb21wdCBnZXJhZG86PC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj57Z2VuZXJhdGVkQ29udGVudC5wcm9tcHR9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgLy8gRW1wdHkgU3RhdGVcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctaW5kaWdvLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1pbmRpZ28tNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFRleHQgXG4gICAgICAgICAgICAgICAgICA/IGBQcm9udG8gcGFyYSB2aXN1YWxpemFyOiBcIiR7c2VsZWN0ZWRUZXh0LnNsaWNlKDAsIDMwKX0ke3NlbGVjdGVkVGV4dC5sZW5ndGggPiAzMCA/ICcuLi4nIDogJyd9XCJgXG4gICAgICAgICAgICAgICAgICA6ICdEZXN0YXF1ZSB1bSB0ZXJtbyBubyB0ZXh0byBwYXJhIGdlcmFyIHZpc3VhbGl6YcOnw6NvJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRUZXh0ICYmIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVHZW5lcmF0ZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgYmctaW5kaWdvLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctaW5kaWdvLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBteC1hdXRvXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5HZXJhciB7Z2VuZXJhdGlvblR5cGUgPT09ICd2aWRlbycgPyAnVsOtZGVvJyA6ICdJbWFnZW0nfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBBbmFseXNpcyBSZXN1bHRzICovfVxuICAgICAge2FuYWx5c2lzRGF0YSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwLTQgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtOTAwIG1iLTJcIj5BbsOhbGlzZSBJQTwvaDQ+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTgwMFwiPkNvbmNlaXRvOjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTcwMCBtbC0yXCI+e2FuYWx5c2lzRGF0YS5jb25jZXB0fTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTgwMFwiPkRlZmluacOnw6NvOjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTcwMCBtdC0xXCI+e2FuYWx5c2lzRGF0YS5kZWZpbml0aW9ufTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTgwMFwiPk7DrXZlbDo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS03MDAgbWwtMiBjYXBpdGFsaXplXCI+e2FuYWx5c2lzRGF0YS5kaWZmaWN1bHR5fTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAge2FuYWx5c2lzRGF0YS5yZWxhdGVkQ29uY2VwdHM/Lmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDBcIj5Db25jZWl0b3MgcmVsYWNpb25hZG9zOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIHthbmFseXNpc0RhdGEucmVsYXRlZENvbmNlcHRzLm1hcCgoY29uY2VwdDogc3RyaW5nLCBpbmRleDogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ibHVlLTIwMCB0ZXh0LWJsdWUtODAwIHRleHQteHMgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjb25jZXB0fVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBIaXN0b3J5ICovfVxuICAgICAge2hpc3RvcnkubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPkhpc3TDs3JpY288L2g0PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTIgb3ZlcmZsb3cteC1hdXRvIHBiLTJcIj5cbiAgICAgICAgICAgIHtoaXN0b3J5Lm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGtleT17aXRlbS50aW1lc3RhbXB9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0R2VuZXJhdGVkQ29udGVudChpdGVtKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIHctMTYgaC0xNiByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBib3JkZXItMiBib3JkZXItdHJhbnNwYXJlbnQgaG92ZXI6Ym9yZGVyLWluZGlnby0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2l0ZW0udHlwZSA9PT0gJ3ZpZGVvJyA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBiZy1ncmF5LTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8VmlkZW8gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgIHNyYz17aXRlbS51cmx9XG4gICAgICAgICAgICAgICAgICAgIGFsdD17YEhpc3TDs3JpY28gJHtpbmRleCArIDF9YH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJQbGF5IiwiRG93bmxvYWQiLCJTcGFya2xlcyIsIkxvYWRlcjIiLCJJbWFnZSIsIkltYWdlSWNvbiIsIlZpZGVvIiwiQWxlcnRDaXJjbGUiLCJ1c2VNZWRpYUdlbmVyYXRpb24iLCJWaXN1YWxpemF0aW9uUGFuZWwiLCJzZWxlY3RlZFRleHQiLCJhbmFseXNpc0RhdGEiLCJvbkdlbmVyYXRlIiwib25WaXN1YWxpemF0aW9uR2VuZXJhdGVkIiwiZ2VuZXJhdGVkQ29udGVudCIsInNldEdlbmVyYXRlZENvbnRlbnQiLCJnZW5lcmF0aW9uVHlwZSIsInNldEdlbmVyYXRpb25UeXBlIiwiaGlzdG9yeSIsInNldEhpc3RvcnkiLCJnZW5lcmF0ZU1lZGlhIiwibG9hZGluZyIsImlzR2VuZXJhdGluZyIsImVycm9yIiwiZ2VuZXJhdGlvbkVycm9yIiwicHJvZ3Jlc3MiLCJnZW5lcmF0ZUNvbnRlbnQiLCJ0ZXh0IiwidHlwZSIsInByb21wdCIsInZpc3VhbGl6YXRpb25Qcm9tcHQiLCJlbmhhbmNlZFByb21wdCIsInJlc3VsdCIsInN0eWxlIiwiZHVyYXRpb24iLCJ1bmRlZmluZWQiLCJkaW1lbnNpb25zIiwid2lkdGgiLCJoZWlnaHQiLCJuZXdDb250ZW50IiwidXJsIiwibWV0YWRhdGEiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwicHJldiIsInNsaWNlIiwiY29uc29sZSIsImhhbmRsZUdlbmVyYXRlIiwidHJpbSIsImRvd25sb2FkQ29udGVudCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJyZXBsYWNlIiwiYm9keSIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwic3BhbiIsInAiLCJNYXRoIiwicm91bmQiLCJ2aWRlbyIsImNvbnRyb2xzIiwicG9zdGVyIiwic291cmNlIiwic3JjIiwiaW1nIiwiYWx0IiwibGVuZ3RoIiwiaDQiLCJjb25jZXB0IiwiZGVmaW5pdGlvbiIsImRpZmZpY3VsdHkiLCJyZWxhdGVkQ29uY2VwdHMiLCJtYXAiLCJpbmRleCIsIml0ZW0iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/visualization/visualization-panel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAnalysis.ts":
/*!**********************************!*\
  !*** ./src/hooks/useAnalysis.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAnalysis: () => (/* binding */ useAnalysis)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ useAnalysis auto */ \n\nfunction useAnalysis() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        data: null,\n        loading: false,\n        error: null\n    });\n    const analyzeText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysis.useCallback[analyzeText]\": async (selectedText, context, visualizationType = 'image')=>{\n            setState({\n                \"useAnalysis.useCallback[analyzeText]\": (prev)=>({\n                        ...prev,\n                        loading: true,\n                        error: null\n                    })\n            }[\"useAnalysis.useCallback[analyzeText]\"]);\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post('/api/analyze', {\n                    selectedText,\n                    context,\n                    visualizationType\n                });\n                if (response.data.success) {\n                    setState({\n                        data: response.data.data,\n                        loading: false,\n                        error: null\n                    });\n                    return response.data.data;\n                } else {\n                    throw new Error(response.data.error || 'Analysis failed');\n                }\n            } catch (error) {\n                const errorMessage = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].isAxiosError(error) ? error.response?.data?.error || error.message : 'Failed to analyze text';\n                setState({\n                    data: null,\n                    loading: false,\n                    error: errorMessage\n                });\n                throw new Error(errorMessage);\n            }\n        }\n    }[\"useAnalysis.useCallback[analyzeText]\"], []);\n    const clearAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysis.useCallback[clearAnalysis]\": ()=>{\n            setState({\n                data: null,\n                loading: false,\n                error: null\n            });\n        }\n    }[\"useAnalysis.useCallback[clearAnalysis]\"], []);\n    const retry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysis.useCallback[retry]\": async ()=>{\n            if (state.data?.visualizationPrompt.originalText) {\n                await analyzeText(state.data.visualizationPrompt.originalText, state.data.visualizationPrompt.scientificContext, state.data.visualizationPrompt.visualizationType);\n            }\n        }\n    }[\"useAnalysis.useCallback[retry]\"], [\n        state.data,\n        analyzeText\n    ]);\n    return {\n        ...state,\n        analyzeText,\n        clearAnalysis,\n        retry\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAnalysis.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useMediaGeneration.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useMediaGeneration.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaGeneration: () => (/* binding */ useMediaGeneration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ useMediaGeneration auto */ \n\nfunction useMediaGeneration() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        data: null,\n        loading: false,\n        error: null,\n        progress: 0\n    });\n    const generateMedia = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaGeneration.useCallback[generateMedia]\": async (prompt, type, options)=>{\n            setState({\n                \"useMediaGeneration.useCallback[generateMedia]\": (prev)=>({\n                        ...prev,\n                        loading: true,\n                        error: null,\n                        progress: 0\n                    })\n            }[\"useMediaGeneration.useCallback[generateMedia]\"]);\n            // Simulate progress updates\n            const progressInterval = setInterval({\n                \"useMediaGeneration.useCallback[generateMedia].progressInterval\": ()=>{\n                    setState({\n                        \"useMediaGeneration.useCallback[generateMedia].progressInterval\": (prev)=>({\n                                ...prev,\n                                progress: Math.min(prev.progress + Math.random() * 20, 90)\n                            })\n                    }[\"useMediaGeneration.useCallback[generateMedia].progressInterval\"]);\n                }\n            }[\"useMediaGeneration.useCallback[generateMedia].progressInterval\"], 500);\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post('/api/generate-media', {\n                    prompt,\n                    type,\n                    style: options?.style || 'educational',\n                    duration: options?.duration || (type === 'video' ? 5 : undefined),\n                    dimensions: options?.dimensions || {\n                        width: 800,\n                        height: 600\n                    }\n                });\n                clearInterval(progressInterval);\n                if (response.data.success) {\n                    setState({\n                        data: {\n                            url: response.data.data.url,\n                            metadata: response.data.data.metadata\n                        },\n                        loading: false,\n                        error: null,\n                        progress: 100\n                    });\n                    return response.data.data;\n                } else {\n                    throw new Error(response.data.error || 'Media generation failed');\n                }\n            } catch (error) {\n                clearInterval(progressInterval);\n                const errorMessage = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].isAxiosError(error) ? error.response?.data?.error || error.message : 'Failed to generate media';\n                setState({\n                    data: null,\n                    loading: false,\n                    error: errorMessage,\n                    progress: 0\n                });\n                throw new Error(errorMessage);\n            }\n        }\n    }[\"useMediaGeneration.useCallback[generateMedia]\"], []);\n    const clearMedia = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaGeneration.useCallback[clearMedia]\": ()=>{\n            setState({\n                data: null,\n                loading: false,\n                error: null,\n                progress: 0\n            });\n        }\n    }[\"useMediaGeneration.useCallback[clearMedia]\"], []);\n    const retry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMediaGeneration.useCallback[retry]\": async ()=>{\n            if (state.data?.metadata) {\n                const { prompt, type } = state.data.metadata;\n                await generateMedia(prompt, type);\n            }\n        }\n    }[\"useMediaGeneration.useCallback[retry]\"], [\n        state.data,\n        generateMedia\n    ]);\n    return {\n        ...state,\n        generateMedia,\n        clearMedia,\n        retry\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useMediaGeneration.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useWebSocket.ts":
/*!***********************************!*\
  !*** ./src/hooks/useWebSocket.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ useWebSocket auto */ \n\nfunction useWebSocket(events) {\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        connected: false,\n        sessionId: null,\n        users: [],\n        error: null\n    });\n    // Initialize socket connection\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocket.useEffect\": ()=>{\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(\"http://localhost:3000\" || 0, {\n                transports: [\n                    'websocket',\n                    'polling'\n                ],\n                autoConnect: true\n            });\n            socketRef.current = socket;\n            // Connection events\n            socket.on('connect', {\n                \"useWebSocket.useEffect\": ()=>{\n                    setState({\n                        \"useWebSocket.useEffect\": (prev)=>({\n                                ...prev,\n                                connected: true,\n                                error: null\n                            })\n                    }[\"useWebSocket.useEffect\"]);\n                    console.log('WebSocket connected');\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('disconnect', {\n                \"useWebSocket.useEffect\": ()=>{\n                    setState({\n                        \"useWebSocket.useEffect\": (prev)=>({\n                                ...prev,\n                                connected: false\n                            })\n                    }[\"useWebSocket.useEffect\"]);\n                    console.log('WebSocket disconnected');\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('connect_error', {\n                \"useWebSocket.useEffect\": (error)=>{\n                    setState({\n                        \"useWebSocket.useEffect\": (prev)=>({\n                                ...prev,\n                                error: error.message\n                            })\n                    }[\"useWebSocket.useEffect\"]);\n                    console.error('WebSocket connection error:', error);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            // Session events\n            socket.on('session-joined', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    setState({\n                        \"useWebSocket.useEffect\": (prev)=>({\n                                ...prev,\n                                sessionId: data.sessionId\n                            })\n                    }[\"useWebSocket.useEffect\"]);\n                    console.log('Joined session:', data.sessionId);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('user-joined', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    setState({\n                        \"useWebSocket.useEffect\": (prev)=>({\n                                ...prev,\n                                users: [\n                                    ...prev.users.filter({\n                                        \"useWebSocket.useEffect\": (id)=>id !== data.userId\n                                    }[\"useWebSocket.useEffect\"]),\n                                    data.userId\n                                ]\n                            })\n                    }[\"useWebSocket.useEffect\"]);\n                    events?.onUserJoined?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('user-left', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    setState({\n                        \"useWebSocket.useEffect\": (prev)=>({\n                                ...prev,\n                                users: prev.users.filter({\n                                    \"useWebSocket.useEffect\": (id)=>id !== data.userId\n                                }[\"useWebSocket.useEffect\"])\n                            })\n                    }[\"useWebSocket.useEffect\"]);\n                    events?.onUserLeft?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            // Text selection events\n            socket.on('text-selected', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onTextSelected?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            // Analysis events\n            socket.on('analysis-started', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onAnalysisStarted?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('analysis-progress', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onAnalysisProgress?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('analysis-completed', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onAnalysisCompleted?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('analysis-error', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onAnalysisError?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            // Generation events\n            socket.on('generation-started', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onGenerationStarted?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('generation-progress', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onGenerationProgress?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('generation-completed', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onGenerationCompleted?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('generation-error', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    events?.onGenerationError?.(data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            return ({\n                \"useWebSocket.useEffect\": ()=>{\n                    socket.disconnect();\n                }\n            })[\"useWebSocket.useEffect\"];\n        }\n    }[\"useWebSocket.useEffect\"], [\n        events\n    ]);\n    // Join a session\n    const joinSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[joinSession]\": (sessionId)=>{\n            if (socketRef.current) {\n                socketRef.current.emit('join-session', sessionId);\n            }\n        }\n    }[\"useWebSocket.useCallback[joinSession]\"], []);\n    // Leave current session\n    const leaveSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[leaveSession]\": ()=>{\n            if (socketRef.current && state.sessionId) {\n                socketRef.current.emit('leave-session', state.sessionId);\n                setState({\n                    \"useWebSocket.useCallback[leaveSession]\": (prev)=>({\n                            ...prev,\n                            sessionId: null,\n                            users: []\n                        })\n                }[\"useWebSocket.useCallback[leaveSession]\"]);\n            }\n        }\n    }[\"useWebSocket.useCallback[leaveSession]\"], [\n        state.sessionId\n    ]);\n    // Emit text selection\n    const emitTextSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[emitTextSelection]\": (selectedText)=>{\n            if (socketRef.current && state.sessionId) {\n                socketRef.current.emit('text-selection', {\n                    sessionId: state.sessionId,\n                    selectedText,\n                    timestamp: Date.now()\n                });\n            }\n        }\n    }[\"useWebSocket.useCallback[emitTextSelection]\"], [\n        state.sessionId\n    ]);\n    // Emit analysis request\n    const emitAnalysisRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[emitAnalysisRequest]\": (text, type)=>{\n            if (socketRef.current && state.sessionId) {\n                socketRef.current.emit('analysis-request', {\n                    sessionId: state.sessionId,\n                    text,\n                    type\n                });\n            }\n        }\n    }[\"useWebSocket.useCallback[emitAnalysisRequest]\"], [\n        state.sessionId\n    ]);\n    // Emit generation request\n    const emitGenerationRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[emitGenerationRequest]\": (prompt, type)=>{\n            if (socketRef.current && state.sessionId) {\n                socketRef.current.emit('generation-request', {\n                    sessionId: state.sessionId,\n                    prompt,\n                    type\n                });\n            }\n        }\n    }[\"useWebSocket.useCallback[emitGenerationRequest]\"], [\n        state.sessionId\n    ]);\n    return {\n        ...state,\n        joinSession,\n        leaveSession,\n        emitTextSelection,\n        emitAnalysisRequest,\n        emitGenerationRequest\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useWebSocket.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@popperjs","vendor-chunks/axios","vendor-chunks/@tiptap","vendor-chunks/lucide-react","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/socket.io-parser","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/engine.io-parser","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/w3c-keyname","vendor-chunks/rope-sequence","vendor-chunks/prosemirror-view","vendor-chunks/prosemirror-transform","vendor-chunks/prosemirror-state","vendor-chunks/prosemirror-schema-list","vendor-chunks/prosemirror-model","vendor-chunks/prosemirror-keymap","vendor-chunks/prosemirror-history","vendor-chunks/prosemirror-gapcursor","vendor-chunks/prosemirror-dropcursor","vendor-chunks/prosemirror-commands","vendor-chunks/orderedmap","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/tippy.js","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5Ccanva-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();