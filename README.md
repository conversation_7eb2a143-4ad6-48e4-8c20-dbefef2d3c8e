# StudyVision AI - SaaS de Estudo Imersivo

Uma plataforma educacional que transforma texto em visualizações imersivas usando IA avançada.

## 🚀 Funcionalidades

- **Editor de Texto Inteligente**: Editor baseado em TipTap com destaque de termos científicos
- **Análise por IA**: Integração com GPT-4 para análise de conceitos científicos
- **Visualizações Geradas**: Criação de imagens e vídeos educativos em tempo real
- **Interface Moderna**: Design responsivo com Tailwind CSS
- **Tempo Real**: WebSockets para atualizações instantâneas

## 🛠️ Stack Tecnológica

- **Frontend**: React + Next.js 15 + TypeScript
- **Styling**: Tailwind CSS v4
- **Editor**: TipTap (ProseMirror)
- **IA**: OpenAI GPT-4
- **Mídia**: VEO3 (vídeos) + Imagen 4 (imagens)
- **Real-time**: WebSockets
- **Icons**: Lucide React

## 📋 Pré-requisitos

- Node.js 18+
- npm ou yarn
- Chave da API OpenAI

## 🔧 Instalação

1. Clone o repositório:
```bash
git clone <repository-url>
cd canva-saas
```

2. Instale as dependências:
```bash
npm install
```

3. Configure as variáveis de ambiente:
```bash
cp .env.example .env.local
```

4. Adicione sua chave da API OpenAI no arquivo `.env.local`:
```env
OPENAI_API_KEY=sk-your-openai-api-key-here
```

5. Execute o servidor de desenvolvimento:
```bash
npm run dev
```

6. Abra [http://localhost:3000](http://localhost:3000) no seu navegador.

## 🎯 Como Usar

1. **Digite ou cole texto científico** no editor
2. **Destaque termos importantes** que deseja visualizar
3. **Clique em "Imagem" ou "Vídeo"** na toolbar flutuante
4. **Aguarde a análise IA** processar o conceito
5. **Visualize o resultado** no painel lateral
6. **Baixe ou compartilhe** as visualizações geradas

## 📁 Estrutura do Projeto

```
src/
├── app/                    # App Router (Next.js 13+)
│   ├── api/               # API Routes
│   │   └── analyze/       # Endpoint de análise IA
│   ├── globals.css        # Estilos globais
│   ├── layout.tsx         # Layout principal
│   └── page.tsx           # Página inicial
├── components/            # Componentes React
│   ├── editor/           # Editor de texto
│   ├── visualization/    # Painel de visualização
│   └── ui/               # Componentes UI base
├── hooks/                # Custom hooks
│   └── useAnalysis.ts    # Hook para análise IA
└── lib/                  # Utilitários
    ├── openai.ts         # Integração OpenAI
    └── utils.ts          # Funções utilitárias
```

## 🔑 Configuração da API

### OpenAI
1. Crie uma conta em [OpenAI Platform](https://platform.openai.com)
2. Gere uma API key em [API Keys](https://platform.openai.com/api-keys)
3. Adicione a chave no arquivo `.env.local`

### VEO3 (Futuro)
- Aguardando disponibilidade pública da API

### Imagen 4 (Futuro)
- Integração planejada com Google Cloud

## 🚀 Deploy

### Vercel (Recomendado)
1. Conecte seu repositório ao Vercel
2. Configure as variáveis de ambiente no dashboard
3. Deploy automático a cada push

### Outras Plataformas
- Netlify
- Railway
- AWS Amplify

## 📊 Roadmap

- [x] Setup do projeto base
- [x] Editor de texto com TipTap
- [x] Integração OpenAI GPT-4
- [ ] Integração VEO3 para vídeos
- [ ] Sistema de WebSockets
- [ ] Painel modo leitura
- [ ] Sistema de exportação
- [ ] Autenticação de usuários
- [ ] Planos de assinatura
- [ ] Dashboard analytics

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Suporte

- Email: <EMAIL>
- Discord: [StudyVision Community](https://discord.gg/studyvision)
- Documentação: [docs.studyvision.ai](https://docs.studyvision.ai)
