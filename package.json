{"name": "canva-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:ws": "node server.js", "build": "next build", "start": "NODE_ENV=production node server.js", "start:next": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "clean": "rmdir /s /q .next 2>nul || echo \"Clean completed\"", "analyze": "ANALYZE=true npm run build", "test": "echo \"No tests specified\" && exit 0", "deploy:vercel": "vercel --prod", "deploy:docker": "docker build -t studyvision-ai . && docker run -p 3000:3000 studyvision-ai"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "axios": "^1.10.0", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "next": "15.3.3", "openai": "^5.5.1", "react": "^19.0.0", "react-dom": "^19.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/socket.io-client": "^3.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "socket.io": "^4.8.1", "tailwindcss": "^4", "typescript": "^5"}}