'use client';

import { useState, useEffect } from 'react';
import { Play, Download, <PERSON>rkles, Loader2, Image as ImageIcon, Video } from 'lucide-react';

interface VisualizationPanelProps {
  selectedText: string;
  onGenerate?: (text: string, type: 'image' | 'video') => void;
}

interface GeneratedContent {
  type: 'image' | 'video';
  url: string;
  prompt: string;
  timestamp: number;
}

export default function VisualizationPanel({ selectedText, onGenerate }: VisualizationPanelProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [generationType, setGenerationType] = useState<'image' | 'video'>('image');
  const [history, setHistory] = useState<GeneratedContent[]>([]);

  // Simulate content generation
  const generateContent = async (text: string, type: 'image' | 'video') => {
    setIsGenerating(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock generated content
    const mockContent: GeneratedContent = {
      type,
      url: type === 'video' 
        ? 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
        : 'https://via.placeholder.com/600x400/4f46e5/ffffff?text=' + encodeURIComponent(text),
      prompt: `Visualização científica de: ${text}`,
      timestamp: Date.now()
    };
    
    setGeneratedContent(mockContent);
    setHistory(prev => [mockContent, ...prev.slice(0, 4)]); // Keep last 5
    setIsGenerating(false);
    
    onGenerate?.(text, type);
  };

  const handleGenerate = () => {
    if (selectedText.trim()) {
      generateContent(selectedText, generationType);
    }
  };

  const downloadContent = () => {
    if (generatedContent) {
      const link = document.createElement('a');
      link.href = generatedContent.url;
      link.download = `${selectedText.replace(/\s+/g, '_')}_${generatedContent.type}.${generatedContent.type === 'video' ? 'mp4' : 'jpg'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
        <div className="flex items-center space-x-2">
          {generatedContent && (
            <>
              <button 
                onClick={() => {/* Play functionality */}}
                className="p-2 text-gray-600 hover:text-indigo-600 transition-colors"
                title="Reproduzir"
              >
                <Play className="h-5 w-5" />
              </button>
              <button 
                onClick={downloadContent}
                className="p-2 text-gray-600 hover:text-indigo-600 transition-colors"
                title="Baixar"
              >
                <Download className="h-5 w-5" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Content Type Selector */}
      <div className="flex items-center space-x-2 mb-4">
        <span className="text-sm text-gray-600">Tipo:</span>
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setGenerationType('image')}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${
              generationType === 'image' 
                ? 'bg-white text-indigo-600 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <ImageIcon className="h-4 w-4" />
            <span>Imagem</span>
          </button>
          <button
            onClick={() => setGenerationType('video')}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${
              generationType === 'video' 
                ? 'bg-white text-indigo-600 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Video className="h-4 w-4" />
            <span>Vídeo</span>
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="border rounded-lg min-h-[350px] bg-gray-50 relative overflow-hidden">
        {isGenerating ? (
          // Loading State
          <div className="absolute inset-0 flex items-center justify-center bg-white/90">
            <div className="text-center">
              <Loader2 className="h-8 w-8 text-indigo-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Gerando visualização...</p>
              <p className="text-sm text-gray-500">"{selectedText}"</p>
            </div>
          </div>
        ) : generatedContent ? (
          // Generated Content
          <div className="p-4">
            {generatedContent.type === 'video' ? (
              <video 
                controls 
                className="w-full h-64 rounded-lg bg-black"
                poster="https://via.placeholder.com/600x400/4f46e5/ffffff?text=Video+Preview"
              >
                <source src={generatedContent.url} type="video/mp4" />
                Seu navegador não suporta vídeo HTML5.
              </video>
            ) : (
              <img 
                src={generatedContent.url} 
                alt={generatedContent.prompt}
                className="w-full h-64 object-cover rounded-lg"
              />
            )}
            
            <div className="mt-4 p-3 bg-white rounded-lg border">
              <p className="text-sm text-gray-600 mb-1">Prompt gerado:</p>
              <p className="text-sm text-gray-900">{generatedContent.prompt}</p>
            </div>
          </div>
        ) : (
          // Empty State
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-8 w-8 text-indigo-600" />
              </div>
              <p className="text-gray-600 mb-4">
                {selectedText 
                  ? `Pronto para visualizar: "${selectedText.slice(0, 30)}${selectedText.length > 30 ? '...' : ''}"`
                  : 'Destaque um termo no texto para gerar visualização'
                }
              </p>
              {selectedText && (
                <button
                  onClick={handleGenerate}
                  className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto"
                >
                  <Sparkles className="h-4 w-4" />
                  <span>Gerar {generationType === 'video' ? 'Vídeo' : 'Imagem'}</span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* History */}
      {history.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Histórico</h4>
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {history.map((item, index) => (
              <button
                key={item.timestamp}
                onClick={() => setGeneratedContent(item)}
                className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-transparent hover:border-indigo-300 transition-colors"
              >
                {item.type === 'video' ? (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <Video className="h-6 w-6 text-gray-500" />
                  </div>
                ) : (
                  <img 
                    src={item.url} 
                    alt={`Histórico ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
