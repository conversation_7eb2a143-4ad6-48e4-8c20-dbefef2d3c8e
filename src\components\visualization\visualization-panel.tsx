'use client';

import { useState, useEffect } from 'react';
import { Play, Download, <PERSON>rkles, Loader2, Image as ImageIcon, Video, AlertCircle } from 'lucide-react';
import { useMediaGeneration } from '@/hooks/useMediaGeneration';

interface VisualizationPanelProps {
  selectedText: string;
  analysisData?: any;
  onGenerate?: (text: string, type: 'image' | 'video') => void;
  onVisualizationGenerated?: (text: string, url: string, type: 'image' | 'video') => void;
}

interface GeneratedContent {
  type: 'image' | 'video';
  url: string;
  prompt: string;
  timestamp: number;
}

export default function VisualizationPanel({ selectedText, analysisData, onGenerate, onVisualizationGenerated }: VisualizationPanelProps) {
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [generationType, setGenerationType] = useState<'image' | 'video'>('image');
  const [history, setHistory] = useState<GeneratedContent[]>([]);

  const {
    generateMedia,
    loading: isGenerating,
    error: generationError,
    progress
  } = useMediaGeneration();

  // Generate content using real API
  const generateContent = async (text: string, type: 'image' | 'video') => {
    try {
      // Use enhanced prompt from analysis if available
      const prompt = analysisData?.visualizationPrompt?.enhancedPrompt ||
                    `Visualização científica educativa de: ${text}`;

      const result = await generateMedia(prompt, type, {
        style: 'educational',
        duration: type === 'video' ? 5 : undefined,
        dimensions: { width: 800, height: 600 }
      });

      const newContent: GeneratedContent = {
        type,
        url: result.url,
        prompt: result.metadata.prompt,
        timestamp: result.metadata.timestamp ? new Date(result.metadata.timestamp).getTime() : Date.now()
      };

      setGeneratedContent(newContent);
      setHistory(prev => [newContent, ...prev.slice(0, 4)]); // Keep last 5

      // Notify parent component about new visualization
      onVisualizationGenerated?.(text, result.url, type);
      onGenerate?.(text, type);
    } catch (error) {
      console.error('Content generation failed:', error);
    }
  };

  const handleGenerate = () => {
    if (selectedText.trim()) {
      generateContent(selectedText, generationType);
    }
  };

  const downloadContent = () => {
    if (generatedContent) {
      const link = document.createElement('a');
      link.href = generatedContent.url;
      link.download = `${selectedText.replace(/\s+/g, '_')}_${generatedContent.type}.${generatedContent.type === 'video' ? 'mp4' : 'jpg'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
        <div className="flex items-center space-x-2">
          {generatedContent && (
            <>
              <button 
                onClick={() => {/* Play functionality */}}
                className="p-2 text-gray-600 hover:text-indigo-600 transition-colors"
                title="Reproduzir"
              >
                <Play className="h-5 w-5" />
              </button>
              <button 
                onClick={downloadContent}
                className="p-2 text-gray-600 hover:text-indigo-600 transition-colors"
                title="Baixar"
              >
                <Download className="h-5 w-5" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Content Type Selector */}
      <div className="flex items-center space-x-2 mb-4">
        <span className="text-sm text-gray-600">Tipo:</span>
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setGenerationType('image')}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${
              generationType === 'image' 
                ? 'bg-white text-indigo-600 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <ImageIcon className="h-4 w-4" />
            <span>Imagem</span>
          </button>
          <button
            onClick={() => setGenerationType('video')}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${
              generationType === 'video' 
                ? 'bg-white text-indigo-600 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Video className="h-4 w-4" />
            <span>Vídeo</span>
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="border rounded-lg min-h-[350px] bg-gray-50 relative overflow-hidden">
        {isGenerating ? (
          // Loading State with Progress
          <div className="absolute inset-0 flex items-center justify-center bg-white/90">
            <div className="text-center max-w-xs">
              <Loader2 className="h-8 w-8 text-indigo-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Gerando {generationType === 'video' ? 'vídeo' : 'imagem'}...</p>
              <p className="text-sm text-gray-500 mb-4">"{selectedText.slice(0, 50)}..."</p>

              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div
                  className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500">{Math.round(progress)}% completo</p>
            </div>
          </div>
        ) : generationError ? (
          // Error State
          <div className="absolute inset-0 flex items-center justify-center bg-red-50">
            <div className="text-center max-w-xs">
              <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
              <p className="text-red-600 mb-2">Erro na geração</p>
              <p className="text-sm text-red-500 mb-4">{generationError}</p>
              <button
                onClick={() => generateContent(selectedText, generationType)}
                className="px-4 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
              >
                Tentar Novamente
              </button>
            </div>
          </div>
        ) : generatedContent ? (
          // Generated Content
          <div className="p-4">
            {generatedContent.type === 'video' ? (
              <video 
                controls 
                className="w-full h-64 rounded-lg bg-black"
                poster="https://via.placeholder.com/600x400/4f46e5/ffffff?text=Video+Preview"
              >
                <source src={generatedContent.url} type="video/mp4" />
                Seu navegador não suporta vídeo HTML5.
              </video>
            ) : (
              <img 
                src={generatedContent.url} 
                alt={generatedContent.prompt}
                className="w-full h-64 object-cover rounded-lg"
              />
            )}
            
            <div className="mt-4 p-3 bg-white rounded-lg border">
              <p className="text-sm text-gray-600 mb-1">Prompt gerado:</p>
              <p className="text-sm text-gray-900">{generatedContent.prompt}</p>
            </div>
          </div>
        ) : (
          // Empty State
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-8 w-8 text-indigo-600" />
              </div>
              <p className="text-gray-600 mb-4">
                {selectedText 
                  ? `Pronto para visualizar: "${selectedText.slice(0, 30)}${selectedText.length > 30 ? '...' : ''}"`
                  : 'Destaque um termo no texto para gerar visualização'
                }
              </p>
              {selectedText && (
                <button
                  onClick={handleGenerate}
                  className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto"
                >
                  <Sparkles className="h-4 w-4" />
                  <span>Gerar {generationType === 'video' ? 'Vídeo' : 'Imagem'}</span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Analysis Results */}
      {analysisData && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Análise IA</h4>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium text-blue-800">Conceito:</span>
              <span className="text-blue-700 ml-2">{analysisData.concept}</span>
            </div>
            <div>
              <span className="font-medium text-blue-800">Definição:</span>
              <p className="text-blue-700 mt-1">{analysisData.definition}</p>
            </div>
            <div>
              <span className="font-medium text-blue-800">Nível:</span>
              <span className="text-blue-700 ml-2 capitalize">{analysisData.difficulty}</span>
            </div>
            {analysisData.relatedConcepts?.length > 0 && (
              <div>
                <span className="font-medium text-blue-800">Conceitos relacionados:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {analysisData.relatedConcepts.map((concept: string, index: number) => (
                    <span key={index} className="px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded">
                      {concept}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* History */}
      {history.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Histórico</h4>
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {history.map((item, index) => (
              <button
                key={item.timestamp}
                onClick={() => setGeneratedContent(item)}
                className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-transparent hover:border-indigo-300 transition-colors"
              >
                {item.type === 'video' ? (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <Video className="h-6 w-6 text-gray-500" />
                  </div>
                ) : (
                  <img
                    src={item.url}
                    alt={`Histórico ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
