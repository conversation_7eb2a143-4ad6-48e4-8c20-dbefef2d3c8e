"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-dropcursor";
exports.ids = ["vendor-chunks/prosemirror-dropcursor"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-dropcursor/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/prosemirror-dropcursor/dist/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dropCursor: () => (/* binding */ dropCursor)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n\n\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        let editorDOM = this.editorView.dom, editorRect = editorDOM.getBoundingClientRect();\n        let scaleX = editorRect.width / editorDOM.offsetWidth, scaleY = editorRect.height / editorDOM.offsetHeight;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    let halfWidth = (this.width / 2) * scaleY;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - halfWidth, bottom: top + halfWidth };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            let halfWidth = (this.width / 2) * scaleX;\n            rect = { left: coords.left - halfWidth, right: coords.left + halfWidth, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            let parentScaleX = rect.width / parent.offsetWidth, parentScaleY = rect.height / parent.offsetHeight;\n            parentLeft = rect.left - parent.scrollLeft * parentScaleX;\n            parentTop = rect.top - parent.scrollTop * parentScaleY;\n        }\n        this.element.style.left = (rect.left - parentLeft) / scaleX + \"px\";\n        this.element.style.top = (rect.top - parentTop) / scaleY + \"px\";\n        this.element.style.width = (rect.right - rect.left) / scaleX + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) / scaleY + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\"\n            ? disableDropCursor(this.editorView, pos, event)\n            : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.dropPoint)(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (!this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-dropcursor/dist/index.js\n");

/***/ })

};
;