"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-history";
exports.ids = ["vendor-chunks/prosemirror-history"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-history/dist/index.js":
/*!********************************************************!*\
  !*** ./node_modules/prosemirror-history/dist/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeHistory: () => (/* binding */ closeHistory),\n/* harmony export */   history: () => (/* binding */ history),\n/* harmony export */   redo: () => (/* binding */ redo),\n/* harmony export */   redoDepth: () => (/* binding */ redoDepth),\n/* harmony export */   redoNoScroll: () => (/* binding */ redoNoScroll),\n/* harmony export */   undo: () => (/* binding */ undo),\n/* harmony export */   undoDepth: () => (/* binding */ undoDepth),\n/* harmony export */   undoNoScroll: () => (/* binding */ undoNoScroll)\n/* harmony export */ });\n/* harmony import */ var rope_sequence__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rope-sequence */ \"(ssr)/./node_modules/rope-sequence/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n// ProseMirror's history isn't simply a way to roll back to a previous\n// state, because ProseMirror supports applying changes without adding\n// them to the history (for example during collaboration).\n//\n// To this end, each 'Branch' (one for the undo history and one for\n// the redo history) keeps an array of 'Items', which can optionally\n// hold a step (an actual undoable change), and always hold a position\n// map (which is needed to move changes below them to apply to the\n// current document).\n//\n// An item that has both a step and a selection bookmark is the start\n// of an 'event' — a group of changes that will be undone or redone at\n// once. (It stores only the bookmark, since that way we don't have to\n// provide a document until the selection is actually applied, which\n// is useful when compressing.)\n// Used to schedule history compression\nconst max_empty_items = 500;\nclass Branch {\n    constructor(items, eventCount) {\n        this.items = items;\n        this.eventCount = eventCount;\n    }\n    // Pop the latest event off the branch's history and apply it\n    // to a document transform.\n    popEvent(state, preserveItems) {\n        if (this.eventCount == 0)\n            return null;\n        let end = this.items.length;\n        for (;; end--) {\n            let next = this.items.get(end - 1);\n            if (next.selection) {\n                --end;\n                break;\n            }\n        }\n        let remap, mapFrom;\n        if (preserveItems) {\n            remap = this.remapping(end, this.items.length);\n            mapFrom = remap.maps.length;\n        }\n        let transform = state.tr;\n        let selection, remaining;\n        let addAfter = [], addBefore = [];\n        this.items.forEach((item, i) => {\n            if (!item.step) {\n                if (!remap) {\n                    remap = this.remapping(end, i + 1);\n                    mapFrom = remap.maps.length;\n                }\n                mapFrom--;\n                addBefore.push(item);\n                return;\n            }\n            if (remap) {\n                addBefore.push(new Item(item.map));\n                let step = item.step.map(remap.slice(mapFrom)), map;\n                if (step && transform.maybeStep(step).doc) {\n                    map = transform.mapping.maps[transform.mapping.maps.length - 1];\n                    addAfter.push(new Item(map, undefined, undefined, addAfter.length + addBefore.length));\n                }\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n            }\n            else {\n                transform.maybeStep(item.step);\n            }\n            if (item.selection) {\n                selection = remap ? item.selection.map(remap.slice(mapFrom)) : item.selection;\n                remaining = new Branch(this.items.slice(0, end).append(addBefore.reverse().concat(addAfter)), this.eventCount - 1);\n                return false;\n            }\n        }, this.items.length, 0);\n        return { remaining: remaining, transform, selection: selection };\n    }\n    // Create a new branch with the given transform added.\n    addTransform(transform, selection, histOptions, preserveItems) {\n        let newItems = [], eventCount = this.eventCount;\n        let oldItems = this.items, lastItem = !preserveItems && oldItems.length ? oldItems.get(oldItems.length - 1) : null;\n        for (let i = 0; i < transform.steps.length; i++) {\n            let step = transform.steps[i].invert(transform.docs[i]);\n            let item = new Item(transform.mapping.maps[i], step, selection), merged;\n            if (merged = lastItem && lastItem.merge(item)) {\n                item = merged;\n                if (i)\n                    newItems.pop();\n                else\n                    oldItems = oldItems.slice(0, oldItems.length - 1);\n            }\n            newItems.push(item);\n            if (selection) {\n                eventCount++;\n                selection = undefined;\n            }\n            if (!preserveItems)\n                lastItem = item;\n        }\n        let overflow = eventCount - histOptions.depth;\n        if (overflow > DEPTH_OVERFLOW) {\n            oldItems = cutOffEvents(oldItems, overflow);\n            eventCount -= overflow;\n        }\n        return new Branch(oldItems.append(newItems), eventCount);\n    }\n    remapping(from, to) {\n        let maps = new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.Mapping;\n        this.items.forEach((item, i) => {\n            let mirrorPos = item.mirrorOffset != null && i - item.mirrorOffset >= from\n                ? maps.maps.length - item.mirrorOffset : undefined;\n            maps.appendMap(item.map, mirrorPos);\n        }, from, to);\n        return maps;\n    }\n    addMaps(array) {\n        if (this.eventCount == 0)\n            return this;\n        return new Branch(this.items.append(array.map(map => new Item(map))), this.eventCount);\n    }\n    // When the collab module receives remote changes, the history has\n    // to know about those, so that it can adjust the steps that were\n    // rebased on top of the remote changes, and include the position\n    // maps for the remote changes in its array of items.\n    rebased(rebasedTransform, rebasedCount) {\n        if (!this.eventCount)\n            return this;\n        let rebasedItems = [], start = Math.max(0, this.items.length - rebasedCount);\n        let mapping = rebasedTransform.mapping;\n        let newUntil = rebasedTransform.steps.length;\n        let eventCount = this.eventCount;\n        this.items.forEach(item => { if (item.selection)\n            eventCount--; }, start);\n        let iRebased = rebasedCount;\n        this.items.forEach(item => {\n            let pos = mapping.getMirror(--iRebased);\n            if (pos == null)\n                return;\n            newUntil = Math.min(newUntil, pos);\n            let map = mapping.maps[pos];\n            if (item.step) {\n                let step = rebasedTransform.steps[pos].invert(rebasedTransform.docs[pos]);\n                let selection = item.selection && item.selection.map(mapping.slice(iRebased + 1, pos));\n                if (selection)\n                    eventCount++;\n                rebasedItems.push(new Item(map, step, selection));\n            }\n            else {\n                rebasedItems.push(new Item(map));\n            }\n        }, start);\n        let newMaps = [];\n        for (let i = rebasedCount; i < newUntil; i++)\n            newMaps.push(new Item(mapping.maps[i]));\n        let items = this.items.slice(0, start).append(newMaps).append(rebasedItems);\n        let branch = new Branch(items, eventCount);\n        if (branch.emptyItemCount() > max_empty_items)\n            branch = branch.compress(this.items.length - rebasedItems.length);\n        return branch;\n    }\n    emptyItemCount() {\n        let count = 0;\n        this.items.forEach(item => { if (!item.step)\n            count++; });\n        return count;\n    }\n    // Compressing a branch means rewriting it to push the air (map-only\n    // items) out. During collaboration, these naturally accumulate\n    // because each remote change adds one. The `upto` argument is used\n    // to ensure that only the items below a given level are compressed,\n    // because `rebased` relies on a clean, untouched set of items in\n    // order to associate old items with rebased steps.\n    compress(upto = this.items.length) {\n        let remap = this.remapping(0, upto), mapFrom = remap.maps.length;\n        let items = [], events = 0;\n        this.items.forEach((item, i) => {\n            if (i >= upto) {\n                items.push(item);\n                if (item.selection)\n                    events++;\n            }\n            else if (item.step) {\n                let step = item.step.map(remap.slice(mapFrom)), map = step && step.getMap();\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n                if (step) {\n                    let selection = item.selection && item.selection.map(remap.slice(mapFrom));\n                    if (selection)\n                        events++;\n                    let newItem = new Item(map.invert(), step, selection), merged, last = items.length - 1;\n                    if (merged = items.length && items[last].merge(newItem))\n                        items[last] = merged;\n                    else\n                        items.push(newItem);\n                }\n            }\n            else if (item.map) {\n                mapFrom--;\n            }\n        }, this.items.length, 0);\n        return new Branch(rope_sequence__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(items.reverse()), events);\n    }\n}\nBranch.empty = new Branch(rope_sequence__WEBPACK_IMPORTED_MODULE_0__[\"default\"].empty, 0);\nfunction cutOffEvents(items, n) {\n    let cutPoint;\n    items.forEach((item, i) => {\n        if (item.selection && (n-- == 0)) {\n            cutPoint = i;\n            return false;\n        }\n    });\n    return items.slice(cutPoint);\n}\nclass Item {\n    constructor(\n    // The (forward) step map for this item.\n    map, \n    // The inverted step\n    step, \n    // If this is non-null, this item is the start of a group, and\n    // this selection is the starting selection for the group (the one\n    // that was active before the first step was applied)\n    selection, \n    // If this item is the inverse of a previous mapping on the stack,\n    // this points at the inverse's offset\n    mirrorOffset) {\n        this.map = map;\n        this.step = step;\n        this.selection = selection;\n        this.mirrorOffset = mirrorOffset;\n    }\n    merge(other) {\n        if (this.step && other.step && !other.selection) {\n            let step = other.step.merge(this.step);\n            if (step)\n                return new Item(step.getMap().invert(), step, this.selection);\n        }\n    }\n}\n// The value of the state field that tracks undo/redo history for that\n// state. Will be stored in the plugin state when the history plugin\n// is active.\nclass HistoryState {\n    constructor(done, undone, prevRanges, prevTime, prevComposition) {\n        this.done = done;\n        this.undone = undone;\n        this.prevRanges = prevRanges;\n        this.prevTime = prevTime;\n        this.prevComposition = prevComposition;\n    }\n}\nconst DEPTH_OVERFLOW = 20;\n// Record a transformation in undo history.\nfunction applyTransaction(history, state, tr, options) {\n    let historyTr = tr.getMeta(historyKey), rebased;\n    if (historyTr)\n        return historyTr.historyState;\n    if (tr.getMeta(closeHistoryKey))\n        history = new HistoryState(history.done, history.undone, null, 0, -1);\n    let appended = tr.getMeta(\"appendedTransaction\");\n    if (tr.steps.length == 0) {\n        return history;\n    }\n    else if (appended && appended.getMeta(historyKey)) {\n        if (appended.getMeta(historyKey).redo)\n            return new HistoryState(history.done.addTransform(tr, undefined, options, mustPreserveItems(state)), history.undone, rangesFor(tr.mapping.maps), history.prevTime, history.prevComposition);\n        else\n            return new HistoryState(history.done, history.undone.addTransform(tr, undefined, options, mustPreserveItems(state)), null, history.prevTime, history.prevComposition);\n    }\n    else if (tr.getMeta(\"addToHistory\") !== false && !(appended && appended.getMeta(\"addToHistory\") === false)) {\n        // Group transforms that occur in quick succession into one event.\n        let composition = tr.getMeta(\"composition\");\n        let newGroup = history.prevTime == 0 ||\n            (!appended && history.prevComposition != composition &&\n                (history.prevTime < (tr.time || 0) - options.newGroupDelay || !isAdjacentTo(tr, history.prevRanges)));\n        let prevRanges = appended ? mapRanges(history.prevRanges, tr.mapping) : rangesFor(tr.mapping.maps);\n        return new HistoryState(history.done.addTransform(tr, newGroup ? state.selection.getBookmark() : undefined, options, mustPreserveItems(state)), Branch.empty, prevRanges, tr.time, composition == null ? history.prevComposition : composition);\n    }\n    else if (rebased = tr.getMeta(\"rebased\")) {\n        // Used by the collab module to tell the history that some of its\n        // content has been rebased.\n        return new HistoryState(history.done.rebased(tr, rebased), history.undone.rebased(tr, rebased), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n    else {\n        return new HistoryState(history.done.addMaps(tr.mapping.maps), history.undone.addMaps(tr.mapping.maps), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n}\nfunction isAdjacentTo(transform, prevRanges) {\n    if (!prevRanges)\n        return false;\n    if (!transform.docChanged)\n        return true;\n    let adjacent = false;\n    transform.mapping.maps[0].forEach((start, end) => {\n        for (let i = 0; i < prevRanges.length; i += 2)\n            if (start <= prevRanges[i + 1] && end >= prevRanges[i])\n                adjacent = true;\n    });\n    return adjacent;\n}\nfunction rangesFor(maps) {\n    let result = [];\n    for (let i = maps.length - 1; i >= 0 && result.length == 0; i--)\n        maps[i].forEach((_from, _to, from, to) => result.push(from, to));\n    return result;\n}\nfunction mapRanges(ranges, mapping) {\n    if (!ranges)\n        return null;\n    let result = [];\n    for (let i = 0; i < ranges.length; i += 2) {\n        let from = mapping.map(ranges[i], 1), to = mapping.map(ranges[i + 1], -1);\n        if (from <= to)\n            result.push(from, to);\n    }\n    return result;\n}\n// Apply the latest event from one branch to the document and shift the event\n// onto the other branch.\nfunction histTransaction(history, state, redo) {\n    let preserveItems = mustPreserveItems(state);\n    let histOptions = historyKey.get(state).spec.config;\n    let pop = (redo ? history.undone : history.done).popEvent(state, preserveItems);\n    if (!pop)\n        return null;\n    let selection = pop.selection.resolve(pop.transform.doc);\n    let added = (redo ? history.done : history.undone).addTransform(pop.transform, state.selection.getBookmark(), histOptions, preserveItems);\n    let newHist = new HistoryState(redo ? added : pop.remaining, redo ? pop.remaining : added, null, 0, -1);\n    return pop.transform.setSelection(selection).setMeta(historyKey, { redo, historyState: newHist });\n}\nlet cachedPreserveItems = false, cachedPreserveItemsPlugins = null;\n// Check whether any plugin in the given state has a\n// `historyPreserveItems` property in its spec, in which case we must\n// preserve steps exactly as they came in, so that they can be\n// rebased.\nfunction mustPreserveItems(state) {\n    let plugins = state.plugins;\n    if (cachedPreserveItemsPlugins != plugins) {\n        cachedPreserveItems = false;\n        cachedPreserveItemsPlugins = plugins;\n        for (let i = 0; i < plugins.length; i++)\n            if (plugins[i].spec.historyPreserveItems) {\n                cachedPreserveItems = true;\n                break;\n            }\n    }\n    return cachedPreserveItems;\n}\n/**\nSet a flag on the given transaction that will prevent further steps\nfrom being appended to an existing history event (so that they\nrequire a separate undo command to undo).\n*/\nfunction closeHistory(tr) {\n    return tr.setMeta(closeHistoryKey, true);\n}\nconst historyKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.PluginKey(\"history\");\nconst closeHistoryKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.PluginKey(\"closeHistory\");\n/**\nReturns a plugin that enables the undo history for an editor. The\nplugin will track undo and redo stacks, which can be used with the\n[`undo`](https://prosemirror.net/docs/ref/#history.undo) and [`redo`](https://prosemirror.net/docs/ref/#history.redo) commands.\n\nYou can set an `\"addToHistory\"` [metadata\nproperty](https://prosemirror.net/docs/ref/#state.Transaction.setMeta) of `false` on a transaction\nto prevent it from being rolled back by undo.\n*/\nfunction history(config = {}) {\n    config = { depth: config.depth || 100,\n        newGroupDelay: config.newGroupDelay || 500 };\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.Plugin({\n        key: historyKey,\n        state: {\n            init() {\n                return new HistoryState(Branch.empty, Branch.empty, null, 0, -1);\n            },\n            apply(tr, hist, state) {\n                return applyTransaction(hist, state, tr, config);\n            }\n        },\n        config,\n        props: {\n            handleDOMEvents: {\n                beforeinput(view, e) {\n                    let inputType = e.inputType;\n                    let command = inputType == \"historyUndo\" ? undo : inputType == \"historyRedo\" ? redo : null;\n                    if (!command)\n                        return false;\n                    e.preventDefault();\n                    return command(view.state, view.dispatch);\n                }\n            }\n        }\n    });\n}\nfunction buildCommand(redo, scroll) {\n    return (state, dispatch) => {\n        let hist = historyKey.getState(state);\n        if (!hist || (redo ? hist.undone : hist.done).eventCount == 0)\n            return false;\n        if (dispatch) {\n            let tr = histTransaction(hist, state, redo);\n            if (tr)\n                dispatch(scroll ? tr.scrollIntoView() : tr);\n        }\n        return true;\n    };\n}\n/**\nA command function that undoes the last change, if any.\n*/\nconst undo = buildCommand(false, true);\n/**\nA command function that redoes the last undone change, if any.\n*/\nconst redo = buildCommand(true, true);\n/**\nA command function that undoes the last change. Don't scroll the\nselection into view.\n*/\nconst undoNoScroll = buildCommand(false, false);\n/**\nA command function that redoes the last undone change. Don't\nscroll the selection into view.\n*/\nconst redoNoScroll = buildCommand(true, false);\n/**\nThe amount of undoable events available in a given state.\n*/\nfunction undoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.done.eventCount : 0;\n}\n/**\nThe amount of redoable events available in a given editor state.\n*/\nfunction redoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.undone.eventCount : 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-history/dist/index.js\n");

/***/ })

};
;