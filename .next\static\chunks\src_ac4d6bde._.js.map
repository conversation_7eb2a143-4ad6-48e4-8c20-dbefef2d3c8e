{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/components/editor/text-editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport Highlight from '@tiptap/extension-highlight';\nimport TextStyle from '@tiptap/extension-text-style';\nimport { Color } from '@tiptap/extension-color';\nimport { useState, useCallback } from 'react';\nimport { Sparkles, Type, Palette, Eye } from 'lucide-react';\n\ninterface TextEditorProps {\n  onTextSelect?: (text: string) => void;\n  onContentChange?: (content: string) => void;\n}\n\nexport default function TextEditor({ onTextSelect, onContentChange }: TextEditorProps) {\n  const [selectedText, setSelectedText] = useState('');\n  const [showToolbar, setShowToolbar] = useState(false);\n\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      Highlight.configure({\n        multicolor: true,\n      }),\n      TextStyle,\n      Color,\n    ],\n    content: `\n      <p>A <mark data-color=\"#fbbf24\">mitose</mark> é um processo fundamental da divisão celular onde uma célula se divide para formar duas células filhas geneticamente idênticas.</p>\n      \n      <p>Durante a <mark data-color=\"#fbbf24\">prófase</mark>, os cromossomos se condensam e tornam-se visíveis ao microscópio. O envelope nuclear começa a se desintegrar.</p>\n      \n      <p>Na <mark data-color=\"#fbbf24\">metáfase</mark>, os cromossomos se alinham no centro da célula, formando a placa metafásica. Este é um momento crucial para garantir que cada célula filha receba o número correto de cromossomos.</p>\n      \n      <p>Durante a <mark data-color=\"#fbbf24\">anáfase</mark>, as cromátides irmãs se separam e migram para polos opostos da célula.</p>\n      \n      <p>Finalmente, na <mark data-color=\"#fbbf24\">telófase</mark>, novos envelopes nucleares se formam ao redor de cada conjunto de cromossomos, completando o processo de divisão.</p>\n    `,\n    onUpdate: ({ editor }) => {\n      const content = editor.getHTML();\n      onContentChange?.(content);\n    },\n    onSelectionUpdate: ({ editor }) => {\n      const { from, to } = editor.state.selection;\n      const text = editor.state.doc.textBetween(from, to, '');\n      \n      if (text.trim().length > 0) {\n        setSelectedText(text.trim());\n        setShowToolbar(true);\n        onTextSelect?.(text.trim());\n      } else {\n        setSelectedText('');\n        setShowToolbar(false);\n      }\n    },\n  });\n\n  const highlightSelection = useCallback((color: string) => {\n    if (editor && selectedText) {\n      editor.chain().focus().toggleHighlight({ color }).run();\n      onTextSelect?.(selectedText);\n    }\n  }, [editor, selectedText, onTextSelect]);\n\n  const generateVisualization = useCallback(() => {\n    if (selectedText) {\n      onTextSelect?.(selectedText);\n    }\n  }, [selectedText, onTextSelect]);\n\n  if (!editor) {\n    return (\n      <div className=\"border rounded-lg p-4 min-h-[400px] bg-gray-50 animate-pulse\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Floating Toolbar */}\n      {showToolbar && selectedText && (\n        <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mb-2 z-10\">\n          <div className=\"bg-white border rounded-lg shadow-lg p-2 flex items-center space-x-2\">\n            <span className=\"text-xs text-gray-600 px-2\">\"{selectedText.slice(0, 20)}...\"</span>\n            <div className=\"w-px h-6 bg-gray-200\"></div>\n            \n            {/* Highlight Colors */}\n            <button\n              onClick={() => highlightSelection('#fbbf24')}\n              className=\"w-6 h-6 bg-yellow-300 rounded hover:scale-110 transition-transform\"\n              title=\"Destacar em amarelo\"\n            />\n            <button\n              onClick={() => highlightSelection('#60a5fa')}\n              className=\"w-6 h-6 bg-blue-400 rounded hover:scale-110 transition-transform\"\n              title=\"Destacar em azul\"\n            />\n            <button\n              onClick={() => highlightSelection('#34d399')}\n              className=\"w-6 h-6 bg-green-400 rounded hover:scale-110 transition-transform\"\n              title=\"Destacar em verde\"\n            />\n            \n            <div className=\"w-px h-6 bg-gray-200\"></div>\n            \n            {/* Generate Visualization Button */}\n            <button\n              onClick={generateVisualization}\n              className=\"flex items-center space-x-1 px-3 py-1 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 transition-colors\"\n            >\n              <Sparkles className=\"h-3 w-3\" />\n              <span>Visualizar</span>\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Editor Toolbar */}\n      <div className=\"border-b p-2 bg-gray-50 rounded-t-lg\">\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => editor.chain().focus().toggleBold().run()}\n            className={`p-2 rounded hover:bg-gray-200 transition-colors ${\n              editor.isActive('bold') ? 'bg-gray-200' : ''\n            }`}\n          >\n            <Type className=\"h-4 w-4\" />\n          </button>\n          \n          <button\n            onClick={() => editor.chain().focus().toggleItalic().run()}\n            className={`p-2 rounded hover:bg-gray-200 transition-colors ${\n              editor.isActive('italic') ? 'bg-gray-200' : ''\n            }`}\n          >\n            <span className=\"text-sm font-serif italic\">I</span>\n          </button>\n\n          <div className=\"w-px h-6 bg-gray-300\"></div>\n\n          <button\n            onClick={() => editor.chain().focus().toggleHighlight({ color: '#fbbf24' }).run()}\n            className={`p-2 rounded hover:bg-gray-200 transition-colors ${\n              editor.isActive('highlight') ? 'bg-gray-200' : ''\n            }`}\n          >\n            <Palette className=\"h-4 w-4\" />\n          </button>\n\n          <div className=\"flex-1\"></div>\n\n          <div className=\"flex items-center space-x-2 text-xs text-gray-600\">\n            <Eye className=\"h-4 w-4\" />\n            <span>Destaque termos para gerar visualizações</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Editor Content */}\n      <div className=\"border border-t-0 rounded-b-lg p-4 min-h-[400px] bg-white prose prose-sm max-w-none\">\n        <EditorContent editor={editor} />\n      </div>\n\n      {/* Selected Text Info */}\n      {selectedText && (\n        <div className=\"mt-2 p-2 bg-indigo-50 border border-indigo-200 rounded text-sm\">\n          <span className=\"text-indigo-700 font-medium\">Texto selecionado:</span>\n          <span className=\"text-indigo-600 ml-2\">\"{selectedText}\"</span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAee,SAAS,WAAW,EAAE,YAAY,EAAE,eAAe,EAAmB;;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,8JAAA,CAAA,UAAU;YACV,sKAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAClB,YAAY;YACd;YACA,0KAAA,CAAA,UAAS;YACT,kKAAA,CAAA,QAAK;SACN;QACD,SAAS,CAAC;;;;;;;;;;IAUV,CAAC;QACD,QAAQ;4CAAE,CAAC,EAAE,MAAM,EAAE;gBACnB,MAAM,UAAU,OAAO,OAAO;gBAC9B,kBAAkB;YACpB;;QACA,iBAAiB;4CAAE,CAAC,EAAE,MAAM,EAAE;gBAC5B,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,KAAK,CAAC,SAAS;gBAC3C,MAAM,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,IAAI;gBAEpD,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG;oBAC1B,gBAAgB,KAAK,IAAI;oBACzB,eAAe;oBACf,eAAe,KAAK,IAAI;gBAC1B,OAAO;oBACL,gBAAgB;oBAChB,eAAe;gBACjB;YACF;;IACF;IAEA,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACtC,IAAI,UAAU,cAAc;gBAC1B,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC;oBAAE;gBAAM,GAAG,GAAG;gBACrD,eAAe;YACjB;QACF;qDAAG;QAAC;QAAQ;QAAc;KAAa;IAEvC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACxC,IAAI,cAAc;gBAChB,eAAe;YACjB;QACF;wDAAG;QAAC;QAAc;KAAa;IAE/B,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,eAAe,8BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;gCAA6B;gCAAE,aAAa,KAAK,CAAC,GAAG;gCAAI;;;;;;;sCACzE,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;;;;;;sCAER,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;;;;;;sCAER,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,OAAM;;;;;;sCAGR,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;4BACtD,WAAW,CAAC,gDAAgD,EAC1D,OAAO,QAAQ,CAAC,UAAU,gBAAgB,IAC1C;sCAEF,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,6LAAC;4BACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;4BACxD,WAAW,CAAC,gDAAgD,EAC1D,OAAO,QAAQ,CAAC,YAAY,gBAAgB,IAC5C;sCAEF,cAAA,6LAAC;gCAAK,WAAU;0CAA4B;;;;;;;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC;oCAAE,OAAO;gCAAU,GAAG,GAAG;4BAC/E,WAAW,CAAC,gDAAgD,EAC1D,OAAO,QAAQ,CAAC,eAAe,gBAAgB,IAC/C;sCAEF,cAAA,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGrB,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qKAAA,CAAA,gBAAa;oBAAC,QAAQ;;;;;;;;;;;YAIxB,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAA8B;;;;;;kCAC9C,6LAAC;wBAAK,WAAU;;4BAAuB;4BAAE;4BAAa;;;;;;;;;;;;;;;;;;;AAKhE;GAjKwB;;QAIP,qKAAA,CAAA,YAAS;;;KAJF", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/components/visualization/visualization-panel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Play, Download, <PERSON>rkles, Loader2, Image as ImageIcon, Video } from 'lucide-react';\n\ninterface VisualizationPanelProps {\n  selectedText: string;\n  onGenerate?: (text: string, type: 'image' | 'video') => void;\n}\n\ninterface GeneratedContent {\n  type: 'image' | 'video';\n  url: string;\n  prompt: string;\n  timestamp: number;\n}\n\nexport default function VisualizationPanel({ selectedText, onGenerate }: VisualizationPanelProps) {\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);\n  const [generationType, setGenerationType] = useState<'image' | 'video'>('image');\n  const [history, setHistory] = useState<GeneratedContent[]>([]);\n\n  // Simulate content generation\n  const generateContent = async (text: string, type: 'image' | 'video') => {\n    setIsGenerating(true);\n    \n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    // Mock generated content\n    const mockContent: GeneratedContent = {\n      type,\n      url: type === 'video' \n        ? 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'\n        : 'https://via.placeholder.com/600x400/4f46e5/ffffff?text=' + encodeURIComponent(text),\n      prompt: `Visualização científica de: ${text}`,\n      timestamp: Date.now()\n    };\n    \n    setGeneratedContent(mockContent);\n    setHistory(prev => [mockContent, ...prev.slice(0, 4)]); // Keep last 5\n    setIsGenerating(false);\n    \n    onGenerate?.(text, type);\n  };\n\n  const handleGenerate = () => {\n    if (selectedText.trim()) {\n      generateContent(selectedText, generationType);\n    }\n  };\n\n  const downloadContent = () => {\n    if (generatedContent) {\n      const link = document.createElement('a');\n      link.href = generatedContent.url;\n      link.download = `${selectedText.replace(/\\s+/g, '_')}_${generatedContent.type}.${generatedContent.type === 'video' ? 'mp4' : 'jpg'}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Visualização</h3>\n        <div className=\"flex items-center space-x-2\">\n          {generatedContent && (\n            <>\n              <button \n                onClick={() => {/* Play functionality */}}\n                className=\"p-2 text-gray-600 hover:text-indigo-600 transition-colors\"\n                title=\"Reproduzir\"\n              >\n                <Play className=\"h-5 w-5\" />\n              </button>\n              <button \n                onClick={downloadContent}\n                className=\"p-2 text-gray-600 hover:text-indigo-600 transition-colors\"\n                title=\"Baixar\"\n              >\n                <Download className=\"h-5 w-5\" />\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Content Type Selector */}\n      <div className=\"flex items-center space-x-2 mb-4\">\n        <span className=\"text-sm text-gray-600\">Tipo:</span>\n        <div className=\"flex bg-gray-100 rounded-lg p-1\">\n          <button\n            onClick={() => setGenerationType('image')}\n            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${\n              generationType === 'image' \n                ? 'bg-white text-indigo-600 shadow-sm' \n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <ImageIcon className=\"h-4 w-4\" />\n            <span>Imagem</span>\n          </button>\n          <button\n            onClick={() => setGenerationType('video')}\n            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${\n              generationType === 'video' \n                ? 'bg-white text-indigo-600 shadow-sm' \n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <Video className=\"h-4 w-4\" />\n            <span>Vídeo</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      <div className=\"border rounded-lg min-h-[350px] bg-gray-50 relative overflow-hidden\">\n        {isGenerating ? (\n          // Loading State\n          <div className=\"absolute inset-0 flex items-center justify-center bg-white/90\">\n            <div className=\"text-center\">\n              <Loader2 className=\"h-8 w-8 text-indigo-600 animate-spin mx-auto mb-4\" />\n              <p className=\"text-gray-600 mb-2\">Gerando visualização...</p>\n              <p className=\"text-sm text-gray-500\">\"{selectedText}\"</p>\n            </div>\n          </div>\n        ) : generatedContent ? (\n          // Generated Content\n          <div className=\"p-4\">\n            {generatedContent.type === 'video' ? (\n              <video \n                controls \n                className=\"w-full h-64 rounded-lg bg-black\"\n                poster=\"https://via.placeholder.com/600x400/4f46e5/ffffff?text=Video+Preview\"\n              >\n                <source src={generatedContent.url} type=\"video/mp4\" />\n                Seu navegador não suporta vídeo HTML5.\n              </video>\n            ) : (\n              <img \n                src={generatedContent.url} \n                alt={generatedContent.prompt}\n                className=\"w-full h-64 object-cover rounded-lg\"\n              />\n            )}\n            \n            <div className=\"mt-4 p-3 bg-white rounded-lg border\">\n              <p className=\"text-sm text-gray-600 mb-1\">Prompt gerado:</p>\n              <p className=\"text-sm text-gray-900\">{generatedContent.prompt}</p>\n            </div>\n          </div>\n        ) : (\n          // Empty State\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Sparkles className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                {selectedText \n                  ? `Pronto para visualizar: \"${selectedText.slice(0, 30)}${selectedText.length > 30 ? '...' : ''}\"`\n                  : 'Destaque um termo no texto para gerar visualização'\n                }\n              </p>\n              {selectedText && (\n                <button\n                  onClick={handleGenerate}\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto\"\n                >\n                  <Sparkles className=\"h-4 w-4\" />\n                  <span>Gerar {generationType === 'video' ? 'Vídeo' : 'Imagem'}</span>\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* History */}\n      {history.length > 0 && (\n        <div className=\"mt-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Histórico</h4>\n          <div className=\"flex space-x-2 overflow-x-auto pb-2\">\n            {history.map((item, index) => (\n              <button\n                key={item.timestamp}\n                onClick={() => setGeneratedContent(item)}\n                className=\"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-transparent hover:border-indigo-300 transition-colors\"\n              >\n                {item.type === 'video' ? (\n                  <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n                    <Video className=\"h-6 w-6 text-gray-500\" />\n                  </div>\n                ) : (\n                  <img \n                    src={item.url} \n                    alt={`Histórico ${index + 1}`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                )}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiBe,SAAS,mBAAmB,EAAE,YAAY,EAAE,UAAU,EAA2B;;IAC9F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAE7D,8BAA8B;IAC9B,MAAM,kBAAkB,OAAO,MAAc;QAC3C,gBAAgB;QAEhB,0BAA0B;QAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,yBAAyB;QACzB,MAAM,cAAgC;YACpC;YACA,KAAK,SAAS,UACV,sEACA,4DAA4D,mBAAmB;YACnF,QAAQ,CAAC,4BAA4B,EAAE,MAAM;YAC7C,WAAW,KAAK,GAAG;QACrB;QAEA,oBAAoB;QACpB,WAAW,CAAA,OAAQ;gBAAC;mBAAgB,KAAK,KAAK,CAAC,GAAG;aAAG,GAAG,cAAc;QACtE,gBAAgB;QAEhB,aAAa,MAAM;IACrB;IAEA,MAAM,iBAAiB;QACrB,IAAI,aAAa,IAAI,IAAI;YACvB,gBAAgB,cAAc;QAChC;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,kBAAkB;YACpB,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG,iBAAiB,GAAG;YAChC,KAAK,QAAQ,GAAG,GAAG,aAAa,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,iBAAiB,IAAI,CAAC,CAAC,EAAE,iBAAiB,IAAI,KAAK,UAAU,QAAQ,OAAO;YACpI,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;kCACZ,kCACC;;8CACE,6LAAC;oCACC,SAAS,KAA+B;oCACxC,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,wEAAwE,EAClF,mBAAmB,UACf,uCACA,qCACJ;;kDAEF,6LAAC,uMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,wEAAwE,EAClF,mBAAmB,UACf,uCACA,qCACJ;;kDAEF,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACZ,eACC,gBAAgB;8BAChB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAE,WAAU;;oCAAwB;oCAAE;oCAAa;;;;;;;;;;;;;;;;;2BAGtD,mBACF,oBAAoB;8BACpB,6LAAC;oBAAI,WAAU;;wBACZ,iBAAiB,IAAI,KAAK,wBACzB,6LAAC;4BACC,QAAQ;4BACR,WAAU;4BACV,QAAO;;8CAEP,6LAAC;oCAAO,KAAK,iBAAiB,GAAG;oCAAE,MAAK;;;;;;gCAAc;;;;;;iDAIxD,6LAAC;4BACC,KAAK,iBAAiB,GAAG;4BACzB,KAAK,iBAAiB,MAAM;4BAC5B,WAAU;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAE,WAAU;8CAAyB,iBAAiB,MAAM;;;;;;;;;;;;;;;;;2BAIjE,cAAc;8BACd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAE,WAAU;0CACV,eACG,CAAC,yBAAyB,EAAE,aAAa,KAAK,CAAC,GAAG,MAAM,aAAa,MAAM,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC,GAChG;;;;;;4BAGL,8BACC,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;;4CAAK;4CAAO,mBAAmB,UAAU,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS/D,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;gCAEC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET,KAAK,IAAI,KAAK,wBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;yDAGnB,6LAAC;oCACC,KAAK,KAAK,GAAG;oCACb,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG;oCAC7B,WAAU;;;;;;+BAZT,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;AAsBnC;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/canva-saas/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { BookO<PERSON>, Sparkles, Play, Download } from 'lucide-react';\nimport TextEditor from '@/components/editor/text-editor';\nimport VisualizationPanel from '@/components/visualization/visualization-panel';\n\nexport default function Home() {\n  const [selectedText, setSelectedText] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n\n  const handleTextSelect = (text: string) => {\n    setSelectedText(text);\n  };\n\n  const handleGenerate = (text: string, type: 'image' | 'video') => {\n    setIsGenerating(true);\n    // Here we'll integrate with OpenAI and media generation APIs\n    console.log('Generating', type, 'for:', text);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-2\">\n              <BookOpen className=\"h-8 w-8 text-indigo-600\" />\n              <h1 className=\"text-2xl font-bold text-gray-900\">StudyVision AI</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\">\n                Login\n              </button>\n              <button className=\"px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700\">\n                Começar Grátis\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Transforme Texto em Visualizações Imersivas\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Destaque qualquer conceito científico e veja-o ganhar vida através de\n            vídeos e imagens geradas por IA em tempo real.\n          </p>\n        </div>\n\n        {/* Demo Section */}\n        <div className=\"grid lg:grid-cols-2 gap-8 mb-12\">\n          {/* Text Editor */}\n          <TextEditor\n            onTextSelect={handleTextSelect}\n            onContentChange={(content) => console.log('Content changed:', content)}\n          />\n\n          {/* Visualization Panel */}\n          <VisualizationPanel\n            selectedText={selectedText}\n            onGenerate={handleGenerate}\n          />\n        </div>\n\n        {/* Features */}\n        <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n          <div className=\"text-center\">\n            <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n              <Sparkles className=\"h-6 w-6 text-indigo-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">IA Avançada</h3>\n            <p className=\"text-gray-600\">\n              Powered by GPT-4 e VEO3 para gerar visualizações precisas e educativas\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n              <Play className=\"h-6 w-6 text-indigo-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Tempo Real</h3>\n            <p className=\"text-gray-600\">\n              Visualizações geradas instantaneamente conforme você estuda\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n              <Download className=\"h-6 w-6 text-indigo-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Exportação</h3>\n            <p className=\"text-gray-600\">\n              Baixe vídeos e imagens para usar em apresentações e estudos\n            </p>\n          </div>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center\">\n          <button className=\"px-8 py-4 bg-indigo-600 text-white text-lg font-semibold rounded-lg hover:bg-indigo-700 transition-colors\">\n            Começar Agora - Grátis\n          </button>\n          <p className=\"text-sm text-gray-600 mt-2\">\n            10 visualizações gratuitas • Sem cartão de crédito\n          </p>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC,MAAc;QACpC,gBAAgB;QAChB,6DAA6D;QAC7D,QAAQ,GAAG,CAAC,cAAc,MAAM,QAAQ;IAC1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAAkE;;;;;;kDAGpF,6LAAC;wCAAO,WAAU;kDAAwF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlH,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAOzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,iJAAA,CAAA,UAAU;gCACT,cAAc;gCACd,iBAAiB,CAAC,UAAY,QAAQ,GAAG,CAAC,oBAAoB;;;;;;0CAIhE,6LAAC,gKAAA,CAAA,UAAkB;gCACjB,cAAc;gCACd,YAAY;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAOjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAA4G;;;;;;0CAG9H,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;GA1GwB;KAAA", "debugId": null}}]}