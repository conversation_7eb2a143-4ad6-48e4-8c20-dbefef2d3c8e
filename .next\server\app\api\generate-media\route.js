(()=>{var e={};e.id=724,e.ids=[724],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3782:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>v});var a={};r.r(a),r.d(a,{OPTIONS:()=>u,POST:()=>c});var i=r(6559),o=r(8088),s=r(7719),n=r(2190);async function p(e){let t=Date.now();try{let r=e.dimensions||{width:800,height:600},a=encodeURIComponent(e.prompt),i=`https://via.placeholder.com/${r.width}x${r.height}/4f46e5/ffffff?text=${a.slice(0,50)}`;return await new Promise(e=>setTimeout(e,2e3+3e3*Math.random())),{success:!0,url:i,metadata:{prompt:e.prompt,type:"image",timestamp:new Date().toISOString(),provider:"placeholder",processingTime:Date.now()-t}}}catch(r){return console.error("Image generation failed:",r),{success:!1,error:r instanceof Error?r.message:"Image generation failed",metadata:{prompt:e.prompt,type:"image",timestamp:new Date().toISOString(),provider:"placeholder",processingTime:Date.now()-t}}}}async function d(e){let t=Date.now();try{return e.duration,await new Promise(e=>setTimeout(e,5e3+1e4*Math.random())),{success:!0,url:"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",metadata:{prompt:e.prompt,type:"video",timestamp:new Date().toISOString(),provider:"sample",processingTime:Date.now()-t}}}catch(r){return console.error("Video generation failed:",r),{success:!1,error:r instanceof Error?r.message:"Video generation failed",metadata:{prompt:e.prompt,type:"video",timestamp:new Date().toISOString(),provider:"sample",processingTime:Date.now()-t}}}}async function l(e){return"image"===e.type?p(e):"video"===e.type?d(e):{success:!1,error:'Invalid media type. Must be "image" or "video".',metadata:{prompt:e.prompt,type:e.type,timestamp:new Date().toISOString(),provider:"none"}}}async function c(e){try{let{prompt:t,type:r,style:a,duration:i,dimensions:o}=await e.json(),s={prompt:t,type:r,style:a||"educational",duration:i||5,dimensions:o||{width:800,height:600}},p=s.prompt&&0!==s.prompt.trim().length?s.prompt.length>1e3?{valid:!1,error:"Prompt is too long (max 1000 characters)"}:["image","video"].includes(s.type)?"video"===s.type&&s.duration&&(s.duration<1||s.duration>30)?{valid:!1,error:"Video duration must be between 1 and 30 seconds"}:{valid:!0}:{valid:!1,error:'Type must be "image" or "video"'}:{valid:!1,error:"Prompt is required"};if(!p.valid)return n.NextResponse.json({error:p.error},{status:400});let d=function(e,t,r="educational"){let a={realistic:"photorealistic, detailed, accurate",illustration:"clean illustration style, vector-like, simplified",scientific:"scientific accuracy, technical precision, research quality",educational:"educational purpose, clear for learning, student-friendly"},i=a[r]||a.educational;return`${e}, ${i}, ${({image:["high quality scientific illustration","detailed educational diagram","clear visual representation","professional medical/scientific style","bright colors for learning","4K resolution"],video:["smooth educational animation","step-by-step visual process","clear transitions","professional educational style","engaging visual storytelling","HD quality"]})[t].join(", ")}`}(s.prompt,s.type,s.style);s.prompt=d;let c=await l(s);if(c.success)return n.NextResponse.json({success:!0,data:{url:c.url,metadata:c.metadata}});return n.NextResponse.json({error:c.error||"Media generation failed",metadata:c.metadata},{status:500})}catch(e){return console.error("Error in generate-media API:",e),n.NextResponse.json({error:"Failed to generate media",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(){return new n.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let m=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/generate-media/route",pathname:"/api/generate-media",filename:"route",bundlePath:"app/api/generate-media/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\canva-saas\\src\\app\\api\\generate-media\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:v,serverHooks:y}=m;function f(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:v})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580],()=>r(3782));module.exports=a})();