'use client';

import { useState, useEffect } from 'react';
import { Users, Wifi, WifiOff, Eye, Activity } from 'lucide-react';
import { useWebSocket } from '@/hooks/useWebSocket';

interface RealtimeStatusProps {
  onTextSelected?: (data: { userId: string; selectedText: string; timestamp: number }) => void;
  onAnalysisUpdate?: (data: any) => void;
  onGenerationUpdate?: (data: any) => void;
}

export default function RealtimeStatus({
  onTextSelected,
  onAnalysisUpdate,
  onGenerationUpdate
}: RealtimeStatusProps) {
  const [sessionId, setSessionId] = useState<string>('');
  const [recentActivity, setRecentActivity] = useState<string[]>([]);

  // Generate session ID only on client side to avoid hydration mismatch
  useEffect(() => {
    setSessionId(`session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  }, []);

  const addActivity = (activity: string) => {
    setRecentActivity(prev => [activity, ...prev.slice(0, 4)]);
  };

  const {
    connected,
    users,
    error,
    joinSession,
    leaveSession,
  } = useWebSocket({
    onTextSelected: (data) => {
      addActivity(`Usuário selecionou: "${data.selectedText.slice(0, 30)}..."`);
      onTextSelected?.(data);
    },
    onUserJoined: (data) => {
      addActivity(`Usuário ${data.userId.slice(-6)} entrou na sessão`);
    },
    onUserLeft: (data) => {
      addActivity(`Usuário ${data.userId.slice(-6)} saiu da sessão`);
    },
    onAnalysisStarted: (data) => {
      addActivity(`Análise iniciada: ${data.text.slice(0, 30)}...`);
      onAnalysisUpdate?.(data);
    },
    onAnalysisCompleted: (data) => {
      addActivity(`Análise concluída`);
      onAnalysisUpdate?.(data);
    },
    onGenerationStarted: (data) => {
      addActivity(`Geração ${data.type} iniciada`);
      onGenerationUpdate?.(data);
    },
    onGenerationCompleted: (data) => {
      addActivity(`Geração ${data.type} concluída`);
      onGenerationUpdate?.(data);
    },
  });

  // Auto-join session on mount
  useEffect(() => {
    if (connected && sessionId) {
      addActivity('Conectado ao sistema em tempo real (modo simulação)');
      // joinSession(sessionId); // Uncomment when using real WebSocket server
    }

    return () => {
      // leaveSession(); // Uncomment when using real WebSocket server
    };
  }, [connected, sessionId]);

  return (
    <div className="bg-white border rounded-lg p-4 shadow-sm">
      {/* Connection Status */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          {connected ? (
            <Wifi className="h-4 w-4 text-green-600" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-600" />
          )}
          <span className={`text-sm font-medium ${connected ? 'text-green-600' : 'text-red-600'}`}>
            {connected ? 'Conectado' : 'Desconectado'}
          </span>
        </div>
        
        {/* Users Count */}
        <div className="flex items-center space-x-1 text-sm text-gray-600">
          <Users className="h-4 w-4" />
          <span>{users.length + 1} usuário{users.length !== 0 ? 's' : ''}</span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
          Erro de conexão: {error}
        </div>
      )}

      {/* Session Info */}
      {sessionId && (
        <div className="mb-4 p-2 bg-gray-50 rounded text-xs">
          <div className="flex items-center space-x-1 text-gray-600">
            <Eye className="h-3 w-3" />
            <span>Sessão: {sessionId.slice(-8)}</span>
          </div>
        </div>
      )}

      {/* Active Users */}
      {users.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Usuários Ativos</h4>
          <div className="space-y-1">
            {users.map((userId) => (
              <div key={userId} className="flex items-center space-x-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Usuário {userId.slice(-6)}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Activity */}
      {recentActivity.length > 0 && (
        <div>
          <div className="flex items-center space-x-1 mb-2">
            <Activity className="h-4 w-4 text-gray-600" />
            <h4 className="text-sm font-medium text-gray-900">Atividade Recente</h4>
          </div>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {recentActivity.map((activity, index) => (
              <div 
                key={index} 
                className="text-xs text-gray-600 p-2 bg-gray-50 rounded"
                style={{ opacity: 1 - (index * 0.2) }}
              >
                {activity}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Connection Instructions */}
      {!connected && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm">
          <p className="text-blue-800 font-medium mb-1">Conectando...</p>
          <p className="text-blue-600">
            O sistema de tempo real permite colaboração e atualizações instantâneas.
          </p>
        </div>
      )}
    </div>
  );
}
