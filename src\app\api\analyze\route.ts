import { NextRequest, NextResponse } from 'next/server';
import { analyzeScientificText, generateEducationalSuggestions } from '@/lib/openai';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { selectedText, context, visualizationType } = body;

    // Validate input
    if (!selectedText || selectedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'Selected text is required' },
        { status: 400 }
      );
    }

    if (selectedText.length > 500) {
      return NextResponse.json(
        { error: 'Selected text is too long (max 500 characters)' },
        { status: 400 }
      );
    }

    // Analyze the scientific text
    const analysisResult = await analyzeScientificText(
      selectedText,
      context || '',
      visualizationType || 'image'
    );

    // Generate educational suggestions
    const educationalSuggestions = await generateEducationalSuggestions(
      analysisResult.concept,
      analysisResult.difficulty
    );

    // Return the complete analysis
    return NextResponse.json({
      success: true,
      data: {
        ...analysisResult,
        educationalSuggestions,
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error) {
    console.error('Error in analyze API:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to analyze text',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
